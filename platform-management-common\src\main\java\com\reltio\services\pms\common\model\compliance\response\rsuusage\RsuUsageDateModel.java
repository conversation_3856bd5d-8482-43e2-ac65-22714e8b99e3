package com.reltio.services.pms.common.model.compliance.response.rsuusage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The type Rsu usage by date model.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class RsuUsageDateModel {
    /**
     * The Date.
     */
    @JsonProperty("date")
    String date;

    /**
     * The Rsu usage.
     */
    @JsonProperty("rsuUsage")
    String rsuUsage;
}
