package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor(force = true)
public class UpdateDNBKeyRequest {

    @JsonProperty("url")
    private final String url;

    @JsonProperty("authUrl")
    private final String authUrl;

    @JsonProperty("username")
    private final String username;

    @JsonProperty("password")
    private final String password;
    // Equals, hashCode, and toString methods are generated by <PERSON>mbo<PERSON>


    @Getter
    @AllArgsConstructor
    public static class DnbPojo {
        private final UpdateDNBKeyRequest dnb;
        // Equals, hashCode, and toString methods are generated by Lombok

    }

}
