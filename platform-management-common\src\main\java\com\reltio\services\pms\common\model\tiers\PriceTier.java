package com.reltio.services.pms.common.model.tiers;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PriceTier {

    @JsonProperty("flatAmount")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer flatAmount;

    @JsonProperty("flatAmountDecimal")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer flatAmountDecimal;

    @JsonProperty("unitAmount")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer unitAmount;

    @JsonProperty("unitAmountDecimal")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer unitAmountDecimal;

    @JsonProperty("upTo")
    private Object upTo;

    @JsonCreator
    public PriceTier(@JsonProperty("flatAmount") Integer flatAmount, @JsonProperty("flatAmountDecimal") Integer flatAmountDecimal,
                     @JsonProperty("unitAmount") Integer unitAmount, @JsonProperty("unitAmountDecimal") Integer unitAmountDecimal, @JsonProperty("upTo") Object upTo) {
        this.flatAmount = flatAmount;
        this.flatAmountDecimal = flatAmountDecimal;
        this.unitAmount = unitAmount;
        this.unitAmountDecimal = unitAmountDecimal;
        this.upTo = upTo;
    }

    public Integer getFlatAmount() {
        return flatAmount;
    }

    public void setFlatAmount(Integer flatAmount) {
        this.flatAmount = flatAmount;
    }

    public Integer getFlatAmountDecimal() {
        return flatAmountDecimal;
    }

    public void setFlatAmountDecimal(Integer flatAmountDecimal) {
        this.flatAmountDecimal = flatAmountDecimal;
    }

    public Integer getUnitAmount() {
        return unitAmount;
    }

    public void setUnitAmount(Integer unitAmount) {
        this.unitAmount = unitAmount;
    }

    public Integer getUnitAmountDecimal() {
        return unitAmountDecimal;
    }

    public void setUnitAmountDecimal(Integer unitAmountDecimal) {
        this.unitAmountDecimal = unitAmountDecimal;
    }

    public Object getUpTo() {
        return upTo;
    }

    public void setUpTo(Object upTo) {
        this.upTo = upTo;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        PriceTier priceTier = (PriceTier) o;

        if (flatAmount != null ? !flatAmount.equals(priceTier.flatAmount) : priceTier.flatAmount != null) return false;
        if (flatAmountDecimal != null ? !flatAmountDecimal.equals(priceTier.flatAmountDecimal) : priceTier.flatAmountDecimal != null)
            return false;
        if (unitAmount != null ? !unitAmount.equals(priceTier.unitAmount) : priceTier.unitAmount != null) return false;
        if (unitAmountDecimal != null ? !unitAmountDecimal.equals(priceTier.unitAmountDecimal) : priceTier.unitAmountDecimal != null)
            return false;
        return upTo.equals(priceTier.upTo);
    }

    @Override
    public int hashCode() {
        int result = flatAmount != null ? flatAmount.hashCode() : 0;
        result = 31 * result + (flatAmountDecimal != null ? flatAmountDecimal.hashCode() : 0);
        result = 31 * result + (unitAmount != null ? unitAmount.hashCode() : 0);
        result = 31 * result + (unitAmountDecimal != null ? unitAmountDecimal.hashCode() : 0);
        result = 31 * result + upTo.hashCode();
        return result;
    }

    public Map<String, Object> priceTierFormattedMap(){
        Map<String, Object> priceTierFormattedMap = new HashMap<>();
        if (flatAmount != null){
            priceTierFormattedMap.put("flat_amount",flatAmount);
        }
        if (flatAmountDecimal != null) {
            priceTierFormattedMap.put("flat_amount_decimal", flatAmountDecimal);
        }
        if (unitAmount != null) {
            priceTierFormattedMap.put("unit_amount", unitAmount);
        }
        if (unitAmountDecimal != null) {
            priceTierFormattedMap.put("unit_amount_decimal", unitAmountDecimal);
        }
        priceTierFormattedMap.put("up_to",upTo);
        return priceTierFormattedMap;
    }
}
