apiVersion: v1
kind: Service
metadata:
  name: {{.Values.serviceName}}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{.Values.serviceName}}
    service: {{.Values.serviceName}}
spec:
  ports:
    - port: 8080
      targetPort: 8080
      name: http
      protocol: TCP
    - port: 9090
      targetPort: 9090
      name: http-health
      protocol: TCP
  selector:
    app: {{.Values.serviceName}}
