package com.reltio.services.pms.common.model.jobs;


import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.TenantPurpose;

import java.util.Map;
import java.util.Set;

public class JobDetailBuilder {
    private String mdmTenantId;
    private Set<String> authUsers;
    private Set<JobDetail.ApplicationClients> clients;
    private String productEdition;
    private String customerId;
    private  String rdmTenantId;
    private Map<String, JobDetail.StreamingDetails> streamingDetails;
    private JobDetail.S3Details s3DetailsMap;
    private String tenantName;
    private TenantPurpose tenantPurpose;
    private String  authUrl ;
    private String rdmUrl;
    private String s3BucketName;
    private String reltioRegion;
    private String secretKey;
    private String geographicalRegion;
    private String accessKey;
    private String apiUrl;
    public  String uiUrl;
    private String region;
    private  Map<String, JobDetail.SfdcUserDetails>  sfdcUserDetailsMap;
    private TenantSize tenantSize;
    private Map<String,JobDetail.AzureBlobStorageDetails> azureBlobStorageDetails;

    public void setRdmUrl(String rdmUrl) {
        this.rdmUrl = rdmUrl;
    }

    public void setReltioRegion(String reltioRegion) {
        this.reltioRegion = reltioRegion;
    }

    public void setGeographicalRegion(String geographicalRegion) {
        this.geographicalRegion = geographicalRegion;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public void setUiUrl(String uiUrl) {
        this.uiUrl = uiUrl;
    }

    public void setS3BucketName(String s3BucketName) {
        this.s3BucketName = s3BucketName;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public void setTenantPurpose(TenantPurpose tenantPurpose) {
        this.tenantPurpose = tenantPurpose;
    }

    public void setAuthUrl(String authUrl) {
        this.authUrl = authUrl;
    }

    public void setRDMUrl(String RDMUrl) {
        this.rdmUrl = RDMUrl;
    }

    public void setQueueUrl(String queueUrl) {
        this.QueueUrl = queueUrl;
    }

    private String QueueUrl;

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public void setS3DetailsMap(JobDetail.S3Details s3DetailsMap) {
        this.s3DetailsMap = s3DetailsMap;
    }

    public void setSfdcUserDetailsMap(Map<String, JobDetail.SfdcUserDetails> sfdcUserDetailsMap) {
        this.sfdcUserDetailsMap = sfdcUserDetailsMap;
    }

    public void setTenantSize(TenantSize tenantSize) {
        this.tenantSize = tenantSize;
    }

    public JobDetailBuilder() {
    }

    public JobDetailBuilder setMdmTenantId(String mdmTenantId) {
        this.mdmTenantId = mdmTenantId;
        return this;
    }

    public JobDetailBuilder setAuthUsers(Set<String> authUsers) {
        this.authUsers = authUsers;
        return this;
    }

    public JobDetailBuilder setRdmTenantId(String rdmTenantId) {
        this.rdmTenantId = rdmTenantId;
        return this;
    }

    public JobDetailBuilder setCustomerId(String customerId) {
        this.customerId = customerId;
        return this;
    }

    public JobDetailBuilder setClients(Set<JobDetail.ApplicationClients> clients) {
        this.clients = clients;
        return this;
    }


    public JobDetailBuilder setProductEdition(String productEdition) {
        this.productEdition = productEdition;
        return this;
    }

    public void setStreamingDetails(Map<String, JobDetail.StreamingDetails> streamingDetails) {
        this.streamingDetails = streamingDetails;
    }

    public void setAzureBlobStorageDetails(Map<String,JobDetail.AzureBlobStorageDetails> azureBlobStorageDetails) {
        this.azureBlobStorageDetails = azureBlobStorageDetails;
    }

    public JobDetail createJobDetail() {
        return JobDetail.createJobDetail(mdmTenantId, authUsers, clients, customerId, productEdition, rdmTenantId, streamingDetails,s3DetailsMap,tenantName,tenantPurpose,reltioRegion,geographicalRegion,apiUrl,uiUrl,rdmUrl,authUrl,region, sfdcUserDetailsMap, tenantSize, azureBlobStorageDetails);
    }
}