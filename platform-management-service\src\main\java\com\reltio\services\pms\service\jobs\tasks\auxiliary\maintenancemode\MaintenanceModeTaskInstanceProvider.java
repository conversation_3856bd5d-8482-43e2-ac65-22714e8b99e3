package com.reltio.services.pms.service.jobs.tasks.auxiliary.maintenancemode;

import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.jobs.tasks.maintenance.MaintenanceModeTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.maintenance.MaintenanceModeTaskInstanceParams;
import com.reltio.services.pms.common.model.pipeline.tasks.MaintenanceModeTaskConfig;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.ValidationErrorCollector;
import com.reltio.services.pms.service.jobs.tasks.TaskInstanceProvider;
import org.apache.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.UUID;

@Service
public class MaintenanceModeTaskInstanceProvider implements TaskInstanceProvider<MaintenanceModeTaskInstance,
        MaintenanceModeTaskConfig, MaintenanceModeTaskInstanceParams> {

    @Override
    public TaskType getTaskType() {
        return TaskType.MAINTENANCE_MODE_TASK;
    }

    @Override
    public void validateTaskConfig(String envId, MaintenanceModeTaskConfig pipelineTaskConfig,
                                   ValidationErrorCollector validationErrorCollector) {
        if (pipelineTaskConfig.getSecondsDelay() < 0) {
            validationErrorCollector.addValidationError(new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.SC_INTERNAL_SERVER_ERROR));
        }
    }

    @Override
    public MaintenanceModeTaskInstance getTaskDetail(String envId, String jobId, MaintenanceModeTaskConfig pipelineTaskConfig,
                                                     MaintenanceModeTaskInstanceParams noInput) {
        return new MaintenanceModeTaskInstance(
                UUID.randomUUID().toString(),
                TaskType.MAINTENANCE_MODE_TASK.toString(),
                jobId,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                envId,
                noInput.getTenantName(),
                pipelineTaskConfig.getValueToSet(),
                pipelineTaskConfig.getSecondsDelay(),
                Collections.emptyList(),
                0L
        );
    }
}
