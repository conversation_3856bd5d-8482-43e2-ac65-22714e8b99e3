package com.reltio.services.pms.common.model.request;


import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.ContractStage;
import com.reltio.services.pms.common.sales.model.ProcessType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import java.util.EnumSet;
import java.util.List;

@AllArgsConstructor
@Builder
@Getter
@NoArgsConstructor
@Setter
public class ProcessContract {
    @NotNull(message = "contractId is required.") String contractId;
    @NotNull(message = "ContractStage is required [CHANGE, NEW, RENEWAL, EXPIRED]") ContractStage contractStage;
    @NotNull(message = "ProcessType is required [DELTA_ONLY, PROVISION_JOBS,SYNC_CONTRACT]") ProcessType processType;
    @Nullable
    List<MatchDataArn> matchDataArnList;
    @Nullable
    EnumSet<PMSProductName> skipTasks;
}
