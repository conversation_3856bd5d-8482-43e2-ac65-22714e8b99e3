package com.reltio.services.pms.convertors.sales.addon;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.common.sales.model.addon.DTProductConfig;
import com.reltio.services.pms.convertors.sales.AbstractTenantAddOnProductProvider;
import com.reltio.services.pms.service.SalesPackageService;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

@Service
public class DT340BProductProvider extends AbstractTenantAddOnProductProvider<DTProductConfig> {


    public DT340BProductProvider(SalesPackageService salesPackageService) {
        super(salesPackageService);
    }


    @Override
    public PMSProductName getProductName() {
        return PMSProductName.DT_340B;
    }

    @Override
    public Map<String, Set<String>> getProductCodesByTenant() {
        return salesPackageService.getSalesAddOnsByProductCodes(PMSProductName.DT_340B);
    }

    @Override
    public DTProductConfig getProductConfig(Set<SalesConfig> salesConfigs, Set<String> tenantCodes, String currentTenantCode) {
        if (salesConfigs.isEmpty()) {
            return null;
        }

        DTProductConfig productConfig = new DTProductConfig(getProductName());
        productConfig.addAllSalesConfigs(salesConfigs);
        productConfig.setQuantity(getQuantity(productConfig));
        return productConfig;
    }

}
