package com.reltio.services.pms.common.model.supportability;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

/**
 * ClusterModel
 * Created by aravuru
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class HZClusterStatsDto implements StatsDto {
    @JsonProperty("startTime")
    private Long startTime;
    @JsonProperty("endTime")
    private Long endTime;
    @JsonProperty("cpu")
    private CpuUsage cpu;
    @JsonProperty("memory")
    private MemoryUsage memory;
    @JsonProperty("alivePods")
    private AlivePodsDto alivePodsDto;


    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HZClusterStatsDto clusterStatsDto = (HZClusterStatsDto) o;
        return Objects.equals(getStartTime(), clusterStatsDto.getStartTime()) &&
                Objects.equals(getEndTime(), clusterStatsDto.getEndTime()) &&
                Objects.equals(getCpu().getAverage(), clusterStatsDto.getCpu().getAverage()) &&
                Objects.equals(getCpu().getMax(), clusterStatsDto.getCpu().getMax()) &&
                Objects.equals(getCpu().getP90(), clusterStatsDto.getCpu().getP90()) &&
                Objects.equals(getCpu().getP95(), clusterStatsDto.getCpu().getP95()) &&
                Objects.equals(getCpu().getCpu80().getCount(), clusterStatsDto.getCpu().getCpu80().getCount()) &&
                Objects.equals(getMemory().getAverage(), clusterStatsDto.getMemory().getAverage()) &&
                Objects.equals(getMemory().getMax(), clusterStatsDto.getMemory().getMax()) &&
                Objects.equals(getMemory().getP90(), clusterStatsDto.getMemory().getP90()) &&
                Objects.equals(getMemory().getP95(), clusterStatsDto.getMemory().getP95()) &&
                Objects.equals(getAlivePodsDto().getCurrent(), clusterStatsDto.getAlivePodsDto().getCurrent()) &&
                Objects.equals(getAlivePodsDto().getTotal(), clusterStatsDto.getAlivePodsDto().getTotal());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getStartTime(), getEndTime(), getCpu(), getMemory(), getAlivePodsDto());
    }
}
