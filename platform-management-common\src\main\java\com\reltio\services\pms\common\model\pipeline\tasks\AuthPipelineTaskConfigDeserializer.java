package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.reltio.services.pms.common.model.AuthTaskUtil;
import com.reltio.services.pms.common.model.config.CustomSettings;
import com.reltio.services.pms.common.model.jobs.tasks.auth.AuthTaskConstants;
import com.reltio.services.pms.common.model.jobs.tasks.auth.AuthTaskInstance;
import java.io.IOException;
import java.util.List;
import com.reltio.services.pms.common.model.jobs.tasks.auth.AuthTaskInstanceDeserializer;
public class AuthPipelineTaskConfigDeserializer extends StdDeserializer<AuthPipelineTaskConfig> {
    private final ObjectMapper mapper;

    public AuthPipelineTaskConfigDeserializer() {
        this(null);
    }

    public AuthPipelineTaskConfigDeserializer(Class<?> vc) {
        super(vc);
        mapper = new ObjectMapper();
    }

    @Override
    public AuthPipelineTaskConfig deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException{
        ObjectMapper mapper = new ObjectMapper();
        JsonNode node = jp.getCodec().readTree(jp);
        String name = node.get(AuthTaskConstants.PIPELINE_NAME).asText();
        AuthTaskInstanceDeserializer authTaskInstanceDeserializer=new AuthTaskInstanceDeserializer();
        List<AuthTaskInstance.GroupConfig> groupConfig =authTaskInstanceDeserializer.getGroupConfig(node);

        AuthTaskInstance.CustomerConfig customerConfig = new AuthTaskInstance.CustomerConfig();
        if (node.hasNonNull(AuthTaskConstants.CUSTOMER_SPECIFIC_ROLE_BODY)) {
            customerConfig.setRoleBody(node.get(AuthTaskConstants.CUSTOMER_SPECIFIC_ROLE_BODY));
        }
        if(node.hasNonNull(AuthTaskConstants.CUSTOMER_ID_PREFIX)){
            customerConfig.setCustomerIdPrefix(node.get(AuthTaskConstants.CUSTOMER_ID_PREFIX).asText());
        }

        AuthTaskInstance.ClientConfig clientConfig = new AuthTaskInstance.ClientConfig();
        if (node.hasNonNull(AuthTaskConstants.CLIENTS_COUNT)) {
            clientConfig.setClientsCount(node.get(AuthTaskConstants.CLIENTS_COUNT).asLong());
        }

        AuthTaskInstance.UserConfig userConfig = new AuthTaskInstance.UserConfig();
        if (node.hasNonNull(AuthTaskConstants.PREASSIGNED_ROLE_NAME)) {
            userConfig.setRequesterSpecificRoleName(node.get(AuthTaskConstants.PREASSIGNED_ROLE_NAME).asText());
        }
        if (node.hasNonNull(AuthTaskConstants.ACCESS_LEVEL) && node.get(AuthTaskConstants.ACCESS_LEVEL).hasNonNull(AuthTaskConstants.MAPPING_NAME)
                && node.get(AuthTaskConstants.ACCESS_LEVEL).hasNonNull(AuthTaskConstants.ACCESS_LEVEL)) {
            userConfig.setAccessLevel(node.get(AuthTaskConstants.ACCESS_LEVEL).get(AuthTaskConstants.ACCESS_LEVEL).asText());
            userConfig.setRoleMappingName(node.get(AuthTaskConstants.ACCESS_LEVEL).get(AuthTaskConstants.MAPPING_NAME).asText());
        }
        if(node.hasNonNull(AuthTaskConstants.USER_NAME_PREFIX)){
            userConfig.setUserNamePrefix(node.get(AuthTaskConstants.USER_NAME_PREFIX).asText());
        }
        if(node.hasNonNull(AuthTaskConstants.DEFAULT_PASSWORD)){
            userConfig.setDefaultPassword(node.get(AuthTaskConstants.DEFAULT_PASSWORD).asBoolean());
        }

        if (node.hasNonNull(AuthTaskConstants.USER_CONFIG)) {
            userConfig = mapper.convertValue(node.get(AuthTaskConstants.USER_CONFIG), AuthTaskInstance.UserConfig.class);
        }

        if (node.hasNonNull(AuthTaskConstants.CUSTOMER_CONFIG)) {
            customerConfig = mapper.convertValue(node.get(AuthTaskConstants.CUSTOMER_CONFIG), AuthTaskInstance.CustomerConfig.class);
            customerConfig = AuthTaskUtil.setCaseSensitiveLoginEnabled(node, customerConfig);
            if (node.get(AuthTaskConstants.CUSTOMER_CONFIG).hasNonNull(AuthTaskConstants.CUSTOMER_SETTINGS) || node.hasNonNull(AuthTaskConstants.CUSTOMER_SETTINGS)) {
                customerConfig.setCustomSettings(getCustomerSettings(node));
            }
        }

        if (node.hasNonNull(AuthTaskConstants.CLIENT_CONFIG)) {
            clientConfig = mapper.convertValue(node.get(AuthTaskConstants.CLIENT_CONFIG), AuthTaskInstance.ClientConfig.class);
        }

        return new AuthPipelineTaskConfig(name, customerConfig, clientConfig, userConfig,groupConfig);
    }

    private CustomSettings getCustomerSettings(JsonNode node) {
        CustomSettings customerSettings = new CustomSettings();
        if (node.get(AuthTaskConstants.CUSTOMER_CONFIG).hasNonNull(AuthTaskConstants.CUSTOMER_SETTINGS)) {
            customerSettings = mapper.convertValue(node.get(AuthTaskConstants.CUSTOMER_CONFIG).get(AuthTaskConstants.CUSTOMER_SETTINGS), CustomSettings.class);
        } else if (node.get(AuthTaskConstants.CUSTOMER_SETTINGS).hasNonNull(AuthTaskConstants.RESET_PASSWORD_EMAIL_TEMPLATE_ID)) {
            customerSettings = mapper.convertValue(node.get(AuthTaskConstants.CUSTOMER_SETTINGS), CustomSettings.class);
        }
        return customerSettings;
    }
}
