package com.reltio.services.pms.common.model.jobs.tasks.tenants.data.clone;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class DataRestoreTaskInstance extends TaskInstance {

    @JsonProperty(value = "sourceTenantId")
    private final String sourceTenantId;

    @JsonProperty(value = "sourceEnvId")
    private final String sourceEnvId;

    @JsonProperty(value = "targetTenantId")
    private final String targetTenantId;

    @JsonProperty(value = "targetEnvId")
    private final String targetEnvId;

    @JsonProperty(value = "storageType")
    private final String storageType;

    @JsonProperty(value = "ticketId")
    private final String ticketId;

    @JsonProperty(value = "targetAccountBackupVaultName")
    private final String targetAccountBackupVaultName;

    @JsonProperty(value = "events")
    private final List<String> events;

    private static final String DEFAULT_TARGET_ACCOUNT_BACKUP_VAULT_NAME = "%s_%s_backup_vault";

    @JsonCreator
    public DataRestoreTaskInstance(@JsonProperty(value = "id") String id,
                                  @JsonProperty(value = "name") String name,
                                  @JsonProperty(value = "envId") String envId,
                                  @JsonProperty(value = "jobId") String jobId,
                                  @JsonProperty(value = "startTime") Long startTime,
                                  @JsonProperty(value = "finishTime") Long finishTime,
                                  @JsonProperty(value = "status") TaskStatus status,
                                  @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                  @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                  @JsonProperty(value = "executingNodeName") String executingNodeName,
                                  @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                  @JsonProperty(value = "sourceTenantId") String sourceTenantId,
                                  @JsonProperty(value = "sourceEnvId") String sourceEnvId,
                                  @JsonProperty(value = "targetTenantId") String targetTenantId,
                                  @JsonProperty(value = "targetEnvId") String targetEnvId,
                                  @JsonProperty (value = "storageType") String storageType,
                                  @JsonProperty(value = "ticketId") String ticketId,
                                  @JsonProperty(value = "targetAccountBackupVaultName") String targetAccountBackupVaultName,
                                  @JsonProperty(value = "events") List<String> events){

        super(id, name, jobId, startTime, finishTime, TaskType.DATA_RESTORE_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.sourceTenantId = sourceTenantId;
        this.sourceEnvId = sourceEnvId;
        this.targetTenantId = targetTenantId;
        this.targetEnvId = targetEnvId;
        this.events = events == null ? new ArrayList<>() : new ArrayList<>(events);
        this.storageType = storageType;
        this.ticketId = ticketId;
        this.targetAccountBackupVaultName = targetAccountBackupVaultName == null ? getSourceAccountBackupVaultDefaultName(targetEnvId,targetTenantId) : targetAccountBackupVaultName;
    }

    private String getSourceAccountBackupVaultDefaultName(String targetEnvId, String targetTenantId) {
        return String.format(DEFAULT_TARGET_ACCOUNT_BACKUP_VAULT_NAME, targetEnvId, targetTenantId);
    }

    public void addEvent(String event) {
        events.add(event);
    }
}
