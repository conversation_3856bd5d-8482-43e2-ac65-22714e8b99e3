package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.service.ActivityLogTenantsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/v1/activityLogTenants", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Activity Log Tenants  ")
public class ActivityLogTenantsController {

    private final ActivityLogTenantsService activityLogTenantsService;

    @Autowired
    public ActivityLogTenantsController(ActivityLogTenantsService activityLogTenantsService) {
        this.activityLogTenantsService = activityLogTenantsService;
    }

    @DeleteMapping(value = "/delete")
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public String deleteActivityLogTenants() {
        activityLogTenantsService.deleteAllActivityLogTenants();
        return "Successfully deleted all activity log tenants";
    }


}
