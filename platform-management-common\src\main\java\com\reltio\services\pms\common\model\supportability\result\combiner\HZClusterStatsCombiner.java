package com.reltio.services.pms.common.model.supportability.result.combiner;

import com.reltio.services.pms.common.model.supportability.AlivePodsDto;
import com.reltio.services.pms.common.model.supportability.CpuUsage;
import com.reltio.services.pms.common.model.supportability.HZClusterStatsDto;
import com.reltio.services.pms.common.model.supportability.MemoryUsage;
import com.reltio.services.pms.common.model.supportability.result.StatsCombiner;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;

/**
 * The type Event stats combiner.
 */
public class HZClusterStatsCombiner implements StatsCombiner<HZClusterStatsDto> {
    /**
     * mergeAllRestRows method is used to merge all the provided rows from GBQ into single record by sum/avg based on the column in GBQ.
     *
     * @param dtoList the dto list
     * @return the list
     */
    @Override
    public HZClusterStatsDto mergeAllRows(List<HZClusterStatsDto> dtoList) {
        HZClusterStatsDto response = dtoList.get(NumberUtils.INTEGER_ZERO);
        HZClusterStatsDto lastResponse = dtoList.get(dtoList.size() - NumberUtils.INTEGER_ONE);
        long startTime = response.getStartTime();
        long endTime = lastResponse.getEndTime();
        int totalRows = dtoList.size();
        float cpuSum = NumberUtils.FLOAT_ZERO;
        float cpuMax = NumberUtils.FLOAT_ZERO;
        float cpuP90Sum = NumberUtils.FLOAT_ZERO;
        float cpuP95Sum = NumberUtils.FLOAT_ZERO;
        float podMemorySum = NumberUtils.FLOAT_ZERO;
        float podMemoryMax = NumberUtils.FLOAT_ZERO;
        float podMemoryP90Sum = NumberUtils.FLOAT_ZERO;
        float podMemoryP95Sum = NumberUtils.FLOAT_ZERO;
        long totalMax = NumberUtils.LONG_ZERO;
        long currentMin = NumberUtils.LONG_ZERO;
        for (HZClusterStatsDto dto : dtoList) {
            cpuSum += dto.getCpu().getAverage();
            cpuMax = Math.max(dto.getCpu().getMax(), cpuMax);
            cpuP90Sum += dto.getCpu().getP90();
            cpuP95Sum += dto.getCpu().getP95();
            podMemorySum += dto.getMemory().getAverage();
            podMemoryMax = Math.max(dto.getMemory().getMax(), podMemoryMax);
            podMemoryP90Sum += dto.getMemory().getP90();
            podMemoryP95Sum += dto.getMemory().getP95();
            totalMax = Math.max(dto.getAlivePodsDto().getTotal(), totalMax);
            currentMin = currentMin == 0 ? dto.getAlivePodsDto().getCurrent() : Math.min(dto.getAlivePodsDto().getCurrent(), currentMin);
        }
        Float cpuAvg = roundOff2Digits(cpuSum / totalRows);
        Float cpuP90Avg = roundOff2Digits(cpuP90Sum / totalRows);
        Float cpuP95Avg = roundOff2Digits(cpuP95Sum / totalRows);
        CpuUsage cpuUsage = new CpuUsage(cpuAvg, roundOff2Digits(cpuMax), cpuP90Avg, cpuP95Avg, null);

        Float podMemoryAvg = roundOff2Digits(podMemorySum / totalRows);
        Float podMemoryP90Avg = roundOff2Digits(podMemoryP90Sum / totalRows);
        Float podMemoryP95Avg = roundOff2Digits(podMemoryP95Sum / totalRows);

        MemoryUsage podMemoryUsage = new MemoryUsage(podMemoryAvg, roundOff2Digits(podMemoryMax), podMemoryP90Avg, podMemoryP95Avg, null);

        HZClusterStatsDto dto = new HZClusterStatsDto();
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setCpu(cpuUsage);
        dto.setMemory(podMemoryUsage);
        dto.setAlivePodsDto(AlivePodsDto.builder()
                .current(currentMin)
                .total(totalMax)
                .build());
        return dto;
    }
}
