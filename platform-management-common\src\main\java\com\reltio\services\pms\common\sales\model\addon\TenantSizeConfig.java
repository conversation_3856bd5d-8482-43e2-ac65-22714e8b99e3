package com.reltio.services.pms.common.sales.model.addon;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@Getter
@Setter
public class TenantSizeConfig extends BaseProductConfig {
    @JsonProperty("tenantSize")
    private TenantSize tenantSize;
    @JsonProperty("cpQuantity")
    private Long cpQuantity;

    public TenantSizeConfig(TenantSize tenantSize) {
        this.tenantSize = tenantSize;
    }

    public TenantSizeConfig() {
        this.pmsProductName = PMSProductName.TENANT_SIZE;
    }
}
