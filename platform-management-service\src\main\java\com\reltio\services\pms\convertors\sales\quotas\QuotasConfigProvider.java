package com.reltio.services.pms.convertors.sales.quotas;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.QuantityType;
import com.reltio.services.pms.common.sales.QuotaPeriod;
import com.reltio.services.pms.common.sales.QuotaType;
import com.reltio.services.pms.common.sales.model.PMSQuotaName;
import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.common.sales.model.quotas.BaseQuotasConfig;

import java.util.Map;
import java.util.Set;

public interface QuotasConfigProvider<P extends BaseQuotasConfig> {

    PMSQuotaName getQuotaName();

    QuotaType getQuotaType();

    QuotaPeriod getQuotaPeriod();

    QuantityType getQuantityType();

    PMSProductName getPMSProductName();


    Set<String> getQuotaProductCodes();

    P getQuotasConfig(Map<String, Set<SalesConfig>> salesConfigByProductCode);

}
