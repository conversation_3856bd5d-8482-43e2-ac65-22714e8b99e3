package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.pipeline.Pipeline;
import com.reltio.services.pms.common.model.pipeline.PipelineConfigDetail;
import com.reltio.services.pms.common.model.pipeline.PipelineType;
import com.reltio.services.pms.service.PipelineService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;

@RestController
@RequestMapping(value = "/api/v1/environments/{envId}/pipelines", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name= "Pipeline Service")
@ReltioSecured(resourceClass = Pms.Environment.Config.Pipeline.class)
public class PipelineController {
    private final PipelineService service;

    @Autowired
    public PipelineController(PipelineService service) {
        this.service = service;
    }

    @PostMapping(value = "/provisionFreeTierTenant", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Pipeline createPipelines(@RequestBody Pipeline pipeline, @PathVariable String envId) {
        if (pipeline.getType() != null && !pipeline.getType().equals(PipelineType.PROVISION_FREE_TIER_TENANT)) {
            throw new PlatformManagementException(PlatformManagementErrorCode.WRONG_PIPELINE_TASK_TYPE, HttpStatus.CONFLICT.value(), pipeline.getType().toString());
        }
        pipeline.setType(PipelineType.PROVISION_FREE_TIER_TENANT);
        return service.createPipeline(envId, pipeline);
    }

    @PostMapping(value = "", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Pipeline createServicesPipelines(@RequestBody Pipeline pipeline, @PathVariable String envId) {
        if (pipeline.getType() != null && !pipeline.getType().equals(PipelineType.GENERIC_PIPELINE)) {
            throw new PlatformManagementException(PlatformManagementErrorCode.WRONG_PIPELINE_TASK_TYPE, HttpStatus.CONFLICT.value(), pipeline.getType().toString());
        }
        pipeline.setType(PipelineType.GENERIC_PIPELINE);
        return service.createPipeline(envId, pipeline);
    }

    @PutMapping(value = "/{name}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Pipeline updatePipelines(@PathVariable String name,
                                    @RequestBody Pipeline pipeline, @PathVariable String envId) {
        if (!name.equals(pipeline.getPipelineId())) {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value());
        }
        return service.updatePipeline(envId, pipeline);
    }

    @GetMapping(value = "/{pipelineId}")
    public Pipeline getPipelines(@PathVariable String pipelineId, @PathVariable String envId) {
        return service.getPipeline(envId, pipelineId);
    }


    @DeleteMapping(value = "/{pipelineId}")
    public Pipeline deletePipeline(@PathVariable String pipelineId, @PathVariable String envId) {
        return service.deletePipeline(envId, pipelineId);
    }

    @GetMapping(value = "")
    public Collection<Pipeline> getAllPipelines(@PathVariable String envId,
                                                @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                                @RequestParam(value = "size", required = false, defaultValue = "20") Integer size) {
        return service.getAllPipelines(envId, size, offset);
    }

    @GetMapping(value = "/{pipelineId}/_defaultValuesForProvisionTenant")
    public PipelineConfigDetail getDefaultValuesForProvisionTenant(@PathVariable String pipelineId, @PathVariable String envId) {
        return service.getDefaultValuesForProvisionTenant(envId, pipelineId);
    }

}
