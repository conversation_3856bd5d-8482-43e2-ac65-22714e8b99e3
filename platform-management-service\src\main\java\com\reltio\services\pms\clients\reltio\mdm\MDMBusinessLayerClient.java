package com.reltio.services.pms.clients.reltio.mdm;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.reltio.services.pms.common.PMSRestTemplate;
import com.reltio.services.pms.service.environment.EnvironmentService;
import org.json.simple.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class MDMBusinessLayerClient {
    private static final String RELTIO_ENVIRONMENT_URL = "%s/reltio";
    private static final String TENANT_API_URL_FORMAT = RELTIO_ENVIRONMENT_URL + "/api/%s";

    private static final String TENANT_BUSINESS_CONFIG_URL_FORMAT = TENANT_API_URL_FORMAT + "/configuration";
    private static final String TENANT_RELATIONS_URL_FORMAT = TENANT_API_URL_FORMAT + "/relations";
    private static final String TENANT_INTERACTIONS_URL_FORMAT = TENANT_API_URL_FORMAT + "/interactions";
    private static final String TENANT_LOOKUPS_URL_FORMAT = TENANT_API_URL_FORMAT + "/lookups";
    private static final String TENANT_ENTITIES_URL_FORMAT = TENANT_API_URL_FORMAT + "/entities";
    private static final String GENERATOR_URL_FORMAT = RELTIO_ENVIRONMENT_URL + "/api/generators";

    private final PMSRestTemplate rest;
    private final EnvironmentService environmentService;


    @Autowired
    public MDMBusinessLayerClient(PMSRestTemplate rest,
                                  EnvironmentService environmentService) {
        this.rest = rest;
        this.environmentService = environmentService;
    }

    public void updateTenantBusinessConfig(String environment, String tenantId, JsonNode config) {
        try {
            String environmentUrl = environmentService.getEnvironment(environment).getUrl();
            String configUrl = String.format(TENANT_BUSINESS_CONFIG_URL_FORMAT, environmentUrl, tenantId);
            rest.put(configUrl, config);
        } catch (Exception e) {
            throw new MdmClientException.BusinessConfigUpdateFailureException("Unable to update Business Config for tenant " + tenantId, e);

        }
    }

    public JsonNode getTenantBusinessConfig(String environment, String tenantId) {
        String environmentUrl = environmentService.getEnvironment(environment).getUrl();
        String configUrl = String.format(TENANT_BUSINESS_CONFIG_URL_FORMAT, environmentUrl, tenantId);
        return rest.getForObject(configUrl, JsonNode.class);

    }

    public void createLookUps(String environment, String tenantId, JsonNode lookUps) {
        try {
            String environmentUrl = environmentService.getEnvironment(environment).getUrl();
            String lookupsUrl = String.format(TENANT_LOOKUPS_URL_FORMAT, environmentUrl, tenantId);
            rest.postForObject(lookupsUrl, lookUps, JsonNode.class);
        } catch (Exception e) {
            throw new MdmClientException.LookupsUpdateFailureException("Unable to update Business Config for tenant " + tenantId, e);

        }
    }

    public void createEntities(String environment, String tenantId, Object entities) {
        String environmentUrl = environmentService.getEnvironment(environment).getUrl();
        String entitiesUrl = String.format(TENANT_ENTITIES_URL_FORMAT, environmentUrl, tenantId);
        createDataObjects(entities, entitiesUrl);
    }

    public void createRelations(String environment, String tenantId, Object relations) {
        String environmentUrl = environmentService.getEnvironment(environment).getUrl();
        String relationUrl = String.format(TENANT_RELATIONS_URL_FORMAT, environmentUrl, tenantId);
        createDataObjects(relations, relationUrl);
    }


    public void createInteractions(String environment, String tenantId, JsonNode interaction) {
        String environmentUrl = environmentService.getEnvironment(environment).getUrl();
        String interactionUrl = String.format(TENANT_INTERACTIONS_URL_FORMAT, environmentUrl, tenantId);
        createDataObjects(interaction, interactionUrl);
    }

    private void createDataObjects(Object objectsJson, String url) {
        ArrayNode response = rest.postForObject(url, objectsJson, ArrayNode.class);
        if (response == null) {
            throw new MdmClientException("Not able to create relation for tenant. No response from server");
        }
        for (JsonNode objectResponse : response) {
            if (!objectResponse.get("successful").asBoolean()) {
                throw new MdmClientException("Not able to create relation for tenant. Entities creation failed with response: " + response);
            }
        }
    }

    public int getTenantProvisioningCompleted(String environment, String tenantId) {
        String allTenantFilter = "(" +
                "equals(type,'configuration/entityTypes/Tenant') and " +
                "equals(attributes.Environment,'na01') and " +
                "exists(attributes.EndUserCustomer.Name) and " +
                "equals(sourceSystems,'PMS')" +
                ")";
        return getReltioEntitiesCount(environment, tenantId, allTenantFilter);
    }

    private int getReltioEntitiesCount(String environment, String tenantId, String allTenantFilter) {
        try {
            String environmentUrl = environmentService.getEnvironment(environment).getUrl();
            String tenantUrl = String.format(TENANT_ENTITIES_URL_FORMAT, environmentUrl, tenantId) + "/_total";
            UriComponents uri = UriComponentsBuilder.fromHttpUrl(tenantUrl)
                    .queryParam("filter", allTenantFilter)
                    .queryParam("active", "active")
                    .queryParam("options", "searchByOv", "ovOnly")
                    .encode().build();

            ObjectNode response = rest.getForObject(uri.toUri(), ObjectNode.class);
            Objects.requireNonNull(response, "Invalid response from server");
            return response.get("total").asInt();
        } catch (Exception ex) {
            throw new MdmClientException("Can't entiites count: ", ex);
        }
    }

    public int getReltioUsersProvisionedTenant(String environment, String tenantId) {
        String reltioOnlyTenantFilter = "(" +
                "equals(type,'configuration/entityTypes/Tenant') and " +
                "equals(attributes.Environment,'na01') and " +
                "equals(attributes.EndUserCustomer.Name,'Reltio') and " +
                "equals(sourceSystems,'PMS')" +
                ")";
        return getReltioEntitiesCount(environment, tenantId, reltioOnlyTenantFilter);
    }

    public void createGenerators(JSONArray config, String environment) {
        String environmentUrl = environmentService.getEnvironment(environment).getUrl();
        String generatorConfigurationURL = String.format(GENERATOR_URL_FORMAT, environmentUrl);
        try {
            rest.postForObject(generatorConfigurationURL, config, JSONArray.class);
        } catch (Exception e) {
            throw new MdmClientException("Cannot create generators ", e);
        }
    }

    @Cacheable(value = "rihEnvironments", cacheManager = "rihCache", key = "'generators-' + #environment")
    public List<String> getGenerators(String environment) {
        String environmentUrl = environmentService.getEnvironment(environment).getUrl();
        String generatorUrl = String.format(GENERATOR_URL_FORMAT, environmentUrl);
        JsonNode generators = rest.getForObject(generatorUrl, JsonNode.class);
        List<String> generatorList = new ArrayList<>();
        generators.iterator().forEachRemaining(g -> {
            generatorList.add(g.get("name").asText());
        });
        return generatorList;
    }
}
