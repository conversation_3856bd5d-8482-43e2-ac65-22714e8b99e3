package com.reltio.services.pms.convertors.sales.quotas;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.QuantityType;
import com.reltio.services.pms.common.sales.QuotaPeriod;
import com.reltio.services.pms.common.sales.QuotaType;
import com.reltio.services.pms.common.sales.model.PMSQuotaName;
import com.reltio.services.pms.common.sales.model.SalesForceProductName;
import com.reltio.services.pms.common.sales.model.quotas.BaseQuotasConfig;
import com.reltio.services.pms.service.SalesPackageService;
import org.springframework.stereotype.Service;

import java.util.Set;

@Service
public class RIHTasksQuotasConfigProvider extends AbstractQuotasConfigProvider<BaseQuotasConfig> {

    public RIHTasksQuotasConfigProvider(SalesPackageService salesPackageService) {
        super(salesPackageService);
    }

    @Override
    public PMSQuotaName getQuotaName() {
        return PMSQuotaName.RIH_TASKS;
    }

    @Override
    public QuotaType getQuotaType() {
        return QuotaType.PACKAGE_LEVEL;
    }

    @Override
    public QuotaPeriod getQuotaPeriod() {
        return QuotaPeriod.MONTHLY;
    }

    @Override
    public QuantityType getQuantityType() {
        return QuantityType.COUNT;
    }

    @Override
    public PMSProductName getPMSProductName() {
        return null;
    }

    @Override
    public Set<String> getQuotaProductCodes() {
        return salesPackageService.getSalesForceQuotas(SalesForceProductName.RIH_TASKS);
    }
}
