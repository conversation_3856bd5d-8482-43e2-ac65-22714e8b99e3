package com.reltio.services.pms.service.jobs.tasks.deprovision.dnb;

import com.reltio.services.pms.clients.reltio.auth.ReltioAuthClient;
import com.reltio.services.pms.clients.reltio.dnb.DNBConnectorClient;
import com.reltio.services.pms.common.model.jobs.tasks.dnbDeprovision.DnbDeProvisionTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.PmsLockService;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.TaskFactory;
import com.reltio.services.pms.service.reltiotenant.TenantsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DnbDeProvisionExecutionTaskFactory implements TaskFactory<DnbDeProvisionTaskInstance, DnbDeProvisionTaskExecutionService> {

    private final DNBConnectorClient dnbConnectorClient;

    private final ReltioAuthClient authClient;

    private final TenantsService tenantsService;

    private final PmsLockService pmsLockService;

    @Autowired
    public DnbDeProvisionExecutionTaskFactory(DNBConnectorClient dnbConnectorClient, ReltioAuthClient authClient, TenantsService tenantsService, PmsLockService pmsLockService) {
        this.dnbConnectorClient = dnbConnectorClient;
        this.authClient = authClient;
        this.tenantsService = tenantsService;
        this.pmsLockService = pmsLockService;
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.DNB_DE_PROVISION_TASK;
    }

    @Override
    public DnbDeProvisionTaskExecutionService createTask(String envId, DnbDeProvisionTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService) {
        return new DnbDeProvisionTaskExecutionService(envId, taskDetail, grafanaDashboardGBQService, dnbConnectorClient, authClient,tenantsService, pmsLockService);
    }
}
