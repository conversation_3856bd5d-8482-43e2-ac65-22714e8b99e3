package com.reltio.services.pms.common.model.jobs.tasks.sfdcDeprovision;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public class SfdcDeProvisionTaskInstance extends TaskInstance {

    @JsonProperty(value="tenantId")
    private final String tenantId;

    @JsonProperty(value="events")
    private final List<String> events;

    @JsonCreator
    public SfdcDeProvisionTaskInstance(@JsonProperty(value = "taskId") String taskId,
                                       @JsonProperty(value = "name") String name,
                                       @JsonProperty(value = "jobId") String jobId,
                                       @JsonProperty(value = "startTime") Long startTime,
                                       @JsonProperty(value = "finishTime") Long finishTime,
                                       @JsonProperty(value = "status") TaskStatus status,
                                       @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                       @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                       @JsonProperty(value = "executingNodeName") String executingNodeName,
                                       @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                       @JsonProperty(value = "envId") String envId,
                                       @JsonProperty(value="tenantId")String tenantId,
                                       @JsonProperty(value="events")List<String> events){
        super(taskId, name, jobId, startTime, finishTime, TaskType.SFDC_DE_PROVISION_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.tenantId=tenantId;
        this.events = events == null ? new ArrayList<>() : new ArrayList<>(events);
    }
    public void addEvent(String event) {
        events.add(event);
    }
}

