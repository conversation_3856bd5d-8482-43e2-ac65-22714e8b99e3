package com.reltio.services.pms.common.model;

import com.reltio.services.pms.common.sales.TenantPurpose;
import org.apache.commons.lang.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public enum CustomerType {
    ENTERPRISE,//if SalesforceId is present
    DNB,//if CustomerId is 9 digits
    FREEMIUM,//CustomerId start with identity360-*** && SalesforceId is absent
    POC,// CustomerId start with POC*** && SalesforceId is absent
    RELTIO_INTERNAL,//CustomerId not start with identity360-*** nor POC** with && SalesforceId is absent
    PARTNER,
    TRAINING,
    TESTDRIVE;

    public static CustomerType getCustomerType(String salesforceId, String customerId) {
        CustomerType customerType = null;
        if (StringUtils.isNotBlank(salesforceId)) {
            customerType = CustomerType.ENTERPRISE;
        } else if (onlyDigits(customerId) && customerId.length() == 9) {
            customerType = CustomerType.DNB;
        } else if (salesforceId == null && StringUtils.isNotBlank(customerId)) {
            if (customerId.startsWith("identity360")) {
                customerType = CustomerType.FREEMIUM;
            } else if (customerId.toLowerCase().startsWith("testdrive")) {
                customerType = CustomerType.TESTDRIVE;
            } else if (customerId.startsWith("POC")) {
                customerType = CustomerType.POC;
            } else if (customerId.startsWith("PEVO")) {
                customerType = CustomerType.PARTNER;
            } else {
                customerType = CustomerType.RELTIO_INTERNAL;
            }
        }
        return customerType;
    }

    public static CustomerType customerTypeByPurpose(TenantPurpose tenantPurpose) {
        CustomerType customerType = null;
        if (tenantPurpose == TenantPurpose.FREE) {
            customerType = CustomerType.FREEMIUM;
        } else if (tenantPurpose == TenantPurpose.TESTDRIVE) {
            customerType = CustomerType.TESTDRIVE;
        }
        return customerType;
    }

    private static boolean onlyDigits(String str) {
        String regex = "[0-9]+";
        Pattern p = Pattern.compile(regex);
        if (str == null) {
            return false;
        }
        Matcher m = p.matcher(str);
        return m.matches();
    }
}
