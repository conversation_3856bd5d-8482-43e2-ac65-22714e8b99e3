package com.reltio.services.pms.common.model.proxy;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;

/**
 * This class created to support the Secure Action Proxy Service storage.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class SecureAction extends BaseFirestoreEntity {

    @JsonProperty("secureKey")
    private String secureKey;

    @JsonProperty("environmentId")
    private String environmentId;

    @JsonProperty("jobId")
    private String jobId;

    @JsonProperty("taskId")
    private String taskId;

    @JsonProperty("status")
    private ActionStatus status;

    @JsonProperty("executionStatus")
    private ExecutionStatus executionStatus;

    public SecureAction(@JsonProperty("secureKey") String secureKey,
                        @JsonProperty("environmentId") String environmentId,
                        @JsonProperty("jobId") String jobId,
                        @JsonProperty("taskId") String taskId,
                        @JsonProperty("status") ActionStatus status,
                        @JsonProperty("executionStatus") ExecutionStatus executionStatus) {
        this.secureKey = secureKey;
        this.environmentId = environmentId;
        this.jobId = jobId;
        this.taskId = taskId;
        this.status = status;
        this.executionStatus = executionStatus;
    }

    public String getSecureKey() {
        return secureKey;
    }

    public void setSecureKey(String secureKey) {
        this.secureKey = secureKey;
    }

    public String getEnvironmentId() {
        return environmentId;
    }

    public void setEnvironmentId(String environmentId) {
        this.environmentId = environmentId;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public ActionStatus getStatus() {
        return status;
    }

    public void setStatus(ActionStatus status) {
        this.status = status;
    }

    public ExecutionStatus getExecutionStatus() {
        return executionStatus;
    }

    public void setExecutionStatus(ExecutionStatus executionStatus) {
        this.executionStatus = executionStatus;
    }

    @Override
    public String getID() {
        return secureKey;
    }
}
