package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.AnalyticsCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.DataCleanUpOptions;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.ExternalServicesCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.MetaDataCleanUp;

import java.util.Objects;

public class CleanTenantTaskConfig extends AbstractPipelineTaskConfig {

    @JsonProperty(value = "dataCleanUpOptions")
    private final DataCleanUpOptions dataCleanUpOptions;

    @JsonProperty(value = "analyticsCleanUp")
    private final AnalyticsCleanUp analyticsCleanUp;

    @JsonProperty(value = "metaDataCleanUp")
    private final MetaDataCleanUp metaDataCleanUp;

    @JsonProperty(value = "externalQueue")
    private final boolean externalQueue;

    @JsonProperty(value = "externalServicesCleanUp")
    private final ExternalServicesCleanUp externalServicesCleanUp;

    @JsonCreator
    public CleanTenantTaskConfig(@JsonProperty(value = "name") String name,
                                 @JsonProperty(value = "dataCleanUpOptions") DataCleanUpOptions dataCleanUpOptions,
                                 @JsonProperty(value = "analyticsCleanUp") AnalyticsCleanUp analyticsCleanUp,
                                 @JsonProperty(value = "metaDataCleanUp") MetaDataCleanUp metaDataCleanUp,
                                 @JsonProperty(value = "externalQueue") Boolean externalQueue,
                                 @JsonProperty(value = "externalServicesCleanUp") ExternalServicesCleanUp externalServicesCleanUp) {
        super(name, TaskType.DELETE_TENANT_DATA_TASK);
        this.dataCleanUpOptions = dataCleanUpOptions == null ? new DataCleanUpOptions(true, true, true, true, true, true) : dataCleanUpOptions;
        this.analyticsCleanUp = analyticsCleanUp == null ? new AnalyticsCleanUp(true, true, true) : analyticsCleanUp;
        this.metaDataCleanUp = metaDataCleanUp == null ? new MetaDataCleanUp(true) : metaDataCleanUp;
        this.externalServicesCleanUp = externalServicesCleanUp == null ? new ExternalServicesCleanUp(true) : externalServicesCleanUp;
        this.externalQueue = externalQueue == null || externalQueue;
    }

    public DataCleanUpOptions getDataCleanUpOptions() {
        return dataCleanUpOptions;
    }

    public AnalyticsCleanUp getAnalyticsCleanUp() {
        return analyticsCleanUp;
    }

    public MetaDataCleanUp getMetaDataCleanUp() {
        return metaDataCleanUp;
    }

    public boolean isExternalQueue() {
        return externalQueue;
    }

    public ExternalServicesCleanUp getExternalServicesCleanUp() {
        return externalServicesCleanUp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        if (!super.equals(o)) {
            return false;
        }

        CleanTenantTaskConfig that = (CleanTenantTaskConfig) o;
        return externalQueue == that.externalQueue &&
                Objects.equals(getDataCleanUpOptions(), that.getDataCleanUpOptions()) &&
                Objects.equals(getAnalyticsCleanUp(), that.getAnalyticsCleanUp()) &&
                Objects.equals(getMetaDataCleanUp(), that.getMetaDataCleanUp()) &&
                Objects.equals(getExternalServicesCleanUp(), that.getExternalServicesCleanUp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getDataCleanUpOptions(), getAnalyticsCleanUp(), getMetaDataCleanUp(), isExternalQueue(), getExternalServicesCleanUp());
    }

    @Override
    public String toString() {
        return TaskType.CLEAN_TENANT_TASK_NAME + "{}";
    }
}
