package com.reltio.services.pms.common.model.jobs.tasks.r360task;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

import java.util.Objects;

public class R360DataSyncTaskInstance extends TaskInstance {
    @JsonProperty(value = "r360TenantEnvironment")
    private final String r360TenantEnvironment;

    @JsonProperty(value = "r360TenantId")
    private final String r360TenantId;

    @JsonProperty(value = "requesterEmail")
    private final String requesterEmail;

    @JsonProperty(value = "tenantId")
    private final String tenantId;

    @JsonProperty(value = "customerId")
    private final String customerId;


    @JsonCreator
    public R360DataSyncTaskInstance(
            @JsonProperty(value = "id") String id,
            @JsonProperty(value = "name") String name,
            @JsonProperty(value = "envId") String envId,
            @JsonProperty(value = "jobId") String jobId,
            @JsonProperty(value = "startTime") Long startTime,
            @JsonProperty(value = "finishTime") Long finishTime,
            @JsonProperty(value = "status") TaskStatus status,
            @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
            @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
            @JsonProperty(value = "executingNodeName") String executingNodeName,
            @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
            @JsonProperty(value = "r360TenantEnvironment", required = true) String r360TenantEnvironment,
            @JsonProperty(value = "r360TenantId") String r360TenantId,
            @JsonProperty(value = "requesterEmail") String requesterEmail,
            @JsonProperty(value = "tenantId") String tenantId,
            @JsonProperty(value = "customerId") String customerId) {
        super(id, name, jobId, startTime, finishTime, TaskType.R360_DATA_SYNC_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.r360TenantEnvironment = r360TenantEnvironment;
        this.r360TenantId = r360TenantId;
        this.requesterEmail = requesterEmail;
        this.tenantId = tenantId;
        this.customerId = customerId;
    }


    public String getR360TenantEnvironment() {
        return r360TenantEnvironment;
    }

    public String getR360TenantId() {
        return r360TenantId;
    }


    public String getRequesterEmail() {
        return requesterEmail;
    }

    public String getTenantId() {
        return tenantId;
    }

    public String getCustomerId() {
        return customerId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof R360DataSyncTaskInstance)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        R360DataSyncTaskInstance that = (R360DataSyncTaskInstance) o;
        return Objects.equals(getR360TenantEnvironment(), that.getR360TenantEnvironment()) &&
                Objects.equals(getR360TenantId(), that.getR360TenantId()) &&
                Objects.equals(getTenantId(), that.getTenantId()) &&
                Objects.equals(getRequesterEmail(), that.getRequesterEmail()) &&
                Objects.equals(getCustomerId(), that.getCustomerId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getR360TenantEnvironment(), getR360TenantId(), getTenantId(), getRequesterEmail(), getCustomerId());
    }

    @Override
    public String toString() {
        return "R360DataSyncTaskInstance{" +
                "r360TenantEnvironment='" + r360TenantEnvironment + '\'' +
                ", r360TenantId='" + r360TenantId + '\'' +
                ", tenantId=" + getTenantId() +
                ", requesterEmail=" + getRequesterEmail() +
                ", customerId=" + getCustomerId() +
                "} " + super.toString();
    }
}
