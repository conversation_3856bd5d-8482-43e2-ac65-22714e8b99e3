package com.reltio.services.pms.common.model.jobs.tasks.tenants.data.clone;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
public class DataBackupTaskInstance extends TaskInstance {

    @JsonProperty(value = "sourceTenantId")
    private final String sourceTenantId;

    @JsonProperty(value = "sourceEnvId")
    private final String sourceEnvId;

    @JsonProperty(value = "storageType")
    private final String storageType;

    @JsonProperty(value = "backupRetentionPeriod")
    private final Integer backupRetentionPeriod;

    @JsonProperty(value = "ticketId")
    private final String ticketId;

    @JsonProperty(value = "sourceAccountBackupVaultName")
    private final String sourceAccountBackupVaultName;

    @JsonProperty(value = "skipTables")
    private final List<String> skipTables;

    @JsonProperty(value = "events")
    private final List<String> events;

    private static final String DEFAULT_SOURCE_ACCOUNT_BACKUP_VAULT_NAME = "%s_%s_backup_vault";

    @JsonCreator
    public DataBackupTaskInstance(@JsonProperty(value = "id") String id,
                                  @JsonProperty(value = "name") String name,
                                  @JsonProperty(value = "envId") String envId,
                                  @JsonProperty(value = "jobId") String jobId,
                                  @JsonProperty(value = "startTime") Long startTime,
                                  @JsonProperty(value = "finishTime") Long finishTime,
                                  @JsonProperty(value = "status") TaskStatus status,
                                  @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                  @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                  @JsonProperty(value = "executingNodeName") String executingNodeName,
                                  @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                  @JsonProperty(value = "sourceTenantId") String sourceTenantId,
                                  @JsonProperty(value = "sourceEnvId") String sourceEnvId,
                                  @JsonProperty (value = "storageType") String storageType,
                                  @JsonProperty (value = "backupRetentionPeriod") Integer backupRetentionPeriod,
                                  @JsonProperty (value = "ticketId") String ticketId,
                                  @JsonProperty(value = "sourceAccountBackupVaultName") String sourceAccountBackupVaultName,
                                  @JsonProperty(value = "skipTables")List<String> skipTables,
                                  @JsonProperty(value = "events") List<String> events){

        super(id, name, jobId, startTime, finishTime, TaskType.DATA_BACKUP_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.sourceTenantId = sourceTenantId;
        this.sourceEnvId = sourceEnvId;
        this.events = events == null ? new ArrayList<>() : new ArrayList<>(events);
        this.storageType = storageType;
        this.backupRetentionPeriod = Objects.requireNonNullElse(backupRetentionPeriod, 30);
        this.ticketId = ticketId;
        this.sourceAccountBackupVaultName = sourceAccountBackupVaultName == null ? getSourceAccountBackupVaultDefaultName(sourceEnvId,sourceTenantId) : sourceAccountBackupVaultName;
        this.skipTables = skipTables == null ? new ArrayList<>() : new ArrayList<>(skipTables);
    }

    public void addEvent(String event) {
        events.add(event);
    }

    private String getSourceAccountBackupVaultDefaultName(String sourceEnvId, String sourceTenantId) {
        return String.format(DEFAULT_SOURCE_ACCOUNT_BACKUP_VAULT_NAME, sourceEnvId, sourceTenantId);
    }
}
