package com.reltio.services.pms.common.model.jobs.tasks.tenants.clone;

import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.Parameters;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.combined.CleanTenantTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.configs.CloneTenantConfigsTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.configs.RetrieveAndStoreTenantConfigsTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.data.clone.ConfigureStreamingAndAnalyticsTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.data.clone.DataBackupTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.data.clone.DataRestoreTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.data.clone.DeleteDynamoDbBackupTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.history.clone.HistoryBackupTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.history.clone.HistoryRestoreTaskInstanceParams;
import lombok.AllArgsConstructor;

import java.util.List;

@AllArgsConstructor
public class TenantsCloneJobInstanceParams extends CleanTenantTaskInstanceParams implements RetrieveAndStoreTenantConfigsTaskInstanceParams , CloneTenantConfigsTaskInstanceParams , DataBackupTaskInstanceParams , DataRestoreTaskInstanceParams, DeleteDynamoDbBackupTaskInstanceParams, ConfigureStreamingAndAnalyticsTaskInstanceParams, HistoryBackupTaskInstanceParams, HistoryRestoreTaskInstanceParams {

    private final String sourceTenantId;

    private final String targetTenantId;

    private final String sourceEnvId;

    private final String targetEnvId;

    private final Integer backupRetentionPeriod;

    private final boolean forceOverridePhyConfig;

    private final String ticketId;

    private final String sourceAccountBackupVaultName;

    private final String targetAccountBackupVaultName;

    private final List<String> skipTables;

    public TenantsCloneJobInstanceParams(String tenantName, Parameters parameters, boolean qaAutomation,
                                         String sourceTenantId, String targetTenantId, String sourceEnvId,
                                         String targetEnvId , Integer backupRetentionPeriod , boolean forceOverridePhyConfig , String ticketId,
                                         String sourceAccountBackupVaultName, String targetAccountBackupVaultName, List<String> skipTables) {
        super(tenantName, parameters, qaAutomation);
        this.sourceTenantId = sourceTenantId;
        this.targetTenantId = targetTenantId;
        this.sourceEnvId = sourceEnvId;
        this.targetEnvId = targetEnvId;
        this.backupRetentionPeriod = backupRetentionPeriod;
        this.forceOverridePhyConfig = forceOverridePhyConfig;
        this.ticketId = ticketId;
        this.sourceAccountBackupVaultName = sourceAccountBackupVaultName;
        this.targetAccountBackupVaultName = targetAccountBackupVaultName;
        this.skipTables = skipTables;
    }

    @Override
    public String getSourceTenantId() {
        return sourceTenantId;
    }

    @Override
    public String getTargetTenantId() {
        return targetTenantId;
    }

    @Override
    public String getSourceEnvId() {
        return sourceEnvId;
    }

    @Override
    public String getTargetEnvId() {
        return targetEnvId;
    }

    @Override
    public Integer getBackupRetentionPeriod() {
        return backupRetentionPeriod;
    }

    @Override
    public String getTicketId(){
        return ticketId;
    }

    @Override
    public String getSourceAccountBackupVaultName() {
        return sourceAccountBackupVaultName;
    }

    @Override
    public String getTargetAccountBackupVaultName() {
        return targetAccountBackupVaultName;
    }

    @Override
    public boolean isForceOverridePhyConfig() {
        return forceOverridePhyConfig;
    }

    @Override
    public List<String> getSkipTables() {
        return skipTables;
    }

}
