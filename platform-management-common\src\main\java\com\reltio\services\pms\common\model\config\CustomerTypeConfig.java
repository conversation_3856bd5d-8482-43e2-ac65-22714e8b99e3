package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.CustomerType;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class CustomerTypeConfig extends PMSConfig {
    /**
     * Customer type config
     */
    @JsonProperty("customerType")
    private Map<CustomerType, CustomerTypeConfigEntry> customerTypeConfigEntryMap;

    @JsonCreator
    public CustomerTypeConfig(@JsonProperty("configName") String configName,
                              @JsonProperty("customerType") Map<CustomerType, CustomerTypeConfigEntry> customerTypeConfigEntryMap) {
        super(configName);
        this.customerTypeConfigEntryMap = customerTypeConfigEntryMap;
    }
}
