package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

public class FeatureToggles {

    @JsonProperty(value = "createNewModel")
    private boolean createNewModel;

    @JsonProperty(value = "trainModel")
    private boolean trainModel;

    @JsonProperty(value = "shareModel")
    private boolean shareModel;

    @JsonProperty(value = "publishModel")
    private boolean publishModel;

    @JsonProperty(value = "autoMerge")
    private boolean autoMerge;

    @JsonCreator
    public FeatureToggles(@JsonProperty(value = "createNewModel") boolean createNewModel,
                          @JsonProperty(value = "trainModel") boolean trainModel,
                          @JsonProperty(value = "shareModel") boolean shareModel,
                          @JsonProperty(value = "publishModel") boolean publishModel,
                          @JsonProperty(value = "autoMerge") boolean autoMerge) {
        this.createNewModel = createNewModel;
        this.trainModel = trainModel;
        this.shareModel = shareModel;
        this.publishModel = publishModel;
        this.autoMerge = autoMerge;
    }

    public boolean isCreateNewModel() {
        return createNewModel;
    }

    public boolean isTrainModel() {
        return trainModel;
    }

    public boolean isShareModel() {
        return shareModel;
    }

    public boolean isPublishModel() {
        return publishModel;
    }

    public boolean isAutoMerge() {
        return autoMerge;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof FeatureToggles)) {
            return false;
        }

        FeatureToggles that = (FeatureToggles) o;
        return isCreateNewModel() == that.isCreateNewModel() &&
                isTrainModel() == that.isTrainModel() &&
                isShareModel() == that.isShareModel() &&
                isPublishModel() == that.isPublishModel() &&
                isAutoMerge() == that.isAutoMerge();
    }

    @Override
    public int hashCode() {
        return Objects.hash(isCreateNewModel(), isTrainModel(), isShareModel(), isPublishModel(), isAutoMerge());
    }

    @Override
    public String toString() {
        return "FeatureToggles{" +
                "createNewModel=" + createNewModel +
                ", trainModel=" + trainModel +
                ", shareModel=" + shareModel +
                ", publishModel=" + publishModel +
                ", autoMerge=" + autoMerge +
                '}';
    }
}
