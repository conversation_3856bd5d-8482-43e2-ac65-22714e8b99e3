package com.reltio.services.pms.security;

import com.reltio.auth.provider.service.security.ReltioPermissionManager;
import com.reltio.auth.service.register.IReltioResource;
import com.reltio.services.pms.common.model.Environment;
import com.reltio.services.pms.common.model.EnvironmentAccessType;
import com.reltio.services.pms.service.environment.EnvironmentService;
import lombok.RequiredArgsConstructor;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * PermissionsManager
 * Created by apylkov
 */
@Component(value = "pmsPermissionsManager")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PMSPermissionsManagerImpl implements PMSPermissionsManager {
    public static final String ROLE_CLASS_TEMPLATE = "com.reltio.security.domain.Pms$Environment$Provisioning$%s";
    public static final String WRITE_PERMISSION = "CREATE";
    private static final Logger LOGGER = Logger.getLogger(PMSPermissionsManagerImpl.class);
    private final ReltioPermissionManager reltioPermissionManager;
    private final EnvironmentService environmentService;


    /**
     * Validates if user have WRITE(CREATE permission) access to specified environment
     *
     * @param env environment to check access to
     * @return true if environment can be accessed by the user, false if not
     */
    @Override
    public boolean hasWriteAccessToEnvironment(@NonNull String env) {
        return hasAccess(env, (pmsClass) -> reltioPermissionManager.hasAccess(pmsClass, WRITE_PERMISSION));
    }

    /**
     * Validates if user have READ(any permission) access to specified environment
     *
     * @param env environment to check access to
     * @return true if environment can be accessed by the user, false if not
     */
    public boolean hasReadAccessToEnvironment(@NonNull String env) {
        return hasAccess(env, reltioPermissionManager::hasAccess);
    }

    /**
     * Validates access to environment depending on the flag environmentAdmin
     *
     * @param env              environment name
     * @param environmentAdmin flag to get permissions for
     * @return true or false
     */
    public boolean hasAccessToEnvironment(@NonNull String env, boolean environmentAdmin) {
        if (environmentAdmin) {
            return hasWriteAccessToEnvironment(env);
        }
        return hasReadAccessToEnvironment(env);
    }

    /**
     * Validates access depending on the validate permissions function
     *
     * @param env                 environment name
     * @param permissionsFunction function to check permissions
     * @return true of false
     */
    private boolean hasAccess(String env, Function<Class<IReltioResource>, Boolean> permissionsFunction) {
        Environment environment = environmentService.getEnvironment(env);
        Class<IReltioResource> pmsClass = getClass(environment.getEnvironmentAccessType());
        if (pmsClass == null) {
            return false;
        }
        return permissionsFunction.apply(pmsClass);
    }


    /**
     * Get {@link IReltioResource} class depending on the type specified
     *
     * @param type type of class to return
     * @return class
     */
    @SuppressWarnings("unchecked")
    private Class<IReltioResource> getClass(EnvironmentAccessType type) {
        String className = String.format(ROLE_CLASS_TEMPLATE, type.getValue());
        try {
            return (Class<IReltioResource>) Class.forName(className);
        } catch (ClassNotFoundException e) {
            LOGGER.error(String.format("Failed to load class %s", className), e);
            return null;
        }
    }

    /**
     * List of available environments for the current user. If user is super user
     *
     * @return list of environments
     */
    @Override
    public List<Environment> getListOfEnvironments(boolean hasWriteAccess) {
        List<EnvironmentAccessType> availableTypes = Arrays.stream(EnvironmentAccessType.values()).filter(type -> {
            Class<IReltioResource> pmsClass = getClass(type);
            if (pmsClass != null) {
                if (hasWriteAccess) {
                    return reltioPermissionManager.hasAccess(pmsClass, WRITE_PERMISSION);
                }
                return reltioPermissionManager.hasAccess(pmsClass);
            }
            return false;
        }).collect(Collectors.toList());
        return environmentService.getAllEnvironments().stream().filter(env -> availableTypes.contains(env.getEnvironmentAccessType())).collect(Collectors.toList());
    }
}
