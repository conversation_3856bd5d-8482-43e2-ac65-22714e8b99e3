package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.ProvisioningStatus;
import com.reltio.services.pms.common.sales.model.addon.AzureBlobStorageProductConfig;
import com.reltio.services.pms.common.sales.model.addon.CleanseProductConfig;
import com.reltio.services.pms.common.sales.model.addon.DNBProductConfig;
import com.reltio.services.pms.common.sales.model.addon.DTProductConfig;
import com.reltio.services.pms.common.sales.model.addon.GeneratorProductConfig;
import com.reltio.services.pms.common.sales.model.addon.MatchIQProductConfig;
import com.reltio.services.pms.common.sales.model.addon.QueueProductConfig;
import com.reltio.services.pms.common.sales.model.addon.RdmProductConfig;
import com.reltio.services.pms.common.sales.model.addon.SFDCConnectorConfig;
import com.reltio.services.pms.common.sales.model.addon.TenantSizeConfig;
import lombok.Getter;

import java.util.Map;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "pmsProductName", defaultImpl = BaseProductConfig.class)
@JsonSubTypes({
        @JsonSubTypes.Type(value = CleanseProductConfig.class, name = PMSProductName.CLEANSE_PRODUCT),
        @JsonSubTypes.Type(value = TenantSizeConfig.class, name = PMSProductName.TENANT_SIZE_PRODUCT),
        @JsonSubTypes.Type(value = QueueProductConfig.class, name = PMSProductName.QUEUES_PRODUCT),
        @JsonSubTypes.Type(value = MatchIQProductConfig.class, name = PMSProductName.MATCHIQ_PRODUCT),
        @JsonSubTypes.Type(value = RdmProductConfig.class, name = PMSProductName.RDM_PRODUCT),
        @JsonSubTypes.Type(value = GeneratorProductConfig.class, name = PMSProductName.GENERATOR_PRODUCT),
        @JsonSubTypes.Type(value = DNBProductConfig.class, name = PMSProductName.DNB_CONNECTOR_PRODUCT),
        @JsonSubTypes.Type(value = DTProductConfig.class, name = PMSProductName.DT_NPI_PRODUCT),
        @JsonSubTypes.Type(value = DTProductConfig.class, name = PMSProductName.DT_DEA_PRODUCT),
        @JsonSubTypes.Type(value = DTProductConfig.class, name = PMSProductName.DT_340B_PRODUCT),
        @JsonSubTypes.Type(value = DTProductConfig.class, name = PMSProductName.DT_AMA_PRODUCT),
        @JsonSubTypes.Type(value = SFDCConnectorConfig.class, name = PMSProductName.SALESFORCE_CONNECTOR_PRODUCT),
        @JsonSubTypes.Type(value = AzureBlobStorageProductConfig.class, name = PMSProductName.AZURE_BLOB_STORAGE_PRODUCT)
})

@Getter
public class BaseProductConfig extends AbstractProductConfig {

    @JsonProperty("pmsProductName")
    protected PMSProductName pmsProductName;

    @JsonProperty("quantity")
    private Integer quantity;

    @JsonProperty("provisioningStatus")
    private ProvisioningStatus provisioningStatus = ProvisioningStatus.UNKNOWN;

    private DeltaStatus deltaStatus;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map<String, Object> changeProperties;


    public void setPmsProductName(PMSProductName pmsProductName) {
        this.pmsProductName = pmsProductName;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public void setProvisioningStatus(ProvisioningStatus provisioningStatus) {
        this.provisioningStatus = provisioningStatus;
    }

    public void setDeltaStatus(DeltaStatus deltaStatus) {
        this.deltaStatus = deltaStatus;
    }

    public void setChangeProperties(Map<String, Object> changeProperties) {
        this.changeProperties = changeProperties;
    }
}
