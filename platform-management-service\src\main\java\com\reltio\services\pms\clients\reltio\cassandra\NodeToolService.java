package com.reltio.services.pms.clients.reltio.cassandra;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.reltio.common.config.TenantConfiguration;
import com.reltio.devops.common.environment.model.NodetoolParamsModel;
import com.reltio.devops.common.exception.EnvironmentException;
import com.reltio.services.pms.service.environment.ClusterService;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Service
public class NodeToolService {

    private static final Logger log = Logger.getLogger(NodeToolService.class);
    private static final String STARTING_NODETOOL_FOR = "Starting nodetool for %s";
    private static final String AUTOCOMPACTION_ERROR = "%s Autocompaction for host %s throws a error on host";
    private static final String AUTOCOMPACTION_ENABLE = "Enable";
    private static final String AUTOCOMPACTION_DISABLE = "Disable";
    private static final Logger LOGGER = Logger.getLogger(NodeToolService.class);
    private final NodeToolCreator nodetoolCreator;
    private final ClusterService clusterService;
    private final NodetoolParamsModel nodetoolParams;
    private final Object monitor = new Object();
    private Cache<String, Map<String, String>> clusterIpsMap = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.HOURS).build();

    @Autowired
    public NodeToolService(NodeToolCreator nodetoolCreator,
                           @Value("${cassandra.user:cuser}") String cassandraUser,
                           @Value("${cassandra.pass:cpwd}") String cassandraPassword,
                           @Value("${cassandra.port:0000}") String cassandraPort,
                           ClusterService clusterService) {
        this.nodetoolCreator = nodetoolCreator;
        this.nodetoolParams = new NodetoolParamsModel(cassandraUser, cassandraPassword, Integer.valueOf(cassandraPort));
        this.clusterService = clusterService;
        Set<String> defaultValues = new HashSet<>(Arrays.asList("cuser", "cpwd", "0000"));
        Set<String> properties = new HashSet<>(Arrays.asList(cassandraUser, cassandraPassword, cassandraPort));
        PropertiesValidator.checkDefaultOrMalformedValues(properties, defaultValues, LOGGER);
    }


    /**
     * @param hosts
     */
    public List<String> invalidateKeyCache(Collection<String> hosts) {
        List<String> result = new ArrayList<>();
        for (String host : hosts) {
            NodetoolInstance nodetool;
            try {
                nodetool = nodetoolCreator.getNodetool(host, nodetoolParams.getPort(), nodetoolParams.getUsername(), nodetoolParams.getPassword());
                log.info(String.format("Invalidate key cache for host %s", host));
                nodetool.invalidateKeyCache(0);
                nodetool.close();
            } catch (Exception e) {
                String msg = String.format("Invalidate key cache for %s failed with msg: %s", String.join(",", hosts), e.getMessage());
                log.warn(msg, e);
                result.add(host);
            }
        }
        return result;
    }


    /**
     * @param tenantPhysicalConfiguration
     * @return
     * @throws Exception
     */
    public Map<String, String> flushKeyspaces(TenantConfiguration tenantPhysicalConfiguration, String env) {
        Map<String, String> privatePublicMap = new HashMap<>();
        for (Map.Entry<String, Set<String>> entry : PhysicalConfigurationHelper.getKeyspaces(tenantPhysicalConfiguration).entrySet()) {
            String clusterName = entry.getKey();
            Set<String> keyspaceNames = entry.getValue();
            //Let's merge hosts from Environment and Tenant Physical Configuration
            Set<String> hosts = PhysicalConfigurationHelper.getHostMap(tenantPhysicalConfiguration).getOrDefault(clusterName, Collections.EMPTY_SET);
            List<String> hostsFromEnv;
            try {
                hostsFromEnv = clusterService.getCassandraNodesByClusterNameAndEnv(clusterName, env);
            } catch (IllegalArgumentException e) {
                log.warn(String.format("Cant find cluster %s in environment", clusterName));
                continue;
            }
            hosts.addAll(hostsFromEnv);
            hosts.forEach(host -> {
                NodetoolInstance nodetool;
                log.info(String.format(STARTING_NODETOOL_FOR, host));
                try {
                    nodetool = nodetoolCreator.getNodetool(host, nodetoolParams.getPort(), nodetoolParams.getUsername(), nodetoolParams.getPassword());
                    privatePublicMap.put(nodetool.getInternalip(), nodetool.getInetAddress(host));
                    for (String keyspaceName : keyspaceNames) {
                        log.info(String.format("Flush keyspace %s for host %s", keyspaceName, host));
                        nodetool.flushKeyspace(keyspaceName, 0);
                        nodetool.close();
                    }
                } catch (Exception e) {
                    log.warn(String.format("Flush for keyspaces on host %s failed with error:%s", host, e.getMessage()));
                }
            });
        }
        return privatePublicMap;
    }

    /**
     * @param tConfig
     * @return
     */
    public List<String> disableAutocompaction(TenantConfiguration tConfig) throws EnvironmentException {
        return triggerAutocompaction(PhysicalConfigurationHelper.getMappings(tConfig), nodetoolParams.getPort(), nodetoolParams.getUsername(), nodetoolParams.getPassword(), false);
    }

    /**
     * @param tConfig
     * @return
     */
    public List<String> enableAutocompaction(TenantConfiguration tConfig) throws EnvironmentException {
        return triggerAutocompaction(PhysicalConfigurationHelper.getMappings(tConfig), nodetoolParams.getPort(), nodetoolParams.getUsername(), nodetoolParams.getPassword(), true);
    }

    public List<String> triggerAutocompaction(Map<String, List<String>> ks, Integer port, String username, String password, boolean enable) {
        List<String> failedNodes = new ArrayList<>();
        String action = enable ? AUTOCOMPACTION_ENABLE : AUTOCOMPACTION_DISABLE;
        for (Map.Entry<String, List<String>> entry : ks.entrySet()) {
            List<String> hosts = entry.getValue();
            for (String host : hosts) {
                NodetoolInstance nodetool;
                try {
                    log.info(String.format(STARTING_NODETOOL_FOR, host));
                    nodetool = nodetoolCreator.getNodetool(host, port, username, password);
                    if (enable) {
                        nodetool.enableAutoCompaction(entry.getKey());
                    } else {
                        nodetool.disableAutoCompaction(entry.getKey());
                    }
                    nodetool.close();
                } catch (IllegalArgumentException e) {
                    log.warn(String.format(AUTOCOMPACTION_ERROR, action, host));
                    failedNodes.add(host);
                } catch (Exception e) {
                    log.error(String.format(AUTOCOMPACTION_ERROR, action, host));
                    failedNodes.add(host);
                }
            }
        }
        return failedNodes;
    }

    /**
     * Get map of private public ips for C*
     *
     * @param clusterName cluster name
     * @param hosts       fqdns
     * @return map
     */
    public Map<String, String> getPrivatePublicMap(String clusterName, List<String> hosts) {
        Map<String, String> ips = clusterIpsMap.getIfPresent(clusterName);
        if (ips == null || ips.isEmpty()) {
            synchronized (monitor) {
                if (clusterIpsMap.getIfPresent(clusterName) == null || Objects.requireNonNull(clusterIpsMap.getIfPresent(clusterName)).isEmpty()) {
                    ips = getPrivatePublicMapInternal(hosts, nodetoolParams);
                    clusterIpsMap.put(clusterName, ips);
                }
            }

        }
        return ips;
    }

    /**
     * Get map of private public ips
     *
     * @param hosts - dns names or public ips
     * @return map of private public ips
     */
    private Map<String, String> getPrivatePublicMapInternal(List<String> hosts, NodetoolParamsModel params) {
        Map<String, String> privatePublicMap = new HashMap<>();
        hosts.forEach(host -> {
            NodetoolInstance nodetool;
            log.info(String.format(STARTING_NODETOOL_FOR, host));
            try {
                nodetool = nodetoolCreator.getNodetool(host, params.getPort(), params.getUsername(), params.getPassword());
                privatePublicMap.put(nodetool.getInternalip(), InetAddress.getByName(host).getHostAddress());
                nodetool.close();
            } catch (Exception e) {
                log.warn(String.format("Can not get nodetool info for %s", host), e);
            }
        });
        return privatePublicMap;
    }
}
