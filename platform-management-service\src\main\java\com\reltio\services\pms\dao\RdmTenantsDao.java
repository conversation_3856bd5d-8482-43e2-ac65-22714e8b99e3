package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.Query;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.RdmTenant;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Service
public class RdmTenantsDao extends AbstractRootCollectionDao<RdmTenant> {
    private static final String RDM_TENANTS_COLLECTION_NAME = "PMS_RDM_TENANTS";
    private static final Logger LOGGER = Logger.getLogger(RdmTenantsDao.class);


    @Autowired
    public RdmTenantsDao(CredentialsProvider provider, @Value("${firestore.env.name}") String deployedEnv, ReltioUserHolder reltioUserHolder) {
        super(provider, RDM_TENANTS_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties = Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties, LOGGER);
    }

    @Override
    protected TypeReference<RdmTenant> getTypeReference() {
        return new TypeReference<RdmTenant>() {
        };
    }

    public void updateMdmTenantsList(String rdmTenantId, String mdmTenantId) {
        try {
            RdmTenant rdmTenant = get(rdmTenantId);
            List<String> associatedMdmTenants = rdmTenant.getAssociatedMdmTenants() == null ? new ArrayList<>() :
                    rdmTenant.getAssociatedMdmTenants();
            associatedMdmTenants.add(mdmTenantId);
            rdmTenant.setAssociatedMdmTenants(associatedMdmTenants);
            createOrUpdate(rdmTenant);
        } catch (InvalidDocumentIdException e) {
            LOGGER.error("Tenant is not present in database: " + rdmTenantId, e);
        }
    }


    public Collection<RdmTenant> getRdmTenantByArrayField(String field, String mdmTenantId) {
        Query query = getBaseCollection().whereArrayContainsAny(field, Collections.singletonList(mdmTenantId));
        return getResultFromQuery(query);
    }

    public RdmTenant getRdmTenant(String rdmTenantId) {
        try {
            return get(rdmTenantId);
        } catch (InvalidDocumentIdException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.TENANT_IS_NOT_PRESENT_IN_DATABASE, HttpStatus.NOT_FOUND.value(), rdmTenantId);
        }
    }

    public Collection<RdmTenant> getTenantsFiltered(String filterField, String value) {
        return super.getAllFiltered(getBaseCollection(), filterField, value);
    }
}
