package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.config.AccessLevel;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.apache.log4j.Logger;
import java.util.Collections;
import java.util.Set;

@Service
public class AccessLevelDao extends AbstractRootCollectionDao<AccessLevel>{
    private static final String CONFIG_COLLECTION_NAME = "PMS_CONFIG";
    private static final Logger LOGGER = Logger.getLogger(AccessLevelDao.class);

    @Autowired
    public AccessLevelDao(CredentialsProvider provider,
                       @Value("${firestore.env.name}") String deployedEnv,
                       ReltioUserHolder reltioUserHolder) {
        super(provider, CONFIG_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    protected TypeReference<AccessLevel> getTypeReference() {
        return new TypeReference<AccessLevel>() {
        };
    }
}
