package com.reltio.services.pms.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TenantConfigurationMetadata {
    private String vaultKey;
    private String kmsKey;
    private boolean useServiceBusFunction;
    private boolean isProd;
    private String bucketKmsKey;
    private boolean bceTenant;
    private String customerName;
    private boolean useSpannerForInteractions;
    private String pmsEnvName;
    private String secEnvName;
}