package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.List;
import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RihConnection {
    @JsonProperty("id")
    private final String id;
    @JsonProperty("name")
    private final String name;
    @JsonProperty("inputsRequired")
    private final List<String> inputsRequired;
    @JsonProperty("environment")
    private final String environment;
    @JsonProperty("customer_id")
    private String customerId;
    @JsonProperty(value = "input")
    private JsonNode input;

    public RihConnection(@JsonProperty("id") String id, @JsonProperty("name") String name, @JsonProperty("inputsRequired") List<String> inputsRequired,
                         @JsonProperty("environment") String environment, @JsonProperty("customer_id") String customerId, @JsonProperty(value = "input") JsonNode input) {
        this.id = id;
        this.name = name;
        this.inputsRequired = inputsRequired;
        this.environment = environment;
        this.customerId = customerId;
        this.input = input;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }
        RihConnection that = (RihConnection) o;
        return Objects.equals(getId(), that.getId()) && Objects.equals(getName(), that.getName()) && Objects.equals(getInputsRequired(), that.getInputsRequired()) && Objects.equals(getEnvironment(), that.getEnvironment()) && Objects.equals(getCustomerId(), that.getCustomerId()) && Objects.equals(getInput(), that.getInput());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getName(), getInputsRequired(), getEnvironment(), getCustomerId(), getInput());
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public List<String> getInputsRequired() {
        return inputsRequired;
    }

    public String getEnvironment() {
        return environment;
    }

    public String getCustomerId() {
        return customerId;
    }

    public JsonNode getInput() {
        return input;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    @Override
    public String toString() {
        return "RihConnection{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", inputsRequired=" + inputsRequired +
                ", environment='" + environment + '\'' +
                ", customerId='" + customerId + '\'' +
                ", input=" + input +
                '}';
    }
}
