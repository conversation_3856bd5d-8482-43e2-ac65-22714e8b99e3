package com.reltio.services.pms.convertors.sales.quotas;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.QuantityType;
import com.reltio.services.pms.common.sales.QuotaPeriod;
import com.reltio.services.pms.common.sales.QuotaType;
import com.reltio.services.pms.common.sales.model.PMSQuotaName;
import com.reltio.services.pms.common.sales.model.SalesForceProductName;
import com.reltio.services.pms.common.sales.model.quotas.BaseQuotasConfig;
import com.reltio.services.pms.service.SalesPackageService;
import org.springframework.stereotype.Service;

import java.util.Set;

@Service
public class WorkflowProcessQuotasConfigProvider extends AbstractQuotasConfigProvider<BaseQuotasConfig> {

    public WorkflowProcessQuotasConfigProvider(SalesPackageService salesPackageService) {
        super(salesPackageService);
    }

    @Override
    public PMSQuotaName getQuotaName() {
        return PMSQuotaName.WORKFLOW_PROCESSES;
    }

    @Override
    public QuotaType getQuotaType() {
        return QuotaType.PACKAGE_LEVEL;
    }

    @Override
    public QuotaPeriod getQuotaPeriod() {
        return QuotaPeriod.YEARLY;
    }

    @Override
    public QuantityType getQuantityType() {
        return QuantityType.MILLIONS;
    }

    @Override
    public PMSProductName getPMSProductName() {
        return PMSProductName.WORKFLOW;
    }

    @Override
    public Set<String> getQuotaProductCodes() {
        return salesPackageService.getSalesForceQuotas(SalesForceProductName.WORKFLOW_PROCESS);
    }
}
