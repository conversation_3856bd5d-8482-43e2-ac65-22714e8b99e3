package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.tenant.Subscription;

import java.util.Collection;
import java.util.Objects;

public class RequesterWithOffers extends Requester {

    @JsonProperty("offers")
    Collection<Subscription> offers;


    @JsonCreator
    public RequesterWithOffers() {

    }

    public RequesterWithOffers(Requester requester, Collection<Subscription> offers) {
        super(requester);
        this.offers = offers;
    }

    public Collection<Subscription> getOffers() {
        return offers;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof RequesterWithOffers)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        RequesterWithOffers that = (RequesterWithOffers) o;
        return Objects.equals(offers, that.offers);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), offers);
    }
}
