package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.auth.domain.ReltioPrivileges;
import com.reltio.collection.CollectionUtils;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.model.compliance.request.ApiUsageReportRequest;
import com.reltio.services.pms.common.model.compliance.request.ApiUsageRequest;
import com.reltio.services.pms.common.model.compliance.request.ComplianceReportRequest;
import com.reltio.services.pms.common.model.compliance.request.ComplianceRequest;
import com.reltio.services.pms.common.model.compliance.request.CpUsageReportRequest;
import com.reltio.services.pms.common.model.compliance.request.CpUsageRequest;
import com.reltio.services.pms.common.model.compliance.response.ApiUsageResponse;
import com.reltio.services.pms.common.model.compliance.response.CpUsageResponse;
import com.reltio.services.pms.common.model.compliance.response.RsuUsageResponse;
import com.reltio.services.pms.common.model.compliance.response.TaskUsageResponse;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByDateResponse;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByEndpointResponse;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByTenantResponse;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingByEnvironmentResponse;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByCustomerResponse;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByEnvironmentModel;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByTenantModel;
import com.reltio.services.pms.common.model.compliance.response.cpusage.ApiByMonthResponse;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByDateResponse;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByEntityResponse;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByMonthResponse;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByTenantResponse;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuBreakdownResponse;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuByTenantResponse;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuUsageByDateResponse;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuUsageByMonthResponse;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioContract;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.SalesAccount;
import com.reltio.services.pms.service.AuthCustomerService;
import com.reltio.services.pms.service.SalesAccountService;
import com.reltio.services.pms.service.compliance.ApiUsageService;
import com.reltio.services.pms.service.compliance.BillingReportService;
import com.reltio.services.pms.service.compliance.ComplianceService;
import com.reltio.services.pms.service.compliance.CpUsageService;
import com.reltio.services.pms.service.compliance.RsuUsageService;
import com.reltio.services.pms.service.reltiotenant.TenantsService;
import com.reltio.services.pms.service.sales.ContractService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * The type Compliance controller.
 */
@RestController
@RequestMapping(value = "/api/v1/compliance", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name="Compliance")
@ReltioSecured(resourceClass = Pms.Compliance.class, privileges = ReltioPrivileges.READ)
public class ComplianceController {
    /**
     * The Compliance service.
     */
    private final ComplianceService complianceService;

    /**
     * The Contract service.
     */
    private final ContractService contractService;

    /**
     * The Sales account service.
     */
    private final SalesAccountService salesAccountService;

    /**
     * The Cp usage service.
     */
    private final CpUsageService cpUsageService;

    /**
     * The Api usage service.
     */
    private final ApiUsageService apiUsageService;

    /**
     * The Billing Report service.
     */
    private final BillingReportService billingReportService;

    /**
     * The Rsu Usage service.
     */
    private final RsuUsageService rsuUsageService;

    /**
     * The AuthCustomer service.
     */
    private final AuthCustomerService authCustomerService;
    /**
     * The Tenants service.
     */
    private final TenantsService tenantsService;

    /**
     * The constant LIMIT.
     */
    private static final int LIMIT = 500;


    /**
     * Instantiates a new Compliance controller.
     *
     * @param complianceService    the compliance service
     * @param contractService      the contract service
     * @param salesAccountService  the sales account service
     * @param cpUsageService       the cp usage service
     * @param billingReportService the billing report service
     * @param rsuUsageService      the rsu usage service
     */
    @Autowired
    public ComplianceController(ComplianceService complianceService, ContractService contractService, SalesAccountService salesAccountService, CpUsageService cpUsageService, ApiUsageService apiUsageService, BillingReportService billingReportService, RsuUsageService rsuUsageService, AuthCustomerService authCustomerService, TenantsService tenantsService) {
        this.complianceService = complianceService;
        this.contractService = contractService;
        this.salesAccountService = salesAccountService;
        this.cpUsageService = cpUsageService;
        this.apiUsageService = apiUsageService;
        this.billingReportService = billingReportService;
        this.rsuUsageService = rsuUsageService;
        this.authCustomerService = authCustomerService;
        this.tenantsService = tenantsService;
    }


    /**
     * Gets task usage.
     *
     * @param complianceRequest the task usage request
     * @return the task usage
     */
    @PostMapping("/taskUsage")
    public Collection<TaskUsageResponse> getTaskUsage(@RequestBody @Valid ComplianceRequest complianceRequest) {
        return complianceService.getTaskUsage(complianceRequest);
    }

    /**
     * Gets cp usage.
     *
     * @param cpUsageRequest the cp usage request
     * @return the cp usage
     */
    @PostMapping("/cpUsage")
    public Collection<CpUsageResponse> getCpUsage(@RequestBody @Valid CpUsageRequest cpUsageRequest) {
        return complianceService.getCpUsage(cpUsageRequest, Boolean.FALSE);
    }

    /**
     * Gets api usage.
     *
     * @param apiUsageRequest the api usage request
     * @return the api usage
     */
    @PostMapping("/apiUsage")
    public Collection<ApiUsageResponse> getApiUsage(@RequestBody @Valid ApiUsageRequest apiUsageRequest) {
        return complianceService.getApiUsage(apiUsageRequest);
    }


    /**
     * Gets rsu usage.
     *
     * @param complianceRequest the rsu usage request
     * @return the rsu usage
     */
    @PostMapping("/rsuUsage")
    public Collection<RsuUsageResponse> getRsuUsage(@RequestBody @Valid ComplianceRequest complianceRequest) {
        return complianceService.getRsuUsage(complianceRequest);
    }


    /**
     * Gets rsu usage by tenant.
     *
     * @param export the export flag
     * @return the rsu usage
     */
    @PostMapping("/rsuUsage/byTenant")
    public RsuByTenantResponse getRsuUsageByTenant(@RequestBody @Valid ComplianceReportRequest complianceReportRequest, @RequestParam Boolean export) {
        Collection<ReltioTenant> tenantCollection = getReltioTenants(complianceReportRequest);
        if (CollectionUtils.isNotEmpty(tenantCollection)) {
            Map<String, TenantPurpose> tenantIdWithPurpose = filterTenantIdAndPurpose(complianceReportRequest, tenantCollection);
            return complianceService.getRsuUsageByTenant(complianceReportRequest, tenantIdWithPurpose, export);
        }
        return new RsuByTenantResponse();

    }

    /**
     * Filters the tenants based on the selected filters.
     *
     * @param complianceReportRequest the compliance report request
     * @param tenantCollection        the collection of all the tenants
     * @return the map of tenant ids as key and purpose as value
     */

    private Map<String, TenantPurpose> filterTenantIdAndPurpose(ComplianceReportRequest complianceReportRequest, Collection<ReltioTenant> tenantCollection) {
        Map<String, TenantPurpose> tenantIdPurposeMap = new HashMap<>();

        for (ReltioTenant tenant : tenantCollection) {
            boolean tenantIdMatches = tenant.getTenantId() != null && !tenant.getTenantId().isEmpty() && tenant.getTenantId().equals(complianceReportRequest.getTenantId());
            boolean tenantPurposeMatches = tenant.getTenantPurpose() != null && tenant.getTenantPurpose().name().equals(complianceReportRequest.getTenantPurpose());
            boolean purposeMatches = tenant.getPurpose() != null && tenant.getPurpose().name().equals(complianceReportRequest.getTenantPurpose());

            if ((complianceReportRequest.getTenantId() == null || tenantIdMatches) &&
                    (complianceReportRequest.getTenantPurpose() == null ||
                            tenantPurposeMatches ||
                            purposeMatches)) {

                if (tenant.getTenantPurpose() != null) {
                    tenantIdPurposeMap.put(tenant.getTenantId(), tenant.getTenantPurpose());
                } else {
                    tenantIdPurposeMap.put(tenant.getTenantId(), tenant.getPurpose());
                }
            }
        }

        return tenantIdPurposeMap;
    }


    /**
     * Gets cp usage by date.
     *
     * @param complianceReportRequest the compliance report request
     * @param export                  the export
     * @return the cp usage by date
     */
    @PostMapping("/cpUsage/byDate")
    public CpUsageByDateResponse getCpUsageByDate(@RequestBody @Valid CpUsageReportRequest complianceReportRequest, @RequestParam Boolean export) {
        Collection<ReltioTenant> tenantCollection = filterBy(getReltioTenants(complianceReportRequest), complianceReportRequest);
        if (CollectionUtils.isNotEmpty(tenantCollection)) {
            List<String> tenantIdList = tenantCollection.stream().filter(Objects::nonNull).map(ReltioTenant::getTenantId).collect(Collectors.toList());
            return complianceService.getCpUsageByDate(complianceReportRequest, tenantIdList, export);
        }
        return new CpUsageByDateResponse();
    }

    /**
     * Gets cp usage by month.
     *
     * @param complianceReportRequest the compliance report request
     * @return the cp usage by date
     */
    @PostMapping("/cpUsage/byMonth")
    public CpUsageByMonthResponse getCpUsageByMonth(@RequestBody @Valid CpUsageReportRequest complianceReportRequest) {
        Collection<ReltioTenant> tenantCollection = filterBy(getReltioTenants(complianceReportRequest), complianceReportRequest);
        if (CollectionUtils.isNotEmpty(tenantCollection)) {
            List<String> tenantIdList = tenantCollection.stream().filter(Objects::nonNull).map(ReltioTenant::getTenantId).collect(Collectors.toList());
            return complianceService.getCpUsageByMonth(complianceReportRequest, tenantIdList);
        }
        return new CpUsageByMonthResponse();
    }

    /**
     * Gets rsu usage breakdown.
     *
     * @param export the export flag
     * @return the rsu usage
     */
    @PostMapping("/rsuUsage/rsuBreakdown")
    public RsuBreakdownResponse getRsuUsageBreakdown(@RequestBody @Valid ComplianceReportRequest complianceReportRequest, @RequestParam Boolean export) {
        Collection<ReltioTenant> tenantCollection = filterBy(getReltioTenants(complianceReportRequest), complianceReportRequest);
        if (CollectionUtils.isNotEmpty(tenantCollection)) {
            List<String> tenantIdList = tenantCollection.stream().filter(Objects::nonNull).map(ReltioTenant::getTenantId).collect(Collectors.toList());
            Collection<SalesAccount> accountCollection = getAccounts(complianceReportRequest);
            RsuBreakdownResponse rsuBreakdownResponse = complianceService.getRsuUsageBreakdown(complianceReportRequest, tenantIdList, export);
            return rsuUsageService.getRsuBreakdownResponse(accountCollection, tenantCollection, rsuBreakdownResponse);
        }
        return new RsuBreakdownResponse();
    }

    /**
     * Gets rsu usage by date.
     *
     * @param complianceReportRequest the compliance report request
     * @param export                  the export
     * @return the rsu usage by date
     */
    @PostMapping("/rsuUsage/byDate")
    public RsuUsageByDateResponse getRsuUsageByDate(@RequestBody @Valid ComplianceReportRequest complianceReportRequest, @RequestParam Boolean export) {
        Collection<ReltioTenant> tenantCollection = getReltioTenants(complianceReportRequest);
        Map<String, TenantPurpose> tenantIdWithPurpose = filterTenantIdAndPurpose(complianceReportRequest, tenantCollection);
        return complianceService.getRsuUsageByDate(complianceReportRequest, tenantIdWithPurpose, export);
    }


    /**
     * Gets api usage by date.
     *
     * @param apiUsageReportRequest the api usage report request
     * @param export                the export
     * @return the api usage by date
     */
    @PostMapping("/apiUsage/byDate")
    public ApiUsageByDateResponse getApiUsageByDate(@RequestBody @Valid ApiUsageReportRequest apiUsageReportRequest, @RequestParam Boolean export) {
        Collection<ReltioTenant> tenantCollection = filterBy(getReltioTenants(apiUsageReportRequest), apiUsageReportRequest);
        if (CollectionUtils.isNotEmpty(tenantCollection)) {
            List<String> tenantIdList = tenantCollection.stream()
                    .filter(Objects::nonNull)
                    .map(ReltioTenant::getTenantId)
                    .filter(StringUtils::isNotBlank)  // Filters out null and empty strings
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(tenantIdList)) {
                return complianceService.getApiUsageByDate(apiUsageReportRequest, tenantIdList, export);
            }
        }
        return new ApiUsageByDateResponse();
    }

    /**
     * Gets api usage by tenant.
     *
     * @param apiUsageReportRequest the api usage report request
     * @param export                the export
     * @return the api usage by tenant
     */
    @PostMapping("/apiUsage/byTenant")
    public ApiUsageByTenantResponse getApiUsageByTenant(@RequestBody @Valid ApiUsageReportRequest apiUsageReportRequest, @RequestParam Boolean export) {
        Collection<ReltioTenant> tenantCollection = filterBy(getReltioTenants(apiUsageReportRequest), apiUsageReportRequest);
        if (CollectionUtils.isNotEmpty(tenantCollection)) {
            List<String> tenantIdList = tenantCollection.stream().filter(Objects::nonNull).map(ReltioTenant::getTenantId).collect(Collectors.toList());
            ApiUsageByTenantResponse response = complianceService.getApiUsageByTenant(apiUsageReportRequest, tenantIdList, export);
            return apiUsageService.getApiUsageByTenant(tenantCollection, response);
        }
        return new ApiUsageByTenantResponse();
    }

    /**
     * Gets api usage by endpoint.
     *
     * @param apiUsageReportRequest the api usage report request
     * @param export                the export
     * @return the api usage by endpoint
     */
    @PostMapping("/apiUsage/byEndpoint")
    public ApiUsageByEndpointResponse getApiUsageByEndpoint(@RequestBody @Valid ApiUsageReportRequest apiUsageReportRequest, @RequestParam Boolean export) {
        Collection<ReltioTenant> tenantCollection = filterBy(getReltioTenants(apiUsageReportRequest), apiUsageReportRequest);
        if (CollectionUtils.isNotEmpty(tenantCollection)) {
            List<String> tenantIdList = tenantCollection.stream().filter(Objects::nonNull).map(ReltioTenant::getTenantId).collect(Collectors.toList());
            ApiUsageByEndpointResponse response = complianceService.getApiUsageByEndpoint(apiUsageReportRequest, tenantIdList, export);
            return apiUsageService.getApiUsageByEndpoint(tenantCollection, response);
        }
        return new ApiUsageByEndpointResponse();
    }

    /**
     * Gets api usage by month.
     *
     * @param apiUsageReportRequest the compliance report request
     * @return the api usage by date
     */
    @PostMapping("/apiUsage/byMonth")
    public ApiByMonthResponse getApiUsageByMonth(@RequestBody @Valid ComplianceReportRequest apiUsageReportRequest) {
        Collection<ReltioTenant> tenantCollection = filterBy(getReltioTenants(apiUsageReportRequest), apiUsageReportRequest);
        if (CollectionUtils.isNotEmpty(tenantCollection)) {
            List<String> tenantIdList = tenantCollection.stream().filter(Objects::nonNull).map(ReltioTenant::getTenantId)
                    .filter(Objects::nonNull).filter(s -> !s.isBlank()).collect(Collectors.toList());
            return complianceService.getApiUsageByMonth(apiUsageReportRequest, tenantIdList);
        }
        return new ApiByMonthResponse();
    }

    /**
     * Gets reltio tenants.
     *
     * @param complianceReportRequest the compliance report request
     * @return the reltio tenants
     */
    @Nullable
    protected Collection<ReltioTenant> getReltioTenants(ComplianceReportRequest complianceReportRequest) {
        if (complianceReportRequest != null && CollectionUtils.isNotEmpty(complianceReportRequest.getAccountIds())) {
            Set<String> accountIdSet = new HashSet<>(complianceReportRequest.getAccountIds());
            boolean includeAll = complianceReportRequest.getIncludeAllTenants() != null ? complianceReportRequest.getIncludeAllTenants() : true;
            Collection<ReltioContract> contractCollection = contractService.getContractsByAccountIds(accountIdSet, includeAll);
            Set<String> authCustomerIdsList = new HashSet<>();
            Collection<ReltioTenant> tenantCollection = new ArrayList<>();
            List<String> tenantIdsListFromContract = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(contractCollection)) {
                contractCollection.stream()
                        .filter(Objects::nonNull)
                        .flatMap(contract -> contract.getPackages().stream())
                        .filter(Objects::nonNull)
                        .flatMap(packageConfig -> packageConfig.getMdmTenants().stream())
                        .filter(Objects::nonNull)
                        .forEach(tenant -> {
                            tenantCollection.add(tenant);
                            if (StringUtils.isNotBlank(tenant.getTenantId())) {
                                tenantIdsListFromContract.add(tenant.getTenantId());
                            }
                        });
            }
            tenantCollection.addAll(tenantsService.getReltioTenantByTenantIds(tenantIdsListFromContract));

            if (includeAll) {
                // fetch the customerIds from sales accounts
                Collection<SalesAccount> salesAccountCollection = salesAccountService.getSalesAccountByAccountIds(accountIdSet);

                if (CollectionUtils.isNotEmpty(salesAccountCollection)) {
                    authCustomerIdsList = salesAccountCollection.stream()
                            .filter(Objects::nonNull)
                            .flatMap(salesAccount -> salesAccount.getAuthCustomerId().stream())
                            .collect(Collectors.toSet());
                }
                // get the tenantIds using the customerIdsList
                List<String> tenantIds = authCustomerService.getTenantIdsForCustomerIds(authCustomerIdsList);
                // get the tenantObject for the tenantIds
                Collection<ReltioTenant> tenantCollection1 = tenantsService.getReltioTenantByTenantIds(tenantIds);
                // Combining the different tenantCollections
                Set<ReltioTenant> combinedTenants = new HashSet<>();
                combinedTenants.addAll(tenantCollection);
                combinedTenants.addAll(tenantCollection1);
                return new ArrayList<>(combinedTenants);
            } else {
                return new ArrayList<>(tenantCollection);
            }

        }
        return Collections.emptyList();
    }


    /**
     * Gets cp usage by tenant.
     *
     * @param cpUsageByTenantRequest the cpUsageByTenantRequest request
     * @param export                 the export
     * @return the cp usage by tenant
     */
    @PostMapping("/cpUsage/byTenant")
    public CpUsageByTenantResponse getCpUsageByTenant(@RequestBody @Valid CpUsageReportRequest cpUsageByTenantRequest, @RequestParam Boolean export) {
        Collection<ReltioTenant> tenantCollection = filterBy(getReltioTenants(cpUsageByTenantRequest), cpUsageByTenantRequest);
        if (CollectionUtils.isNotEmpty(tenantCollection)) {
            List<String> tenantIdList = tenantCollection.stream().filter(Objects::nonNull).map(ReltioTenant::getTenantId).collect(Collectors.toList());
            Collection<SalesAccount> accountCollection = getAccounts(cpUsageByTenantRequest);
            CpUsageByTenantResponse cpUsageByTenant = complianceService.getCpUsageByTenant(cpUsageByTenantRequest, tenantIdList, export);
            return cpUsageService.getCpUsageByTenant(accountCollection, tenantCollection, cpUsageByTenant);
        }
        return new CpUsageByTenantResponse();
    }

    /**
     * Filter by collection.
     *
     * @param reltioTenants          the reltio tenants
     * @param cpUsageByTenantRequest the cp usage by tenant request
     * @return the collection
     */
    private Collection<ReltioTenant> filterBy(Collection<ReltioTenant> reltioTenants, ComplianceReportRequest cpUsageByTenantRequest) {
        Collection<ReltioTenant> filteredTenantList = new ArrayList<>();
        if (StringUtils.isNotBlank(cpUsageByTenantRequest.getTenantId())) {
            filteredTenantList.addAll(reltioTenants.stream().filter(tenantDetails -> Objects.equals(tenantDetails.getTenantId(), cpUsageByTenantRequest.getTenantId())).toList());
        }
        if (StringUtils.isNotBlank(cpUsageByTenantRequest.getTenantPurpose())) {
            if (CollectionUtils.isNotEmpty(filteredTenantList)) {
                return filteredTenantList.stream().filter(details -> (Objects.nonNull(details.getTenantPurpose()) && Objects.equals(cpUsageByTenantRequest.getTenantPurpose(), details.getTenantPurpose().name())) || ((Objects.nonNull(details.getPurpose()) && Objects.equals(cpUsageByTenantRequest.getTenantPurpose(), details.getPurpose().name())))).collect(Collectors.toList());
            } else {
                return reltioTenants.stream().filter(details -> (Objects.nonNull(details.getTenantPurpose()) && Objects.equals(cpUsageByTenantRequest.getTenantPurpose(), details.getTenantPurpose().name())) || ((Objects.nonNull(details.getPurpose()) && Objects.equals(cpUsageByTenantRequest.getTenantPurpose(), details.getPurpose().name())))).collect(Collectors.toList());
            }
        }
        return CollectionUtils.isNotEmpty(filteredTenantList) ? filteredTenantList : reltioTenants;
    }

    /**
     * Gets accounts.
     *
     * @param complianceReportRequest the compliance report request
     * @return the accounts
     */
    private Collection<SalesAccount> getAccounts(ComplianceReportRequest complianceReportRequest) {
        if (CollectionUtils.isNotEmpty(complianceReportRequest.getAccountIds())) {
            return salesAccountService.getSalesAccounts(LIMIT, NumberUtils.INTEGER_ZERO);
        }
        return Collections.emptyList();
    }

    /**
     * Gets cp usage by entity.
     *
     * @param cpUsageByEntityRequest the cp usage by entity request
     * @param export                 the export
     * @return the cp usage by entity
     */
    @PostMapping("/cpUsage/byEntity")
    public CpUsageByEntityResponse getCpUsageByEntity(@RequestBody @Valid CpUsageReportRequest cpUsageByEntityRequest, @RequestParam Boolean export) {
        Collection<ReltioTenant> tenantCollection = filterBy(getReltioTenants(cpUsageByEntityRequest), cpUsageByEntityRequest);
        if (CollectionUtils.isNotEmpty(tenantCollection)) {
            List<String> tenantIdList = tenantCollection.stream().filter(Objects::nonNull).map(ReltioTenant::getTenantId).collect(Collectors.toList());
            Collection<SalesAccount> accountCollection = getAccounts(cpUsageByEntityRequest);
            CpUsageRequest cpUsageRequest = new CpUsageRequest(cpUsageByEntityRequest.getStartDate(), cpUsageByEntityRequest.getEndDate(), tenantIdList, cpUsageByEntityRequest.getSize(), cpUsageByEntityRequest.getOffset(), cpUsageByEntityRequest.getSortOrder(), cpUsageByEntityRequest.getSortField(), cpUsageByEntityRequest.getEntityType(), cpUsageByEntityRequest.getEntityRule());
            cpUsageRequest.setEntityTypeBreakdown(true);
            List<CpUsageResponse> cpUsageByEntity = complianceService.getCpUsage(cpUsageRequest, export);
            return cpUsageService.getCpUsageByEntity(accountCollection, tenantCollection, cpUsageByEntity);
        }
        return new CpUsageByEntityResponse();
    }


    /**
     * Gets billing report by environment.
     *
     * @param billingReportRequest the billing report request
     * @return the billing report by environment
     */
    @PostMapping("/billingReport/byEnvironment")
    public BillingByEnvironmentResponse getBillingByEnvironment(@RequestBody @Valid ComplianceReportRequest billingReportRequest, @RequestParam Boolean export) {
        Collection<ReltioTenant> tenantCollection = getReltioTenants(billingReportRequest);
        Map<String, TenantPurpose> tenantIdWithPurpose = filterTenantIdAndPurpose(billingReportRequest, tenantCollection);
        List<BillingReportByEnvironmentModel> billingReportByEnvironmentResponse = complianceService.getBillingByEnvironment(billingReportRequest, tenantIdWithPurpose);
        return billingReportService.getBillingUsageByEnvironment(getAccounts(billingReportRequest), tenantCollection, billingReportByEnvironmentResponse, billingReportRequest, export);
    }

    /**
     * Gets billing report by customer.
     *
     * @param billingReportRequest the billing report request
     * @param export               the export
     * @return the billing report by customer
     */
    @PostMapping("/billingReport/byCustomer")
    public BillingReportByCustomerResponse getBillingByCustomer(@RequestBody @Valid ComplianceReportRequest billingReportRequest, @RequestParam Boolean export) {
        Collection<ReltioTenant> tenantCollection = getReltioTenants(billingReportRequest);
        Map<String, TenantPurpose> tenantIdWithPurpose = filterTenantIdAndPurpose(billingReportRequest, tenantCollection);
        List<BillingReportByTenantModel> billingReportByTenantResponse = complianceService.getBillingByTenant(billingReportRequest, tenantIdWithPurpose);
        return billingReportService.getBillingUsageByCustomer(getAccounts(billingReportRequest), tenantCollection, billingReportByTenantResponse, billingReportRequest, export);
    }

    @PostMapping("/rsuUsage/byMonth")
    public RsuUsageByMonthResponse getRsuUsageByMonth(@RequestBody @Valid ComplianceReportRequest rsuUsageReportRequest) {
        Collection<ReltioTenant> tenantCollection = getReltioTenants(rsuUsageReportRequest);
        if (CollectionUtils.isNotEmpty(tenantCollection)) {
            Map<String, TenantPurpose> tenantIdWithPurpose = filterTenantIdAndPurpose(rsuUsageReportRequest, tenantCollection);
            return complianceService.getRsuUsageByMonth(rsuUsageReportRequest, tenantIdWithPurpose);
        }
        return new RsuUsageByMonthResponse();
    }
}
