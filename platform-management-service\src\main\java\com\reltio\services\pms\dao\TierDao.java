package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.tiers.Tier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TierDao extends AbstractLevel1CollectionDao<Tier>{
    private static final String TIER_COLLECTION_NAME = "TIERS";

    @Autowired
    public TierDao(CredentialsProvider provider, ProductEditionDao productEditionDao, ReltioUserHolder reltioUserHolder) {
        super(provider, productEditionDao, TIER_COLLECTION_NAME, reltioUserHolder);
    }

    @Override
    protected TypeReference<Tier> getTypeReference() {
        return new TypeReference<Tier>(){
        };
    }

}
