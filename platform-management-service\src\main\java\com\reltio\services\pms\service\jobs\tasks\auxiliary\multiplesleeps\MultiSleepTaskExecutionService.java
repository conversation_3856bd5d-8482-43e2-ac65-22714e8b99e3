package com.reltio.services.pms.service.jobs.tasks.auxiliary.multiplesleeps;

import com.reltio.services.pms.common.model.jobs.tasks.MultiSleepTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.AbstractTaskExecutionService;

public class MultiSleepTaskExecutionService extends AbstractTaskExecutionService<MultiSleepTaskInstance> {
    public MultiSleepTaskExecutionService(String jonId, MultiSleepTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService) {
        super(jonId, taskDetail, grafanaDashboardGBQService);
    }

    @Override
    protected void executeTask() throws InterruptedException {
        Thread.sleep(taskDetail.getSleepTime());

        Long noOfAlreadySleep = taskDetail.getNoOfSleepAlreadySleep() + 1;
        taskDetail.setNoOfSleepAlreadySleep(noOfAlreadySleep);
        if (noOfAlreadySleep < taskDetail.getNoOfSleep()) {
            taskDetail.setStatus(TaskStatus.WAITING);
        } else {
            taskDetail.setStatus(TaskStatus.COMPLETED);
        }
    }
}
