package com.reltio.services.pms.common.model.jobs.tasks.tenants.deprovision.rdm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * DeprovisionRDMTenantExecutionTaskInstance
 * Created by apylkov
 */
@Getter
public class DeprovisionRDMTenantExecutionTaskInstance extends TaskInstance {

    @JsonProperty(value = "rdmTenantId")
    private final String rdmTenantId;

    @JsonProperty(value = "mdmTenantId")
    private final String mdmTenantId;

    @JsonProperty(value = "detachAllAssociatedRdmTenants")
    private final boolean detachAllAssociatedRdmTenants;

    @JsonProperty(value = "deleteRdmIfNoAssociatedMdm")
    private final boolean deleteRdmIfNoAssociatedMdm;

    @JsonProperty(value = "failOnError")
    private final boolean failOnError;

    @JsonProperty(value = "events")
    private final List<String> events;

    public DeprovisionRDMTenantExecutionTaskInstance(@JsonProperty(value = "taskId") String taskId,
                                                     @JsonProperty(value = "name") String name,
                                                     @JsonProperty(value = "jobId") String jobId,
                                                     @JsonProperty(value = "startTime") Long startTime,
                                                     @JsonProperty(value = "finishTime") Long finishTime,
                                                     @JsonProperty(value = "type") TaskType type,
                                                     @JsonProperty(value = "status") TaskStatus status,
                                                     @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                                     @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                                     @JsonProperty(value = "executingNodeName") String executingNodeName,
                                                     @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                                     @JsonProperty(value = "rdmTenantId") String rdmTenantId,
                                                     @JsonProperty(value = "mdmTenantId") String mdmTenantId,
                                                     @JsonProperty(value = "detachAllAssociatedRdmTenants") Boolean detachAllAssociatedRdmTenants,
                                                     @JsonProperty(value = "events") List<String> events,
                                                     @JsonProperty(value = "envId") String envId,
                                                     @JsonProperty(value = "deleteRdmIfNoAssociatedMdm") Boolean deleteRdmIfNoAssociatedMdm,
                                                     @JsonProperty(value = "failOnError") Boolean failOnError) {
        super(taskId, name, jobId, startTime, finishTime, type, status, lastUpdatedTime, taskFailureContext, executingNodeName,
                serviceNodeStatus, envId);
        this.setType(TaskType.DELETE_RDM_TENANT_TASK);
        this.events = new ArrayList<>(Optional.ofNullable(events).orElse(new ArrayList<>()));
        this.rdmTenantId = rdmTenantId;
        this.mdmTenantId = mdmTenantId;
        this.detachAllAssociatedRdmTenants = Optional.ofNullable(detachAllAssociatedRdmTenants).orElse(Boolean.FALSE);
        this.deleteRdmIfNoAssociatedMdm = Optional.ofNullable(deleteRdmIfNoAssociatedMdm).orElse(Boolean.FALSE);
        this.failOnError = Optional.ofNullable(failOnError).orElse(Boolean.TRUE);
    }

    public void addEvent(String event) {
        events.add(event);
    }
}
