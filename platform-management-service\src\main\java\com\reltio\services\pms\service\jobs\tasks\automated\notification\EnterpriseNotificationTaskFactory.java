package com.reltio.services.pms.service.jobs.tasks.automated.notification;

import com.reltio.services.pms.common.model.jobs.tasks.notification.EnterpriseNotificationTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.TaskFactory;
import com.reltio.services.pms.service.notification.EmailNotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EnterpriseNotificationTaskFactory implements TaskFactory<EnterpriseNotificationTaskInstance, EnterpriseNotificationTaskExecutionService> {

    private final EmailNotificationService emailNotificationService;

    @Autowired
    public EnterpriseNotificationTaskFactory(EmailNotificationService emailNotificationService) {
        this.emailNotificationService = emailNotificationService;
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.ENTERPRISE_NOTIFICATION_TASK;
    }

    @Override
    public EnterpriseNotificationTaskExecutionService createTask(String envId, EnterpriseNotificationTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService) {
        return new EnterpriseNotificationTaskExecutionService(envId, taskDetail, grafanaDashboardGBQService, emailNotificationService);
    }

}
