package com.reltio.services.pms.common.model.pipeline.tasks;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class DeProvisionDtssPipelineTaskConfig extends AbstractPipelineTaskConfig{

    @JsonCreator
    public DeProvisionDtssPipelineTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.DTSS_DE_PROVISION_TASK);
    }

    @Override
    public PMSProductName getProductName(){
        return PMSProductName.DTSS;
    }
}
