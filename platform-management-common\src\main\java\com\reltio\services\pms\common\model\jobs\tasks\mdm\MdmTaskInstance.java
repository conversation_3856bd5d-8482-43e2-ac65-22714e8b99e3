package com.reltio.services.pms.common.model.jobs.tasks.mdm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.common.config.LcaConfig;
import com.reltio.devops.common.environment.ServicePurpose;
import com.reltio.devops.common.environment.ServiceType;
import com.reltio.services.pms.common.model.ConfigOverrideElement;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.MatchingStrategy;
import com.reltio.services.pms.common.model.pipeline.tasks.RESTRequest;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.TenantPurpose;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Getter
public class MdmTaskInstance extends TaskInstance {

    public static final int LCA_NATIVE_TIME_OUT = 100;
    public static final int LCA_LAMBDA_TIME_OUT = 100;
    public static final int LCA_DVF_TIME_OUT = 1000;
    @JsonProperty(value = "productEdition")
    private final String productEdition;

    @JsonProperty(value = "customerName")
    private final String customerName;

    @JsonProperty(value = "physicalConfigTemplate")
    private final String physicalConfigTemplate;

    @JsonProperty(value = "tenantName")
    private final String tenantName;

    @JsonProperty(value = "addSampleData")
    private final boolean addSampleData;

    @JsonProperty(value = "events")
    private final List<String> events;

    @JsonProperty(value = "postProvisionAPIs")
    private final List<RESTRequest> postProvisionAPIs;

    @JsonProperty(value = "preProvisionAPIs")
    private final List<RESTRequest> preProvisionAPIs;

    @JsonProperty(value = "tenantSize")
    private final TenantSize tenantSize;

    @JsonProperty(value = "industry")
    private final String industry;

    @JsonProperty(value = "cleanseRegions")
    private final List<String> cleanseRegions;

    @JsonProperty(value = "matchingStrategy")
    private final MatchingStrategy matchingStrategy;

    @JsonProperty(value = "ttlMappingName")
    private final String ttlMappingName;

    @JsonProperty(value = "enableRiq")
    private final boolean enableRiq;
    @JsonProperty(value = "tenantPurpose")
    private final TenantPurpose tenantPurpose;
    @JsonProperty(value = "customerId")
    private final String customerId;
    @JsonProperty(value = "dataStorageArn")
    private final String dataStorageArn;
    @JsonProperty(value = "matchStorageArn")
    private final String matchStorageArn;
    @JsonProperty("storagePriorityList")
    private final Map<ServicePurpose, List<ServiceType>> storagePriorityList;
    @JsonProperty("consoleUrl")
    private final String consoleUrl;
    @JsonProperty("lcaConfig")
    private final LcaConfig lcaConfig;
    @JsonProperty("useSpannerCloudFunction")
    private final Boolean useSpannerCloudFunction;
    @JsonProperty("configOverrideElements")
    private List<ConfigOverrideElement> configOverrideElements;
    @JsonProperty(value = "loqateProcesses")
    private String loqateProcesses;
    @JsonProperty(value = "isQaAutomation")
    private Boolean isQaAutomation;
    @JsonProperty(value = "newInstanceId")
    private String newInstanceId;
    @Setter
    @JsonProperty(value = "warmingTaskId")
    private String warmingTaskId;
    @Setter
    @JsonProperty(value = "tenantConfiguration")
    private String tenantConfiguration;
    @JsonProperty(value = "retryAttempts")
    private int retryAttempts;
    @JsonProperty("uiFilesToSkip")
    private final Set<String> uiFilesToSkip;
    @Setter
    private boolean warmupRequired;
    @Setter
    private String vaultKey = null;
    @JsonProperty("keyRequired")
    @Setter
    private boolean keyRequired;
    @Setter
    private boolean useServiceBusFunction;
    @Setter
    private boolean bceTenant;
    @Setter
    private Set<String> predicateRegions;

    @JsonCreator
    public MdmTaskInstance(@JsonProperty(value = "id") String id,
                           @JsonProperty(value = "name") String name,
                           @JsonProperty(value = "envId") String envId,
                           @JsonProperty(value = "jobId") String jobId,
                           @JsonProperty(value = "startTime") Long startTime,
                           @JsonProperty(value = "finishTime") Long finishTime,
                           @JsonProperty(value = "status") TaskStatus status,
                           @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                           @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                           @JsonProperty(value = "executingNodeName") String executingNodeName,
                           @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                           @JsonProperty(value = "productEdition") String productEdition,
                           @JsonProperty(value = "customerName") String customerName,
                           @JsonProperty(value = "physicalConfigTemplate") String physicalConfigTemplate,
                           @JsonProperty(value = "tenantName") String tenantName,
                           @JsonProperty(value = "addSampleData") boolean addSampleData,
                           @JsonProperty(value = "events") List<String> events,
                           @JsonProperty(value = "postProvisionAPIs") List<RESTRequest> postProvisionAPIs,
                           @JsonProperty(value = "preProvisionAPIs") List<RESTRequest> preProvisionAPIs,
                           @JsonProperty("tenantSize") TenantSize tenantSize,
                           @JsonProperty(value = "industry") String industry,
                           @JsonProperty(value = "cleanseRegions") List<String> cleanseRegions,
                           @JsonProperty(value = "matchingStrategy") MatchingStrategy matchingStrategy,
                           @JsonProperty(value = "ttlMappingName") String ttlMappingName,
                           @JsonProperty(value = "enableRiq") boolean enableRiq,
                           @JsonProperty("configOverrideElements") List<ConfigOverrideElement> configOverrideElements,
                           @JsonProperty("tenantPurpose") TenantPurpose tenantPurpose,
                           @JsonProperty("customerId") String customerId,
                           @JsonProperty("loqateProcesses") String loqateProcesses,
                           @JsonProperty(value = "dataStorageArn") String dataStorageArn,
                           @JsonProperty(value = "matchStorageArn") String matchStorageArn,
                           @JsonProperty(value = "storagePriorityList") Map<ServicePurpose, List<ServiceType>> storagePriorityList,
                           @JsonProperty(value = "consoleUrl") String consoleUrl,
                           @JsonProperty(value = "lcaConfig") LcaConfig lcaConfig,
                           @JsonProperty(value = "isQaAutomation") Boolean isQaAutomation,
                           @JsonProperty(value = "newInstanceId") String newInstanceId,
                           @JsonProperty(value = "useSpannerCloudFunction") Boolean useSpannerCloudFunction,
                           @JsonProperty(value = "warmingTaskId") String warmingTaskId,
                           @JsonProperty("uiFilesToSkip") Set<String> uiFilesToSkip) {
        super(id, name, jobId, startTime, finishTime, TaskType.MDM_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.productEdition = productEdition;
        this.customerName = customerName;
        this.physicalConfigTemplate = physicalConfigTemplate;
        this.tenantName = tenantName;
        this.addSampleData = addSampleData;
        this.events = events == null ? new ArrayList<>() : new ArrayList<>(events);
        this.postProvisionAPIs = postProvisionAPIs;
        this.preProvisionAPIs = preProvisionAPIs;
        this.tenantSize = tenantSize;
        this.industry = industry;
        this.cleanseRegions = cleanseRegions == null ? Collections.emptyList() : cleanseRegions;
        this.matchingStrategy = matchingStrategy;
        this.ttlMappingName = ttlMappingName;
        this.enableRiq = enableRiq;
        this.configOverrideElements = configOverrideElements;
        this.tenantPurpose = tenantPurpose;
        this.customerId = customerId;
        this.loqateProcesses = loqateProcesses;
        this.dataStorageArn = dataStorageArn;
        this.matchStorageArn = matchStorageArn;
        this.storagePriorityList = storagePriorityList;
        this.consoleUrl = consoleUrl;
        this.lcaConfig = Objects.isNull(lcaConfig) ? lcaConfig : getLcaConfigCopy(lcaConfig);
        this.isQaAutomation = isQaAutomation != null && isQaAutomation;
        this.newInstanceId = newInstanceId;
        this.useSpannerCloudFunction = useSpannerCloudFunction == null ? Boolean.TRUE : useSpannerCloudFunction;
        this.warmingTaskId = warmingTaskId;
        this.uiFilesToSkip = uiFilesToSkip != null ? new HashSet<>(uiFilesToSkip) : null;
        this.retryAttempts = 0;
    }

    public void incrementRetryAttempts() {
        this.retryAttempts++;
    }

    private LcaConfig getLcaConfigCopy(LcaConfig lcaConfig) {
        LcaConfig config = new LcaConfig();
        if (Objects.isNull(lcaConfig)) {
            config.setDvfTimeOut(LCA_DVF_TIME_OUT);
            config.setLambdaTimeOut(LCA_LAMBDA_TIME_OUT);
            config.setNativeTimeOut(LCA_NATIVE_TIME_OUT);
            return config;
        }
        config.setLambdaTimeOut(lcaConfig.getLambdaTimeOut());
        config.setNativeTimeOut(lcaConfig.getNativeTimeOut());
        config.setDvfTimeOut(lcaConfig.getDvfTimeOut());
        return config;
    }

    public void addEvent(String event) {
        events.add(event);
    }

    public void mergeOverrideElements(List<ConfigOverrideElement> newElements) {
        List<ConfigOverrideElement> currentOverrideElements = getConfigOverrideElements();
        currentOverrideElements.addAll(newElements);
        this.configOverrideElements = currentOverrideElements;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }

        MdmTaskInstance that = (MdmTaskInstance) o;
        return isAddSampleData() == that.isAddSampleData() &&
                isEnableRiq() == that.isEnableRiq() &&
                Objects.equals(getProductEdition(), that.getProductEdition()) &&
                Objects.equals(getCustomerName(), that.getCustomerName()) &&
                Objects.equals(getPhysicalConfigTemplate(), that.getPhysicalConfigTemplate()) &&
                Objects.equals(getTenantName(), that.getTenantName()) &&
                Objects.equals(getEvents(), that.getEvents()) &&
                Objects.equals(getPostProvisionAPIs(), that.getPostProvisionAPIs()) &&
                Objects.equals(getPreProvisionAPIs(), that.getPreProvisionAPIs()) &&
                Objects.equals(getTenantSize(), that.getTenantSize()) &&
                Objects.equals(getIndustry(), that.getIndustry()) &&
                Objects.equals(getCleanseRegions(), that.getCleanseRegions()) &&
                Objects.equals(getMatchingStrategy(), that.getMatchingStrategy()) &&
                Objects.equals(getTtlMappingName(), that.getTtlMappingName()) &&
                Objects.equals(getConfigOverrideElements(), that.getConfigOverrideElements()) &&
                Objects.equals(getLoqateProcesses(), that.getLoqateProcesses()) &&
                Objects.equals(getDataStorageArn(), that.getDataStorageArn()) &&
                Objects.equals(getMatchStorageArn(), that.getMatchStorageArn()) &&
                Objects.equals(getLcaConfig(), that.getLcaConfig()) &&
                Objects.equals(getConsoleUrl(), that.getConsoleUrl());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getProductEdition(), getCustomerName(), getPhysicalConfigTemplate(),
                getTenantName(), isAddSampleData(), getEvents(), getPostProvisionAPIs(), getPreProvisionAPIs(), getTenantSize(), getIndustry(), getCleanseRegions(), getMatchingStrategy(), getTtlMappingName(), getConfigOverrideElements(), isEnableRiq(), getLoqateProcesses(), getDataStorageArn(), getMatchStorageArn(), getConsoleUrl(), getLcaConfig());
    }

}