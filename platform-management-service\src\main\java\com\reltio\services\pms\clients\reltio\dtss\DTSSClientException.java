package com.reltio.services.pms.clients.reltio.dtss;

public class DTSSClientException extends RuntimeException {

    public DTSSClientException() {
    }

    public DTSSClientException(String message) {
        super(message);
    }

    public DTSSClientException(String message, Throwable cause) {
        super(message, cause);
    }

    public DTSSClientException(Throwable cause) {
        super(cause);
    }

    public DTSSClientException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
