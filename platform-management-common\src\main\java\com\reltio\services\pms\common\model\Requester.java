package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.tenant.Subscription;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;

import java.util.Collections;
import java.util.HashSet;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;


@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@Getter
@Setter
public class Requester extends BaseFirestoreEntity {

    @JsonProperty("firstName")
    private String firstName;

    @JsonProperty("lastName")
    private String lastName;

    @JsonProperty("fullName")
    private String fullName;

    @JsonProperty("company")
    private String company;

    @JsonProperty("email")
    private String email;

    @JsonProperty("country")
    private String country;

    @JsonProperty("offerTypes")
    private HashSet<String> offerTypes;

    @JsonProperty("basicCustomerId")
    private String basicCustomerId;

    @JsonProperty("enterpriseCustomerId")
    private String enterpriseCustomerId;

    @JsonProperty("reltioPackageType")
    private ReltioPackageType reltioPackageType;

    @JsonCreator
    public Requester(@JsonProperty("firstName") String firstName,
                     @JsonProperty("lastName") String lastName,
                     @JsonProperty("company") String company,
                     @JsonProperty("email") String email,
                     @JsonProperty("country") String country,
                     @JsonProperty("offerTypes") HashSet<String> offerTypes,
                     @JsonProperty("basicCustomerId") String basicCustomerId,
                     @JsonProperty("enterpriseCustomerId") String enterpriseCustomerId,
                     @JsonProperty("reltioPackageType")ReltioPackageType reltioPackageType) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.company = company;
        this.email = email;
        this.country = country;
        this.offerTypes = offerTypes;
        this.basicCustomerId = basicCustomerId;
        this.enterpriseCustomerId = enterpriseCustomerId;
        this.reltioPackageType=reltioPackageType == null ? ReltioPackageType.MDM :reltioPackageType;
    }

    public Requester(TenantRequest tenantRequest) {
        this.email = tenantRequest.getEmail();
        this.company = tenantRequest.getCompany();
        this.firstName = tenantRequest.getFirstName();
        this.lastName = tenantRequest.getLastName();
        this.country = tenantRequest.getCountry();
        this.fullName = tenantRequest.getFullName();
        Subscription subscription = new Subscription(tenantRequest);
        this.offerTypes = new HashSet<>(Collections.singletonList(subscription.getID()));
        this.reltioPackageType=tenantRequest.getReltioPackageType();
    }

    public Requester(Requester requester) {
        this.email = requester.getEmail();
        this.company = requester.getCompany();
        this.firstName = requester.getFirstName();
        this.lastName = requester.getLastName();
        this.country = requester.getCountry();
        this.fullName = requester.getFullName();
        this.offerTypes = requester.getOfferTypes();
        this.basicCustomerId = requester.getBasicCustomerId();
        this.enterpriseCustomerId = requester.getEnterpriseCustomerId();
        this.reltioPackageType=requester.getReltioPackageType();
    }

    protected Requester() {

    }

    @Override
    public String getID() {
        return email;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Requester requester = (Requester) o;
        return firstName.equals(requester.firstName) && lastName.equals(requester.lastName) && Objects.equals(fullName, requester.fullName) && Objects.equals(company, requester.company) && email.equals(requester.email) && Objects.equals(country, requester.country) && offerTypes.equals(requester.offerTypes) && Objects.equals(basicCustomerId, requester.basicCustomerId) && Objects.equals(enterpriseCustomerId, requester.enterpriseCustomerId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(firstName, lastName, fullName, company, email, country, offerTypes, basicCustomerId, enterpriseCustomerId);
    }
}
