package com.reltio.services.pms.exception;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;

@Getter
public class ConflictsFoundException extends RuntimeException {
    private final transient JsonNode conflicts;
    private final transient JsonNode partialMergedConfig;
    public ConflictsFoundException(String message, JsonNode conflicts, JsonNode partialMergedConfig) {
        super(message);
        this.conflicts = conflicts;
        this.partialMergedConfig = partialMergedConfig;
    }
}