package com.reltio.services.pms.clients.reltio.mdm;

import com.amazonaws.HttpMethod;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.reltio.jackson.databind.ObjectMapper;
import com.reltio.common.config.MessagingDestination;
import com.reltio.common.config.RDMConfig;
import com.reltio.common.config.RDMTenant;
import com.reltio.common.config.StreamingConfig;
import com.reltio.common.config.TenantCleanseFunctionConfiguration;
import com.reltio.common.config.TenantConfiguration;
import com.reltio.devops.common.exception.EnvironmentException;
import com.reltio.io.IOUtils;
import com.reltio.services.pms.common.PMSRestTemplate;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.common.model.pipeline.tasks.RdmCache;
import com.reltio.services.pms.service.environment.EnvironmentService;
import com.reltio.services.pms.validator.PropertiesValidator;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import org.apache.log4j.Logger;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class MDMClient {
    public static final int CONTINUOUS_SUCCESSFUL_READ_REQUIRED = 5;
    public static final int MAX_READ_ATTEMPT = 20;
    public static final String API_DISABLE_ILM_POLICY = "%s/reltio/tools/%s/disableIndexLifecycleManagement?type=activities";
    public static final String API_DELETE_ILM_POLICY = "%s/reltio/tools/%s/deleteIndexLifecyclePolicy";
    private static final Logger LOG = Logger.getLogger(MDMClient.class);
    private static final String RELTIO_ENVIRONMENT_URL = "%s/reltio";
    public static final String ENABLE_PITR_URL = RELTIO_ENVIRONMENT_URL + "/tenants/%s/dataStorages/%s/dynamoDBPointInTimeRecovery";
    private static final String SYSTEM_CONFIG_URL_FORMAT = RELTIO_ENVIRONMENT_URL + "/admin/systemConfig";
    private static final String CLUSTER_CONFIG_URL_FORMAT = RELTIO_ENVIRONMENT_URL + "/admin/clusterNodeConfig";
    private static final String TENANTS_URL_FORMAT = RELTIO_ENVIRONMENT_URL + "/tenants";
    public static final String SHOW_ALL_TENANTS_URL = TENANTS_URL_FORMAT + "?showAll=true";
    private static final String TENANT_URL_FORMAT = RELTIO_ENVIRONMENT_URL + "/tenants/%s";
    private static final String TENANT_THROTTLING_CONFIG_URL = TENANT_URL_FORMAT + "/throttling";
    private static final String TENANT_CLEANSE_CONFIG_URL = TENANT_URL_FORMAT + "/cleanse";
    private static final String TENANT_UPDATE_OPTIONAL_PARAMETERS_URL = TENANT_URL_FORMAT + "/optionalParameters";
    private static final String TENANT_PERMISSIONS_URL_FORMAT = RELTIO_ENVIRONMENT_URL + "/permissions/%s";
    private static final String UPDATE_TENANT_URL_FORMAT = RELTIO_ENVIRONMENT_URL + "/tenants";
    private static final String TENANT_ID_FORMAT = "{tenantId}";
    private static final String ENABLED = "enabled";
    private static final String TENANT_ID = "tenantId";
    private static final String ELASTIC_SEARCH_INDEX_URL_FORMAT = RELTIO_ENVIRONMENT_URL + "/tools/%s/getIndicesUuid";
    private static final String TENANT_L3_CONFIGURATION_URL_FORMAT = RELTIO_ENVIRONMENT_URL + "/api/%s/configuration/_noInheritance";
    private static final String TENANT_L3_CONFIGURATION_URL_POST = RELTIO_ENVIRONMENT_URL + "/api/%s/configuration/";
    private static final String DVF_URL_PATTERN = "%s/api/%s/dataValidationFunctions";
    private static final String RDM_CONFIG = "rdmConfig";
    private static final String TASK_FAILED = "FAILED";
    private static final String TASK_FINISHED = "COMPLETED";
    private static final String STREAMING_CONFIG = "streamingConfig";
    private static final String MESSAGING = "messaging";
    private static final String DESTINATIONS = "destinations";
    private static final String TENANT_STATUS_URL = RELTIO_ENVIRONMENT_URL + "/status/tenant/%s";
    private static final String RUNNING_TASKS_URL = RELTIO_ENVIRONMENT_URL + "/%s/tasks?showHidden=true";
    private static final String ENCRYPTION_CONFIGURATION_URL = RELTIO_ENVIRONMENT_URL + "/tenants/%s/encryption/aws/dynamoDB";
    private static final Logger LOGGER = Logger.getLogger(MDMClient.class);
    private final EnvironmentService environmentService;
    private final ObjectMapper mapper;
    private final com.fasterxml.jackson.databind.ObjectMapper objectMapper;
    private final PMSRestTemplate pmsRest;
    @Value("${reltio.auth.server.url}")
    private String authUrl;

    @Autowired
    public MDMClient(PMSRestTemplate pmsRestTemplate,
                     EnvironmentService environmentService,
                     @Value("${reltio.auth.server.url}") String authUrl) {
        this.pmsRest = pmsRestTemplate;
        this.environmentService = environmentService;
        this.objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
        this.mapper = new ObjectMapper();
        this.authUrl = authUrl;
        Set<String> properties = new HashSet<>();
        properties.add(authUrl);
        PropertiesValidator.validateProperties(properties, LOGGER);
    }

    public void updateRequests(String url, String tenantId, JsonNode requestBody, HttpMethod httpMethod, HttpHeaders headers) {
        String requestUrl = url.replace(TENANT_ID_FORMAT, tenantId);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Accept", MediaType.APPLICATION_JSON_VALUE);
        httpHeaders.add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        if (httpMethod != null) {
            if (httpMethod == HttpMethod.PUT) {
                pmsRest.put(requestUrl, requestBody);
            }
            if (httpMethod == HttpMethod.POST) {
                addHeaders(headers, httpHeaders);
                HttpEntity<JsonNode> entity = new HttpEntity<>(requestBody, httpHeaders);
                pmsRest.exchange(requestUrl, org.springframework.http.HttpMethod.POST, entity, JsonNode.class);
            }
        }
    }

    private static void addHeaders(HttpHeaders headers, HttpHeaders httpHeaders) {
        if (headers != null) {
            for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
                httpHeaders.add(entry.getKey(), entry.getValue().getFirst());
            }
        }
    }

    public void deleteRequests(String url, String tenantId, HttpMethod httpMethod, HttpHeaders headers) {
        String requestUrl = url.replace(TENANT_ID_FORMAT, tenantId);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Accept", MediaType.APPLICATION_JSON_VALUE);
        httpHeaders.add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        if (httpMethod != null) {
            if (httpMethod == HttpMethod.DELETE) {
                addHeaders(headers, httpHeaders);
                HttpEntity<JsonNode> entity = new HttpEntity<>(httpHeaders);
                pmsRest.exchange(requestUrl, org.springframework.http.HttpMethod.DELETE, entity, JsonNode.class);
            }
        }
    }

    public String getTenantConfigurationAsString(String environment, String tenantId) {
        String environmentUrl = environmentService.getEnvironment(environment).getUrl();
        String tenantPhysicalConfigurationURL = String.format(TENANT_URL_FORMAT, environmentUrl, tenantId);
        return String.valueOf(pmsRest.getForObject(tenantPhysicalConfigurationURL, String.class));
    }

    public TenantConfiguration getTenantConfiguration(String environment, String tenantId) {
        try {
            String tenantObject = getTenantConfigurationAsString(environment, tenantId);
            return mapper.readValue(tenantObject, TenantConfiguration.class);
        } catch (Exception ex) {
            throw new MdmClientException(String.format("Can't read the tenant physical Config due to %s",
                    ex.getMessage()), ex);
        }
    }

    public TenantConfiguration getTenantConfigurationReturnsNullIfTenantNotFound(String environment, String tenantId) {
        try {
            String tenantObject = getTenantConfigurationAsString(environment, tenantId);
            return mapper.readValue(tenantObject, TenantConfiguration.class);
        } catch (Exception ex) {
            LOGGER.warn(String.format("Tenant does not exist with tenantId %s", tenantId));
            return null;
        }
    }

    public void updateTenantPhysicalConfig(String environment, String tenantConfig) {
        try {
            String environmentUrl = environmentService.getEnvironment(environment).getUrl();
            String tenantUrl = String.format(TENANTS_URL_FORMAT, environmentUrl);
            JsonNode tenantConfigNode = objectMapper.readTree(tenantConfig);
            String tenantId = tenantConfigNode.get("tenantId").asText();
            UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(tenantUrl)
                    .queryParam("tenantId", tenantId)
                    .queryParam("allowOverwriteAlConfig", Boolean.TRUE)
                    .queryParam("options", "allowDataStorageChange")
                    .build();
            updateTenantPhyConf(environment, uriComponents, tenantConfig);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    public boolean checkWarmupTaskCompletion(String environment, String tenantId, String taskId) {
        JSONObject taskDetails = getTask(environment, tenantId, taskId);
        if (taskDetails.get("status").equals(TASK_FINISHED)) {
            return true;
        } else if (taskDetails.get("status").equals(TASK_FAILED)) {
            throw new MdmClientException(String.format("Warmup task has failed. Task details %s", taskDetails.toJSONString()));
        } else {
            return false;
        }
    }

    public String startDynamoWarmupTaskReturnTaskId(String environment, String tenantId) {
        try {
            String environmentUrl = environmentService.getEnvironment(environment).getUrl();
            String warmupTaskStartUrl = String.format("%s/reltio/api/%s/warmupDynamoDBTables", environmentUrl, tenantId);
            UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(warmupTaskStartUrl)
                    .queryParam("skipWaitActiveTable", Boolean.FALSE)
                    .queryParam("finalCapacityType", "OnDemand")
                    .build();
            String returnedWarmupTask = pmsRest.postForObject(uriComponents.toUri(), null, String.class);
            com.fasterxml.reltio.jackson.databind.JsonNode warmupTask = mapper.readTree(returnedWarmupTask);
            return warmupTask.get("id").asText();
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    public JSONObject getTask(String environment, String tenantId, String taskId) {
        String environmentUrl = environmentService.getEnvironment(environment).getUrl();
        String tasksEndpoint = String.format("%s/reltio/%s/tasks/%s", environmentUrl, tenantId, taskId);
        return pmsRest.getForObject(tasksEndpoint, JSONObject.class);
    }

    public String updateTenantPhysicalConfigOnSpecificDataLoad(String environment, String tenantConfig, String tenantId) {
        try {
            if (Objects.isNull(getTenantConfiguration(environment, tenantId))) {
                throw new PlatformManagementException(PlatformManagementErrorCode.TENANT_NOT_FOUND_IN_PLATFORM, HttpStatus.BAD_REQUEST.value());
            }
            String environmentUrl = environmentService.getEnvironment(environment).getUrl();
            String tenantUrl = String.format(UPDATE_TENANT_URL_FORMAT, environmentUrl);
            UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(tenantUrl)
                    .queryParam(TENANT_ID, tenantId)
                    .queryParam("allowOverwriteAlConfig", Boolean.TRUE)
                    .queryParam("options", "allowDataStorageChange")
                    .build();
            return updateTenantPhyConf(environment, uriComponents, tenantConfig);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    private String updateTenantPhyConf(String environment, UriComponents uriComponents, String tenantConfig) {
        int maxRetries = 5;
        int retryCount = 0;
        while (true) {
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<String> request = new HttpEntity<>(tenantConfig, headers);
                String returnedTenantConfig = pmsRest.exchange(uriComponents.toUri(), org.springframework.http.HttpMethod.POST,
                        request, String.class);
                com.fasterxml.reltio.jackson.databind.JsonNode tenantConfiguration = mapper.readTree(returnedTenantConfig);
                waitForSyncOfTenant(environment, tenantConfiguration.get(TENANT_ID).asText());
                return returnedTenantConfig;
            } catch (Exception ex) {
                if (ex instanceof SocketTimeoutException && retryCount < maxRetries) {
                    LOGGER.warn("Socket timeout occurred while updating tenant physical configuration. Retrying...");
                    retryCount++;
                    waitForRetry();
                } else if (ex instanceof HttpStatusCodeException) {
                    LOGGER.error("HTTP Status code exception occurred  while updating tenant physical configuration : ", ex.getCause());
                    throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
                } else if (ex instanceof IOException) {
                    LOGGER.error("IO exception occurred while updating tenant physical configuration : ", ex.getCause());
                    throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
                } else {
                    LOGGER.error("Exceeded maximum retry attempts while updating tenant physical configuration. Aborting.", ex.getCause());
                    throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
                }
            }
        }
    }

    private void waitForRetry() {
        try {
            // Wait for 15 seconds before retrying
            Thread.sleep(15000);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
        }
    }

    public void updateTenantPhysicalConfigOnSpecificDataLoad(String environment, TenantConfiguration tenantConfig, String tenantId) {
        try {
            String config = mapper.writeValueAsString(tenantConfig);
            updateTenantPhysicalConfigOnSpecificDataLoad(environment, config, tenantId);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    public void waitForSyncOfTenant(String environment, String tenantId) {
        try {
            waitForSyncOfTenantInternal(environment, tenantId, 0, 0);
        } catch (InterruptedException iex) {
            Thread.currentThread().interrupt();
        }
    }

    private void waitForSyncOfTenantInternal(String environment, String tenantId, int continuousSuccessfulRead, int totalAttempt) throws InterruptedException {
        if (continuousSuccessfulRead > CONTINUOUS_SUCCESSFUL_READ_REQUIRED) {
            return;
        }

        try {
            String environmentUrl = environmentService.getEnvironment(environment).getUrl();
            String tenantPhysicalConfigurationURL = String.format(TENANT_URL_FORMAT, environmentUrl, tenantId);
            pmsRest.getForObject(tenantPhysicalConfigurationURL, String.class);
        } catch (HttpClientErrorException.BadRequest exception) {
            LOGGER.error("Exception occurred getting tenant physical configuration with the maximum retry of" + totalAttempt, exception);
            if (totalAttempt > MAX_READ_ATTEMPT) {
                throw exception;
            }
            Thread.sleep(20000);
            waitForSyncOfTenantInternal(environment, tenantId, 0, totalAttempt + 1);
        }
        waitForSyncOfTenantInternal(environment, tenantId, continuousSuccessfulRead + 1, totalAttempt + 1);

    }

    public void addMessagingDestination(String environment, String tenantId, List<MessagingDestination> messagingDestinations) {
        try {

            String tenantConfig = getTenantConfigurationAsString(environment, tenantId);
            JsonNode tenantConfigNode = objectMapper.readTree(tenantConfig);
            JsonNode streamingConfigNode = tenantConfigNode.get(STREAMING_CONFIG);
            ObjectNode messagingNode;
            ArrayNode destinationsArrayNode;

            if (streamingConfigNode.has(MESSAGING) && streamingConfigNode.get(MESSAGING).isObject()) {
                messagingNode = (ObjectNode) streamingConfigNode.get(MESSAGING);
                destinationsArrayNode = (ArrayNode) messagingNode.get(DESTINATIONS);
            } else {
                messagingNode = objectMapper.createObjectNode();
                destinationsArrayNode = objectMapper.createArrayNode();
                messagingNode.set(DESTINATIONS, destinationsArrayNode);
                ((ObjectNode) streamingConfigNode).set(MESSAGING, messagingNode);
            }

            for (MessagingDestination destination : messagingDestinations) {
                objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
                JsonNode destinationNode = objectMapper.valueToTree(destination);
                destinationsArrayNode.add(destinationNode);
            }
            String updatedTenantConfig = objectMapper.writeValueAsString(tenantConfigNode);
            updateTenantPhysicalConfig(environment, updatedTenantConfig);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }

    }

    private TenantConfiguration addDefaultRdmConfig(String envId, TenantConfiguration tenantConfiguration, String rdmServiceUrl) {
        try {
            RDMConfig rdmConfig = new RDMConfig(rdmServiceUrl, authUrl, new ArrayList<>());
            JSONObject rdmJson = mapper.convertValue(rdmConfig, JSONObject.class);
            rdmJson.remove("cached");
            String tenantConfigurationAsString = mapper.writeValueAsString(tenantConfiguration);
            JSONObject physConfig = mapper.readValue(tenantConfigurationAsString, JSONObject.class);
            physConfig.put(RDM_CONFIG, rdmJson);
            String environmentUrl = environmentService.getEnvironment(envId).getUrl();
            String tenantUrl = String.format(TENANTS_URL_FORMAT, environmentUrl);
            return pmsRest.postForObject(tenantUrl, physConfig.toString(), TenantConfiguration.class);
        } catch (Exception e) {
            throw new MdmClientException("Not able to add rdm config ", e);
        }
    }

    public String associateRdmAndMdmTenants(String mdmTenantID, String envId, RdmCache rdmCache, Boolean publishRDMErrors, String rdmTenantID, Boolean trackTranscodeErrors, String rdmServiceUrl) {
        try {
            TenantConfiguration tenantConfiguration = getTenantConfiguration(envId, mdmTenantID);
            if (tenantConfiguration.getRdmConfig() == null) {
                tenantConfiguration = addDefaultRdmConfig(envId, tenantConfiguration, rdmServiceUrl);
            }
            Objects.requireNonNull(tenantConfiguration, "addDefaultRdmConfig method returned null");
            List<RDMTenant> existingRdmTenantList = tenantConfiguration.getRdmConfig().getRdmTenants();

            RDMTenant rdmTenantToAdd = new RDMTenant(rdmTenantID, null);
            Set<RDMTenant> set = existingRdmTenantList == null ? new HashSet<>() : new HashSet<>(existingRdmTenantList);
            set.add(rdmTenantToAdd);

            List<RDMTenant> finalRdmTenantList = new ArrayList<>(set);
            tenantConfiguration.getRdmConfig().setRdmTenants(finalRdmTenantList);
            String tenantConfigurationAsString = mapper.writeValueAsString(tenantConfiguration);
            JSONObject node = mapper.readValue(tenantConfigurationAsString, JSONObject.class);

            Map<String, Object> map = mapper.convertValue(node.get(RDM_CONFIG), Map.class);

            Map<String, Object> cache = new HashMap<>();
            cache.put("type", rdmCache.getType());
            cache.put(ENABLED, rdmCache.getEnabled());
            map.put("cache", cache);
            map.put("publishRDMErrors", publishRDMErrors);
            map.put("trackTranscodeErrors", trackTranscodeErrors);
            map.put("serviceUri", rdmServiceUrl);
            map.remove("cached");
            node.put(RDM_CONFIG, map);

            return updateTenantPhysicalConfigOnSpecificDataLoad(envId, mapper.writeValueAsString(node), mdmTenantID);

        } catch (Exception ex) {
            throw new MdmClientException("Can not Associate  Rdm  Tenant with Mdm Tenant", ex);
        }
    }

    public JsonNode getEnvironmentSystemConfig(String environment) {
        try {
            String environmentUrl = environmentService.getEnvironment(environment).getUrl();
            String url = String.format(SYSTEM_CONFIG_URL_FORMAT, environmentUrl);
            return pmsRest.getForObject(url, JsonNode.class);
        } catch (Exception ex) {
            throw new MdmClientException("Encountered error in getting system config", ex);
        }
    }

    @Cacheable("clusternodeconfig")
    public JsonNode getEnvironmentClusterNodeConfig(String environment) {
        try {
            String environmentUrl = environmentService.getEnvironment(environment).getUrl();
            String url = String.format(CLUSTER_CONFIG_URL_FORMAT, environmentUrl);
            return pmsRest.getForObject(url, JsonNode.class);
        } catch (Exception ex) {
            throw new MdmClientException("Encountered error in getting cluster node config", ex);
        }
    }

    public void applyThrottlingConfig(String environment, String tenantId, String config) {
        putCall(environment, tenantId, config, TENANT_THROTTLING_CONFIG_URL);
    }

    public void updateTenantSize(String environment, String tenantId, String config) {
        putCall(environment, tenantId, config, TENANT_UPDATE_OPTIONAL_PARAMETERS_URL);
    }

    private void putCall(String environment, String tenantId, String config, String url) {
        try {
            String environmentUrl = environmentService.getEnvironment(environment).getUrl();
            String tenantUrl = String.format(url, environmentUrl, tenantId);
            pmsRest.put(tenantUrl, config);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    public void updateTenantPermissionConfig(String environment, String tenantId, JsonNode permissionsConfig) {
        String environmentUrl = environmentService.getEnvironment(environment).getUrl();
        String permissionsUrl = String.format(TENANT_PERMISSIONS_URL_FORMAT, environmentUrl, tenantId);
        pmsRest.postForObject(permissionsUrl, permissionsConfig, JsonNode.class);
    }

    public JsonNode getTenantPermissionConfig(String environment, String tenantId) {
        String environmentUrl = environmentService.getEnvironment(environment).getUrl();
        String permissionsUrl = String.format(TENANT_PERMISSIONS_URL_FORMAT, environmentUrl, tenantId);
        return pmsRest.getForObject(permissionsUrl, JsonNode.class);
    }

    public JsonNode getDataValidationFunctions(String environment, String tenantId) {
        String dvfUrl = environmentService.getEnvironment(environment).getDefaultUrls().get(ServiceType.DATA_VALIDATION_SERVICE);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Accept", MediaType.APPLICATION_JSON_VALUE);
        httpHeaders.add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        httpHeaders.add("env", environment);
        HttpEntity<JsonNode> entity = new HttpEntity<>(httpHeaders);
        try {
            return pmsRest.exchange(
                    String.format(DVF_URL_PATTERN, dvfUrl, tenantId),
                    org.springframework.http.HttpMethod.GET,
                    entity,
                    JsonNode.class
            );
        } catch (Exception e) {
            LOGGER.error("Error occurred while calling data validation service: " + e.getMessage(), e);
            return null;
        }
    }

    public List<String> getTenantsList(String environment) {
        String environmentUrl = environmentService.getEnvironment(environment).getUrl();
        String tenantPhysicalConfigurationURL = String.format(SHOW_ALL_TENANTS_URL, environmentUrl);
        String[] tenants = pmsRest.getForObject(tenantPhysicalConfigurationURL, String[].class);
        return tenants == null ? new ArrayList<>() : Arrays.asList(tenants);
    }

    public JsonNode getCleanseConfigForTenant(String tenantId, String envId) {
        String environmentUrl = environmentService.getEnvironment(envId).getUrl();
        String finalUrl = String.format(TENANT_CLEANSE_CONFIG_URL, environmentUrl, tenantId);
        return pmsRest.getForObject(finalUrl, JsonNode.class);
    }

    public void updateTenantCleanseConfig(String tenantId, String envId, List<TenantCleanseFunctionConfiguration> tenantCleanseFunctionConfigurationList) {
        String environmentUrl = environmentService.getEnvironment(envId).getUrl();
        String finalUrl = String.format(TENANT_CLEANSE_CONFIG_URL, environmentUrl, tenantId);
        pmsRest.put(finalUrl, tenantCleanseFunctionConfigurationList);
    }

    public Map<String, String> getElasticSearchIndexForTenant(String environment, String tenantId) {
        try {
            String environmentUrl = environmentService.getEnvironment(environment).getUrl();
            String allElasticSearchIndexURL = String.format(ELASTIC_SEARCH_INDEX_URL_FORMAT, environmentUrl, tenantId);
            return pmsRest.getForObject(allElasticSearchIndexURL, Map.class);
        } catch (Exception ex) {
            throw new MdmClientException("Can't read the tenant Elastic Search Index Config", ex);
        }
    }

    public Map<String, String> getElasticSearchIndexForOtherEnvironment(String environment, String tenantId) {
        try {
            return getElasticSearchIndexForTenant(environment, tenantId);
        } catch (MdmClientException ex) {
            LOG.info("tenant not found " + tenantId + " in env: " + environment);
            return new HashMap<>();
        }
    }

    public void enablePITRBackup(String environment, String tenantId, String dataStorageId) throws EnvironmentException {
        JsonNode pointInTimeRecoveryConfig;
        String apiEnvironment = environmentService.getEnvironment(environment).getUrl();
        String url = String.format(ENABLE_PITR_URL, apiEnvironment, tenantId, dataStorageId);
        try {
            String pointInTimeRecoveryConfigStr = IOUtils.readResource(StreamingConfig.class, "mdm/pointInTimeRecovery.json");
            pointInTimeRecoveryConfig = objectMapper.readTree(pointInTimeRecoveryConfigStr);
            pmsRest.put(url, pointInTimeRecoveryConfig);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
        }
    }

    public void checkStreamingAndDataPipelineConfigConfigUpdate(String environment, String tenantId) {
        TenantConfiguration tenantConfiguration = getTenantConfiguration(environment, tenantId);
        if (tenantConfiguration.getStreamingConfig() == null || !tenantConfiguration.getStreamingConfig().isStreamingEnabled()) {
            waitForStreamingConfigToBeSync(environment, tenantId);
        }
        if (tenantConfiguration.getDataPipelineConfig() == null || !tenantConfiguration.getDataPipelineConfig().isEnabled()) {
            waitForDataPipelineConfigToBeSync(environment, tenantId);
        }
    }

    private void waitForStreamingConfigToBeSync(String environment, String tenantId) {
        if (!environmentService.getUnitTest()) {
            try {
                waitForStreamingConfigToBeSyncOfTenantInternal(environment, tenantId, 0);
            } catch (InterruptedException iex) {
                Thread.currentThread().interrupt();
            }
        }
    }

    private void waitForStreamingConfigToBeSyncOfTenantInternal(String environment, String tenantId, int continuousSuccessfulRead) throws InterruptedException {
        if (continuousSuccessfulRead > CONTINUOUS_SUCCESSFUL_READ_REQUIRED) {
            return;
        }
        Thread.sleep(10000);
        TenantConfiguration tenantConfiguration = getTenantConfiguration(environment, tenantId);
        if (tenantConfiguration.getStreamingConfig() != null && tenantConfiguration.getStreamingConfig().isStreamingEnabled()) {
            return;
        }
        waitForStreamingConfigToBeSyncOfTenantInternal(environment, tenantId, continuousSuccessfulRead + 1);
    }

    public void waitForDataPipelineConfigToBeSync(String environment, String tenantId) {
        try {
            waitForDataPipelineConfigToBeSyncOfTenantInternal(environment, tenantId, 0);
        } catch (InterruptedException iex) {
            Thread.currentThread().interrupt();
        }
    }

    private void waitForDataPipelineConfigToBeSyncOfTenantInternal(String environment, String tenantId, int continuousSuccessfulRead) throws InterruptedException {
        if (continuousSuccessfulRead > CONTINUOUS_SUCCESSFUL_READ_REQUIRED) {
            return;
        }
        if (!environmentService.getUnitTest()) {
            Thread.sleep(10000);
        }
        TenantConfiguration tenantConfiguration = getTenantConfiguration(environment, tenantId);
        if (tenantConfiguration.getStreamingConfig() != null && tenantConfiguration.getStreamingConfig().isStreamingEnabled()) {
            return;
        }
        waitForDataPipelineConfigToBeSyncOfTenantInternal(environment, tenantId, continuousSuccessfulRead + 1);
    }

    public void deleteILMPolicy(String serviceUrl, String tenantId) {
        String urlDisable = String.format(API_DISABLE_ILM_POLICY, serviceUrl, tenantId);
        try {
            validateILMStatus(pmsRest.postForObject(urlDisable, null, JsonNode.class));
            String urlDelete = String.format(API_DELETE_ILM_POLICY, serviceUrl, tenantId);
            validateILMStatus(pmsRest.postForObject(urlDelete, null, JsonNode.class));
        } catch (Exception e) {
            if (e instanceof HttpClientErrorException.NotFound) {
                LOG.warn("ILM is not enabled");
            } else {
                throw e;
            }
        }
    }

    /**
     * Get ILM status
     *
     * @param node ILM service response
     */
    private void validateILMStatus(JsonNode node) {
        String status = null;
        if (node == null) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "Cant get status from ILM service, response is null");
        }
        if (node.get("status") != null) {
            status = node.get("status").asText();
        }
        if (status == null) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "Cant get status from ILM service");
        }
        if (status.equals("false")) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "Operation with ILM has failed, response status is false.");
        }
    }

    public boolean checkRdmTenantInMdmL3configuration(String envId, String tenantId) {
        String envUrl = environmentService.getEnvironment(envId).getUrl();
        String url = String.format(TENANT_L3_CONFIGURATION_URL_FORMAT, envUrl, tenantId);
        try {
            Map<String, Object> result = pmsRest.getForObject(url, Map.class);
            if (result != null && result.containsKey("rdmTenantId")) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.warn(String.format("Cannot get L3 configuration for tenant %s", tenantId));
        }
        return false;
    }

    public boolean checkRdmTenantExistsForMdmTenantUsingPhyConfig(String envId, String tenantId) {
        TenantConfiguration phyConfig = getTenantConfiguration(envId, tenantId);
        com.fasterxml.reltio.jackson.databind.JsonNode jsonNode = mapper.convertValue(phyConfig, com.fasterxml.reltio.jackson.databind.JsonNode.class);
        com.fasterxml.reltio.jackson.databind.JsonNode objNode = jsonNode.get(RDM_CONFIG);
        com.fasterxml.reltio.jackson.databind.JsonNode rdmNode = objNode.get("rdmTenants");
        if (rdmNode != null && rdmNode.isArray()) {
            ArrayList<com.fasterxml.reltio.jackson.databind.JsonNode> rdmArrayList = mapper.convertValue(rdmNode, ArrayList.class);
            if (!rdmArrayList.isEmpty()) {
                return true;
            }
        }
        return false;
    }

    public String getTenantSize(String envId, String tenantId) {
        try {
            String size = null;
            String environmentUrl = environmentService.getEnvironment(envId).getUrl();
            String tenantUrl = String.format(TENANT_UPDATE_OPTIONAL_PARAMETERS_URL, environmentUrl, tenantId);
            JsonNode node = pmsRest.getForObject(tenantUrl, JsonNode.class);
            if (node != null && node.get("tenantSize") != null) {
                size = node.get("tenantSize").asText();
            }
            return size;

        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    public String getTenantsPhyConfigWithNoRetries(String environment, String tenantId) {
        String environmentUrl = environmentService.getEnvironment(environment).getUrl();
        String tenantPhysicalConfigurationURL = String.format(TENANT_URL_FORMAT, environmentUrl, tenantId);
        try {
            return String.valueOf(pmsRest.getObjectWithNoRetries(tenantPhysicalConfigurationURL, String.class));
        } catch (Exception ex) {
            throw new MdmClientException(String.format("Can't read the tenant physical Config due to %s . Please ensure the tenant exists or try again later",
                    ex.getMessage()), ex);
        }

    }

    public void updateTenantPhyConfig(String environment, String tenantId, String tenantConfig, boolean makePostCall) {
        try {
            String environmentUrl = environmentService.getEnvironment(environment).getUrl();
            String tenantUrl = String.format(TENANTS_URL_FORMAT, environmentUrl);
            UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(tenantUrl)
                    .queryParam(TENANT_ID, tenantId).build();
            if (makePostCall) {
                updateTenantPhyConf(environment, uriComponents, tenantConfig);
            }
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    public void updateTenantPhyConfigOptionalParams(String envId, String tenantId, String config) {
        putCall(envId, tenantId, config, TENANT_UPDATE_OPTIONAL_PARAMETERS_URL);
    }

    public void enableInteractionsV2Storage(String environment, String tenantId) {
        String environmentUrl = environmentService.getEnvironment(environment).getUrl();
        String requestUrl = String.format("%s/reltio/tenants/%s/enableV2Interactions?force=true", environmentUrl, tenantId);
        try {
            pmsRest.put(requestUrl, null);
            LOGGER.info(String.format("Successfully enabled V2 interactions for tenant: %s", tenantId));
        } catch (Exception e) {
            LOGGER.error(String.format("Failed to enable V2 interactions for tenant %s: %s", tenantId, e.getMessage()));
        }
    }

    public JsonNode checkTenantStatus(String envId, String tenantId) {
        String environmentUrl = environmentService.getEnvironment(envId).getUrl();
        String requestUrl = String.format(TENANT_STATUS_URL, environmentUrl, tenantId);
        try{
            return pmsRest.getForObject(requestUrl, JsonNode.class);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    public JsonNode checkTenantRunningTasks(String envId, String tenantId) {
        String environmentUrl = environmentService.getEnvironment(envId).getUrl();
        String requestUrl = String.format(RUNNING_TASKS_URL, environmentUrl, tenantId);
        try {
            return pmsRest.getForObject(requestUrl, JsonNode.class);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }

    }

    public void updateEncryption(String envId, String tenantId , String config){
        String environmentUrl = environmentService.getEnvironment(envId).getUrl();
        String requestUrl = String.format(ENCRYPTION_CONFIGURATION_URL, environmentUrl, tenantId);
        try {
            pmsRest.put(requestUrl, config);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    @Cacheable(value = "tenantL3Config", cacheManager = "l3CacheManager", key = "#environment + '_' + #tenantId",
            condition = "!#forceRefresh", unless = "#result == null")
    public JsonNode getL3Config(String environment, String tenantId, boolean forceRefresh) {
        String envUrl = environmentService.getEnvironment(environment).getUrl();
        String url = String.format(TENANT_L3_CONFIGURATION_URL_FORMAT, envUrl, tenantId);
        try {
            return pmsRest.getForObject(url, JsonNode.class);
        } catch (Exception e) {
            LOGGER.warn(String.format("Cannot get L3 configuration for tenant %s in environment %s", tenantId, environment), e);
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e,
                    String.format("Failed to get L3 configuration for tenant %s in environment %s", tenantId, environment));
        }
    }
    @CachePut(value = "tenantL3Config", cacheManager = "l3CacheManager", key = "#environment + '_' + #tenantId")
    public JsonNode updateL3Config(String environment, String tenantId, JsonNode newL3Config) {
        String envUrl = environmentService.getEnvironment(environment).getUrl();
        String url = String.format(TENANT_L3_CONFIGURATION_URL_POST, envUrl, tenantId);
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<JsonNode> request = new HttpEntity<>(newL3Config, headers);
            pmsRest.put(url, request, JsonNode.class);
            return newL3Config;
        } catch (Exception e) {
            LOGGER.error(String.format("Failed to update L3 configuration for tenant %s in environment %s", tenantId, environment), e);
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e,
                    String.format("Failed to update L3 configuration for tenant %s in environment %s", tenantId, environment));
        }
    }

}
