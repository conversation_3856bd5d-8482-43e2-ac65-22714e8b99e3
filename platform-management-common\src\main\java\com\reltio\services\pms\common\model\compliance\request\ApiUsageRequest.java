package com.reltio.services.pms.common.model.compliance.request;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * The type Cp usage request.
 */
@NoArgsConstructor
@Getter
@Setter
public class ApiUsageRequest extends ComplianceRequest {

    /**
     * The endpoint
     */
    String endPoint;

    public ApiUsageRequest(String startDate, String endDate, List<String> tenantIds, Integer size, Integer offset, String sortOrder, String sortField, String endPoint) {
        super(startDate, endDate, tenantIds, size, offset, sortOrder, sortField);
        this.endPoint = endPoint;
    }

    public ApiUsageRequest(String endPoint) {
        this.endPoint = endPoint;
    }

}
