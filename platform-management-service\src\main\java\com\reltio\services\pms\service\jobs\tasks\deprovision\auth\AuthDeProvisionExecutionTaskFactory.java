package com.reltio.services.pms.service.jobs.tasks.deprovision.auth;

import com.reltio.services.pms.clients.reltio.auth.ReltioAuthClient;
import com.reltio.services.pms.clients.reltio.mdm.MDMClient;
import com.reltio.services.pms.common.model.jobs.tasks.authDeprovision.AuthDeProvisionTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.dao.AuthCustomerDao;
import com.reltio.services.pms.dao.RdmTenantsDao;
import com.reltio.services.pms.service.PmsLockService;
import com.reltio.services.pms.service.environment.EnvironmentService;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.TaskFactory;
import com.reltio.services.pms.service.reltiotenant.TenantsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AuthDeProvisionExecutionTaskFactory implements TaskFactory<AuthDeProvisionTaskInstance, AuthDeProvisionTaskExecutionService> {

    private final ReltioAuthClient authService;
    private final MDMClient mdmClient;
    private final EnvironmentService environmentService;
    private final TenantsService tenantsService;
    private final PmsLockService pmsLockService;
    private final RdmTenantsDao rdmTenantDao;
    private final AuthCustomerDao  authCustomerDao;

    @Autowired
    public AuthDeProvisionExecutionTaskFactory(ReltioAuthClient authService, MDMClient mdmClient,
                                               EnvironmentService environmentService, TenantsService tenantsService,
                                               PmsLockService pmsLockService, RdmTenantsDao rdmTenantDao, AuthCustomerDao  authCustomerDao) {
        this.authService = authService;
        this.mdmClient = mdmClient;
        this.environmentService = environmentService;
        this.tenantsService = tenantsService;
        this.pmsLockService = pmsLockService;
        this.rdmTenantDao = rdmTenantDao;
        this.authCustomerDao = authCustomerDao;
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.AUTH_DE_PROVISION_TASK;
    }

    @Override
    public AuthDeProvisionTaskExecutionService createTask(String envId, AuthDeProvisionTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService) {
        return new AuthDeProvisionTaskExecutionService(envId, taskDetail, grafanaDashboardGBQService, authService,
                mdmClient, environmentService, tenantsService, pmsLockService, rdmTenantDao, authCustomerDao);
    }
}
