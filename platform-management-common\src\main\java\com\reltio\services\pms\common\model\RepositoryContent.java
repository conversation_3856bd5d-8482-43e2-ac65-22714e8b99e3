package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class RepositoryContent extends BaseFirestoreEntity {

    @JsonProperty("fileName")
    private String fileName;

    @JsonProperty("path")
    private String path;

    @JsonProperty("content")
    private String content;

    @Override
    public String getID() {
        return getPath();
    }


}
