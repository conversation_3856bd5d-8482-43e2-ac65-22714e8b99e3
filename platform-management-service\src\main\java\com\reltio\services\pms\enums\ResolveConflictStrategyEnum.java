package com.reltio.services.pms.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum ResolveConflictStrategyEnum {
    WARN("warn"),
    IGNORE("ignore"),
    MERGE("merge"),
    ADD("add");

    private final String value;

    ResolveConflictStrategyEnum(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return value;
    }

    @JsonCreator
    public static ResolveConflictStrategyEnum fromValue(String value) {
        for (ResolveConflictStrategyEnum strategy : values()) {
            if (strategy.value.equalsIgnoreCase(value)) {
                return strategy;
            }
        }
        throw new IllegalArgumentException("Invalid resolveConflicts value: " + value);
    }
}
