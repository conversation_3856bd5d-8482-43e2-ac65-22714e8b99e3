package com.reltio.services.pms.common.model.compliance.response.billingreport;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class BillingReportByCustomerModel {
    /**
     * The Customer.
     */
    @JsonProperty("customer")
    String customer;

    /**
     * The CP Quota.
     */
    @JsonProperty("cpQuota")
    Long cpQuota;
    /**
     * The Task quota.
     */
    @JsonProperty("taskQuota")
    Long taskQuota;
    /**
     * The Billing Start Date.
     */
    @JsonProperty("billingStartDate")
    String billingStartDate;
    /**
     * The Billing End Date.
     */
    @JsonProperty("billingEndDate")
    String billingEndDate;

    /**
     * The Task usage.
     */
    @JsonProperty("taskUsage")
    long taskUsage;
    /**
     * The Task percentage.
     */
    @JsonProperty("taskPercentage")
    double taskPercentage;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BillingReportByCustomerModel that = (BillingReportByCustomerModel) o;
        return Objects.equals(customer, that.customer) &&
                Objects.equals(billingStartDate, that.billingStartDate) &&
                Objects.equals(billingEndDate, that.billingEndDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(customer, billingStartDate, billingEndDate);
    }
}
