package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.FirestoreOptions;
import com.google.cloud.firestore.QueryDocumentSnapshot;
import com.google.common.annotations.VisibleForTesting;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.RequesterActivity;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.reltio.services.pms.common.model.RequesterActivityConstant.CREATION_TIME_FIELD_NAME;
import static java.util.stream.Collectors.toList;

@Service
public class RequesterActivityDao {

    private static final String REQUESTER_ACTIVITIES_COLLECTION_NAME_PATTERN = "PMS_REQUESTER_ACTIVITIES_%s";

    private final String pmsCollectionName;
    private final ObjectMapper objectMapper;
    private final Firestore db;
    private static final Logger LOGGER = Logger.getLogger(RequesterActivityDao.class);

    @Autowired
    public RequesterActivityDao(CredentialsProvider provider,
                                @Value("${firestore.env.name}") String deployedEnv,
                                ObjectMapper objectMapper) {
        this.db = FirestoreOptions.newBuilder().setCredentialsProvider(provider).build().getService();
        this.pmsCollectionName = String.format(REQUESTER_ACTIVITIES_COLLECTION_NAME_PATTERN, deployedEnv);
        this.objectMapper = objectMapper;
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    public void createActivity(RequesterActivity requesterActivity) {
        try {
            requesterActivity.setCreationTime(System.currentTimeMillis());
            Map<String, Object> map = objectMapper.convertValue(requesterActivity, new TypeReference<Map<String, Object>>() {
            });
            db.collection(pmsCollectionName).add(map).get();
        }catch (Exception e){
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public List<RequesterActivity> getActivitiesBetween(long startTime, long endTime) {
        try {
            return db.collection(pmsCollectionName)
                    .whereGreaterThanOrEqualTo(CREATION_TIME_FIELD_NAME, startTime)
                    .whereLessThan(CREATION_TIME_FIELD_NAME, endTime)
                    .get()
                    .get()
                    .getDocuments()
                    .stream()
                    .map(QueryDocumentSnapshot::getData)
                    .map(e -> objectMapper.<RequesterActivity>convertValue(e, new TypeReference<RequesterActivity>() {
                    }))
                    .collect(toList());

        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }

    }


    @VisibleForTesting
    public void deleteAll() {
        try {
            db.collection(pmsCollectionName).listDocuments().forEach(DocumentReference::delete);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }

    }

}
