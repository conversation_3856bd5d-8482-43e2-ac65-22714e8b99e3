package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.auth.domain.ReltioPrivileges;
import com.reltio.devops.common.environment.ServicePurpose;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.model.Environment;
import com.reltio.services.pms.common.model.EnvironmentParam;
import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.common.model.compliance.response.EnvironmentValidateResponse;
import com.reltio.services.pms.security.PMSPermissionsManager;
import com.reltio.services.pms.service.environment.EnvironmentService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(value = "/api/v1/environments", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Environments")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EnvironmentController {
    private static final String URL_TEMPLATE = "https://%s.reltio.com";
    private final EnvironmentService environmentService;
    private final PMSPermissionsManager pmsPermissionsManager;

    @GetMapping("/{environmentName}")
    @PreAuthorize("@pmsPermissionsManager.hasReadAccessToEnvironment(#environmentName)")
    public Environment getEnvironment(@PathVariable String environmentName) {
        return environmentService.getEnvironment(environmentName);
    }

    @GetMapping("")
    public Collection<Environment> getAllEnvironments(@RequestParam(value = "environmentAdmin", defaultValue = "false") boolean environmentAdmin) {
        return pmsPermissionsManager.getListOfEnvironments(environmentAdmin);
    }

    @PutMapping(value = "/{environmentName}")
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public Environment updateEnvironment(@PathVariable String environmentName,
                                         @RequestHeader(name = "envUrl", required = false) String url) {
        url = (url == null) ? String.format(URL_TEMPLATE, environmentName) : url;
        return environmentService.updateEnvironmentURL(environmentName, url);
    }

    @PostMapping(value = "", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public Environment updateSecondryEnvironment(@RequestBody Environment environment) {
        return environmentService.createOrupdateEnvironment(environment);
    }

    @PutMapping(value = "/{environmentName}/defaultUrlMapping", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public Environment updateEnvironment(@PathVariable String environmentName,
                                         @RequestBody Map<ServiceType, String> manualValues) {
        return environmentService.updateDefaultUrlValues(environmentName, manualValues);
    }

    @PostMapping(value = "/{environmentName}/sync")
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public Environment cloneEnvironment(@PathVariable String environmentName,
                                        @RequestParam(value = "updateClusters", defaultValue = "true") Boolean updateClusters,
                                        @RequestHeader(name = "envUrl", required = false) String url) {
        url = (url == null) ? String.format(URL_TEMPLATE, environmentName) : url;
        return environmentService.cloneFromBitbucket(environmentName.toLowerCase(), updateClusters, url);
    }

    @PostMapping(value = "/cloneAll")
    public Map<String, List<String>> cloneClusters() {
        Map<String, List<String>> result = new HashMap<>();
        List<String> succeedEnvs = new ArrayList<>();
        List<String> failedEnvs = new ArrayList<>();
        List<String> skippedEnvs = new ArrayList<>();
        for (String environmentName : environmentService.getAllEnvironmentNames()) {
            Environment currentEnv = environmentService.getEnvironment(environmentName);
            if (currentEnv.getSkip()) {
                skippedEnvs.add(environmentName);
            } else {
                String url = (currentEnv.getUrl() == null) ? String.format(URL_TEMPLATE, environmentName) : currentEnv.getUrl();
                try {
                    environmentService.cloneFromBitbucket(environmentName.toLowerCase(), true, url);
                    succeedEnvs.add(environmentName);
                } catch (Exception e) {
                    failedEnvs.add(environmentName);
                }
            }
        }
        result.put("succeed", succeedEnvs);
        result.put("failed", failedEnvs);
        result.put("skipped", skippedEnvs);
        return result;
    }

    @GetMapping("/{environmentName}/environmentParam")
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public EnvironmentParam getEnvironmentParam(@PathVariable String environmentName) {
        return environmentService.getEnvironment(environmentName.toLowerCase()).getEnvironmentParam();
    }

    @PutMapping(value = "/{environmentName}/environmentParam", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public EnvironmentParam updateEnvironment(@PathVariable String environmentName,
                                              @RequestBody EnvironmentParam param) {
        return environmentService.updateEnvironmentParam(environmentName, param);
    }

    @PutMapping(value = "/{environmentName}/skip")
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public Environment skipEnvironment(@PathVariable String environmentName) {
        Environment environment = environmentService.getEnvironment(environmentName);
        environment.setSkip(true);
        return environmentService.createOrupdateEnvironment(environment);
    }

    @PutMapping(value = "/{environmentName}/resume")
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public Environment resumeEnvironment(@PathVariable String environmentName) {
        Environment environment = environmentService.getEnvironment(environmentName);
        environment.setSkip(false);
        return environmentService.createOrupdateEnvironment(environment);
    }

    @PostMapping(value = "/{environmentName}/storagePriorityList", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.class, privileges = ReltioPrivileges.CREATE)
    public Environment createStoragePriorityListMapping(@RequestBody Map<ServicePurpose, List<com.reltio.devops.common.environment.ServiceType>> storagePriorityList, @PathVariable String environmentName) {
        return environmentService.createStoragePriorityListMapping(storagePriorityList, environmentName);
    }

    @PutMapping(value = "/{environmentName}/storagePriorityList", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.class, privileges = ReltioPrivileges.UPDATE)
    public Environment updateStoragePriorityListMapping(@RequestBody Map<ServicePurpose, List<com.reltio.devops.common.environment.ServiceType>> storagePriorityList, @PathVariable String environmentName) {
        Environment environment = environmentService.getEnvironment(environmentName);
        environment.setStoragePriorityList(storagePriorityList);
        return environmentService.updateEnvironmentStoragePriorityList(environment);
    }

    @GetMapping(value = "/{environmentName}/storagePriorityList")
    @ReltioSecured(resourceClass = Pms.Environment.class, privileges = ReltioPrivileges.READ)
    public Map<ServicePurpose, List<com.reltio.devops.common.environment.ServiceType>> getStoragePriorityListMapping(@PathVariable String environmentName) {
        return environmentService.getEnvironment(environmentName).getStoragePriorityList();

    }

    @PutMapping(value = "/{environmentName}/dataSetDeploymentCloud")
    @ReltioSecured(resourceClass = Pms.Environment.class, privileges = ReltioPrivileges.UPDATE)
    public Environment updateDataSetAndDeploymentCloud(@PathVariable String environmentName,
                                                       @RequestParam(value = "dataSet") String dataSet,
                                                       @RequestParam(value = "deploymentCloud") String deploymentCloud) {
        return environmentService.updateDataSetAndCloud(environmentName, dataSet, deploymentCloud);
    }

    @DeleteMapping(value = "/{environmentName}")
    public Environment deleteEnv(@PathVariable String environmentName) {
        return environmentService.deleteEnv(environmentName);
    }

    @GetMapping("/{environmentName}/validateEnvironment")
    public EnvironmentValidateResponse validateEnvironment(@PathVariable String environmentName) {
        return environmentService.validateEnvironment(environmentName);
    }

}
