package com.reltio.services.pms.common.model.jobs.tasks.mdm;

import com.reltio.services.pms.common.model.jobs.tasks.TaskInstanceParams;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;

import java.util.List;
import java.util.Set;

public interface MdmTaskInstanceParams extends TaskInstanceParams {

    boolean isEnableRiq();

    String getCustomerName();

    String getTenantId();

    String getProductEdition();

    TenantSize getTenantSize();

    String getIndustry();

    List<String> getCleanseRegions();

    TenantPurpose getTenantPurpose();

    String getCustomerId();

    String getLoqateProcesses();

    String getDataStorageArn();

    String getMatchStorageArn();

    Boolean getIsQaAutomation();

    String getNewInstanceId();

    ReltioPackageType getReltioPackageType();

    Boolean getUseSpannerCloudFunction();

    default boolean isWarmupRequired() {
        return false;
    }

    default String getvaultKey(){
        return null;
    }

    default boolean isKeyRequired(){
        return false;
    }

    default boolean getUseServiceBusFunction(){
        return false;
    }

    default boolean isBceTenant(){
        return false;
    }

    default Set<String> getPredicateRegions(){
        return null;
    }
}
