package com.reltio.services.pms.common.model.supportability.result;

import com.reltio.collection.CollectionUtils;
import com.reltio.services.pms.common.model.supportability.StatsDto;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@FunctionalInterface
public interface StatsCombiner<T extends StatsDto> {

    T mergeAllRows(List<T> dtoList);

    default Float roundOff2Digits(Float val) {
        return Math.round(val * 100.0) / 100.0f;
    }

    /**
     * Combine list.
     *
     * @param dtoList the dto list
     * @return the list
     */
    default List<T> combine(List<T> dtoList) {
        Map<Long, List<T>> clusterMap = dtoList.stream()
                .collect(Collectors.groupingBy(T::getStartTime));
        List<T> valueList = new ArrayList<>();
        for (Map.Entry<Long, List<T>> entry : clusterMap.entrySet()) {
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                valueList.add(mergeAllRows(entry.getValue()));
            }
        }
        return valueList;
    }
}

