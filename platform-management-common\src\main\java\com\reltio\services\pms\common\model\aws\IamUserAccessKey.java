package com.reltio.services.pms.common.model.aws;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;
import java.util.regex.Pattern;

public class IamUserAccessKey {
    public static final Pattern ALPHANUMERIC_VALIDATION_PATTERN = Pattern.compile("^[a-zA-Z0-9]*$");
    public static final int ID_AND_KEY = 2;

    @JsonProperty(value = "accessKeyId")
    private final String accessKeyId;

    @JsonProperty(value = "secretAccessKey")
    private final String secretAccessKey;

    @JsonCreator
    public IamUserAccessKey(@JsonProperty(value = "accessKeyId") String accessKeyId,
                            @JsonProperty(value = "secretAccessKey") String secretAccessKey) {
        this.accessKeyId = accessKeyId;
        this.secretAccessKey = secretAccessKey;
    }

    public IamUserAccessKey(String secret) {
        String[] idKeyPair = secret.split(":");
        if (idKeyPair.length != ID_AND_KEY) {
            throw new IllegalArgumentException();
        }
        accessKeyId = idKeyPair[0];
        secretAccessKey = idKeyPair[1];
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public String getSecretAccessKey() {
        return secretAccessKey;
    }

    public String getIdKeyPair() {
        return String.format("%s:%s", accessKeyId, secretAccessKey);
    }

    public boolean areKeysAlphaNumeric() {
        return ALPHANUMERIC_VALIDATION_PATTERN.matcher(getAccessKeyId()).matches() && ALPHANUMERIC_VALIDATION_PATTERN.matcher(getSecretAccessKey()).matches();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        IamUserAccessKey that = (IamUserAccessKey) o;
        return Objects.equals(getAccessKeyId(), that.getAccessKeyId()) &&
                Objects.equals(getSecretAccessKey(), that.getSecretAccessKey());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getAccessKeyId(), getSecretAccessKey());
    }

    @Override
    public String toString() {
        return "IamUserAccessKey{" +
                "accessKeyId='" + accessKeyId + '\'' +
                ", secretAccessKey='" + secretAccessKey + '\'' +
                '}';
    }
}
