package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class RdmCache {

    @JsonProperty(value = "enabled")
    private final Boolean enabled;

    @JsonProperty(value = "type")
    private final String type;

    @JsonCreator
    public RdmCache(
            @JsonProperty(value = "enabled" , required = true) Boolean enabled,
            @JsonProperty(value = "type" , required = true) String type
    ) {

        this.enabled = enabled;
        this.type = type;
    }


    public Boolean getEnabled() {
        return enabled;
    }

    public String getType() {
        return type;
    }

    @Override
    public String toString() {
        return "cache" +":"+ "{" +
                "enabled=" + enabled +
                ", type='" + type + '\'' +
                '}';
    }
}
