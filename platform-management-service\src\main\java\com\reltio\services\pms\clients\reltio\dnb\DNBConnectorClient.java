package com.reltio.services.pms.clients.reltio.dnb;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.reltio.io.IOUtils;
import com.reltio.services.pms.common.IPMSRestTemplate;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.Environment;
import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.common.model.UpdateDNBKeyRequest;
import com.reltio.services.pms.common.model.enterprise.contracts.DTSSType;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import com.reltio.services.pms.service.ProductEditionsService;
import com.reltio.services.pms.service.reltiotenant.TenantsService;
import com.reltio.services.pms.service.environment.EnvironmentService;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.json.simple.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.io.IOException;
import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Service
@Retryable(exceptionExpression = "${retry.shouldRetry:true}", maxAttempts = 5, backoff = @Backoff(delay = 2000, multiplier = 5))
public class DNBConnectorClient {
    private final IPMSRestTemplate restTemplate;
    private final EnvironmentService environmentService;
    private final DNBConnectorClientHelper dnbConnectorClientHelper;
    private static final String ENVIRONMENT_URL = "EnvironmentURL";
    private static final String TENANT_ID = "tenantId";
    private static final String CONFIG_KIND="Config-Kind";
    private static final String CONFIG_KIND_VALUE="b2b_velocity_pack";
    @Value("${reltio.auth.server.url}/oauth/token")
    private String oAuthEndpointUrl;
    private final ObjectMapper mapper;
    private final ProductEditionsService productEditionsService;
    static final String DATA_TENANT_ID_REGEX = "((\\{\\{)\\bdataTenantId.*\\b(\\}\\}))";
    private static final Logger LOGGER = Logger.getLogger(DNBConnectorClient.class);

    private static final String GET_DNB_PROFILE_BY_TENANT_URL = "%s/b2bIntegration/config/tenants";

    private static final String DNB_KEYS_UPDATE_URL = "%s/b2bIntegration/config/profiles/%s/mappings/config";

    private final TenantsService tenantsService;


    @Autowired
    public DNBConnectorClient(@Qualifier("console") IPMSRestTemplate rest,
                              EnvironmentService environmentService,
                              ProductEditionsService productEditionsService, TenantsService tenantsService) {
        restTemplate = rest;
        this.environmentService = environmentService;
        this.dnbConnectorClientHelper = new DNBConnectorClientHelper();
        mapper = new ObjectMapper();
        this.productEditionsService = productEditionsService;
        this.tenantsService = tenantsService;
        Set<String> properties=new HashSet<>();
        properties.add(oAuthEndpointUrl);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    public void createProfile(String profile, TenantPurpose tenantPurpose,String envId) {
        String finalUrl = dnbConnectorClientHelper.createUrlForCreateProfile(profile, getBaseURL(tenantPurpose,envId));
        try {
            JSONArray jsonArray = new JSONArray();
            restTemplate.postForEntity(finalUrl, jsonArray, JsonNode.class);

        } catch (Exception ex) {
            throw new DNBConnectorClientException("Can not create profile", ex);
        }
    }

    public boolean checkIfProfileExist(String profile, TenantPurpose tenantPurpose,String envId) {

        String finalUrl = dnbConnectorClientHelper.createUrlForGetCreatedMapping(profile, getBaseURL(tenantPurpose,envId));
        try {
            restTemplate.getForObject(finalUrl, JsonNode.class);
            return true;

        } catch (HttpClientErrorException.NotFound ex) {
            return false;
        }
    }

    public void configureProfileMappings(String profile, String productEdition, TenantPurpose tenantPurpose,
                                         String envId, ReltioPackageType reltioPackageType) {
        try {
            String productEditionNameWithReltioPackageType=productEditionsService.getProductEditionNameWithReltioPackageType(productEdition,reltioPackageType);
            Map<String, String> mappings = productEditionsService.getDnbProfileMappingsForProductEdition(productEditionNameWithReltioPackageType);
            if(mappings!=null) {
                for (Map.Entry<String, String> mapping : mappings.entrySet()) {
                    String file = mapping.getValue();
                    JsonNode request = mapper.readTree(file);
                    String url = dnbConnectorClientHelper.createUrlToAddDnbMappingsToProfile(profile, getBaseURL(tenantPurpose, envId), mapping.getKey());
                    restTemplate.postForObject(url, request, JsonNode.class);
                }
            }
        } catch (Exception ex) {
            throw new DNBConnectorClientException("Can not configure profile mappings", ex);
        }
    }

    public void registerTenantInProfile(String envId, String tenantID, String dataTenantId, TenantPurpose tenantPurpose, String profile) {
        Environment environment = environmentService.getEnvironment(envId);
        String finalUrl = dnbConnectorClientHelper.createUrlToRegisterProfile(profile, getBaseURL(tenantPurpose,envId));
        HttpHeaders headers = new HttpHeaders();
        headers.put(ENVIRONMENT_URL, Collections.singletonList(environment.getUrl()));
        headers.put(TENANT_ID, Collections.singletonList(tenantID));
        if (dataTenantId != null) {
            headers.put("tenantIDdT", Collections.singletonList(dataTenantId));
        }
        headers.put(CONFIG_KIND,Collections.singletonList(CONFIG_KIND_VALUE));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        try {
            restTemplate.exchange(finalUrl, HttpMethod.POST, entity, JsonNode.class);
        } catch (Exception ex) {
            throw new DNBConnectorClientException("Exception while registering tenant: ", ex);
        }
    }

    public JsonNode getMappingForCreatedProfile(String tenantID, String profile, TenantPurpose tenantPurpose,String envId) {
        Environment environment = environmentService.getEnvironment(tenantPurpose.getCloudUserMarker());
        String finalUrl = dnbConnectorClientHelper.createUrlForGetCreatedMapping(profile, getBaseURL(tenantPurpose,envId));
        HttpHeaders headers = new HttpHeaders();
        headers.put(ENVIRONMENT_URL, Collections.singletonList(environment.getUrl()));
        headers.put(TENANT_ID, Collections.singletonList(tenantID));
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        try {
            ResponseEntity<JsonNode> response = restTemplate.exchange(finalUrl, HttpMethod.GET, entity, JsonNode.class);
            if (response.toString().contains("error")) {
                throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST,
                        HttpStatus.INTERNAL_SERVER_ERROR.value(), response.toString());
            }
            return response.getBody();
        } catch (Exception ex) {
            throw new DNBConnectorClientException("Exception while getting mapping: ", ex);
        }
    }

    private String getBaseURL(TenantPurpose tenantPurpose,String envId) {
        String dnbConnectorProdUrl=environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.DNB_CONNECTOR_PROD);
        String dnbConnectorTestUrl=environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.DNB_CONNECTOR_TEST);

        if (tenantPurpose == TenantPurpose.PROD) {
            return dnbConnectorProdUrl;
        } else {
            return dnbConnectorTestUrl;
        }
    }

    public String getProfileNameByTenantForB2B(String envId, String customerTenant, TenantPurpose tenantPurpose) {
        Environment environment = environmentService.getEnvironment(envId);
        String baseURL = getBaseURL(tenantPurpose,envId);
        String url = String.format("%s/b2bIntegration/config/tenants", baseURL);
        HttpHeaders headers = new HttpHeaders();
        headers.put(ENVIRONMENT_URL, Collections.singletonList(environment.getUrl()));
        headers.put(TENANT_ID, Collections.singletonList(customerTenant));
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        try {
            ResponseEntity<JsonNode> response = restTemplate.exchange(url, HttpMethod.GET, entity, JsonNode.class);
            JsonNode body = response.getBody();
            if (body == null) {
                throw new PlatformManagementException(PlatformManagementErrorCode.PROFILE_NOT_FOUND_FOR_TENANT, HttpStatus.BAD_REQUEST.value(), customerTenant);
            }

            return body.get("tenant").get(TENANT_ID).asText();
        } catch (HttpClientErrorException.NotFound ex) {
            return null;
        }
    }

    public String getDataTenantByTenant(String envId, String customerTenant, TenantPurpose tenantPurpose) {
        Environment environment = environmentService.getEnvironment(envId);
        String baseURL = getBaseURL(tenantPurpose,envId);
        if (baseURL == null) {
            return null;
        }
        String url = String.format("%s/b2bIntegration/config/tenants", baseURL);
        HttpHeaders headers = new HttpHeaders();
        headers.put(ENVIRONMENT_URL, Collections.singletonList(environment.getUrl()));
        headers.put(TENANT_ID, Collections.singletonList(customerTenant));
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        try {
            ResponseEntity<JsonNode> response = restTemplate.exchange(url, HttpMethod.GET, entity, JsonNode.class);
            JsonNode body = response.getBody();
            if (body == null) {
                throw new PlatformManagementException(PlatformManagementErrorCode.PROFILE_NOT_FOUND_FOR_TENANT, HttpStatus.BAD_REQUEST.value(), customerTenant);
            }
            return body.has("dataTenant") ? body.get("dataTenant").get(TENANT_ID).asText() : null;
        } catch (HttpClientErrorException.NotFound ex) {
            return null;
        }
    }

    //Edit DNB Connector
    public JsonNode registerCustomerTenant(String envId, String tenantID) throws IOException {
        Environment environment = environmentService.getEnvironment(envId);
        String environmentUrl = environment.getUrl();
        String url = String.format("%s/%s/%s/%s", environmentUrl, "dtss", "tenants", "customerTenants");
        String file = IOUtils.readResource(DNBConnectorClient.class, "dnbconnector_requests/register_tenant_request.json")
                .replace("{tenant_id}", tenantID)
                .replace("{oauth_end_point_url}", oAuthEndpointUrl)
                .replace("{reltio_api_url}", environmentUrl + "/reltio/api")
                .replace("{email}", "<EMAIL>");
        JsonNode request = mapper.readTree(file);
        try {
            return restTemplate.postForObject(url, request, JsonNode.class);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
        }
    }

    public JsonNode subscriptionOfCustomerTenantDataTenant(String envId, String dataTenantId, String customerTenantId
            , String offerType, DTSSType dtssSubscriptionType,ReltioPackageType reltioPackageType) throws IOException {
        Environment environment = environmentService.getEnvironment(envId);
        String environmentUrl = environment.getUrl();
        String url = String.format("%s/dtss/subscriptions/", environmentUrl);
        String productEditionNameWithReltioPackageType=
                productEditionsService.getProductEditionNameWithReltioPackageType(offerType,reltioPackageType);
        String file = productEditionsService.getDtssFileContent(productEditionNameWithReltioPackageType, dtssSubscriptionType)
                .replaceAll(DATA_TENANT_ID_REGEX, dataTenantId)
                .replace("{{tenantId}}", customerTenantId);
        JsonNode request = mapper.readTree(file);
        try {
            return restTemplate.postForObject(url, request, JsonNode.class);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
        }
    }

    public JsonNode addDNBKeys(UpdateDNBKeyRequest updateDNBKeyRequest, String envId,String tenantId) {
        TenantPurpose tenantPurpose = tenantsService.getTenant(tenantId).getTenantPurpose();
        JsonNode dnbProfile = getDNBprofile(envId,tenantId,tenantPurpose);
        String profileName = Optional.ofNullable(dnbProfile)
                .map(node -> node.get("profile"))
                .map(JsonNode::asText)
                .orElseThrow(() -> new DNBConnectorClientException("Profile name is null or not present"));
        return applyNewDNBKeys(updateDNBKeyRequest, tenantId, profileName,tenantPurpose,envId);
    }

    @Nullable
    private JsonNode getDNBprofile(String envId,String tenantId,TenantPurpose tenantPurpose) {
        String baseURL = getBaseURL(tenantPurpose,envId);
        String url = String.format(GET_DNB_PROFILE_BY_TENANT_URL,baseURL);
        HttpHeaders headers = buildHttpHeaders(envId,tenantId);
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        try {
            ResponseEntity<JsonNode> response = restTemplate.exchange(url, HttpMethod.GET, entity, JsonNode.class);
            JsonNode body = response.getBody();
            if (body == null) {
                throw new PlatformManagementException(PlatformManagementErrorCode.PROFILE_NOT_FOUND_FOR_TENANT, HttpStatus.BAD_REQUEST.value(),tenantId);
            }
            return body;
        } catch (HttpClientErrorException.NotFound ex) {
            return null;
        }
    }

    @NotNull
    public JsonNode applyNewDNBKeys(UpdateDNBKeyRequest updateDNBKeyRequest, String tenantId, String profileName, TenantPurpose tenantPurpose,String envId) {
        try {
            String sanitizedProfileName = profileName != null ? profileName.replaceAll("\"", "") : null;
            String baseURL = getBaseURL(tenantPurpose,envId);
            String url = String.format(DNB_KEYS_UPDATE_URL, baseURL, sanitizedProfileName);
            UpdateDNBKeyRequest.DnbPojo dnbPojo = new UpdateDNBKeyRequest.DnbPojo(updateDNBKeyRequest);
            HttpEntity<UpdateDNBKeyRequest.DnbPojo> entity = new HttpEntity<>(dnbPojo);
            ResponseEntity<JsonNode> response = restTemplate.exchange(url, HttpMethod.POST, entity, JsonNode.class);
            JsonNode body = response.getBody();
            if (body == null) {
                throw new PlatformManagementException(PlatformManagementErrorCode.PROFILE_NOT_FOUND_FOR_TENANT, HttpStatus.BAD_REQUEST.value(),tenantId);
            }
            return body;
        } catch (HttpClientErrorException.NotFound ex) {
            throw new DNBConnectorClientException("Not able to add keys to the tenant",ex);
        }

    }


    private HttpHeaders buildHttpHeaders(String envId,String tenantId) {
        HttpHeaders headers = new HttpHeaders();
        headers.set(ENVIRONMENT_URL, environmentService.getEnvironment(envId).getUrl());
        headers.set(TENANT_ID, tenantId);
        return headers;
    }

    public JsonNode deRegisterTenant(String tenantId,String envId) throws JsonProcessingException {
        TenantPurpose tenantPurpose = tenantsService.getTenant(tenantId).getTenantPurpose();
        String baseURL = getBaseURL(tenantPurpose, envId);
        String url = String.format(GET_DNB_PROFILE_BY_TENANT_URL, baseURL);
        HttpHeaders headers = buildHttpHeaders(envId, tenantId);
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        try {
            ResponseEntity<JsonNode> response = restTemplate.exchange(url, HttpMethod.DELETE, entity, JsonNode.class);
            JsonNode body = response.getBody();
            if (body == null) {
                throw new PlatformManagementException(PlatformManagementErrorCode.ERROR_WHILE_DE_REGISTERING_TENANT_FROM_DNB, HttpStatus.BAD_REQUEST.value(), tenantId);
            }
            return body;
        } catch (HttpClientErrorException.NotFound ex) {
                return mapper.readTree(ex.getResponseBodyAsString());
        }

    }
}
