package com.reltio.services.pms.common.model.supportability.result.combiner;

import com.reltio.services.pms.common.model.supportability.DynamoDBStatsDto;
import com.reltio.services.pms.common.model.supportability.DynamoDBTablesStatsDto;
import com.reltio.services.pms.common.model.supportability.result.StatsCombiner;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;

/**
 * The type Event stats combiner.
 */
public class DynamoDBStatsCombiner implements StatsCombiner<DynamoDBTablesStatsDto> {
    /**
     * mergeAllRestRows method is used to merge all the provided rows from GBQ into single record by sum/avg based on the column in GBQ.
     *
     * @param dtoList the dto list
     * @return the list
     */
    @Override
    public DynamoDBTablesStatsDto mergeAllRows(List<DynamoDBTablesStatsDto> dtoList) {
        DynamoDBTablesStatsDto response = dtoList.get(NumberUtils.INTEGER_ZERO);
        DynamoDBTablesStatsDto lastResponse = dtoList.get(dtoList.size() - NumberUtils.INTEGER_ONE);
        long startTime = response.getStartTime();
        long endTime = lastResponse.getEndTime();
        int totalRows = dtoList.size();
        double rcuAvg = NumberUtils.FLOAT_ZERO;
        double rcuMax = NumberUtils.FLOAT_ZERO;
        double rcuP90 = NumberUtils.FLOAT_ZERO;
        double rcuP95 = NumberUtils.FLOAT_ZERO;

        double wcuAvg = NumberUtils.FLOAT_ZERO;
        double wcuMax = NumberUtils.FLOAT_ZERO;
        double wcuP90 = NumberUtils.FLOAT_ZERO;
        double wcuP95 = NumberUtils.FLOAT_ZERO;

        for (DynamoDBTablesStatsDto dto : dtoList) {
            rcuAvg += dto.getRcu().getAverage();
            rcuMax = Math.max(dto.getRcu().getMax(), rcuMax);
            rcuP90 += dto.getRcu().getP90();
            rcuP95 += dto.getRcu().getP95();

            wcuAvg += dto.getWcu().getAverage();
            wcuMax = Math.max(dto.getWcu().getMax(), wcuMax);
            wcuP90 += dto.getWcu().getP90();
            wcuP95 += dto.getWcu().getP95();
        }
        rcuAvg = rcuAvg / totalRows;
        rcuP90 = rcuP90 / totalRows;
        rcuP95 = rcuP95 / totalRows;

        wcuAvg = wcuAvg / totalRows;
        wcuP90 = wcuP90 / totalRows;
        wcuP95 = wcuP95 / totalRows;

        DynamoDBTablesStatsDto dto = new DynamoDBTablesStatsDto();
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setRcu(DynamoDBStatsDto.builder()
                .average(rcuAvg)
                .max(rcuMax)
                .p90(rcuP90)
                .p95(rcuP95)
                .build());
        dto.setWcu(DynamoDBStatsDto.builder()
                .average(wcuAvg)
                .max(wcuMax)
                .p90(wcuP90)
                .p95(wcuP95)
                .build());

        return dto;
    }
}
