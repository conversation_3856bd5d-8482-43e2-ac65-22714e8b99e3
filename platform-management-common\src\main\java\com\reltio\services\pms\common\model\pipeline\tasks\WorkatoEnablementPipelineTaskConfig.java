package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.Requester;
import com.reltio.services.pms.common.model.Rih;
import com.reltio.services.pms.common.sales.PMSProductName;

import java.util.Objects;

public class WorkatoEnablementPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonProperty(value = "rih")
    private final Rih rih;

    @JsonProperty(value = "productEdition")
    private final String productEdition;

    @JsonProperty(value = "dnb_api_secret")
    private final String dnbApiSecret;

    @JsonProperty(value = "dnb_api_key")
    private final String dnbApiKey;

    @JsonProperty(value = "requester")
    private Requester requester;

    @JsonCreator
    public WorkatoEnablementPipelineTaskConfig(@JsonProperty(value = "name") String name,
                                               @JsonProperty(value = "rih", required = false) Rih rih,
                                               @JsonProperty(value = "productEdition", required = false) String productEdition,
                                               @JsonProperty(value = "dnb_api_secret") String dnbApiSecret,
                                               @JsonProperty(value = "dnb_api_key") String dnbApiKey,
                                               @JsonProperty(value = "requester") Requester requester) {
        super(name, TaskType.WORKATO_ENABLEMENT_TASK);
        this.rih = rih;
        this.productEdition = productEdition;
        this.dnbApiSecret = dnbApiSecret;
        this.dnbApiKey = dnbApiKey;
        this.requester = requester;
    }

    public Rih getRih() {
        return rih;
    }

    public String getProductEdition() {
        return productEdition;
    }

    public String getDnbApiSecret() {
        return dnbApiSecret;
    }

    public String getDnbApiKey() {
        return dnbApiKey;
    }

    public Requester getRequester() {
        return requester;
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.RIH;
    }

    @Override
    public boolean visibleInContract() {
        return true;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }
        WorkatoEnablementPipelineTaskConfig that = (WorkatoEnablementPipelineTaskConfig) o;
        return super.equals(that) && Objects.equals(getProductEdition(), that.getProductEdition())
                && Objects.equals(getRih(), that.getRih()) && Objects.equals(getDnbApiSecret(), that.getDnbApiSecret())
                && Objects.equals(getDnbApiKey(), that.getDnbApiKey()) && Objects.equals(getRequester(), that.getRequester());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getProductEdition(), getRih(), getDnbApiSecret(), getDnbApiKey(), getRequester());
    }

}
