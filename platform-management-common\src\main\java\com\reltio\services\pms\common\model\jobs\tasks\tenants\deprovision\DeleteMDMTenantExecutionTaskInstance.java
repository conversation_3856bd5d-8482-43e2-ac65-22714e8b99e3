package com.reltio.services.pms.common.model.jobs.tasks.tenants.deprovision;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.AnalyticsCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.DataCleanUpOptions;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.ExternalServicesCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.MetaDataCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.Parameters;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.separated.execution.CleanTenantExecutionTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * DeprovisionTenantExecutionTaskInstance
 * Created by apylkov
 */
@Getter
@EqualsAndHashCode(callSuper = false)
public class DeleteMDMTenantExecutionTaskInstance extends CleanTenantExecutionTaskInstance {

    @JsonProperty("skipUsers")
    private List<String> skipUsers;
    @JsonProperty("skipCustomers")
    private List<String> skipCustomers;
    private final boolean failOnError;

    @JsonCreator
    public DeleteMDMTenantExecutionTaskInstance(@JsonProperty(value = "taskId") String taskId,
                                                @JsonProperty(value = "name") String name,
                                                @JsonProperty(value = "jobId") String jobId,
                                                @JsonProperty(value = "startTime") Long startTime,
                                                @JsonProperty(value = "finishTime") Long finishTime,
                                                @JsonProperty(value = "status") TaskStatus status,
                                                @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                                @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                                @JsonProperty(value = "executingNodeName") String executingNodeName,
                                                @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                                @JsonProperty(value = "envId") String envId,
                                                @JsonProperty(value = "tenantName") String tenantName,
                                                @JsonProperty(value = "events") List<String> events,
                                                @JsonProperty(value = "currentState") List<String> currentState,
                                                @JsonProperty("skipUsers") List<String> skipUsers,
                                                @JsonProperty("skipCustomers") List<String> skipCustomers,
                                                @JsonProperty(value = "failOnError") Boolean failOnError) {
        super(taskId, name, jobId, startTime, finishTime, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId,
                new DataCleanUpOptions(true, true, true, true, true, true),
                new ExternalServicesCleanUp(true),
                true,
                new MetaDataCleanUp(true),
                new AnalyticsCleanUp(true, true, true),
                tenantName, events, currentState, new Parameters(true, true, ""), false);
        //rewrite type from clean to delete
        this.skipUsers = skipUsers == null ? new ArrayList<>() : new ArrayList<>(skipUsers);
        this.skipCustomers = skipCustomers == null? new ArrayList<>() : new ArrayList<>(skipCustomers);
        this.setType(TaskType.DELETE_MDM_TENANT_TASK);
        this.failOnError = Optional.ofNullable(failOnError).orElse(Boolean.TRUE);
    }

}
