package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;


@JsonIgnoreProperties(ignoreUnknown = true)
public class ServiceNode extends BaseFirestoreEntity {

    @JsonProperty("name")
    private final String name;
    @JsonProperty("lastSeen")
    private final Long lastSeen;

    @JsonCreator
    public ServiceNode(@JsonProperty("name") String name,
                       @JsonProperty("lastSeen") Long lastSeen) {
        this.name = name;
        this.lastSeen = lastSeen;
    }

    public String getName() {
        return name;
    }

    public Long getLastSeen() {
        return lastSeen;
    }

    @Override
    public String getID() {
        return getName();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ServiceNode)) {
            return false;
        }
        ServiceNode that = (ServiceNode) o;
        return Objects.equals(getName(), that.getName()) &&
                Objects.equals(getLastSeen(), that.getLastSeen());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getName(), getLastSeen());
    }

    @Override
    public String toString() {
        return "ServiceNode{" +
                "name='" + name + '\'' +
                ", lastSeen=" + lastSeen +
                '}';
    }
}