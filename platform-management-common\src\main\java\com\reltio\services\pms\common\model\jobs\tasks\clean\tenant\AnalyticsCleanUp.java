package com.reltio.services.pms.common.model.jobs.tasks.clean.tenant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnalyticsCleanUp {
    @JsonProperty(value = "riq")
    private boolean riq;

    @JsonProperty(value = "gbQClean")
    private boolean gbQClean;

    @JsonProperty(value = "matchIQ")
    private boolean matchIQ;

    @JsonCreator
    public AnalyticsCleanUp(@JsonProperty(value = "riq") boolean riq,
                            @JsonProperty(value = "gbQClean") boolean gbQClean,
                            @JsonProperty(value = "matchIQ") boolean matchIQ) {
        this.riq = riq;
        this.gbQClean = gbQClean;
        this.matchIQ = matchIQ;
    }

    public boolean isRiq() {
        return riq;
    }

    public boolean isGbQClean() {
        return gbQClean;
    }

    public boolean isMatchIQ() {
        return matchIQ;
    }

    public void setRiq(boolean riq) {
        this.riq = riq;
    }

    public void setGbQClean(boolean gbQClean) {
        this.gbQClean = gbQClean;
    }

    public void setMatchIQ(boolean matchIQ) {
        this.matchIQ = matchIQ;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if(o == null || this.getClass() != o.getClass()) return false;

        AnalyticsCleanUp that = (AnalyticsCleanUp) o;

        return isRiq() == that.isRiq() &&
                isGbQClean() == that.isGbQClean() &&
                isMatchIQ() == that.isMatchIQ();
    }

    @Override
    public int hashCode() {
        return Objects.hash(isRiq(), isGbQClean(), isMatchIQ());
    }

    @Override
    public String toString() {
        return "AnalyticsCleanUp{" +
                "riq=" + riq +
                ", gbQClean=" + gbQClean +
                ", matchIQ=" + matchIQ +
                '}';
    }
}
