package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.devops.common.environment.CloudProvider;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.CustomerType;
import com.reltio.services.pms.common.model.config.AccessLevel;
import com.reltio.services.pms.common.model.config.CleanseRegionsMapping;
import com.reltio.services.pms.common.model.config.CloudConfigEntry;
import com.reltio.services.pms.common.model.config.CustomerTypeConfigEntry;
import com.reltio.services.pms.common.model.config.EnvConfigEntry;
import com.reltio.services.pms.common.model.config.IndustryTTL;
import com.reltio.services.pms.common.model.config.ReltioPackageTypeProductCodes;
import com.reltio.services.pms.common.model.config.RepositoryConfig;
import com.reltio.services.pms.common.model.config.SalesForceProductCodes;
import com.reltio.services.pms.common.model.jobs.tasks.rih.RIHPaths;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import com.reltio.services.pms.common.sales.model.SalesForceProductName;
import com.reltio.services.pms.service.PMSConfigService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The type Pms config controller.
 */
@RestController
@RequestMapping(value = "/api/v1/pmsConfig", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name= "PMS Config Service")
@ReltioSecured(resourceClass = Pms.Environment.Config.class)
public class PMSConfigController {

    /**
     * The constant AWS_TO_AZURE_MAPPING.
     */
    public static final String AWS_TO_AZURE_MAPPING = "/AwsToAzureMapping";
    /**
     * The constant GCP_TO_AZURE_MAPPING.
     */
    public static final String GCP_TO_AZURE_MAPPING = "/gcpToAzureMapping";
    /**
     * The constant AZURE_TO_GCP_MAPPING.
     */
    public static final String AZURE_TO_GCP_MAPPING = "/azureToGcpMapping";
    /**
     * The constant AZURE_TO_AWS_MAPPING.
     */
    public static final String AZURE_TO_AWS_MAPPING = "/azureToAwsMapping";
    /**
     * The constant AWS_TO_GCP_MAPPING.
     */
    public static final String AWS_TO_GCP_MAPPING = "/AwsToGcpMapping";
    /**
     * The constant GCP_TO_AWS_MAPPING.
     */
    public static final String GCP_TO_AWS_MAPPING = "/gcpToAwsMapping";
    /**
     * The constant RIH_MAPPING.
     */
    public static final String RIH_MAPPING = "/rihMapping";
    /**
     * The constant configName.
     */
    public static final String configName = "configName";
    /**
     * The constant status.
     */
    public static final String status = "status";
    /**
     * The constant deleted.
     */
    public static final String deleted = "deleted";


    private final PMSConfigService pmsConfigService;

    /**
     * Instantiates a new Pms config controller.
     *
     * @param pmsConfigService the pms config service
     */
    @Autowired
    public PMSConfigController(PMSConfigService pmsConfigService) {
        this.pmsConfigService = pmsConfigService;
    }

    /**
     * Create access level roles mapping access level.
     *
     * @param accessLevel the access level
     * @return the access level
     */
    @PostMapping(value = "/roleMapping", consumes = MediaType.APPLICATION_JSON_VALUE)
    public AccessLevel createAccessLevelRolesMapping(@RequestBody AccessLevel accessLevel) {
        return pmsConfigService.createOrUpdateAccessLevelRolesMapping(accessLevel);
    }

    /**
     * Update access level roles mapping access level.
     *
     * @param mappingName the mapping name
     * @param accessLevel the access level
     * @return the access level
     */
    @PutMapping(value = "/roleMapping/{mappingName}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public AccessLevel updateAccessLevelRolesMapping(@PathVariable String mappingName, @RequestBody AccessLevel accessLevel) {
        if (!mappingName.equals(accessLevel.getID())) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CONFIG_NAMES_IN_PATH_AND_BODY_MUST_BE_EQUAL, HttpStatus.BAD_REQUEST.value(),
                    String.format("Path name: %s, Body name: %s", mappingName, accessLevel.getID()));
        }
        return pmsConfigService.createOrUpdateAccessLevelRolesMapping(accessLevel);
    }

    /**
     * Gets access level role mapping.
     *
     * @param mappingName the mapping name
     * @return the access level role mapping
     */
    @GetMapping(value = "/roleMapping/{mappingName}")
    public AccessLevel getAccessLevelRoleMapping(@PathVariable String mappingName) {
        return pmsConfigService.getAccessLevelRolesMapping(mappingName);
    }

    /**
     * Delete access level role mapping map.
     *
     * @param mappingName the mapping name
     * @return the map
     */
    @DeleteMapping(value = "/roleMapping/{mappingName}")
    public Map<String, String> deleteAccessLevelRoleMapping(@PathVariable String mappingName) {
        Map<String, String> statusMap = new HashMap<>();
        pmsConfigService.deleteAccessLevel(mappingName);
        statusMap.put(configName, mappingName);
        statusMap.put(status, deleted);
        return statusMap;
    }

    /**
     * Create industry ttl mapping industry ttl.
     *
     * @param industryTTL the industry ttl
     * @return the industry ttl
     */
    @PostMapping(value = "/ttlMapping", consumes = MediaType.APPLICATION_JSON_VALUE)
    public IndustryTTL createIndustryTTLMapping(@RequestBody IndustryTTL industryTTL) {
        return pmsConfigService.createIndustryTTL(industryTTL);
    }

    /**
     * Update industry ttl mapping industry ttl.
     *
     * @param mappingName the mapping name
     * @param industryTTL the industry ttl
     * @return the industry ttl
     */
    @PutMapping(value = "/ttlMapping/{mappingName}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public IndustryTTL updateIndustryTTLMapping(@PathVariable String mappingName, @RequestBody IndustryTTL industryTTL) {
        if (!mappingName.equals(industryTTL.getID())) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CONFIG_NAMES_IN_PATH_AND_BODY_MUST_BE_EQUAL, HttpStatus.BAD_REQUEST.value(),
                    String.format("Path name: %s, Body name: %s", mappingName, industryTTL.getID()));
        }
        return pmsConfigService.updateIndustryTTL(industryTTL);
    }

    /**
     * Gets industry ttl mapping.
     *
     * @param mappingName the mapping name
     * @return the industry ttl mapping
     */
    @GetMapping(value = "/ttlMapping/{mappingName}")
    public IndustryTTL getIndustryTTLMapping(@PathVariable String mappingName) {
        return pmsConfigService.getIndustryTTL(mappingName);
    }

    /**
     * Delete industry ttl mapping map.
     *
     * @param mappingName the mapping name
     * @return the map
     */
    @DeleteMapping(value = "/ttlMapping/{mappingName}")
    public Map<String, String> deleteIndustryTTLMapping(@PathVariable String mappingName) {
        Map<String, String> statusMap = new HashMap<>();
        pmsConfigService.deleteIndustryTTL(mappingName);
        statusMap.put(configName, mappingName);
        statusMap.put(status, deleted);
        return statusMap;
    }


    /**
     * Create cleanse regions mapping cleanse regions mapping.
     *
     * @param cleanseRegionsMapping the cleanse regions mapping
     * @return the cleanse regions mapping
     */
    @PostMapping(value = "/cleanseRegionsMapping", consumes = MediaType.APPLICATION_JSON_VALUE)
    public CleanseRegionsMapping createCleanseRegionsMapping(@RequestBody CleanseRegionsMapping cleanseRegionsMapping) {
        return pmsConfigService.createCleanseRegions(cleanseRegionsMapping);
    }

    /**
     * Update cleanse regions mapping cleanse regions mapping.
     *
     * @param mappingName           the mapping name
     * @param cleanseRegionsMapping the cleanse regions mapping
     * @return the cleanse regions mapping
     */
    @PutMapping(value = "/cleanseRegionsMapping/{mappingName}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public CleanseRegionsMapping updateCleanseRegionsMapping(@PathVariable String mappingName, @RequestBody CleanseRegionsMapping cleanseRegionsMapping) {
        if (cleanseRegionsMapping.getID().equals(mappingName)) {
            return pmsConfigService.updateCleanseRegions(cleanseRegionsMapping);
        } else {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), "Configname is not matching");
        }
    }

    /**
     * Gets cleanse regions mapping.
     *
     * @param mappingName the mapping name
     * @return the cleanse regions mapping
     */
    @GetMapping(value = "/cleanseRegionsMapping/{mappingName}")
    public CleanseRegionsMapping getCleanseRegionsMapping(@PathVariable String mappingName) {
        return pmsConfigService.getCleanseRegions(mappingName);

    }

    /**
     * Delete cleanse regions mapping map.
     *
     * @param mappingName the mapping name
     * @return the map
     */
    @DeleteMapping(value = "/cleanseRegionsMapping/{mappingName}")
    public Map<String, String> deleteCleanseRegionsMapping(@PathVariable String mappingName) {
        Map<String, String> statusMap = new HashMap<>();
        pmsConfigService.deleteCleanseRegions(mappingName);
        statusMap.put(configName, mappingName);
        statusMap.put(status, deleted);
        return statusMap;
    }

    /**
     * Update gcp to aws mapping map.
     *
     * @param mapping the mapping
     * @return the map
     */
    @PutMapping(value = GCP_TO_AWS_MAPPING, consumes = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, String> updateGcpToAwsMapping(@RequestBody Map<String, String> mapping) {
        return pmsConfigService.updateGcpToAwsMapping(mapping);
    }

    /**
     * Gets gcp to aws mapping.
     *
     * @return the gcp to aws mapping
     */
    @GetMapping(value = GCP_TO_AWS_MAPPING)
    public Map<String, String> getGcpToAwsMapping() {
        return pmsConfigService.getGcpToAwsMapping();

    }

    /**
     * Update aws to gcp mapping map.
     *
     * @param mapping the mapping
     * @return the map
     */
    @PutMapping(value = AWS_TO_GCP_MAPPING, consumes = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, String> updateAwsToGcpMapping(@RequestBody Map<String, String> mapping) {
        return pmsConfigService.updateAwsToGcpMapping(mapping);
    }

    /**
     * Gets aws to gcp mapping.
     *
     * @return the aws to gcp mapping
     */
    @GetMapping(value = AWS_TO_GCP_MAPPING)
    public Map<String, String> getAwsToGcpMapping() {
        return pmsConfigService.getAwsToGcpMapping();

    }

    /**
     * Update azure to aws mapping map.
     *
     * @param mapping the mapping
     * @return the map
     */
    @PutMapping(value = AZURE_TO_AWS_MAPPING, consumes = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, String> updateAzureToAwsMapping(@RequestBody Map<String, String> mapping) {
        return pmsConfigService.updateAzureToAwsMapping(mapping);
    }

    /**
     * Gets azure to aws mapping.
     *
     * @return the azure to aws mapping
     */
    @GetMapping(value = AZURE_TO_AWS_MAPPING)
    public Map<String, String> getAzureToAwsMapping() {
        return pmsConfigService.getAzureToAwsMapping();

    }

    /**
     * Update azure to gcp mapping map.
     *
     * @param mapping the mapping
     * @return the map
     */
    @PutMapping(value = AZURE_TO_GCP_MAPPING, consumes = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, String> updateAzureToGcpMapping(@RequestBody Map<String, String> mapping) {
        return pmsConfigService.updateAzureToGcpMapping(mapping);
    }

    /**
     * Gets azure to gcp mapping.
     *
     * @return the azure to gcp mapping
     */
    @GetMapping(value = AZURE_TO_GCP_MAPPING)
    public Map<String, String> getAzureToGcpMapping() {
        return pmsConfigService.getAzureToGcpMapping();

    }


    /**
     * Update gcp to azure mapping map.
     *
     * @param mapping the mapping
     * @return the map
     */
    @PutMapping(value = GCP_TO_AZURE_MAPPING, consumes = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, String> updateGcpTOAzureMapping(@RequestBody Map<String, String> mapping) {
        return pmsConfigService.updateGcpToAzureMapping(mapping);
    }

    /**
     * Gets gcp to azure mapping.
     *
     * @return the gcp to azure mapping
     */
    @GetMapping(value = GCP_TO_AZURE_MAPPING)
    public Map<String, String> getGcpToAzureMapping() {
        return pmsConfigService.getGcpToAzureMapping();

    }

    /**
     * Update aws to azure mapping map.
     *
     * @param mapping the mapping
     * @return the map
     */
    @PutMapping(value = AWS_TO_AZURE_MAPPING, consumes = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, String> updateAwsToAzureMapping(@RequestBody Map<String, String> mapping) {
        return pmsConfigService.updateAwsToAzureMapping(mapping);
    }

    /**
     * Gets aws to azure mapping.
     *
     * @return the aws to azure mapping
     */
    @GetMapping(value = AWS_TO_AZURE_MAPPING)
    public Map<String, String> getAwsToAzureMapping() {
        return pmsConfigService.getAwsToAzureMapping();

    }

    /**
     * Gets all sales force product codes.
     *
     * @return the all sales force product codes
     */
    @GetMapping(value = "/salesforce/productCodes")
    public SalesForceProductCodes getAllSalesForceProductCodes() {
        return pmsConfigService.getSaleForceProductCodes();
    }

    /**
     * Gets sale force product codes.
     *
     * @param <T>         the type parameter
     * @param productName the product name
     * @return the sale force product codes
     */
    @GetMapping(value = "/salesforce/productCodes/{productName}")
    public <T> T getSaleForceProductCodes(@PathVariable SalesForceProductName productName) {
        return pmsConfigService.getSaleForceProductCodes(productName);
    }

    /**
     * Update sales force product codes sales force product codes.
     *
     * @param productName  the product name
     * @param productCodes the product codes
     * @return the sales force product codes
     */
    @PutMapping(value = "/salesforce/productCodes/{productName}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public SalesForceProductCodes updateSalesForceProductCodes(@PathVariable SalesForceProductName productName, @RequestBody SalesForceProductCodes productCodes) {
        return pmsConfigService.updateSalesForceProductCodes(productName, productCodes);
    }

    /**
     * Create product codes config sales force product codes.
     *
     * @param productCodes the product codes
     * @return the sales force product codes
     */
    @PostMapping(value = "/salesforce/productCodes", consumes = MediaType.APPLICATION_JSON_VALUE)
    public SalesForceProductCodes createProductCodesConfig(@RequestBody SalesForceProductCodes productCodes) {
        return pmsConfigService.createSalesForceProductCodes(productCodes);
    }

    /**
     * Gets all product codes config by package type.
     *
     * @return the all product codes config by package type
     */
    @GetMapping(value = "/packageType/productCodes/config")
    public ReltioPackageTypeProductCodes getAllProductCodesConfigByPackageType() {
        return pmsConfigService.getAllProductCodesConfigByPackageType();
    }

    /**
     * Gets product codes config by package type.
     *
     * @param packageType the package type
     * @return the product codes config by package type
     */
    @GetMapping(value = "/packageType/productCodes/config/{packageType}")
    public List<Map<String, PMSProductName>> getProductCodesConfigByPackageType(@PathVariable ReltioPackageType packageType) {
        return pmsConfigService.getProductCodesConfigByPackageType(packageType);
    }

    /**
     * Update product codes config by package type reltio package type product codes.
     *
     * @param packageType  the package type
     * @param productCodes the product codes
     * @return the reltio package type product codes
     */
    @PutMapping(value = "/packageType/productCodes/config/{packageType}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ReltioPackageTypeProductCodes updateProductCodesConfigByPackageType(@PathVariable ReltioPackageType packageType, @RequestBody ReltioPackageTypeProductCodes productCodes) {
        return pmsConfigService.updateProductCodesConfigByPackageType(packageType, productCodes);
    }


    /**
     * Create product codes by package type reltio package type product codes.
     *
     * @param packageTypeProductCodes the package type product codes
     * @return the reltio package type product codes
     */
    @PostMapping(value = "/packageType/productCodes/config", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ReltioPackageTypeProductCodes createProductCodesByPackageType(@RequestBody ReltioPackageTypeProductCodes packageTypeProductCodes) {
        return pmsConfigService.createPackageTypeProductCodesConfig(packageTypeProductCodes);
    }

    /**
     * Gets customer type config
     *
     * @return the customer type config
     */
    @GetMapping(value = "/customerTypeConfig")
    public Map<CustomerType, CustomerTypeConfigEntry>  getTenantProvisioningCustomerConfig() {
        return pmsConfigService.getTenantProvisioningCustomerConfig().getCustomerTypeConfigEntryMap();
    }

    /**
     * Update customer type config
     *
     * @param customerTypeConfig customer type config
     * @return the customer type config
     */
    @PutMapping(value = "/customerTypeConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Map<CustomerType, CustomerTypeConfigEntry> updateTenantProvisioningCustomerConfig(@RequestBody Map<CustomerType, CustomerTypeConfigEntry> customerTypeConfig) {
        return pmsConfigService.updateCustomerTypeConfig(customerTypeConfig);
    }

    /**
     * Gets cloud config
     *
     * @return the cloud config
     */
    @GetMapping(value = "/cloudConfig")
    public Map<CloudProvider, CloudConfigEntry> getTenantProvisioningCloudConfig() {
        return pmsConfigService.getTenantProvisioningCloudConfig().getCloudConfigEntryMap();
    }

    /**
     * Update cloud config
     *
     * @param cloudConfig cloud config
     * @return the cloud config
     */
    @PutMapping(value = "/cloudConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Map<CloudProvider, CloudConfigEntry> updateTenantProvisioningCloudConfig(@RequestBody Map<CloudProvider, CloudConfigEntry> cloudConfig) {
        return pmsConfigService.updateCloudConfig(cloudConfig);
    }

    /**
     * Gets env config
     *
     * @return the env config
     */
    @GetMapping(value = "/envConfig")
    public Map<String, EnvConfigEntry> getTenantProvisioningEnvConfig() {
        return pmsConfigService.getTenantProvisioningEnvConfig().getEnvConfigEntryMap();
    }

    /**
     * Update cloud config
     *
     * @param envConfig cloud config
     * @return the env config
     */
    @PutMapping(value = "/envConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, EnvConfigEntry> updateTenantProvisioningEnvConfig(@RequestBody Map<String, EnvConfigEntry> envConfig) {
        return pmsConfigService.updateEnvConfig(envConfig);
    }

    /**
     * Gets all repository configuration.
     *
     * @return the all repository configuration
     * @throws InvalidDocumentIdException the invalid document id exception
     */
    @GetMapping(value = "/repoDetails")
    public RepositoryConfig getAllRepositoryConfiguration() throws InvalidDocumentIdException {
        return pmsConfigService.getRepoConfigDetails();
    }

    /**
     * Create repo configuration repository config.
     *
     * @param repositoryConfig the repository config
     * @return the repository config
     */
    @PostMapping(value = "/repoDetails", consumes = MediaType.APPLICATION_JSON_VALUE)
    public RepositoryConfig createRepoConfiguration(@RequestBody RepositoryConfig repositoryConfig) {
        return pmsConfigService.createRepoConfiguration(repositoryConfig);
    }

    /**
     * Update RIH products to recipe file paths mapping.
     *
     * @param mapping the mapping
     * @return the map
     */
    @PutMapping(value = RIH_MAPPING, consumes = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, RIHPaths> updateRIHMapping(@RequestBody Map<String, RIHPaths> mapping) {
        return pmsConfigService.updateRIHMapping(mapping);
    }

    /**
     * Gets RIH products to recipe file paths mapping.
     *
     * @return the RIH products to recipe file paths mapping.
     */
    @GetMapping(value = RIH_MAPPING)
    public Map<String, RIHPaths> getRIHMapping() {
        return pmsConfigService.getRIHMapping();

    }
}
