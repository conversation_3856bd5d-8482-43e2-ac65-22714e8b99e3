package com.reltio.services.pms.common.sales.model.addon;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@Getter
@Setter
public class AzureBlobStorageProductConfig extends BaseProductConfig {

    @JsonProperty(value = "resourceGroupName")
    private String resourceGroupName;

    @JsonProperty(value = "storageAccountName")
    private String storageAccountName;

    public AzureBlobStorageProductConfig() {
        this.pmsProductName = PMSProductName.AZURE_BLOB_STORAGE;
    }
}
