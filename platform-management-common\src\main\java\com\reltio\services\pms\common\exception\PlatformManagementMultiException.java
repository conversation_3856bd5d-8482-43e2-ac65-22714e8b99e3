package com.reltio.services.pms.common.exception;

import java.util.ArrayList;
import java.util.Collection;

@SuppressWarnings("serial")
public class PlatformManagementMultiException extends PlatformManagementException {

    private final Collection<PlatformManagementException> exceptions;

    public PlatformManagementMultiException(PlatformManagementErrorCode code, int httpStatus, Collection<PlatformManagementException> exceptions) {
        super(code, httpStatus);
        this.exceptions = new ArrayList<>(exceptions);
    }

    public PlatformManagementMultiException(PlatformManagementErrorCode code, int httpStatus, String[] messageParameters, String[] detailMessageParameters, Collection<PlatformManagementException> exceptions) {
        super(code, httpStatus, messageParameters, detailMessageParameters, null);
        this.exceptions = new ArrayList<>(exceptions);
    }

    public Collection<PlatformManagementException> getExceptions() {
        return new ArrayList<>(exceptions);
    }
}