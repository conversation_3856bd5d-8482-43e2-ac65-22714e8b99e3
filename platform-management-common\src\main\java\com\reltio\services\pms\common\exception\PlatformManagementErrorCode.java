package com.reltio.services.pms.common.exception;

/**
 * Created by ${user.name} on 2017/12/14 08:56:40.
 */
public enum PlatformManagementErrorCode {
    INTERNAL_ERROR(1),
    GENERIC_ERROR(2),
    BAD_REQUEST(3),
    PRODUCT_EDITION_NOT_FOUND(4),
    PRODUCT_EDITION_ALREADY_EXIST(5),
    PRODUCT_EDITION_L3_DOES_NOT_EXIST(6),
    PRODUCT_EDITION_L3_NOT_VALID_JSON(7),
    PRODUCT_EDITION_LOOKUP_FILE_DOES_NOT_EXIST(8),
    PRODUCT_EDITION_LOOKUPS_NOT_VALID_JSON(9),
    PRODUCT_EDITION_SAMPLE_DATA_FILE_DOES_NOT_EXIST(10),
    PRODUCT_EDITION_SAMPLE_DATA_FILE_NOT_VALID_JSON(11),
    PRODUCT_EDITION_INVALID(12),
    ENVIRONMENT_DOES_NOT_EXIST_IN_REPOSITORY(13),
    ENVIRONMENT_IS_NOT_REGISTERED(14),
    RESOURCE_FILE_IS_NOT_AVAILABLE(15),
    CLUSTER_NOT_FOUND(16),
    PROPERTY_IS_NOT_SEARCH_SUPPORTED(17),
    ENVIRONMENT_NAMES_NOT_EQUAL(18),
    TEMPLATE_NAMES_NOT_EQUAL(19),
    DATA_CLUSTER_NOT_FOUND(20),
    ES_CLUSTER_NOT_FOUND(21),
    DATALOAD_CLUSTER_NOT_FOUND(22),
    STORAGE_TEMPLATE_NOT_FOUND(23),
    TENANT_STORAGE_CONFIG_NOT_FOUND(24),
    REQUESTER_DOES_NOT_EXIST(26),
    EMAIL_IS_A_MANDATORY_FILED(27),
    NONE_OR_BOTH_PARAMETERS_MUST_BE_SPECIFIED(28),
    PIPELINE_NOT_FOUND(29),
    PIPELINE_ALREADY_EXIST(30),
    NO_TASK_DETAIL_PROVIDER_FOR_TASK_TYPE(31),
    JOB_NOT_FOUND(32),
    UNKNOWN_EMAIL_CONTENT_TYPE(33),
    THE_TASK_IS_ALREADY_RESOLVED(34),
    TEMPLATE_EMAIL_IS_UNAVAILABLE(35),
    RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED(36),
    ROLE_IS_NOT_VALID(37),
    THE_CLIENT_ALREADY_EXISTS(38),
    ENVIRONMENT_DOES_NOT_CORRESPOND_TO_TENANTID(39),
    PRODUCT_EDITION_UI_CONFIG_FOLDER_NOT_EXIST(40),
    PRODUCT_EDITION_UI_CONFIG_FILE_NOT_VALID_JSON(41),
    INTERNAL_DB_ERROR(42),
    TASK_NOT_FOUND(43),
    UI_CONFIG_UPDATE_EXCEPTION(44),
    TERMS_FILE_UNAVAILABLE(45),
    PIPELINE_VALIDATION_ERROR(46),
    REQUESTER_ALREADY_HAVE_RELTIO_ACCOUNT(47),
    UNABLE_TO_PARSE_PHYSICAL_CONFIGURATION_TEMPLATE(48),
    UNABLE_TO_PREPARE_TENANT_URL(49),
    NO_JOB_EXISTS_FOR_THE_REQUESTER(50),
    THE_JOB_IS_COMPLETED(51),
    EMAIL_IS_NOT_VERIFIED_YET(52),
    PROVISION_IS_IN_PROGRESS(53),
    EMAIL_WAS_NOT_VERIFIED(54),
    THE_JOB_HAS_NOT_SUCCEED(55),
    THE_TENANT_HAS_NO_CUSTOMER_ASSIGNED(56),
    THE_TENANT_BELONGS_TO_SEVERAL_CUSTOMERS(57),
    NO_CLIENT_EXISTS_FOR_THE_CUSTOMER(58),
    CUSTOMER_NOT_FOUND(59),
    USER_NOT_FOUND(60),
    ROLES_NOT_CONFIGURED(61),
    CONFIG_IS_NOT_AVAILABLE(62),
    FILTER_AND_ORDER_FIELDS_MUST_BE_DIFFERENT(63),
    TENANT_IS_NOT_PRESENT_IN_DATABASE(64),
    STRIPE_EXCEPTION(65),
    TIER_ALREADY_EXISTS(66),
    TIER_NOT_FOUND(67),
    PRICE_IS_IN_USE(68),
    PRODUCT_EDITION_PERMISSIONS_DOES_NOT_EXIST(69),
    PRODUCT_EDITION_PERMISSIONS_NOT_VALID_JSON(70),
    NO_SUCH_ACCESS_LEVEL(71),
    OFFERTYPE_IS_REQUIRED(72),
    TASK_IS_NOT_IN_RUNNABLE_STATE(74),
    NO_NEED_IN_UPDATE(75),
    SUITABLE_REPLACEMENT_NOT_FOUND(76),
    PRODUCT_EDITION_RDM_LOOKUP_FILE_DOES_NOT_EXIST(77),
    PRODUCT_EDITION_RDM_LOOKUPS_NOT_VALID_JSON(78),
    WRONG_FORMAT_TTL(79),
    UNABLE_TO_CREATE_SERVICE_ACCOUNT(80),
    UNABLE_TO_CREATE_SERVICE_ACCOUNT_KEY(81),
    UNABLE_TO_GET_SERVICE_ACCOUNT(82),
    UNABLE_TO_MANIPULATE_WITH_SERVICE_ACCOUNT_KEY(83),
    UNABLE_TO_DELETE_SERVICE_ACCOUNT(84),
    TTL_MAPPING_ALREADY_EXISTS(85),
    TTL_MAPPING_DOES_NOT_EXIST(86),
    MATCH_CLUSTER_NOT_FOUND(87),
    FREE_TRIER_JOB_ALREADY_CREATED(88),
    RDM_LOOKUP_PATH_NOT_CONFIGURED_IN_PRODUCT_EDITION(89),
    CLEANSE_REGION_MAPPING_ALREADY_EXISTS(90),
    CLEANSE_REGION_MAPPING_DOES_NOT_EXIST(91),
    NO_LOOKUP_FOR_CONFIGURED_FOR_PRODUCT_EDITION(92),
    REQUIRED_TASK_PARAMETER_IS_NULL(93),
    JOB_STATUS_CANNOT_BE_CHANGED(94),
    PIPELINE_WITHOUT_TASKS(95),
    RDM_TASK_CONFIG_NOT_FOUND_IN_PIPELINE(96),
    UNKNOWN_TASK_PARAMETER(97),
    CONFIG_NAMES_IN_PATH_AND_BODY_MUST_BE_EQUAL(98),
    MDM_TASK_CONFIG_NOT_FOUND_IN_PIPELINE(99),
    PRODUCT_EDITION_IS_NOT_THERE_IN_PIPELINE_AND_TASK_PARAM(100),
    ENVIRONMENT_DOES_NOT_SUPPORT_TENANT_SIZE(101),
    EMAIL_IN_PATH_AND_BODY_MUST_BE_EQUAL(102),
    TTL_MAPPING_NAME_IS_MANDATORY_IN_PROD_PIPELINE(103),
    TTL_MAPPING_DOES_NOT_CONTAIN_TTL_FOR_INDUSTRY(104),
    DEFAULT_TTL_IS_NOT_CONFIGURED_FOR_NON_PROD_ENV(105),
    NEED_AT_LEAST_ONE_OWNER_FOR_TENANT(106),
    TENANTS_NOT_FROM_SAME_CUSTOMER(107),
    DUPLICATE_TASK_TYPE(108),
    TASK_NOT_SUPPORTED_IN_PIPELINE_AT_ENDPOINT(109),
    INVALID_STREAMING_CLOUD(110),
    GCP_REGION_MAPPING_ABSENT(110),
    WRONG_PIPELINE_TASK_TYPE(111),
    PROCESS_PARAMETER_IS_NOT_VALID(112),
    TENANT_PURPOSE_REQUIRED_FOR_ENABLEMENT_OF_NON_EXISTING_TENANT(113),
    MDM_TENANTS_NOT_FOUND_IN_JOB_REQUEST(114),
    RDM_TENANT_NOT_FOUND_IN_JOB_REQUEST(115),
    GENERATOR_CONFIG_DOES_NOT_EXSIST(116),
    MULTIPLE_AUTH_USER_EXIST_REQUESTER(117),
    MULTIPLE_AUTH_USER_EXIST_OWNER(118),
    TENANT_EXIST_WITH_GIVEN_ID(119),
    CUSTOMER_ID_IS_NOT_VALID(120),
    CUSTOMER_ID_LENGTH_IS_NOT_IN_RANGE(121),
    DTSS_CONFIG_DOES_NOT_EXSIST(122),
    AZURE_REGION_MAPPING_ABSENT(123),
    TENANTS_NOT_FROM_SAME_CUSTOMER_DURING_SHILED_ENABLMENT(124),
    CONTRACT_NOT_FOUND(125),
    INCORRECT_FORMAT_FOUND(126),
    DATE_CANNOT_BE_PARSED(128),
    BUSINESS_CUSTOMER_NOT_FOUND(129),
    CANT_DELETE_GBT_TABLE(130),
    RETRY_EXCEPTION(133),
    UNABLE_TO_GET_JOB_DETAILS(134),
    CONTRACT_HAS_CRITICAL_ERRORS(135),
    PROFILE_NOT_FOUND_FOR_TENANT(137),
    TENANT_NOT_SUBSCRIBED(138),
    INCORRECT_TENANT_TYPE(139),
    CONTRACT_WITH_THIS_ACCOUNTID_NOT_FOUND(140),
    CONTRACT_WITH_THIS_CUSTOMERID_NOT_FOUND(141),
    CONTRACT_WITH_THIS_CUSTOMERNAME_NOT_FOUND(142),
    CANNOT_PARSE_PHYSICAL_CONFIGURATION(143),
    PRODUCT_EDITION_DVF_NOT_VALID_JSON(144),
    PRODUCT_EDITION_DVF_NOT_EXIST(145),
    DUPLICATE_DTSS_SUBSCRIPTION_TYPE_OR_DATA_TENANT(146),
    CLEAN_TENANT_TASK_CONFIG_NOT_FOUND_IN_PIPELINE(147),
    CLEANABILITY_NOT_ENABLED(148),
    TENANT_NOT_ISOLATED(149),
    BUSINESS_CUSTOMER_WITH_CUSTOMERID_NOT_FOUND(150),
    TENANT_NOT_FOUND_IN_PLATFORM(151),
    CLEAN_UP_TASK_FAILED(152),
    PROD_TENANT_DATA_DELETION_NOT_SUPPORTED(153),
    TENANT_SYNC_REQUIRED(154),
    DNB_MAPPING_DOES_NOT_EXIST(155),
    BOTH_RDMTENANTTOREUSE_AND_RDMTENANTID_CANNOT_BE_USED(156),
    SUBSCRIPTIONID_IS_WRONG(157),
    CONTRACT_OR_SUBSCRIPTION_NOT_FOUND(158),
    NO_SUBSCRIPTION_NUM(159),
    NO_SUBSCRIPTION_ID(160),
    NO_JOB_FOUND_FOR_THIS_TENANTID(161),
    ZENDESK_TICKET_NOT_FOUND(162),
    TENANT_FOR_ZENDESK_NOT_FOUND(163),
    USER_FOR_ZENDESK_NOT_FOUND(164),
    THERE_IS_NO_CLUSTERS_SATISFYING_STORAGE_PRIORITY_LIST(165),
    SERVICE_URL_IS_NOT_AVAILABLE(166),
    INCORRECT_GCP_PROJECT_NAME(167),
    DATA_TENANT_IS_NOT_CORRECT(168),
    ZENDESK_TENANT_IS_HIPAA(169),
    USER_NOT_RELTIO(170),
    EMAIL_NOT_VALID(171),
    NOT_A_VALID_STORAGE(172),
    INVALID_ZENDESK_TICKET_STATUS(173),
    USER_DOES_NOT_HAVE_ROLE_TO_REMOVE(174),
    UI_CONFIG_PATH_DOES_NOT_EXSIST(175),
    NO_REQUESTS_FOR_ZENDESK_ID_TO_REVOKE(176),
    NO_OKTA_USER_PROFILE(177),
    GBQ_CONFIG_IS_NOT_DEFINED_FOR_TENANT(178),
    SUPPORTABILITY_DB_ERROR(179),
    PRODUCT_EDITION_BRANCH_DOES_NOT_EXIST(180),
    CONTRACT_NOT_IN_EXPIRED_STATE(181),
    COMPLIANCE_DB_ERROR(182),
    CONFIG_IS_NOT_AVAILABLE_IN_CONFIG_SERVICE(183),
    NOT_ABLE_TO_UPDATE_CONFIG_SERVICE(184),
    INCORRECT_TENANT_STATE(185),
    CANNOT_CREATE_JOB_EARLY(186),
    CAN_NOT_CREATE_NEW_TENANT_IN_CONTRACT_STAGE_CHANGE(187),
    NOT_ABLE_TO_FIND_ENVIRONMENT(188),
    PRODUCT_EDITION_NOT_FOUND_IN_CONTRACT(189),
    DOC_ID_IS_EMPTY(190),
    CUSTOMER_CONTACT_IS_NOT_PRESENT_IN_SALESFORCE_CONTRACT(192),
    DNB_MAPPING_OR_DTS_CONFIG_NOT_AVAILABLE_FOR_PRODUCT_EDITION(193),
    INVALID_AZURE_CLOUD(194),
    SERVICE_NODE_NOT_FOUND(195),
    RESPONSE_FROM_NOTIFICATION_SERVICE_CANNOT_BE_PROCESSED(196),
    ERROR_GETTING_PROFILES_FOR_THIS_TENANT(197),
    TENANT_IS_NOT_REGISTERED_SALESFORCE(198),
    ERROR_WHILE_DE_REGISTERING_TENANT_FROM_DNB(199),
    TRAINING_TENANT_ALREADY_EXISTS_FOR_USER(200),
    DELETE_MDM_TASK_FAILED(201),
    SALES_PACKAGE_MAPPING_NOT_EXIST(202),
    RELTIO_PACKAGE_TYPE_PRODUCT_CODE_CONFIG_NOT_EXIST(203),
    PACKAGE_TYPE_PRODUCT_CODE_NOT_VALID(204),
    NO_SUCH_PRODUCT_EDITION_EXISTS_IN_PMS(205),
    NEED_BOTH_SUBJECT_AND_BODY(206),
    DUPLICATE_PROVISIONING_REQUEST(207),
    ERROR_WHILE_LOCKING(208),
    FAILED_TO_ACQUIRE_LOCK(209),
    TENANT_PROVISIONING_CUSTOMER_TYPE_CONFIG_NOT_FOUND(210),
    TENANT_PROVISIONING_CLOUD_CONFIG_NOT_FOUND(211),
    SECONDARY_NAME_DOES_NOT_EXIST(212),
    UNABLE_TO_FETCH_RESPONSE(213),
    MULTIPLE_OWNERS_NOT_ALLOWED_FOR_TRAINING_TENANT(214),
    NO_JOB_EXISTS_FOR_PARENT_JOB_ID_IN_PMS(215),
    NO_JOB_HAS_DRAFT_STATUS_FOR_PARENT_JOB_ID(216),
    JOB_NOT_IN_DRAFT_STATE(217),
    CANNOT_EXCEED_LENGTH(218),
    JOB_CREATION_FAILED(219),
    UNABLE_TO_MAP_PHYSICAL_CONFIGURATION_PARAMETERS(220),
    INCORRECT_PATH(221),
    UPDATED_PARAMS_INPUT_MAP_IS_EMPTY_OR_NULL(222),
    PARAM_TO_BE_UPDATED_IS_NOT_PRESENT_IN_PMS_CONFIG(223),
    CANNOT_UPDATE_A_READ_ONLY_PARAM(224),
    PHYSICAL_CONFIG_KEY_ABSENT_IN_PMS_CONFIG(225),
    NOT_AN_ALLOWED_VALUE(226),
    NO_UNIQUE_IDENTIFIER(227),
    NO_UPDATE_VERB_PRESENT_IN_PMS_CONFIG(228),
    NO_PARENT_NODE_PRESENT_IN_PHYSICAL_CONFIG(229),
    NO_MATCHING_ARRAY_ELEMENT_FOUND(230),
    TENANT_PROVISIONING_ENV_CONFIG_NOT_FOUND(231),
    MULTIPLE_TENANT_IDS_ARE_NOT_ALLOWED_WITH_CUSTOM_QUEUE_NAMES(232),
    NO_MATCHING_STREAMING_CONFIG_FOUND_FOR_CLOUD_PROVIDER(233),
    STREAMING_QUEUES_ARE_NOT_ENABLED_FOR_THE_TENANT(234),
    DELETE_TENANT_FROM_SYSTEM_KEYSPACE_FAILED(235),
    MULTIPLE_OWNERS_ARE_NOT_ALLOWED_IN_TRAINING_CUSTOMER_TYPE(236),
    INVALID_FILTER_COMBINATION(237),
    INVALID_DATE_FORMAT(238),
    ERROR_WHILE_DELETING_AZURE_QUEUE_RESOURCES(239),
    DE_PROVISION_JOB_ALREADY_EXISTS(240),
    UI_CONFIG_GET_EXCEPTION(241),
    CANNOT_CREATE_DATA_CLONE_JOB_WITH_SPECIFIED_STORAGES(242),
    CANNOT_CREATE_DATA_CLONE_JOB_WITH_DIFFERENT_PHYSICAL_CONFIGURATION_PARAMETERS(243),
    DVF_NOT_VALID_JSON(244),
    INCORRECT_ZENDESK_URL(245),
    CANNOT_CREATE_DATA_BACKUP_JOB_WITH_SPECIFIED_STORAGES(246),
    CANNOT_CREATE_DATA_BACKUP_JOB_WITH_DIFFERENT_PHYSICAL_CONFIGURATION_PARAMETERS(247),
    CANNOT_CREATE_DATA_RESTORE_JOB_WITH_INCORRECT_TENANT_STATUS(248),
    CANNOT_CREATE_DATA_RESTORE_JOB_WHEN_TENANT_HAS_RUNNING_TASKS(246),
    CANNOT_CREATE_BACKUP_JOB_WITH_MISSING_PARAMS(249),
    DATA_BACKUP_JOB_ALREADY_EXISTS_WITH_GIVEN_SOURCE_TENANT_ID_AND_TICKET_ID(250),
    DATA_RESTORE_JOB_ALREADY_EXISTS_WITH_GIVEN_SOURCE_TENANT_ID_AND_TICKET_ID(251),
    QUEUE_ALREADY_EXIST(252),
    RETENTION_PERIOD_VALUE_IS_NOT_VALID(253),
    HISTORY_BACKUP_JOB_ALREADY_EXISTS_WITH_GIVEN_SOURCE_TENANT_ID_AND_TICKET_ID(254),
    HISTORY_RESTORE_JOB_ALREADY_EXISTS_WITH_GIVEN_SOURCE_TENANT_ID_AND_TICKET_ID(255),
    ERROR_WHILE_CREATING_GBT_BACKUP(256),
    ERROR_WHILE_RESTORING_HISTORY_FROM_GBT_BACKUP(257),
    HISTORY_STORAGE_SPECIFIED_FOR_THE_TENANT_IS_NOT_GBT(258);


    private final int code;

    PlatformManagementErrorCode(int code) {
        this.code = code;
    }

    public int code() {
        return code;
    }
}
