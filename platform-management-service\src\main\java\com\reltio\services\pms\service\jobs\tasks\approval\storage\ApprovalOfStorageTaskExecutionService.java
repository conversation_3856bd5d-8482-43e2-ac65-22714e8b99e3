package com.reltio.services.pms.service.jobs.tasks.approval.storage;

import com.reltio.common.config.TenantConfiguration;
import com.reltio.services.pms.common.model.EmailContentType;
import com.reltio.services.pms.common.model.StorageTemplate;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.approve.ApprovalStatus;
import com.reltio.services.pms.common.model.jobs.tasks.approve.storage.ApprovalOfStorageTaskInstance;
import com.reltio.services.pms.common.model.proxy.ActionStatus;
import com.reltio.services.pms.common.model.proxy.ExecutionStatus;
import com.reltio.services.pms.common.model.proxy.approve.ApprovalSecureAction;
import com.reltio.services.pms.service.StorageTemplateService;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.AbstractTaskExecutionService;
import com.reltio.services.pms.service.jobs.tasks.provisioning.mdm.TenantConfigurationConstructor;
import com.reltio.services.pms.service.notification.EmailNotificationService;
import com.reltio.services.pms.service.proxy.SecureActionProxyService;

import java.util.HashMap;
import java.util.Map;

public class ApprovalOfStorageTaskExecutionService extends AbstractTaskExecutionService<ApprovalOfStorageTaskInstance> {

    private final EmailNotificationService emailNotificationService;
    private final SecureActionProxyService<ApprovalSecureAction> secureActionProxyService;
    private final StorageTemplateService storageTemplateService;
    private final TenantConfigurationConstructor tenantConfigurationConstructor;

    public ApprovalOfStorageTaskExecutionService(String envId, ApprovalOfStorageTaskInstance taskDetail,
                                                 GrafanaDashboardGBQService grafanaDashboardGBQService,
                                                 EmailNotificationService emailNotificationService,
                                                 SecureActionProxyService<ApprovalSecureAction> secureActionProxyService, StorageTemplateService storageTemplateService,
                                                 TenantConfigurationConstructor tenantConfigurationConstructor) {
        super(envId, taskDetail, grafanaDashboardGBQService);
        this.emailNotificationService = emailNotificationService;
        this.secureActionProxyService = secureActionProxyService;
        this.storageTemplateService = storageTemplateService;
        this.tenantConfigurationConstructor = tenantConfigurationConstructor;
    }

    @Override
    public void executeTask() throws Exception {
        if (taskDetail.getResolution() == null) {
            taskDetail.setResolution(ApprovalStatus.UNRESOLVED);

            ApprovalSecureAction approvalSecureAction = new ApprovalSecureAction(null, envId, taskDetail.getJobId(), taskDetail.getTaskId(), ActionStatus.OPEN, ExecutionStatus.OPEN, ApprovalStatus.UNRESOLVED);
            approvalSecureAction = secureActionProxyService.createAction(approvalSecureAction);
            taskDetail.setSecureActionId(approvalSecureAction.getID());
            Map<String, Object> requestParameters = new HashMap<>();
            boolean isDataInCassandra = true;
            boolean isMatchInCassandra = true;
            if (taskDetail.getDefaultStorageTemplate() != null) {
                StorageTemplate proposedTemplate = storageTemplateService.getStorageTemplate(taskDetail.getDefaultStorageTemplate(), taskDetail.getEnvId());
                requestParameters.put("matchCluster", proposedTemplate.getMatchCluster());
                requestParameters.put("matchHost", proposedTemplate.getMatchHost());
                requestParameters.put("dataCluster", proposedTemplate.getDataCluster());
                requestParameters.put("dataHost", proposedTemplate.getDataHost());
                requestParameters.put("esCluster", proposedTemplate.getEsCluster());
                requestParameters.put("esHost", proposedTemplate.getEsHost());
            } else {
                TenantConfiguration configuration = tenantConfigurationConstructor.generateTenantDataConfiguration(
                        taskDetail.getEnvId(), taskDetail.getCustomerName(), taskDetail.getTenantId(), null,
                        taskDetail.getTenantSize(), "P1Y", null, null, null, taskDetail.getStoragePriorityList(),
                        taskDetail.getLcaConfig(), taskDetail.getTenantPurpose(), Boolean.FALSE, null, Boolean.FALSE, null);
                isDataInCassandra = configuration.getDataStorageConfig().getDataKeyspaceConfig().getDataStorageId() == null;
                isMatchInCassandra = configuration.getDataStorageConfig().getMatchKeyspaceConfig().getDataStorageId() == null;
                String DYNAMO_STORAGE = "DYNAMO_DB";
                requestParameters.put("matchHost", !isMatchInCassandra ? DYNAMO_STORAGE : configuration.getDataStorageConfig().getMatchKeyspaceConfig().getHost());
                requestParameters.put("matchCluster", !isMatchInCassandra ? DYNAMO_STORAGE : configuration.getDataStorageConfig().getMatchKeyspaceConfig().getClusterName());
                requestParameters.put("dataHost", !isDataInCassandra ? DYNAMO_STORAGE : configuration.getDataStorageConfig().getDataKeyspaceConfig().getHost());
                requestParameters.put("dataCluster", !isDataInCassandra ? DYNAMO_STORAGE : configuration.getDataStorageConfig().getDataKeyspaceConfig().getClusterName());
                requestParameters.put("esHost", configuration.getSearchStorageConfiguration().getEsHosts() == null ? "" :
                        configuration.getSearchStorageConfiguration().getEsHosts());
                requestParameters.put("esCluster", configuration.getSearchStorageConfiguration().getEsClusterName() == null ? "" :
                        configuration.getSearchStorageConfiguration().getEsClusterName());
            }
            requestParameters.put("customer", taskDetail.getCustomerName());
            requestParameters.put("tenantSize", taskDetail.getTenantSize());
            requestParameters.put("environment", taskDetail.getEnvId());
            requestParameters.put("isDataInCassandra", isDataInCassandra);
            requestParameters.put("isMatchInCassandra", isMatchInCassandra);
            emailNotificationService.notify(EmailContentType.APPROVAL_STORAGE_REQUEST, approvalSecureAction.getSecureKey(), taskDetail.getApproverEmails(), requestParameters);
            taskDetail.setStatus(TaskStatus.WAITING);
        } else if (taskDetail.getResolution().equals(ApprovalStatus.APPROVED)) {
            taskDetail.setStatus(TaskStatus.COMPLETED);
        } else if (taskDetail.getResolution().equals(ApprovalStatus.MODIFIED)) {
            taskDetail.setStatus(TaskStatus.COMPLETED);
        } else if (taskDetail.getResolution().equals(ApprovalStatus.UNRESOLVED)) {
            taskDetail.setStatus(TaskStatus.WAITING);
        }
    }
}
