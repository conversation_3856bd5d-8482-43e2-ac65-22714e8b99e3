package com.reltio.services.pms.controller.api;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.model.supportability.Facet;
import com.reltio.services.pms.common.model.supportability.StatsDto;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * Supportability Resources
 * Created by apylkov
 */
@RequestMapping(value = "/api/v1/supportability/*", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name= "Supportability Metrics")
@ReltioSecured(resourceClass = Pms.Monitoring.class)
@Validated
public interface SupportabilityResources {

    /**
     * Get REST API metrics from GBQ using request params
     *
     * @param startTime     start time
     * @param endTime       end time
     * @param tenantId      tenant id to use
     * @param operationType service name to use (Config, Write, Read etc)
     * @param clusterType   cluster type to use (All, ui, dataload etc)
     * @return the list of DTO with metrics
     */
    @GetMapping("/rest/metrics")
    ResponseEntity<Map<String, List<StatsDto>>> getRestMetrics(@RequestParam Long startTime,
                                                               @RequestParam Long endTime,
                                                               @RequestParam String tenantId,
                                                               @RequestParam(required = false) String operationType,
                                                               @RequestParam(required = false) String clusterType,
                                                               @RequestParam(required = false) Facet facet);

    /**
     * Get Platform Tasks stats from GBQ using request params
     *
     * @param startTime start time
     * @param endTime   end time
     * @param tenantId  tenant id to use
     * @return the list of DTO with metrics
     */
    @GetMapping("/tasks/metrics")
    ResponseEntity<List<StatsDto>> getTasksMetrics(@RequestParam Long startTime,
                                                   @RequestParam Long endTime,
                                                   @RequestParam String tenantId,
                                                   @RequestParam(required = false) Facet facet);

    /**
     * Get Platform Event stats from GBQ using request params
     *
     * @param startTime    start time
     * @param endTime      end time
     * @param tenantId     tenant id to use
     * @param queueType    queue name to use (ALL, MATCH, CRUD etc)
     * @param consumerType consumer type to use (All, Dataprocess, Queueprocess, Api, dataload, grouping etc)
     * @return the list of DTO with metrics
     */
    @GetMapping("/events/metrics")
    ResponseEntity<Map<String, List<StatsDto>>> getEventsMetrics(@RequestParam Long startTime,
                                                                 @RequestParam Long endTime,
                                                                 @RequestParam String tenantId,
                                                                 @RequestParam(required = false) String queueType,
                                                                 @RequestParam(required = false) String consumerType,
                                                                 @RequestParam(required = false) Facet facet);

    /**
     * Get Platform Event stats from GBQ using request params
     *
     * @param startTime          start time
     * @param endTime            end time
     * @param tenantId           tenant id to use
     * @param queueType          queue name to use (ALL, MATCH, CRUD etc)
     * @param consumerType       consumer type to use (All, Dataprocess, Queueprocess, Api, dataload, grouping etc)
     * @param processorTypeGroup processor type group to use
     * @return the list of DTO with metrics
     */
    @GetMapping("/events/metrics/byProcessorTypeGroup")
    ResponseEntity<Map<String, List<StatsDto>>> getProcessorGroupEventsMetrics(@RequestParam Long startTime,
                                                                               @RequestParam Long endTime,
                                                                               @RequestParam String tenantId,
                                                                               @RequestParam String queueType,
                                                                               @RequestParam String consumerType,
                                                                               @RequestParam String processorTypeGroup,
                                                                               @RequestParam(required = false) Facet facet);

    /**
     * Get Cluster level stats from GBQ using request params
     *
     * @param startTime   start time
     * @param endTime     end time
     * @param tenantId    tenant id to use
     * @param clusterType cluster type to use (DATALOAD, UI, AUTH, GRAPH, DATAPROCESS, QUEUEPROCESS, GROUPING  etc)
     * @return the list of DTO with metrics
     */
    @GetMapping("/cluster/metrics")
    ResponseEntity<Map<String, List<StatsDto>>> getClusterMetrics(@RequestParam Long startTime,
                                                                  @RequestParam Long endTime,
                                                                  @RequestParam String tenantId,
                                                                  @RequestParam String clusterType,
                                                                  @RequestParam(required = false) Facet facet);

    /**
     * Get Table level stats from GBQ using request params
     *
     * @param startTime start time
     * @param endTime   end time
     * @param tenantId  tenant id to use
     * @param tableName table name to get stats for or null to get all tables stats
     * @return the list of DTO with metrics
     */
    @GetMapping("/tables/metrics")
    ResponseEntity<Map<String, List<StatsDto>>> getTablesMetrics(@RequestParam Long startTime,
                                                                 @RequestParam Long endTime,
                                                                 @RequestParam String tenantId,
                                                                 @RequestParam String tableName,
                                                                 @RequestParam(required = false) Facet facet);

    /**
     * Get Cluster level stats from GBQ using request params
     *
     * @param startTime     start time
     * @param endTime       end time
     * @param tenantId      tenant id to use
     * @param healthCheckId healthcheck id
     * @return the list of DTO with metrics
     */
    @GetMapping("/tenanthealth/metrics")
    ResponseEntity<Map<String, List<StatsDto>>> getTenantHealthMetrics(@RequestParam Long startTime,
                                                                       @RequestParam Long endTime,
                                                                       @RequestParam String tenantId,
                                                                       @RequestParam(required = false) String healthCheckId,
                                                                       @RequestParam(required = false) Facet facet);
}


