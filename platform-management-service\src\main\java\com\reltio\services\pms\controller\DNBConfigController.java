package com.reltio.services.pms.controller;


import com.fasterxml.jackson.databind.JsonNode;
import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.model.UpdateDNBKeyRequest;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import com.reltio.services.pms.service.DNBConfigService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;

@RestController
@RequestMapping(value = "/api/v1/dnbConfig", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name= "DNB Config Service")
@ReltioSecured(resourceClass = Pms.Tenant.class)
public class DNBConfigController {

    private final DNBConfigService dnbConfigService;

    @Autowired
    public DNBConfigController(DNBConfigService dnbConfigService) {
        this.dnbConfigService = dnbConfigService;
    }

    @PostMapping(value = "/addDNBKeys/{envId}/{tenantId}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public JsonNode addDNBKeys(@RequestBody UpdateDNBKeyRequest updateDNBKeyRequest, @PathVariable String envId,@PathVariable String tenantId) {
        return dnbConfigService.updateNBKeys(updateDNBKeyRequest,envId,tenantId);
    }
}
