package com.reltio.services.pms.common.sales.model.addon;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.tenant.TenantStreamingInfo;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@Getter
@Setter
public class QueueProductConfig extends BaseProductConfig {

    @JsonProperty("tenantStreamingInfos")
    private List<TenantStreamingInfo> tenantStreamingInfos;

    @JsonProperty("tenantStreamingInfo")
    private TenantStreamingInfo tenantStreamingInfo;

    public QueueProductConfig() {
        this.pmsProductName = PMSProductName.QUEUES;
    }

    public QueueProductConfig(List<TenantStreamingInfo> tenantStreamingInfos) {
        this.tenantStreamingInfos = new ArrayList<>(tenantStreamingInfos);
    }

    public QueueProductConfig(TenantStreamingInfo tenantStreamingInfo) {
        this.tenantStreamingInfo = tenantStreamingInfo;
    }

    public List<TenantStreamingInfo> getTenantStreamingInfos() {
        if (tenantStreamingInfos != null) {
            // Filter out null entries if any
            return tenantStreamingInfos.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else if (tenantStreamingInfo != null) {
            return Collections.singletonList(tenantStreamingInfo);
        } else {
            return Collections.emptyList();
        }
    }

}
