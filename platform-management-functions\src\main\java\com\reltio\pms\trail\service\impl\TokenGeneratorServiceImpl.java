package com.reltio.pms.trail.service.impl;

import com.reltio.pms.trail.domain.AuthenticationProperties;
import com.reltio.pms.trail.domain.AuthenticationResponse;
import com.reltio.pms.trail.service.APICallFailureException;
import com.reltio.pms.trail.service.GenericException;
import com.reltio.pms.trail.service.RestAPIService;
import com.reltio.pms.trail.service.TokenGeneratorService;
import com.reltio.pms.trail.util.GenericUtilityService;
import org.apache.commons.codec.binary.Base64;

import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;


/**
 * This class implements way to get the token from the Auth server
 */
public class TokenGeneratorServiceImpl extends Thread implements
                                                      TokenGeneratorService {
    private static final Logger logger = Logger.getLogger(TokenGeneratorServiceImpl.class.getName());

    private String authURL;
    private String clientId;
    private Map<String, String> authHeaders = new HashMap<>();
    private AuthenticationResponse authenticationResponse;
    private RestAPIService apiService = new SimpleRestAPIServiceImpl();
    private boolean isRunning;
    private boolean isClientCredentialsAuth;

    /**
     * This constructor will store the user details and throws exception if it
     * is invalid;
     *
     * @param clientId
     * @param clientSecret
     * @param authURL
     * @throws com.reltio.pms.trail.service.APICallFailureException
     * @throws com.reltio.pms.trail.service.GenericException
     */
    public TokenGeneratorServiceImpl(
            String clientId,
            String clientSecret,
            String authURL
    ) throws APICallFailureException, GenericException {
        if (GenericUtilityService.checkNullOrEmpty(authURL)) {
            this.authURL = AuthenticationProperties.DEFAULT_AUTH_SERVER_URL;
        } else {
            this.authURL = authURL;
        }
        setDaemon(true);
        populateAuthHeaders(clientId, clientSecret);
        getNewToken();
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.reltio.cst.service.TokenGeneratorService#startBackgroundTokenGenerator
     * ()
     */
    @Override
    @Deprecated
    public boolean startBackgroundTokenGenerator() {
        if (!isRunning) {
            isRunning = true;
            this.start();
            return true;
        }

        return false;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.reltio.cst.service.TokenGeneratorService#getToken()
     */
    @Override
    public String getToken() throws APICallFailureException, GenericException {
        if (authenticationResponse == null) {
            getNewToken();
        }
        return authenticationResponse.getAccessToken();
    }

    @Override
    public String getRefreshToken() throws APICallFailureException, GenericException {
        if (authenticationResponse == null) {
            getNewToken();
        }
        return authenticationResponse.getRefreshToken();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.reltio.cst.service.TokenGeneratorService#getNewToken()
     */
    @Override
    public String getNewToken() throws APICallFailureException,
            GenericException {
        String responseStr;
        if (isClientCredentialsAuth()) {
            responseStr = getAccessTokenByClientCredentials(authURL, 1);
            authenticationResponse = AuthenticationProperties.GSON.fromJson(responseStr, AuthenticationResponse.class);
        } else if (authenticationResponse == null) {
            responseStr = getAccessToken(authURL, clientId, 1);
            authenticationResponse = AuthenticationProperties.GSON.fromJson(responseStr, AuthenticationResponse.class);
        } else {
            getToken();
        }
        return authenticationResponse.getAccessToken();
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.reltio.cst.service.TokenGeneratorService#stopBackgroundTokenGenerator
     * ()
     */
    @Override
    @Deprecated
    public boolean stopBackgroundTokenGenerator() {

        if (isRunning && this.isAlive()) {
            logger.info("Background Token Generation process Stopped...");
            this.interrupt();
            return true;
        }
        return false;
    }

    /*
     * (non-Javadoc)
     *
     * @see java.lang.Thread#run()
     */
    @Override
    public void run() {

        logger.info("Background Token Generation process Started...");

        // Pre-validation of authentication object
        if (authenticationResponse == null) {
            try {
                getToken();
            } catch (APICallFailureException | GenericException e) {
                e.printStackTrace();
            }
        }

        // Infinite loop to run the thread in background for updating the token
        while (!Thread.currentThread().isInterrupted()) {
            try {
                // Wait for 4 mins
                Thread.sleep(240000);
                getToken();
            } catch (APICallFailureException | GenericException e) {
                e.printStackTrace();
            } catch (InterruptedException ignored) {
            }
        }
    }


    private String getAccessToken(String url, String clientId, int retryCount) throws APICallFailureException, GenericException {
        logger.info(String.format("url: %s, clientId: %s", url, clientId));
        return doAuthAPICall(url, "grant_type=client_credentials", retryCount);

    }

    private String getAccessTokenByClientCredentials(String url, int retryCount) {
        try {
            return doAuthAPICall(url, "grant_type=client_credentials", retryCount);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * @param url
     * @param retryCount
     * @return
     * @throws APICallFailureException
     * @throws GenericException
     */
    private String doAuthAPICall(String url, String body, int retryCount)
            throws APICallFailureException, GenericException {
        try {
            return apiService.post(url, authHeaders, body);
        } catch (APICallFailureException e) {
            logger.info("Auth Call Failed: Error Code = "
                    + e.getErrorCode() + " |||| Error Message: "
                    + e.getErrorResponse());

            switch (e.getErrorCode()) {
                case AuthenticationProperties.INVALID_USER_CREDENTIALS: {
                    // Send the Exception if the failure due to invalid user credentials
                    throw e;
                }
                case 401: {
                    if (e.getErrorResponse().contains(AuthenticationProperties.INVALID_REFRESH_TOKEN_ERROR)) {
                        return AuthenticationProperties.INVALID_REFRESH_TOKEN_ERROR;
                    }
                    if (retryCount < AuthenticationProperties.RETRY_LIMIT) {
                        logger.info("Retrying with new token..");
                        return doAuthAPICall(url, body, ++retryCount);
                    }
                    break;
                }
                case 502: {
                    if (retryCount < AuthenticationProperties.RETRY_LIMIT_FOR_502) {
                        try {
                            long sleepTime = (long) (Math.pow(2, retryCount) - 1) * 1000;
                            logger.info("Retrying in " + sleepTime + " milliseconds..");
                            Thread.sleep(sleepTime);
                            return doAuthAPICall(url, body, ++retryCount);
                        } catch (InterruptedException ex) {
                            logger.info("Unexpected interruption exception.. " + ex.getMessage());
                        }
                    }

                    break;
                }
                case 503: {
                    if (retryCount < AuthenticationProperties.RETRY_LIMIT_FOR_503) {
                        try {
                            long sleepTime = (long) (Math.pow(2, retryCount) - 1) * 1000;
                            logger.info("Retrying in " + sleepTime + " milliseconds..");
                            Thread.sleep(sleepTime);
                            return doAuthAPICall(url, body, ++retryCount);
                        } catch (InterruptedException ex) {
                            logger.info(
                                    "Unexpected interruption exception.. " + ex.getMessage());
                        }
                    }
                    break;
                }
                case 504: {
                    if (retryCount < AuthenticationProperties.RETRY_LIMIT_FOR_504) {
                        try {
                            long sleepTime = (long) (Math.pow(2, retryCount) - 1) * 1000;
                            logger.info("Retrying in " + sleepTime + " milliseconds..");
                            Thread.sleep(sleepTime);
                            return doAuthAPICall(url, body, ++retryCount);
                        } catch (InterruptedException ex) {
                            logger.info("Unexpected interruption exception.. " + ex.getMessage());
                        }
                    }
                    break;
                }
            }
            throw e;

        } catch (GenericException e) {
            logger.info("Auth Call Failed Due to unexpected Exception: Error Message: " + e.getExceptionMessage());

            // Retry till count reaches to expected Retry limit
            if (retryCount < AuthenticationProperties.RETRY_LIMIT) {
                return doAuthAPICall(url, body, ++retryCount);
            }

            throw e;
        }
    }

    /**
     * This is util method to populate the required headers for get token
     */
    private void populateAuthHeaders(String clientId, String clientSecret) {
        String rawCreds = clientId + ":" + clientSecret;
        authHeaders.put(AuthenticationProperties.AUTH_SERVER_HEADER, AuthenticationProperties.AUTH_HEADER_BASIC_STR + getClientCredentials(rawCreds));
        authHeaders.put(AuthenticationProperties.CONTENT_TYPE_HEADER, "application/x-www-form-urlencoded");
    }


    /**
     * @return the authURL
     */
    public String getAuthURL() {
        return authURL;
    }

    /**
     * @param authURL the authURL to set
     */
    public void setAuthURL(String authURL) {
        this.authURL = authURL;
    }


    /**
     * @return the clientCredentials
     */
    public String getClientCredentials(String rawCreds) {
        return new String(Base64.encodeBase64(rawCreds.getBytes(Charset.defaultCharset())),
                Charset.defaultCharset());
    }

    /**
     * @return the isClientCredentialsAuth
     */
    public boolean isClientCredentialsAuth() {
        return isClientCredentialsAuth;
    }


    /**
     * @param isClientCredentialsAuth the isClientCredentialsAuth to set
     */
    public void setClientCredentialsAuth(boolean isClientCredentialsAuth) {
        this.isClientCredentialsAuth = isClientCredentialsAuth;
    }

}
