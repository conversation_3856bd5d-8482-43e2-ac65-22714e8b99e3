package com.reltio.services.pms.service.jobs.tasks.auxiliary.sleep;

import com.reltio.services.pms.common.model.jobs.tasks.SleepTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.AbstractTaskExecutionService;

public class SleepTaskExecutionService extends AbstractTaskExecutionService<SleepTaskInstance> {
    public SleepTaskExecutionService(String jobId, SleepTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService) {
        super(jobId, taskDetail, grafanaDashboardGBQService);
    }

    @Override
    public void executeTask() throws InterruptedException {
        Thread.sleep(taskDetail.getSleepTime());
        updateTask();

        taskDetail.setStatus(TaskStatus.COMPLETED);
    }
}
