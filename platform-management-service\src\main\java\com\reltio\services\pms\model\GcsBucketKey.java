package com.reltio.services.pms.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class GcsBucketKey {
    @JsonProperty("projectId")
    private String projectId;

    @JsonProperty("locationId")
    private String locationId;

    @JsonProperty("keyRingId")
    private String keyRingId;

    @JsonProperty("keyId")
    private String keyId;
}
