package com.reltio.services.pms.common.model.jobs.tasks.tenants.data.clone;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ConfigureStreamingAndAnalyticsTaskInstance extends TaskInstance {

    @JsonProperty(value = "sourceTenantId")
    private final String sourceTenantId;

    @JsonProperty(value = "targetTenantId")
    private final String targetTenantId;

    @JsonProperty(value = "sourceEnvId")
    private final String sourceEnvId;

    @JsonProperty(value = "targetEnvId")
    private final String targetEnvId;

    @JsonProperty(value = "targetStreamingConfig")
    private final String targetStreamingConfig;

    @JsonProperty(value = "isAnalyticsEnabledOnTarget")
    private final boolean isAnalyticsEnabledOnTarget ;

    @JsonProperty(value = "enable")
    private final boolean enable;


    @JsonCreator
    public ConfigureStreamingAndAnalyticsTaskInstance (
                                  @JsonProperty(value = "id") String id,
                                  @JsonProperty(value = "name") String name,
                                  @JsonProperty(value = "envId") String envId,
                                  @JsonProperty(value = "jobId") String jobId,
                                  @JsonProperty(value = "startTime") Long startTime,
                                  @JsonProperty(value = "finishTime") Long finishTime,
                                  @JsonProperty(value = "status") TaskStatus status,
                                  @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                  @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                  @JsonProperty(value = "executingNodeName") String executingNodeName,
                                  @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                  @JsonProperty(value = "sourceTenantId") String sourceTenantId,
                                  @JsonProperty(value = "targetTenantId") String targetTenantId,
                                  @JsonProperty(value = "sourceEnvId") String sourceEnvId,
                                  @JsonProperty(value = "targetEnvId")String targetEnvId,
                                  @JsonProperty(value = "targetStreamingConfig")String targetStreamingConfig,
                                  @JsonProperty(value = "isAnalyticsEnabledOnTarget")boolean isAnalyticsEnabledOnTarget,
                                  @JsonProperty(value = "enable")boolean enable) {



        super(id, name, jobId, startTime, finishTime, TaskType.CONFIGURE_STREAMING_AND_ANALYTICS_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.sourceTenantId = sourceTenantId;
        this.targetTenantId = targetTenantId;
        this.sourceEnvId = sourceEnvId;
        this.targetEnvId = targetEnvId;
        this.targetStreamingConfig = targetStreamingConfig;
        this.isAnalyticsEnabledOnTarget = isAnalyticsEnabledOnTarget;
        this.enable = enable;
    }
}
