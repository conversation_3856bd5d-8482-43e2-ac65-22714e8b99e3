package com.reltio.services.pms.service.jobs.tasks.auxiliary.multiplesleeps;

import com.reltio.services.pms.common.model.jobs.tasks.MultiSleepTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstanceParams;
import com.reltio.services.pms.common.model.pipeline.tasks.MultiSleepPipelineTaskConfig;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.jobs.tasks.TaskInstanceProvider;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class MultiSleepTaskInstanceProvider implements TaskInstanceProvider<MultiSleepTaskInstance, MultiSleepPipelineTaskConfig, TaskInstanceParams> {

    @Override
    public TaskType getTaskType() {
        return TaskType.MULTI_SLEEP_TASK;
    }

    @Override
    public MultiSleepTaskInstance getTaskDetail(String envId, String jobId, MultiSleepPipelineTaskConfig pipelineTaskConfig, TaskInstanceParams noInput) {
        return new MultiSleepTaskInstance(
                UUID.randomUUID().toString(),
                TaskType.SLEEP_TASK.toString(),
                jobId,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                pipelineTaskConfig.getSleepTime(),
                pipelineTaskConfig.getNoOfSleep(),
                0L,
                envId

        );
    }
}
