import com.reltio.pms.trail.service.APICallFailureException;
import com.reltio.pms.trail.service.GenericException;
import com.reltio.pms.trail.service.ReltioAPIService;
import com.reltio.pms.trail.service.impl.SimpleReltioAPIServiceImpl;
import com.reltio.pms.trail.service.impl.TokenGeneratorServiceImpl;

public class TestMain {

    public static void main(String[] args) throws APICallFailureException, GenericException {
        ReltioAPIService reltioAPIService = new SimpleReltioAPIServiceImpl(new TokenGeneratorServiceImpl( "***","***", "https://auth-stg.reltio.com/oauth/token"));
    }
}
