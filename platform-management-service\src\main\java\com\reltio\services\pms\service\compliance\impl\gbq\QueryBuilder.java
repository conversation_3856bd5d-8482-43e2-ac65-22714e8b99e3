package com.reltio.services.pms.service.compliance.impl.gbq;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.text.StrSubstitutor;

import java.util.Map;

/**
 * QueryBuilder
 * <p>
 * Helper class to build query
 */
public class QueryBuilder {

    /**
     * The constant API_USAGE_QUERY_TEMPLATE.
     */
    public static final String API_USAGE_QUERY_TEMPLATE =
            "SELECT" +
                    "  ReportDate," +
                    "  TenantId," +
                    "  Environment," +
                    "  HTTPMethod," +
                    "  HandlerMapping," +
                    "  ApiUsage" +
                    " FROM (" +
                    "  SELECT" +
                    "    day AS ReportDate," +
                    "    environment as Environment," +
                    "    tenantId AS TenantId," +
                    "    httpMethod AS HTTPMethod," +
                    "    handlerMapping AS HandlerMapping," +
                    "    SUM(requestCount) AS ApiUsage" +
                    "  FROM `${projectId}.${dataset}.${tableName}`" +
                    "  WHERE" +
                    "    userCustomer NOT IN ('SYSTEM', 'Reltio', 'null', 'internal') AND" +
                    "    userCustomer IS NOT NULL" +
                    "  GROUP BY" +
                    "    day," +
                    "    environment," +
                    "    tenantId," +
                    "    httpMethod," +
                    "    handlerMapping" +
                    ")" +
                    " WHERE" +
                    "  ReportDate >= PARSE_DATE('%Y%m%d', '${startDate}')" +
                    "  AND ReportDate <= PARSE_DATE('%Y%m%d', '${endDate}')" +
                    "  AND TenantId IN (${tenantId})";


    public static final String API_USAGE_ENDPOINT_SUBQUERY_TEMPLATE="SELECT\n" +
            "        day AS ReportDate,\n" +
            "        tenantId AS TenantID,\n" +
            "        httpMethod AS HTTPMethod,\n" +
            "        handlerMapping AS HandlerMapping,\n" +
            "        sourceSystem AS SourceSystem,\n" +
            "        requestCount AS ApiUsage  \n" +
            "    FROM `${projectId}.${dataset}.${tableName}`\n" +
            "    WHERE\n" +
            "        userCustomer NOT IN ('SYSTEM', 'Reltio', 'null', 'internal')\n" +
            "        AND userCustomer IS NOT NULL\n" +
            "        AND day >= PARSE_DATE('%Y%m%d', '${startDate}')\n" +
            "        AND day <= PARSE_DATE('%Y%m%d', '${endDate}')\n" +
            "        AND tenantId IS NOT NULL\n" +
            "        AND tenantId IN (${tenantId})\n" ;

    /**
     * The constant TASKS_USAGE_TEMPLATE.
     */
    public static final String TASKS_USAGE_TEMPLATE =
            " SELECT ReportDate," +
                    "  BillingStartDate," +
                    "  BillingEndDate," +
                    "  Environment," +
                    "  TenantId," +
                    "  TaskCount" +
                    " FROM (" +
                    "  SELECT" +
                    "    date AS ReportDate," +
                    "    billing_start_date AS BillingStartDate," +
                    "    billing_end_date AS BillingEndDate," +
                    "    environment AS Environment," +
                    "    tenant AS TenantId," +
                    "    MAX(tasks) AS TaskCount " +
                    "FROM `${projectId}.${dataset}.${tableName}` AS task_usage GROUP BY" +
                    "    date," +
                    "    billing_start_date," +
                    "    billing_end_date," +
                    "    environment," +
                    "    tenant" +
                    ") WHERE " +
                    " ReportDate >=  PARSE_DATE('%Y%m%d','${startDate}')" +
                    " AND ReportDate <=  PARSE_DATE('%Y%m%d','${endDate}')" +
                    " AND TenantId IN (${tenantId})";


    /**
     * The constant RSU_USAGE_QUERY_TEMPLATE.
     */
    public static final String RSU_USAGE_QUERY_TEMPLATE =
            "SELECT\n" +
                    "  ReportDate,\n" +
                    "  CASE\n" +
                    "    WHEN Environment = 'eu-360' THEN 'euprod-01'\n" +
                    "    WHEN Environment = 'na01' THEN 'gus-trial'\n" +
                    "    WHEN Environment = 'ap-360' THEN 'approd-01'\n" +
                    "    ELSE Environment\n" +
                    "  END AS Environment,\n" +
                    "  TenantID,\n" +
                    "  TotalRSU,\n" +
                    "  totalSize,\n" +
                    "  dataSize,\n" +
                    "  matchData,\n" +
                    "  historyData,\n" +
                    "  activityData,\n" +
                    "  indexData,\n" +
                    "  interactionSize,\n" +
                    "  count(*) OVER() AS full_count \n" +
                    "FROM (\n" +
                    "  SELECT\n" +
                    "    EXTRACT(DATE FROM TIMESTAMP_SECONDS(timeReport)) AS ReportDate,\n" +
                    "    environment AS Environment,\n" +
                    "    tenantID AS tenantID,\n" +
                    "    COALESCE(datasize, 0) + COALESCE(historysize, 0) + COALESCE(interactionsize, 0) + COALESCE(matchsize, 0) + COALESCE(dynamomatchsize, 0) + COALESCE(indextotal, 0) + COALESCE(activitysize, 0) + COALESCE(gbtHistory.size, 0) + COALESCE(gbqActivities.size, 0) AS TotalRSU,\n" +
                    "    COALESCE(totalSize, 0) AS totalSize,\n" +
                    "    COALESCE(dataSize, 0) AS dataSize,\n" +
                    "    COALESCE(matchSize, 0) + COALESCE(dynamoMatchSize, 0) AS matchData,\n" +
                    "    COALESCE(historySize, 0) + COALESCE(gbtHistory.size, 0) AS historyData,\n" +
                    "    COALESCE(activitySize, 0) + COALESCE(gbqActivities.size, 0) AS activityData,\n" +
                    "    COALESCE(indexTotal, 0) AS indexData,\n" +
                    "    COALESCE(interactionSize, 0) AS interactionSize,\n" +
                    "    ROW_NUMBER() OVER (\n" +
                    "            PARTITION BY EXTRACT(DATE FROM TIMESTAMP_SECONDS(timeReport)), environment, tenantID\n" +
                    "            ORDER BY (COALESCE(datasize, 0) + COALESCE(historysize, 0) + COALESCE(interactionsize, 0) + COALESCE(matchsize, 0) + COALESCE(dynamomatchsize, 0) + COALESCE(indextotal, 0) + COALESCE(activitysize, 0) + COALESCE(gbtHistory.size, 0) + COALESCE(gbqActivities.size, 0)) DESC)\n" +
                    "    AS RecordRank\n" +
                    "FROM `${projectId}.${dataset}.${tableName}`\n" +
                    "WHERE\n" +
                    "    EXTRACT(DATE FROM TIMESTAMP_SECONDS(timeReport)) >= PARSE_DATE('%Y%m%d','${startDate}') AND EXTRACT(DATE FROM TIMESTAMP_SECONDS(timeReport)) <= PARSE_DATE('%Y%m%d','${endDate}')\n" +
                    ")\n" +
                    "WHERE\n" +
                    "  RecordRank = 1\n" +
                    "  AND ReportDate >= PARSE_DATE('%Y%m%d','${startDate}')\n" +
                    "  AND ReportDate <= PARSE_DATE('%Y%m%d','${endDate}')\n" +
                    "  AND TenantID IN ( ${tenantId} )";

    /**
     * The constant RSU_USAGE_BY_TENANT_TEMPLATE.
     */
    public static final String RSU_USAGE_BY_TENANT_TEMPLATE = "SELECT ReportDate, TenantId, SUM(TotalRsu) as RsuUsage, Environment ,count(*) OVER() AS TotalCount FROM ( " + RSU_USAGE_QUERY_TEMPLATE + " ) GROUP BY ReportDate,Environment, TenantId";

    /**
     * The constant RSU_USAGE_BY_DATE_TEMPLATE.
     */
    public static final String RSU_USAGE_BY_DATE_TEMPLATE = "SELECT ReportDate,SUM(TotalRsu) as RsuUsage , count(*) OVER() AS TotalCount FROM ( " + RSU_USAGE_QUERY_TEMPLATE + " ) GROUP BY ReportDate ";


    /**
     * The constant RSU_USAGE_BY_BREAKDOWN_TEMPLATE.
     */
    public static final String RSU_USAGE_BY_BREAKDOWN_TEMPLATE = "SELECT ReportDate,TenantId,SUM(TotalRsu) as totalSize ,SUM(dataSize) AS dataSize, SUM(historyData) AS historyData, SUM(activityData) AS activityData, SUM(matchData) AS matchData, SUM(indexData) AS indexData, SUM(interactionSize) AS interactionSize, count(*) OVER() AS TotalCount FROM ( " + RSU_USAGE_QUERY_TEMPLATE + " ) GROUP BY ReportDate, TenantId ";

    /**
     * The constant CP_USAGE_TEMPLATE.
     */
    public static final String CP_USAGE_TEMPLATE = "SELECT" +
            "  ReportDate," +
            "  Environment," +
            "  TenantID," +
            "  CpUsage," +
            " FROM (" +
            "  SELECT" +
            "    EXTRACT(DATE FROM TIMESTAMP_SECONDS(timeReport)) AS ReportDate," +
            "    environment AS Environment," +
            "    tenantID AS TenantID," +
            "    matchRuleEntities AS MatchRuleEntities," +
            "    COALESCE((SELECT SUM(SAFE_CAST(SPLIT(c, '->')[SAFE_OFFSET(1)] AS INT64)) FROM UNNEST(ARRAY(SELECT * FROM UNNEST(SPLIT(matchRuleEntities, ';')) a WHERE SPLIT(a, '->')[SAFE_OFFSET(0)] != 'Location')) c), 0)  AS CpUsage," +
            "    ROW_NUMBER() OVER (" +
            "      PARTITION BY EXTRACT(DATE FROM TIMESTAMP_SECONDS(timeReport)), environment, tenantID" +
            "      ORDER BY COALESCE((SELECT SUM(SAFE_CAST(SPLIT(c, '->')[SAFE_OFFSET(1)] AS INT64)) FROM UNNEST(ARRAY(SELECT * FROM UNNEST(SPLIT(matchRuleEntities, ';')) a WHERE SPLIT(a, '->')[SAFE_OFFSET(0)] != 'Location')) c), 0) DESC)" +
            "    AS RecordRank" +
            "  FROM `${projectId}.${dataset}.${tableName}`" +
            ")" +
            " WHERE" +
            "  RecordRank = 1" +
            "  AND ReportDate >= PARSE_DATE('%Y%m%d', '${startDate}')" +
            "  AND ReportDate <= PARSE_DATE('%Y%m%d', '${endDate}')" +
            "  AND TenantId IN (${tenantId})";

    /**
     * The constant CP_USAGE_ENTITY_TEMPLATE.
     */
    public static final String CP_USAGE_ENTITY_TEMPLATE = "SELECT" +
            "  ReportDate," +
            "  CASE" +
            "    WHEN Environment = 'eu-360' THEN 'euprod-01'" +
            "    WHEN Environment = 'na01' THEN 'gus-trial'" +
            "    WHEN Environment = 'ap-360' THEN 'approd-01'" +
            "    ELSE Environment" +
            "  END AS Environment," +
            "  TenantId," +
            "  matchRuleEntitiesRecord.matchRuleEntityType AS EntityType," +
            "  matchRuleEntitiesRecord.matchRuleEntityCount AS CpUsage," +
            "  COUNT(*) OVER () AS TotalCount," +
            " FROM (" +
            "  SELECT" +
            "    EXTRACT(DATE FROM TIMESTAMP_SECONDS(timeReport)) AS ReportDate," +
            "    environment AS Environment," +
            "    tenantID AS TenantId," +
            "    matchRuleEntities AS MatchRuleEntities," +
            "    COALESCE((SELECT SUM(SAFE_CAST(SPLIT(c, '->')[SAFE_OFFSET(1)] AS INT64)) FROM UNNEST(ARRAY(SELECT * FROM UNNEST(SPLIT(matchRuleEntities, ';')) a WHERE SPLIT(a, '->')[SAFE_OFFSET(0)] != 'Location')) c), 0) AS TenantMatchRuleEntitiesCountTotal," +
            "    ROW_NUMBER() OVER (PARTITION BY EXTRACT(DATE FROM TIMESTAMP_SECONDS(timeReport)), environment, tenantID ORDER BY COALESCE((SELECT SUM(SAFE_CAST(SPLIT(c, '->')[SAFE_OFFSET(1)] AS INT64)) FROM UNNEST(ARRAY(SELECT * FROM UNNEST(SPLIT(matchRuleEntities, ';')) a WHERE SPLIT(a, '->')[SAFE_OFFSET(0)] != 'Location')) c), 0) DESC) AS RecordRank" +
            "  FROM `${projectId}.${dataset}.${tableName}`" +
            "  WHERE" +
            "    EXTRACT(DATE FROM TIMESTAMP_SECONDS(timeReport)) >= PARSE_DATE('%Y%m%d', '${startDate}') AND" +
            "    EXTRACT(DATE FROM TIMESTAMP_SECONDS(timeReport)) <= PARSE_DATE('%Y%m%d', '${endDate}')" +
            "  )" +
            "  LEFT JOIN UNNEST(ARRAY(SELECT * FROM UNNEST(SPLIT(matchRuleEntities, ';')) a WHERE SPLIT(a, '->')[SAFE_OFFSET(0)] != 'Location')) matchRuleEntitiesRecordRaw" +
            "  LEFT JOIN UNNEST(ARRAY(SELECT STRUCT(SPLIT(matchRuleEntitiesRecordRaw, '->')[SAFE_OFFSET(0)] AS matchRuleEntityType," +
            "  SAFE_CAST(SPLIT(matchRuleEntitiesRecordRaw, '->')[SAFE_OFFSET(1)] AS INT64) AS matchRuleEntityCount)))" +
            "  matchRuleEntitiesRecord" +
            " WHERE" +
            "  RecordRank = 1" +
            "  AND ReportDate >= PARSE_DATE('%Y%m%d', '${startDate}')" +
            "  AND ReportDate <= PARSE_DATE('%Y%m%d','${endDate}')" +
            "  AND TenantId IN (${tenantId})";

    public static final String CP_USAGE_BY_ENTITY_FOR_NON="SELECT" +
            "  ReportDate," +
            "  CASE" +
            "    WHEN Environment = 'eu-360' THEN 'euprod-01'" +
            "    WHEN Environment = 'na01' THEN 'gus-trial'" +
            "    WHEN Environment = 'ap-360' THEN 'approd-01'" +
            "    ELSE Environment" +
            "  END AS Environment," +
            "  TenantId," +
            "  nonMatchRuleEntitiesRecord.nonMatchRuleEntityType AS EntityType," +
            "  nonMatchRuleEntitiesRecord.nonMatchRuleEntityCount AS CpUsage," +
            "  COUNT(*) OVER () AS TotalCount," +
            " FROM (" +
            "  SELECT" +
            "    EXTRACT(DATE FROM TIMESTAMP_SECONDS(timeReport)) AS ReportDate," +
            "    environment AS Environment," +
            "    tenantID AS TenantId," +
            "     nonMatchRuleEntities AS NonMatchRuleEntities," +
            "    COALESCE((SELECT SUM(SAFE_CAST(SPLIT(c, '->')[SAFE_OFFSET(1)] AS INT64))\n" +
            "        FROM UNNEST(SPLIT(NonMatchRuleEntities, ';')) c), 0) AS TenantNonMatchRuleEntitiesCountTotal," +
            "    ROW_NUMBER() OVER (PARTITION BY EXTRACT(DATE FROM TIMESTAMP_SECONDS(timeReport)), environment, tenantID ORDER BY COALESCE((SELECT SUM(SAFE_CAST(SPLIT(c, '->')[SAFE_OFFSET(1)] AS INT64))\n" +
            "        FROM UNNEST(SPLIT(nonMatchRuleEntities, ';')) c), 0) DESC) AS RecordRank" +
            "  FROM `${projectId}.${dataset}.${tableName}`" +
            "  WHERE" +
            "    EXTRACT(DATE FROM TIMESTAMP_SECONDS(timeReport)) >= PARSE_DATE('%Y%m%d', '${startDate}') AND" +
            "    EXTRACT(DATE FROM TIMESTAMP_SECONDS(timeReport)) <= PARSE_DATE('%Y%m%d', '${endDate}')" +
            "  )" +
            "  LEFT JOIN UNNEST(SPLIT(nonMatchRuleEntities, ';')) nonMatchRuleEntitiesRecordRaw" +
            "  LEFT JOIN UNNEST(ARRAY(\n" +
            "    SELECT STRUCT(SPLIT(nonMatchRuleEntitiesRecordRaw, '->')[SAFE_OFFSET(0)] AS nonMatchRuleEntityType,\n" +
            "      SAFE_CAST(SPLIT(nonMatchRuleEntitiesRecordRaw, '->')[SAFE_OFFSET(1)] AS INT64) AS nonMatchRuleEntityCount))) nonMatchRuleEntitiesRecord" +
            " WHERE" +
            "  RecordRank = 1" +
            "  AND ReportDate >= PARSE_DATE('%Y%m%d', '${startDate}')" +
            "  AND ReportDate <= PARSE_DATE('%Y%m%d','${endDate}')" +
            "  AND TenantId IN (${tenantId})";


    /**
     * The constant BILLING_BY_ENVIRONMENT_TEMPLATE.
     */
    public static final String BILLING_BY_ENVIRONMENT_TEMPLATE = "SELECT\n" +
            " BillingStartDate,\n" +
            " BillingEndDate,\n" +
            " Environment,\n" +
            " TenantId,\n" +
            " SUM(TaskCount) AS TotalTaskCount\n" +
            "FROM (\n" +
            " SELECT\n" +
            "   billing_start_date AS BillingStartDate,\n" +
            "   billing_end_date AS BillingEndDate,\n" +
            "   environment AS Environment,\n" +
            "   tenant AS TenantId,\n" +
            "   MAX(tasks) AS TaskCount\n" +
            " FROM `${projectId}.${dataset}.${tableName}` AS task_usage\n" +
            " WHERE\n" +
            "   date >= PARSE_DATE('%Y%m%d','${startDate}')\n" +
            "   AND date <= PARSE_DATE('%Y%m%d','${endDate}')\n" +
            "   AND tenant IN (${tenantId})\n" +
            " GROUP BY\n" +
            "date,\n"+
            "   billing_start_date,\n" +
            "   billing_end_date,\n" +
            "   environment,\n" +
            "   tenant\n" +
            ")\n" +
            "GROUP BY\n" +
            " BillingStartDate,\n" +
            " BillingEndDate,\n" +
            " Environment,\n" +
            " TenantId\n";

    /**
     * The constant BILLING_BY_CUSTOMER_TEMPLATE.
     */
    public static final String BILLING_BY_CUSTOMER_TEMPLATE = "SELECT\n" +
            " BillingStartDate,\n" +
            " BillingEndDate,\n" +
            " TenantId,\n" +
            " SUM(TaskCount) AS TotalTaskCount\n" +
            "FROM (\n" +
            " SELECT\n" +
            "   billing_start_date AS BillingStartDate,\n" +
            "   billing_end_date AS BillingEndDate,\n" +
            "   tenant AS TenantId,\n" +
            "   MAX(tasks) AS TaskCount\n" +
            " FROM `${projectId}.${dataset}.${tableName}` AS task_usage\n" +
            " WHERE\n" +
            "   date >= PARSE_DATE('%Y%m%d','${startDate}')\n" +
            "   AND date <= PARSE_DATE('%Y%m%d','${endDate}')\n" +
            "   AND tenant IN (${tenantId})\n" +
            " GROUP BY\n" +
            "   date,\n" +
            "   billing_start_date,\n" +
            "   billing_end_date,\n" +
            "   tenant\n" +
            ")\n" +
            "GROUP BY\n" +
            " BillingStartDate,\n" +
            " BillingEndDate,\n" +
            " TenantId\n";

    /**
     * Gets api usage by date template.
     *
     * @param apiUsageByDateTemplate the api usage by date template
     * @return the api usage by date template
     */
    public static String getApiUsageByDateTemplate(String apiUsageByDateTemplate) {
        return "SELECT ReportDate,SUM(ApiUsage) as ApiUsage , count(*) OVER() AS TotalCount FROM ( " + apiUsageByDateTemplate + " ) GROUP BY ReportDate ";
    }

    public static String getApiUsageEndpointTemplate(String apiUsageByEndpointTemplate){
        return "SELECT\n" +
                "    a.ReportDate,\n" +
                "    a.TenantId,\n" +
                "    a.HTTPMethod,\n" +
                "    a.HandlerMapping,\n" +
                "    a.SourceSystem,\n" +
                "    SUM(a.ApiUsage) as ApiUsage,\n" +
                "    COUNT(*) OVER() AS TotalCount\n" +
                "FROM ("+apiUsageByEndpointTemplate +")AS a\n" +
                "WHERE a.TenantId IS NOT NULL\n" +
                "GROUP BY\n" +
                "    a.ReportDate,  \n" +
                "    a.TenantId,\n" +
                "    a.HTTPMethod,\n" +
                "    a.HandlerMapping,\n" +
                "    a.SourceSystem\n" ;
    }

    /**
     * Gets api usage by tenant template.
     *
     * @param apiUsageByTenantTemplate the api usage by tenant template
     * @return the api usage by tenant template
     */
    public static String getApiUsageByTenantTemplate(String apiUsageByTenantTemplate) {
        return "SELECT ReportDate,TenantId,SUM(ApiUsage) as ApiUsage , count(*) OVER() AS TotalCount FROM ( " + apiUsageByTenantTemplate + " ) GROUP BY ReportDate,TenantId ";
    }

    public static String getApiUsageByMonthTemplate(String apiUsageByMonthTemplate) {
        return "SELECT EXTRACT(YEAR FROM ReportDate) AS Year,  FORMAT_DATE('%B', DATE_TRUNC(ReportDate, MONTH)) AS Month,SUM(ApiUsage) as ApiUsage , count(*) OVER() AS TotalCount FROM ( " + apiUsageByMonthTemplate + " ) GROUP BY Year, Month ";
    }

    public static String getCpUsageByTenantTemplate(String cpUsageByTenantTemplate){
        return "SELECT ReportDate,TenantId,SUM(CpUsage) as CpUsage , count(*) OVER() AS TotalCount FROM ( " + cpUsageByTenantTemplate + " ) GROUP BY ReportDate,TenantId ";
    }

    public static String getCpUsageByDateTemplate(String cpUsageByDateTemplate){
        return "SELECT ReportDate,SUM(CpUsage) as CpUsage , count(*) OVER() AS TotalCount FROM ( " + cpUsageByDateTemplate + " ) GROUP BY ReportDate ";
    }

    public static String getCpUsageByMonthTemplate(String cpUsageByDateTemplate){
        return "SELECT  EXTRACT(YEAR FROM ReportDate) AS Year,FORMAT_DATE('%B', DATE_TRUNC(ReportDate, MONTH)) as Month ,MAX(CpUsage) as CpUsage FROM ( " + cpUsageByDateTemplate + " )  GROUP BY Year,Month ";
    }

    public static String getRsuUsageByMonthTemplate(String rsuUsageByDateTemplate){
        return "SELECT  EXTRACT(YEAR FROM ReportDate) AS Year,FORMAT_DATE('%B', DATE_TRUNC(ReportDate, MONTH)) as Month ,MAX(RsuUsage) as RsuUsage,SUM(RsuUsage) AS TotalRsuUsage  FROM ( " + rsuUsageByDateTemplate + " )  GROUP BY Year,Month ";
    }


    /**
     * The Query.
     */
    private String query;

    /**
     * Instantiates a new Query builder.
     *
     * @param query  the query
     * @param params the params
     */
    public QueryBuilder(String query, Map<String, Object> params) {
        this.query = StrSubstitutor.replace(query, params, "${", "}");
    }

    /**
     * Add additional parameter to query using AND operator. If value is empty will not modify the query
     *
     * @param param param name
     * @param value param value
     * @return class instance
     */
    public QueryBuilder addAdditionalANDParam(String param, String value) {
        if (StringUtils.isNotEmpty(value)) {
            query += String.format(" and %s = '%s'", param, value);
        }
        return this;
    }

    public QueryBuilder addINParam(String field, String[] values) {
        if (values != null && values.length > 0) {
            if (!query.isEmpty()) {
                query += " AND ";
            }

            query += field + " IN (";
            for (int i = 0; i < values.length; i++) {
                if (i > 0) {
                    query += ", ";
                }
                query += "'" + values[i] + "'";
            }
            query += ")";
        }
        return this;
    }



    /**
     * Order by query builder.
     *
     * @param field   the field
     * @param orderBy the order by
     * @return the query builder
     */
    public QueryBuilder orderBy(String field, String orderBy) {
        if (StringUtils.isNotEmpty(field)) {
            query += String.format(" ORDER BY %s %s", field, orderBy);
        }
        return this;
    }

    /**
     * Limit query builder.
     *
     * @param limit the page size
     * @return the query builder
     */
    public QueryBuilder limit(String limit) {
        if (StringUtils.isNotEmpty(limit)) {
            query += String.format(" LIMIT %s ", limit);
        }
        return this;
    }

    /**
     * Offset query builder.
     *
     * @param offset the page number
     * @return the query builder
     */
    public QueryBuilder offset(String offset) {
        if (StringUtils.isNotEmpty(offset)) {
            query += String.format(" OFFSET %s ", offset);
        }
        return this;
    }

    /**
     * Get result query
     *
     * @return query string
     */
    public String build() {
        return query;
    }
}
