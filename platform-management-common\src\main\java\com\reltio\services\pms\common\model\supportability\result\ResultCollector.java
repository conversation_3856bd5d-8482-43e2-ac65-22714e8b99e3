package com.reltio.services.pms.common.model.supportability.result;

import com.reltio.services.pms.common.model.supportability.Facet;
import com.reltio.services.pms.common.model.supportability.StatsDto;
import com.reltio.services.pms.common.model.supportability.request.Params;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * ResultCollector
 * Created by apylkov
 */
public class ResultCollector<T> {
    /**
     * The Result.
     */
    private final Map<String, List<T>> result;

    /**
     * Instantiates a new Result collector.
     */
    public ResultCollector() {
        this.result = new HashMap<>();
    }

    /**
     * Add stats to the map, update list of stats if the same type exist
     *
     * @param operationType type to add
     * @param stats         stats to add
     */
    public void add(String operationType, T stats) {
        result.putIfAbsent(operationType, new ArrayList<>());
        result.get(operationType).add(stats);
    }

    /**
     * Get stats map
     *
     * @return stats map
     */
    public Map<String, List<T>> getResult() {
        return result;
    }

    /**
     * Gets faceted results method is used to combine the every 10 mins record to based on every hour/every day based on params
     *
     * @param params the params
     * @return the faceted results
     */
    public Map<String, List<T>> getFacetedResult(Params params, StatsCombiner statsCombiner) {
        if (Objects.nonNull(params.getFacet())) {
            Map<String, List<T>> valueMap = new HashMap<>();
            for (Map.Entry<String, List<T>> entry : result.entrySet()) {
                List<T> valueList = new ArrayList<>();
                List<List<T>> partitionList = groupByTime(params.getFacet(), params.getStartTime(), params.getEndTime(), entry.getValue());
                for (int instance = NumberUtils.INTEGER_ZERO; instance < partitionList.size(); instance++) {
                    valueList.addAll(statsCombiner.combine(partitionList.get(instance)));
                }
                valueMap.put(entry.getKey(), valueList);
            }
            return valueMap;
        }
        return result;
    }

    /**
     * Group by time method is used to group based on time interval either by hour/day.
     *
     * @param facet     the facet
     * @param startTime the start time
     * @param endTime   the end time
     * @param values    the values
     * @return the list
     */
    private List<List<T>> groupByTime(Facet facet, long startTime, long endTime, List<T> values) {
        List<List<T>> outputList = new LinkedList<>();
        startTime = (startTime / facet.getSeconds()) * facet.getSeconds();
        while (startTime <= endTime) {
            List<T> list = new ArrayList<>();
            long upcomingTime = startTime + facet.getSeconds();
            for (T item : values) {
                StatsDto dto = (StatsDto) item;
                if (dto.getStartTime() >= startTime && dto.getEndTime() <= upcomingTime) {
                    dto.setStartTime(startTime);
                    dto.setEndTime(upcomingTime);
                    list.add((T) dto);
                }
            }
            outputList.add(list);
            startTime = upcomingTime;
        }
        return outputList;
    }
}
