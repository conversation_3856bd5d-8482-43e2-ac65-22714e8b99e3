package com.reltio.services.pms.common.model.jobs.tasks.contracts.sync;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

public class ContractsSyncTaskInstance extends TaskInstance {

    @JsonProperty
    public Long activatedTime;

    @JsonCreator
    public ContractsSyncTaskInstance(@JsonProperty(value = "id") String id,
                                     @JsonProperty(value = "name") String name,
                                     @JsonProperty(value = "jobId") String jobId,
                                     @JsonProperty(value = "startTime") Long startTime,
                                     @JsonProperty(value = "finishTime") Long finishTime,
                                     @JsonProperty(value = "status") TaskStatus status,
                                     @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                     @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                     @JsonProperty(value = "envId") String envId,
                                     @JsonProperty(value = "activatedTime") Long activatedTime) {
        super(id, name, jobId, startTime, finishTime, TaskType.CONTRACTS_SYNC_TASK, status, lastUpdatedTime, taskFailureContext,
                null, null, envId);
        this.activatedTime = activatedTime;
    }

    public Long getActivatedTime() {
        return activatedTime;
    }
}
