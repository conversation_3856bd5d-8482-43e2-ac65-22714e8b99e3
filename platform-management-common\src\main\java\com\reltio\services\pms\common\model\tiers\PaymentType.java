package com.reltio.services.pms.common.model.tiers;

import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.util.StdConverter;

import java.util.Arrays;

@JsonDeserialize(converter = PaymentType.TypeConverter.class)
public enum PaymentType {
    FREE("Free"),
    ONLINE("Online"),
    OFFLINE("Offline");

    private final String value;

    PaymentType(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return value;
    }

    public static PaymentType convertFromString(String value) {
        return Arrays.stream(PaymentType.values())
                .filter(e -> e.value.equals(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid value '" + value + "'"));

    }

    public static final class TypeConverter extends StdConverter<String, PaymentType> {
        @Override
        public PaymentType convert(String value) {
            return PaymentType.convertFromString(value);

        }
    }
}
