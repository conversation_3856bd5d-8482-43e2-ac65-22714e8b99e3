apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{.Values.serviceName}}
  namespace: {{ .Release.Namespace }}
  {{- if .Values.registerServiceInDNS }}
  annotations:
    external-dns.alpha.kubernetes.io/target: {{ .Release.Namespace }}-{{ .Values.lb }}.{{ .Values.domain }}
  {{- end }}
spec:
  hosts:
    - {{ .Values.hostname }}
    - {{ .Values.etalonHostname }}
  gateways:
    - helm-istio-system/{{ .Release.Namespace }}-elb-https-gateway
  http:
    - match:
      - uri:
          prefix: /healthcheck
      route:
        - destination:
            port:
              number: 9090
            host: {{.Values.serviceName}}.{{ .Release.Namespace }}.svc.cluster.local
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            port:
              number: 8080
            host: {{.Values.serviceName}}.{{ .Release.Namespace }}.svc.cluster.local
