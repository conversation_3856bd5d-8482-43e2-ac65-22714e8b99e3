package com.reltio.services.pms.common.model.compliance.request;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * The type Cp usage request.
 */
@NoArgsConstructor
@Getter
@Setter
public class CpUsageRequest extends ComplianceRequest {

    /**
     * The Entity type breakdown.
     */
    boolean entityTypeBreakdown;
    /**
     * The Entity type filter.
     */
    String entityType;
    /**
     * The Entity rule filter.
     */
    String entityRule;
    public CpUsageRequest(String startDate, String endDate, List<String> tenantIds, Integer size, Integer offset, String sortOrder, String sortField, boolean entityTypeBreakdown, String entityType,String entityRule) {
        super(startDate, endDate, tenantIds, size, offset, sortOrder, sortField);
        this.entityTypeBreakdown = entityTypeBreakdown;
        this.entityType = entityType;
        this.entityRule = entityRule;
    }

    public CpUsageRequest(boolean entityTypeBreakdown, String entityType,String entityRule) {
        this.entityTypeBreakdown = entityTypeBreakdown;
        this.entityType = entityType;
        this.entityRule = entityRule;
    }

    public CpUsageRequest(String startDate, String endDate, List<String> tenantIds, Integer size, Integer offset, String sortOrder, String sortField, String entityType, String entityRule) {
        super(startDate, endDate, tenantIds, size, offset, sortOrder, sortField);
        this.entityType = entityType;
        this.entityRule = entityRule;
    }
}
