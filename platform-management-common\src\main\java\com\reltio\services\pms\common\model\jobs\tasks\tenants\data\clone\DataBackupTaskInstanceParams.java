package com.reltio.services.pms.common.model.jobs.tasks.tenants.data.clone;

import com.reltio.services.pms.common.model.jobs.tasks.TaskInstanceParams;

import java.util.List;

public interface DataBackupTaskInstanceParams extends TaskInstanceParams {
    String getSourceTenantId();
    String getSourceEnvId();
    Integer getBackupRetentionPeriod();
    String getTicketId();
    String getSourceAccountBackupVaultName();
    List<String> getSkipTables();
}
