package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;


@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class Restore extends BaseFirestoreEntity {
    @JsonProperty(value = "id")
    private String id;

    @JsonProperty(value = "sourceTenantId")
    private String sourceTenantId;

    @JsonProperty(value = "sourceEnvId")
    private String sourceEnvId;

    @JsonProperty(value = "targetTenantId")
    private String targetTenantId;

    @JsonProperty(value = "targetEnvId")
    private String targetEnvId;

    @JsonProperty(value = "ticketId")
    private String ticketId;

    @JsonProperty(value = "jobId")
    private String jobId;

    @JsonProperty(value = "taskId")
    private String taskId;

    @JsonProperty(value = "dataRestoreArnsWithTableNames")
    private Map<String, String> dataRestoreArnsWithTableNames;

    @JsonProperty(value = "dataTableToRestoreFailureReason")
    private Map<String,String> dataTableToRestoreFailureReason;

    @JsonProperty(value = "matchRestoreArnsWithTableNames")
    private Map<String, String> matchRestoreArnsWithTableNames;

    @JsonProperty(value = "matchTableToRestoreFailureReason")
    private Map<String,String> matchTableToRestoreFailureReason;

    @Override
    public String getID() {
        return id;
    }
}
