package com.reltio.services.pms.clients.reltio.imagehosting;


import com.reltio.services.pms.clients.reltio.IReltioClient;
import com.reltio.services.pms.common.PMSRestTemplate;
import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.service.environment.EnvironmentService;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Service
public class ImageHostingServiceClient implements IReltioClient {
    private static final String CONFIGURE_IH_TENANT_URL = "%s/config/%s/%s/";
    private final PMSRestTemplate rest;
    private final EnvironmentService environmentService;
    private final String authServerUrl;
    private static final Logger LOGGER = Logger.getLogger(ImageHostingServiceClient.class);


    @Autowired
    public ImageHostingServiceClient(PMSRestTemplate rest,
                                     EnvironmentService environmentService,
                                     @Value("${reltio.auth.server.url}") String authServerUrl) {
        this.rest = rest;
        this.environmentService=environmentService;
        this.authServerUrl = authServerUrl;
        Set<String> properties=new HashSet<>();
        properties.add(authServerUrl);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }


    public void configureTenant(String environmentId, String tenantId, String secretKey, String accessKey, String s3HostUrl, String bucketName) {
        String serviceUrl=environmentService.getEnvironment(environmentId).getDefaultUrls().get(ServiceType.IH);
        if(!serviceUrl.contains("/image-hosting")){
            serviceUrl=serviceUrl+"/image-hosting";
        }

        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("secretKey", secretKey);
        requestParams.put("accessKey", accessKey);
        requestParams.put("authServer", authServerUrl);
        requestParams.put("s3HostUrl", s3HostUrl);
        requestParams.put("bucket", bucketName);
        requestParams.put("sourceImagesPath", "image/sourceimages");
        requestParams.put("previewImagesPath", "image/previewimages");
        requestParams.put("thumbnailImagesPath", "image/thumbnailimages");
        String url = String.format(CONFIGURE_IH_TENANT_URL, serviceUrl, environmentId, tenantId);

        rest.postForObject(url, requestParams, org.json.simple.JSONObject.class);
    }

    @SuppressWarnings("unchecked")
    public Map<String,Object> getIhConfigForTenant(String environmentId, String tenantId) {
        String serviceUrl=environmentService.getEnvironment(environmentId).getDefaultUrls().get(ServiceType.IH);
        if(!serviceUrl.contains("/image-hosting")){
            serviceUrl=serviceUrl+"/image-hosting";
        }
        String url = String.format(CONFIGURE_IH_TENANT_URL, serviceUrl, environmentId, tenantId);
        try {
            return rest.getForObject(url, Map.class);
        }catch (HttpClientErrorException.BadRequest e){
            LOGGER.warn(String.format("No IH config found for tenant %s , %s",tenantId, e.getMessage()));
            return null;
        }
    }

    public void deleteIhConfigForTenant(String environmentId, String tenantId) {
        String serviceUrl=environmentService.getEnvironment(environmentId).getDefaultUrls().get(ServiceType.IH);
        if(!serviceUrl.contains("/image-hosting")){
            serviceUrl=serviceUrl+"/image-hosting";
        }
        String url = String.format(CONFIGURE_IH_TENANT_URL, serviceUrl, environmentId, tenantId);
        rest.delete(url);


    }

    public boolean isEnabledForTenant(String tenantId, String environmentId) {
        String serviceUrl = Optional.ofNullable(environmentService.getEnvironment(environmentId).getDefaultUrls().get(ServiceType.IH))
                .orElse("");
        if (serviceUrl.isEmpty()) {
            return false;
        }if(!serviceUrl.contains("/image-hosting")){
            serviceUrl=serviceUrl+"/image-hosting";
        }
        String url = String.format(CONFIGURE_IH_TENANT_URL, serviceUrl, environmentId, tenantId);
        try {
            rest.getObject(url, String.class);
            return Boolean.TRUE;
        } catch (Exception ex) {
            return Boolean.FALSE;
        }
    }

}
