package com.reltio.services.pms.convertors.sales.addon;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.common.sales.model.addon.SFDCConnectorConfig;
import com.reltio.services.pms.convertors.sales.AbstractTenantAddOnProductProvider;
import com.reltio.services.pms.service.SalesPackageService;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

@Service
public class SalesforceProductProvider extends AbstractTenantAddOnProductProvider<SFDCConnectorConfig> {


    public SalesforceProductProvider(SalesPackageService salesPackageService) {
        super(salesPackageService);
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.SALESFORCE_CONNECTOR;
    }

    @Override
    public Map<String, Set<String>> getProductCodesByTenant() {
        return salesPackageService.getSalesAddOnsByProductCodes(PMSProductName.SALESFORCE_CONNECTOR);
    }

    @Override
    public SFDCConnectorConfig getProductConfig(Set<SalesConfig> salesConfigs, Set<String> tenantCodes, String currentTenantCode) {
        if (salesConfigs.isEmpty()) {
            return null;
        }

        SFDCConnectorConfig productConfig = new SFDCConnectorConfig();
        productConfig.setPmsProductName(getProductName());
        productConfig.addAllSalesConfigs(salesConfigs);
        productConfig.setQuantity(getQuantity(productConfig));
        return productConfig;
    }

}
