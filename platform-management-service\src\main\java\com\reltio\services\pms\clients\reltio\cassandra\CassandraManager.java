package com.reltio.services.pms.clients.reltio.cassandra;

import com.datastax.driver.core.Cluster;
import com.datastax.driver.core.Host;
import com.datastax.driver.core.Metadata;
import com.datastax.driver.core.ResultSet;
import com.datastax.driver.core.Row;
import com.datastax.driver.core.Session;
import com.datastax.driver.core.SocketOptions;
import com.datastax.driver.core.exceptions.DriverException;
import com.datastax.driver.core.exceptions.InvalidQueryException;
import com.datastax.driver.core.exceptions.NoHostAvailableException;
import com.datastax.driver.core.policies.AddressTranslator;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.net.InetSocketAddress;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class CassandraManager implements AutoCloseable, CassandraClient {

    private static final Logger log = Logger.getLogger(CassandraManager.class);
    private static final String cqlStatementUseKeyspace = "USE \"%s\";";
    private static final String cqlStatementTruncate = "TRUNCATE \"%s\";";
    private static final String cqlStatementGetTenant = "SELECT * FROM \"TENANTS\" WHERE key='Tenants' AND column1='%s';";
    private static final String cqlStatementDeleteTenantMeta = "DELETE FROM \"METADATA\" WHERE key='%s';";
    private static final String cqlStatementTenantDeleteConfig = "DELETE FROM \"TENANTS\" WHERE key='%s';";
    private static final String cqlStatementTenantDeleteLink = "DELETE FROM \"TENANTS\" WHERE key='Tenants' AND column1='%s';";
    private static final String cqlStatementDrop = "DROP KEYSPACE \"%s\";";
    private static final String cqlStatementDropTable = "DROP TABLE IF EXISTS \"%s\";";
    private final Map<String, String> privatePublicIps;
    private final List<String> hosts;
    private final int timeout;
    private Cluster cluster;
    private Session session;

    public CassandraManager(List<String> hostList, Map<String, String> privatePublicIps, int timeout) throws CassandraException {
        this.privatePublicIps = privatePublicIps;
        this.hosts = hostList;
        this.timeout = timeout;
        connect(this);
    }

    public CassandraManager(List<String> hostList, Map<String, String> privatePublicIps, int timeout, CassandraClient cassandraClient) throws CassandraException {
        this.privatePublicIps = privatePublicIps;
        this.hosts = hostList;
        this.timeout = timeout;
        connect(cassandraClient);
    }

    private Cluster getCluster() {
        return cluster;
    }

    private Session getSession() {
        return session;
    }

    @Override
    public Cluster createCluster(boolean isSSL, List<String> hosts, Map<String, String> privatePublicIps, int timeout) throws NoSuchAlgorithmException {
        Cluster.Builder builder = Cluster.builder();
        builder = builder.addContactPoints(hosts.toArray(new String[0])).
                withAddressTranslator(new CAddresTranslator(privatePublicIps)).
                withSocketOptions(new SocketOptions().setConnectTimeoutMillis(timeout).setReadTimeoutMillis(timeout))
                .withoutJMXReporting();
        if (isSSL) {
            builder.withNettyOptions(new CassandraSSLOptions());
        }
        return builder.build();
    }

    private Metadata getMetadata() throws NoSuchAlgorithmException {
        Metadata metadata;
        try {
            metadata = cluster.getMetadata();
        } catch (NoHostAvailableException e) {
            cluster = createCluster(true, hosts, privatePublicIps, timeout);
            metadata = cluster.getMetadata();
        }
        return metadata;
    }

    private void connect(CassandraClient cassandraClient) throws CassandraException {
        try {
            cluster = cassandraClient.createCluster(false, hosts, privatePublicIps, timeout);
            Metadata metadata = getMetadata();
            log.info(String.format("Connected to cluster: %s", metadata.getClusterName()));
            for (Host host : metadata.getAllHosts()) {
                log.info(String.format("Data center: %s; Host: %s; Rack: %s",
                        host.getDatacenter(), host.getAddress(), host.getRack()));
            }
            session = cluster.connect();
        } catch (Exception e) {
            log.error("Cant connect to C* cluster", e);
            throw new CassandraException("Can not connect to Cassandra cluster: " + String.join(",", hosts), e);
        }
    }

    @Override
    public void close() {
        try {
            getCluster().close();
        } catch (DriverException e) {
            log.error(String.format("Error while closing Cassandra connection to %s ", hosts), e);
        }
    }

    public void truncateCFs(String keySpaceName, Collection<String> cfNames) throws CassandraException {
        truncateOrDropCFs(keySpaceName, cfNames, false);
    }

    public void dropCFs(String keySpaceName, Collection<String> cfNames) throws CassandraException {
        truncateOrDropCFs(keySpaceName, cfNames, true);
    }

    private void truncateOrDropCFs(String keySpaceName, Collection<String> cfNames, boolean drop) throws CassandraException {
        log.info(String.format("%s Cfs for hosts: %s, keyspace: %s, tables: %s"
                , drop ? "Drop" : "Truncate", hosts, keySpaceName, String.join(",", cfNames)));
        // UseKeySpace
        Session existingSession = getSession();
        if (existingSession.isClosed()) {
            throw new CassandraException("Session was unexpectedly closed");
        }
        Map<String, String> cfsWithErrors = new HashMap<>();
        String useStatement = String.format(cqlStatementUseKeyspace, keySpaceName);
        try {
            existingSession.execute(useStatement);
        } catch (InvalidQueryException e) {
            log.warn(String.format("Exception during %s due to %s", useStatement, e));
            throw new CassandraException("Cannot execute the statement", e);
        }
        for (String cfName : cfNames) {
            try {
                String cql = drop ? String.format(cqlStatementDropTable, cfName) : String.format(cqlStatementTruncate, cfName);
                existingSession.execute(cql);
            } catch (InvalidQueryException e) {
                //skip this
            } catch (Exception e) {
                cfsWithErrors.put(cfName, e.getMessage());
                log.warn(String.format("Column family %s can not be truncated. Cause: %s", cfName, e.getMessage()));
            }
        }
        if (!cfsWithErrors.isEmpty()) {
            List<String> messagesList = new ArrayList<>();
            for (Map.Entry<String, String> entry : cfsWithErrors.entrySet()) {
                messagesList.add(String.format("Failed to TRUNCATE table: %s. Message: %s", entry.getKey(), entry.getValue()));
            }
            String msg = String.join("; ", messagesList);
            throw new CassandraException(msg);
        }
    }

    /**
     * Drop specified keyspace
     *
     * @param keySpaceName keyspace name
     * @throws CassandraException in case drop has failed
     */
    public void dropKeySpace(String keySpaceName) throws CassandraException {
        Session acquiredSession = getSession();
        if ((acquiredSession != null) && (keySpaceName != null) && (!keySpaceName.isEmpty())) {
            try {
                //Drop keyspace
                acquiredSession.execute(String.format(cqlStatementDrop, keySpaceName));
            } catch (Exception e) {
                String msg = String.format("Can not DROP %s on cluster %s. Error: %s", keySpaceName, getCluster().getClusterName(), e.getMessage());
                log.error(msg, e);
                throw new CassandraException(msg, e);
            }
        }
    }

    /**
     * @param systemKeyspace system keyspace name
     * @param tenantName     tenant name to retrieve id for
     * @return tenant internal id
     */
    public int getTenantId(String systemKeyspace, String tenantName) throws CassandraException {
        Session acquiredSession = getSession();
        // UseKeySpace
        try {
            acquiredSession.execute(String.format(cqlStatementUseKeyspace, systemKeyspace));
            String cql = String.format(cqlStatementGetTenant, tenantName);
            ResultSet result = acquiredSession.execute(cql);
            String tenantId = null;
            if (result.iterator().hasNext()) {
                Row tableNames = result.iterator().next();
                tenantId = tableNames.getString(2);
            }
            return Integer.parseInt(Optional.ofNullable(tenantId).orElse("-1"));
        } catch (Exception e) {
            throw new CassandraException(e.getMessage(), e);
        }
    }

    /**
     * Delete Tenant from System keyspace including METADATA configuration
     *
     * @param systemKeyspace system keyspace name
     * @param tenantId       tenant id to delete
     * @throws CassandraException in case deletion has failed
     */
    public void deleteTenant(String systemKeyspace, String tenantId) throws CassandraException {
        Session acquiredSession = getSession();
        if ((acquiredSession != null) && StringUtils.isNotEmpty(systemKeyspace)) {

            String cql;
            // UseKeySpace
            cql = String.format(cqlStatementUseKeyspace, systemKeyspace);
            acquiredSession.execute(cql);

            cql = String.format(cqlStatementGetTenant, tenantId);
            ResultSet result = acquiredSession.execute(cql);
            String internalTenantId = null;
            if (result.iterator().hasNext()) {
                Row tableNames = result.iterator().next();
                internalTenantId = tableNames.getString(2);
            }
            if ((internalTenantId != null) && (!internalTenantId.isEmpty())) {
                try {

                    // DELETE tenant description
                    cql = String.format(cqlStatementTenantDeleteConfig, internalTenantId);
                    acquiredSession.execute(cql);

                    // DELETE tenant link
                    cql = String.format(cqlStatementTenantDeleteLink, tenantId);
                    acquiredSession.execute(cql);

                    // DELETE tenant from METATDATA
                    cql = String.format(cqlStatementDeleteTenantMeta, tenantId);
                    acquiredSession.execute(cql);
                } catch (Exception e) {
                    String msg = String.format("Can not delete tenant %s from system keyspace %s on %s cluster", tenantId, systemKeyspace, cluster.getClusterName());
                    throw new CassandraException(msg, e);
                }

            } else {
                String msg = String.format("Can not find tenant %s in system keyspace %s on %s cluster", tenantId, systemKeyspace, cluster.getClusterName());
                throw new CassandraException(msg);
            }

        }
    }
}

class CAddresTranslator implements AddressTranslator {
    private final Map<String, String> privatePublicMap;

    public CAddresTranslator(Map<String, String> privatePublicMap) {
        this.privatePublicMap = privatePublicMap;
    }

    @Override
    public void init(Cluster cluster) {
        //We do not need to implement this
    }

    @Override
    public InetSocketAddress translate(InetSocketAddress address) {
        String ip;
        if (address.isUnresolved()) {
            ip = address.toString();
        } else {
            ip = address.getAddress().getHostAddress();
        }
        String publicIp = privatePublicMap.get(ip);
        if (publicIp == null)
        // We do not need to translate address as it already public
        {
            return address;
        }
        return new InetSocketAddress(publicIp, address.getPort());
    }


    public void close() {
        //We do not need to implement this
    }

}