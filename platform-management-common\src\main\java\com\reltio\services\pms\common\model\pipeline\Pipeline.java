package com.reltio.services.pms.common.model.pipeline;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import com.reltio.services.pms.common.model.pipeline.tasks.AbstractPipelineTaskConfig;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class Pipeline extends BaseFirestoreEntity {
    @JsonProperty("pipelineId")
    private final String pipelineId;

    @JsonProperty("type")
    private PipelineType type;

    @JsonProperty("description")
    private final String description;

    @JsonProperty("tasks")
    private final List<AbstractPipelineTaskConfig> tasks;

    @JsonProperty("alertOwners")
    private final List<String> alertOwners;

    @JsonCreator
    public Pipeline(
            @JsonProperty(value = "pipelineId", required = true) String pipelineId,
            @JsonProperty(value = "type") PipelineType type,
            @JsonProperty(value = "description") String description,
            @JsonProperty(value = "tasks", required = true) List<AbstractPipelineTaskConfig> tasksConfig,
            @JsonProperty(value = "alertOwners") List<String> alertOwners) {
        this.pipelineId = pipelineId;
        this.type = type;
        this.description = description;
        this.tasks = tasksConfig;
        this.alertOwners = alertOwners == null ? new ArrayList<>() : alertOwners;
    }

    public Pipeline(String pipelineId, PipelineType type, String description, List<AbstractPipelineTaskConfig> tasksConfig) {
        this.pipelineId = pipelineId;
        this.type = type;
        this.description = description;
        this.tasks = tasksConfig;
        this.alertOwners = new ArrayList<>();
    }

    public String getPipelineId() {
        return pipelineId;
    }

    public String getDescription() {
        return description;
    }

    public List<AbstractPipelineTaskConfig> getTasks() {
        return tasks;
    }

    public PipelineType getType() {
        return type;
    }

    public void setType(PipelineType type) {
        this.type = type;
    }

    public List<String> getAlertOwners() {
        return alertOwners;
    }

    public boolean containsTaskType(TaskType taskType) {
        return getTasks().stream().anyMatch(task -> task.getType() == taskType);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Pipeline)) {
            return false;
        }
        Pipeline pipeline = (Pipeline) o;
        return Objects.equals(getPipelineId(), pipeline.getPipelineId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getPipelineId());
    }

    @Override
    public String getID() {
        return getPipelineId();
    }

    @Override
    public String toString() {
        return "Pipeline{" +
                "pipelineId='" + pipelineId + '\'' +
                ", description='" + description + '\'' +
                ", tasks=" + tasks +
                '}';
    }
}
