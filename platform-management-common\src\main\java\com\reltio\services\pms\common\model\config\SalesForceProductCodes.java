package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.SalesForceProductName;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;
import java.util.Set;

/**
 * The type Sales force package.
 */
@Getter
@Setter
public class SalesForceProductCodes extends PMSConfig {

    /**
     * The Product codes.
     */
    @JsonProperty("productCodes")
    private Map<SalesForceProductName, Set<String>> productCodes;

    /**
     * The Add ons.
     */
    @JsonProperty("addOns")
    private Map<PMSProductName, Map<String, Set<String>>> addOns;

    /**
     * The RIH add ons (new collection).
     */
    @JsonProperty("rihAddOns")
    private Map<String, String> rihAddOns;


    /**
     * Instantiates a new Sales force product codes.
     *
     * @param configName   the config name
     * @param productCodes the product codes
     * @param addOns       the add ons
     * @param rihAddOns    the RIH add ons
     */
    @JsonCreator
    public SalesForceProductCodes(@JsonProperty("configName") String configName,
                                  @JsonProperty("productCodes") Map<SalesForceProductName, Set<String>> productCodes, @JsonProperty("addOns") Map<PMSProductName, Map<String, Set<String>>> addOns,
                                  @JsonProperty("rihAddOns") Map<String, String> rihAddOns) {
        super(configName);
        this.productCodes = productCodes;
        this.addOns = addOns;
        this.rihAddOns = rihAddOns;
    }


}
