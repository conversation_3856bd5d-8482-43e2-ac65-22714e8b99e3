package com.reltio.services.pms.common.model.supportability;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The type Latency by time.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class LatencyByTime {

    /**
     * The Over 30 s.
     */
    @JsonProperty("over30s")
    private Integer over30s;
    /**
     * The Over 1 m.
     */
    @JsonProperty("over1m")
    private Integer over1m;
    /**
     * The Over 10 m.
     */
    @JsonProperty("over10m")
    private Integer over10m;
}
