package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.ServiceNode;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.util.Collections;
import java.util.Set;

@Service
public class ServiceNodeDao extends AbstractRootCollectionDao<ServiceNode> {

    private static final String SERVICE_NODES_COLLECTION_NAME = "PMS_SERVICE_NODES";
    private static final Logger LOGGER = Logger.getLogger(ServiceNodeDao.class);

    public ServiceNodeDao(CredentialsProvider provider,
                          @Value("${firestore.env.name}") String deployedEnv, ReltioUserHolder reltioUserHolder) {
        super(provider, SERVICE_NODES_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    protected TypeReference<ServiceNode> getTypeReference() {
        return new TypeReference<ServiceNode>() {
        };
    }
}
