package com.reltio.services.pms.service.jobs.tasks.auxiliary.maintenancemode;

import com.reltio.common.config.TenantConfiguration;
import com.reltio.services.pms.clients.reltio.mdm.MDMCleanTenantClient;
import com.reltio.services.pms.clients.reltio.mdm.MDMClient;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.maintenance.MaintenanceModeTaskInstance;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.AbstractTaskExecutionService;
import org.springframework.http.HttpStatus;

public class MaintenanceModeTaskExecutionService extends AbstractTaskExecutionService<MaintenanceModeTaskInstance> {


    private static final String MAINTANANCE_MODE_ENABLED = "Maintenance mode enabled.";
    private static final String MAINTANANCE_MODE_DISABLED = "Maintenance mode disabled.";
    private static final String RETRIEVED_TENANT_PHYSICAL_CONFIG = "Retrieving tenant physical config completed.";

    private static final long MILLIS_IN_SECOND = 1000;
    private final MDMCleanTenantClient mdmCleanTenantClient;
    private final MDMClient mdmClient;

    public MaintenanceModeTaskExecutionService(String jonId,
                                               MaintenanceModeTaskInstance taskDetail,
                                               GrafanaDashboardGBQService grafanaDashboardGBQService,
                                               MDMCleanTenantClient mdmCleanTenantClient,
                                               MDMClient mdmClient) {
        super(jonId, taskDetail, grafanaDashboardGBQService);
        this.mdmCleanTenantClient = mdmCleanTenantClient;
        this.mdmClient = mdmClient;
    }

    @Override
    public void executeTask() throws InterruptedException {
        if (!taskDetail.getEvents().contains(MAINTANANCE_MODE_DISABLED) && !taskDetail.getEvents().contains(MAINTANANCE_MODE_ENABLED)) {
            TenantConfiguration tenantConfiguration = mdmClient.getTenantConfiguration(taskDetail.getEnvId(), taskDetail.getTenantName());
            taskDetail.addEvent(RETRIEVED_TENANT_PHYSICAL_CONFIG);
            if (tenantConfiguration.getMaintenance() == null ||
                    !tenantConfiguration.getMaintenance().equals(taskDetail.getValueToSet())) {
                if (taskDetail.getValueToSet().equals(Boolean.TRUE)) {
                    mdmCleanTenantClient.enableMaintenanceModeForTenant(taskDetail.getEnvId(), taskDetail.getTenantName());
                    taskDetail.addEvent(MAINTANANCE_MODE_ENABLED);
                } else {
                    mdmCleanTenantClient.disableMaintenanceModeForTenant(taskDetail.getEnvId(), taskDetail.getTenantName());
                    taskDetail.addEvent(MAINTANANCE_MODE_DISABLED);
                }
            } else {
                throw new PlatformManagementException(PlatformManagementErrorCode.INCORRECT_TENANT_STATE,
                        HttpStatus.BAD_REQUEST.value(), taskDetail.getTenantName(),
                        tenantConfiguration.getMaintenance().toString());
            }
            taskDetail.setModeChangeTimestamp(System.currentTimeMillis());
            taskDetail.setStatus(TaskStatus.WAITING);
        } else {
            waitAfterMaintenanceModeChange();
        }
    }

    private void waitAfterMaintenanceModeChange() {
        if (System.currentTimeMillis() - taskDetail.getModeChangeTimestamp() < taskDetail.getSecondsDelay() * MILLIS_IN_SECOND) {
            taskDetail.setStatus(TaskStatus.WAITING);
        } else {
            taskDetail.setStatus(TaskStatus.COMPLETED);
        }
    }
}
