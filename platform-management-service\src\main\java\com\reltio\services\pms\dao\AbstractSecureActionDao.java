package com.reltio.services.pms.dao;

import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.proxy.SecureAction;

/**
 * This class provides the standard way to get the specific document under the environment document which would be used for adding any new Action types.
 */
public abstract class AbstractSecureActionDao<T extends SecureAction> extends AbstractRootCollectionDao<T> {

    public AbstractSecureActionDao(CredentialsProvider provider, String collectionPrefix, String environment, ReltioUserHolder reltioUserHolder) {
        super(provider, collectionPrefix, environment, reltioUserHolder);
    }
}
