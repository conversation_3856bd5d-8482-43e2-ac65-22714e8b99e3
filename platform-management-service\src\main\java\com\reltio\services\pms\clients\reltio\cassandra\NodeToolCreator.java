package com.reltio.services.pms.clients.reltio.cassandra;

import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
public class NodeToolCreator {
    public NodetoolInstance getNodetool(String host, int port, String username, String password) throws IllegalAccessException, NoSuchFieldException, IOException {
        return new NodetoolInstance(host, port, username, password);
    }
}
