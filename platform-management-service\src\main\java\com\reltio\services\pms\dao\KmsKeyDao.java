package com.reltio.services.pms.dao;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.model.KmsKeyDetails;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class KmsKeyDao extends AbstractLevel1CollectionDao<KmsKeyDetails> {

    private static final String KEY_COLLECTION_NAME = "KmsKeys";

    private final ReltioTenantDao reltioTenantDao;

    @Autowired
    public KmsKeyDao(CredentialsProvider provider, ReltioTenantDao reltioTenantDao,
                     ReltioUserHolder reltioUserHolder){
        super(provider, reltioTenantDao, KEY_COLLECTION_NAME, reltioUserHolder);
        this.reltioTenantDao = reltioTenantDao;
    }
    public KmsKeyDetails saveKmsKeyDetails(KmsKeyDetails kmsKeyDetails) {
        String tenantId = kmsKeyDetails.getTenantId();
        if (isDatabaseProvisioned(tenantId)) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR,
                    HttpStatus.BAD_REQUEST.value(), "Cannot modify KMS Key details for tenant " + tenantId + " because database is already provisioned.");
        }
        create(tenantId, kmsKeyDetails);
        return kmsKeyDetails;
    }
    public KmsKeyDetails getKmsKeyDetailsByTenantId(String tenantId) throws InvalidDocumentIdException {
        return get(tenantId, tenantId);
    }
    public void deleteKmsKeyDetails(String tenantId) {
        if (isDatabaseProvisioned(tenantId)) {
            throw new PlatformManagementException(
                    PlatformManagementErrorCode.INTERNAL_DB_ERROR,
                    HttpStatus.BAD_REQUEST.value(),
                    "Cannot delete KMS Key details for tenant " + tenantId + " because the database is already provisioned."
            );
        }
        delete(tenantId, tenantId);
    }
    public boolean isDatabaseProvisioned(String tenantId) {
        try {
            ReltioTenant tenant = reltioTenantDao.get(tenantId);
            if (tenant != null && tenant.getTenantConfigurationValues() != null && tenant.getTenantConfigurationValues().getDataStorage() != null) {
                return tenant.getTenantConfigurationValues().getDataStorage().getDatabaseId() != null;
            }
            return false;
        }catch (InvalidDocumentIdException ex) {
            log.error("Failed to retrieve tenant details for tenant ID: {}", tenantId, ex);
            return false;
        }
    }

    @Override
    protected TypeReference<KmsKeyDetails> getTypeReference() {
        return new TypeReference<>() {
        };
    }
}