package com.reltio.services.pms.common.model.jobs;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.TenantPurpose;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Getter
@Setter
public class JobDetail {

    private String mdmTenantId;
    private Set<String> authUsers;
    private Set<JobDetail.ApplicationClients> clients;
    private String customerId;
    private final String productEdition;
    private final String rdmTenantId;
    private Map<String, StreamingDetails> streamingDetails;
    private S3Details s3Details;
    private String tenantName;
    private TenantPurpose tenantPurpose;
    private String reltioRegion;
    private String geographicalRegion;
    private String apiUrl;
    public String uiUrl;
    private String authUrl;
    private String rdmUrl;
    private Map<String, SfdcUserDetails> sfdcUserDetailsMap;
    private TenantSize tenantSize;
    private String region;
    private Map<String,AzureBlobStorageDetails> azureBlobStorageDetails;

    private JobDetail(String mdmTenantId, Set<String> authUsers, Set<JobDetail.ApplicationClients> clients, String customerId, String productEdition, String rdmTenantId, Map<String, StreamingDetails> streamingDetails) {
        this.mdmTenantId = mdmTenantId;
        this.authUsers = authUsers;
        this.clients = clients;
        this.customerId = customerId;
        this.productEdition = productEdition;
        this.rdmTenantId = rdmTenantId;
        this.streamingDetails = streamingDetails;

    }

    public JobDetail(String mdmTenantId, Set<String> authUsers, Set<ApplicationClients> clients, String customerId, String productEdition, String rdmTenantId, Map<String, StreamingDetails> streamingDetails, S3Details s3Details, String tenantName, TenantPurpose tenantPurpose, String reltioRegion, String geographicalRegion, String apiUrl, String uiUrl, String rdmUrl, String authUrl, String region, Map<String, SfdcUserDetails> sfdcUserDetails, TenantSize tenantSize, Map<String,AzureBlobStorageDetails> azureBlobStorageDetails) {
        this.mdmTenantId = mdmTenantId;
        this.authUsers = authUsers;
        this.clients = clients;
        this.customerId = customerId;
        this.productEdition = productEdition;
        this.rdmTenantId = rdmTenantId;
        this.streamingDetails = streamingDetails;
        this.s3Details = s3Details;
        this.tenantName = tenantName;
        this.tenantPurpose = tenantPurpose;
        this.reltioRegion = reltioRegion;
        this.geographicalRegion = geographicalRegion;
        this.apiUrl = apiUrl;
        this.uiUrl = uiUrl;
        this.rdmUrl = rdmUrl;
        this.authUrl = authUrl;
        this.region = region;
        this.sfdcUserDetailsMap = sfdcUserDetails;
        this.tenantSize = tenantSize;
        this.azureBlobStorageDetails = azureBlobStorageDetails;
    }


    public static JobDetail createJobDetail(String mdmTenantId, Set<String> authUsers, Set<JobDetail.ApplicationClients> clients, String customerId, String productEdition, String rdmTenantId, Map<String, StreamingDetails> streamingDetails,
                                            S3Details s3DetailsMap, String tenantName, TenantPurpose tenantPurpose, String reltioRegion, String geographicalRegion, String apiUrl, String uiUrl, String rdmUrl, String authUrl, String region,  Map<String, SfdcUserDetails> sfdcUserDetails, TenantSize tenantSize, Map<String,AzureBlobStorageDetails> azureBlobStorageDetails) {
        return new JobDetail(mdmTenantId, authUsers, clients, customerId, productEdition, rdmTenantId, streamingDetails, s3DetailsMap, tenantName, tenantPurpose, reltioRegion, geographicalRegion, apiUrl, uiUrl, rdmUrl, authUrl, region, sfdcUserDetails, tenantSize, azureBlobStorageDetails);
    }

    public Map<String, SfdcUserDetails> getSfdcUserDetails() {
        return sfdcUserDetailsMap;
    }

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Getter
    @Setter
    public static class StreamingDetails {

        private List<String> queueArn;

        private List<String> subscriptionName;

        private List<String> queueName;

        private String serviceBusNamespaceName;

        private JsonNode accessKey;

        public StreamingDetails(List<String> queueArn,
                                List<String> subscriptionName,
                                JsonNode accessKey,
                                String serviceBusNamespaceName,
                                List<String> queueName) {
            this.queueArn = queueArn;
            this.subscriptionName = subscriptionName;
            this.accessKey = accessKey;
            this.serviceBusNamespaceName = serviceBusNamespaceName;
            this.queueName = queueName;
        }
    }

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Getter
    @Setter
    public static class ApplicationClients {
        private String clientName;

        private String clientSecret;


        public ApplicationClients(String clientName, String clientSecret) {
            this.clientName = clientName;
            this.clientSecret = clientSecret;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }

            ApplicationClients that = (ApplicationClients) o;
            return Objects.equals(getClientName(), that.getClientName()) &&
                    Objects.equals(getClientSecret(), that.getClientSecret());
        }

        @Override
        public int hashCode() {
            return Objects.hash(getClientName(), getClientSecret());
        }
    }

    @Getter
    @Setter
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class S3Details {
        private String s3BucketName;

        private String s3BucketArn;

        private String accessKey;

        private String secretKey;

        public S3Details(String s3BucketName, String s3BucketArn, String accessKey, String secretKey) {
            this.s3BucketName = s3BucketName;
            this.s3BucketArn = s3BucketArn;
            this.accessKey = accessKey;
            this.secretKey = secretKey;
        }

    }

    @Getter
    @Setter
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class SfdcUserDetails {
        private String username;

        private String email;

        private String password;

        public SfdcUserDetails(String username, String email, String password) {
            this.username = username;
            this.email = email;
            this.password = password;
        }

    }

    @Getter
    @Setter
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class AzureBlobStorageDetails {
        private String storageAccountName;

        private String key;

        private String connectionString;

        public AzureBlobStorageDetails(String storageAccountName, String key, String connectionString) {
            this.storageAccountName = storageAccountName;
            this.key = key;
            this.connectionString = connectionString;
        }

    }

}


