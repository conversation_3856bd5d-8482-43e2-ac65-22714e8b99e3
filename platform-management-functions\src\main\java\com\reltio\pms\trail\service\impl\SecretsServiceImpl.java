package com.reltio.pms.trail.service.impl;

import com.google.cloud.secretmanager.v1.AccessSecretVersionResponse;
import com.google.cloud.secretmanager.v1.SecretManagerServiceClient;
import com.google.cloud.secretmanager.v1.SecretVersionName;
import com.google.gson.Gson;
import com.reltio.pms.trail.domain.AuthSecrets;
import com.reltio.pms.trail.service.SecretsService;

import java.io.IOException;

import static com.reltio.pms.trail.FunctionConstants.*;

public class SecretsServiceImpl implements SecretsService {

    private static final Gson gson = new Gson();

    public SecretsServiceImpl() {
    }

    @Override
    public AuthSecrets getSecrets() {
        try(SecretManagerServiceClient secretManagerServiceClient = SecretManagerServiceClient.create()) {
           // SecretManagerServiceClient secretManagerServiceClient = SecretManagerServiceClient.create();
            SecretVersionName secretVersionName = SecretVersionName.of(System.getenv().get(PROJECT_ID), System.getenv().get(SECRET_NAME), System.getenv().get(SECRET_VERSION));
            AccessSecretVersionResponse secretVersionResponse = secretManagerServiceClient.accessSecretVersion(secretVersionName);
            return gson.fromJson(secretVersionResponse.getPayload().getData().toStringUtf8(), AuthSecrets.class);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }

    }
}
