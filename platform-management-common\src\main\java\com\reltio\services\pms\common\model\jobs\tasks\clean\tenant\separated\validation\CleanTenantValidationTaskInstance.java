package com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.separated.validation;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.Parameters;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class CleanTenantValidationTaskInstance extends TaskInstance {

    private final String tenantName;

    private final List<String> events;

    private final List<String> currentState;

    private final Parameters parameters;

    private final Long duration;

    @JsonCreator
    public CleanTenantValidationTaskInstance(@JsonProperty(value = "taskId") String taskId,
                                             @JsonProperty(value = "name") String name,
                                             @JsonProperty(value = "jobId") String jobId,
                                             @JsonProperty(value = "startTime") Long startTime,
                                             @JsonProperty(value = "finishTime") Long finishTime,
                                             @JsonProperty(value = "status") TaskStatus status,
                                             @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                             @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                             @JsonProperty(value = "executingNodeName") String executingNodeName,
                                             @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                             @JsonProperty(value = "envId") String envId,
                                             @JsonProperty(value = "tenantName") String tenantName,
                                             @JsonProperty(value = "events") List<String> events,
                                             @JsonProperty(value = "currentState") List<String> currentState,
                                             @JsonProperty(value = "parameters") Parameters parameters) {
        super(taskId, name, jobId, startTime, finishTime, TaskType.DELETE_TENANT_DATA_VALIDATION_TASK, status, lastUpdatedTime,
                taskFailureContext, executingNodeName, serviceNodeStatus, envId);
        this.tenantName = tenantName;
        this.events = events == null ? new ArrayList<>() : new ArrayList<>(events);
        this.currentState = currentState == null ? new ArrayList<>() : new ArrayList<>(currentState);
        this.parameters = parameters;
        if (finishTime == null) {
            this.duration = startTime == 0 ? 0 : System.currentTimeMillis() - startTime;
        } else {
            this.duration = finishTime - startTime;
        }
    }

    public String getTenantName() {
        return tenantName;
    }

    public List<String> getEvents() {
        return events;
    }

    public Parameters getParameters() {
        return parameters;
    }

    public Long getDuration() {
        return duration;
    }

    public void addEvent(String event) {
        events.add(event);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        CleanTenantValidationTaskInstance that = (CleanTenantValidationTaskInstance) o;
        return tenantName.equals(that.tenantName) && Objects.equals(events, that.events) && Objects.equals(currentState, that.currentState) && parameters.equals(that.parameters) && Objects.equals(duration, that.duration);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), tenantName, events, currentState, parameters, duration);
    }
}
