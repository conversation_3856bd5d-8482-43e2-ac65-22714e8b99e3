package com.reltio.services.pms.common.model;


import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.Map;

import static com.reltio.services.pms.common.model.RequesterActivityConstant.*;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RequesterActivity {

    @JsonProperty("requesterEmail")
    private String requesterEmail;

    @JsonProperty("activityType")
    private ActivityType activityType;


    @JsonProperty(CREATION_TIME_FIELD_NAME)
    private long creationTime;

    private Map<String, String> activityFields = new HashMap<>();

    @JsonCreator
    public RequesterActivity( @JsonProperty("requesterEmail") String requesterEmail,
                             @JsonProperty("activityType") ActivityType activityType) {
        this.requesterEmail = requesterEmail;
        this.activityType = activityType;
    }

    @JsonAnyGetter
    public Map<String, String> getActivityFields() {
        return activityFields;
    }

    @JsonAnySetter
    public void setActivityField(String name, String value) {
        activityFields.put(name, value);
    }

    public String getField(String name){
        return activityFields.get(name);
    }

    public boolean containsField(String name){
        return activityFields.containsKey(name);
    }

    public String getRequesterEmail() {
        return requesterEmail;
    }

    public ActivityType getActivityType() {
        return activityType;
    }

    public long getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(long creationTime) {
        this.creationTime = creationTime;
    }
}
