package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.tenant.Invitee;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DeletedUserDao extends AbstractLevel1CollectionDao<Invitee> {
    private static final String DELETED_USERS_COLLECTION_NAME = "DELETED_USERS";

    @Autowired
    public DeletedUserDao(CredentialsProvider provider, ReltioTenantDao reltioTenantDao, ReltioUserHolder reltioUserHolder) {
        super(provider, reltioTenantDao, DELETED_USERS_COLLECTION_NAME, reltioUserHolder);
    }

    @Override
    protected TypeReference<Invitee> getTypeReference() {
        return new TypeReference<Invitee>() {
        };
    }
}