package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Setter
public class PMSConfig extends BaseFirestoreEntity {

    @JsonProperty("configName")
    private String configName;

    @Getter
    @JsonProperty("config")
    private Map<String, Object> config;

    @JsonCreator
    public PMSConfig(@JsonProperty("configName") String configName) {
        this.configName = configName;
    }

    @Override
    public String getID() {
        return configName;
    }

}
