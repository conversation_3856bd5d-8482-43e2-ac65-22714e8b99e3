package com.reltio.services.pms.convertors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.convertors.sales.SalesConfigDeserializer;


public class CustomObjectMapper extends ObjectMapper {


    public CustomObjectMapper() {
        super();

        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addDeserializer(SalesConfig.class, new SalesConfigDeserializer(SalesConfig.class));
        registerModule(simpleModule);
    }


}
