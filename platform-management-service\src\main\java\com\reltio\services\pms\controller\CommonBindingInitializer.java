package com.reltio.services.pms.controller;

import com.reltio.services.pms.common.model.OfferType;
import com.reltio.services.pms.common.model.OfferTypePropertyEditor;
import com.reltio.services.pms.common.model.tenant.OfferFunction;
import com.reltio.services.pms.common.model.tenant.OfferFunctionPropertyEditor;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.InitBinder;

@ControllerAdvice
public class CommonBindingInitializer {

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        binder.registerCustomEditor(OfferType.class, new OfferTypePropertyEditor());

        binder.registerCustomEditor(OfferFunction.class, new OfferFunctionPropertyEditor());
    }
}