package com.reltio.services.pms.common.model.jobs.tasks.updateR360Tenant;

import com.reltio.services.pms.common.model.jobs.tasks.TaskInstanceParams;
import com.reltio.services.pms.common.sales.TenantPurpose;

public interface UpdateR360TenantTaskInstanceParms extends TaskInstanceParams {


    String getmdmTenantID();

    String getShortDescription();

    String getTenantRecordType();

    String getOwnedByReltioDept();

    String getContractId();

    String getRdmTenantId();

    TenantPurpose getTenantPurpose();






}
