package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class TenantsCloneContent extends BaseFirestoreEntity{

    @JsonProperty(value = "id")
    private String id;

    @JsonProperty(value = "sourceTenantConfigs")
    private Map<String,Object> sourceTenantConfigs;

    @JsonProperty(value = "targetTenantConfigs")
    private Map<String,Object> targetTenantConfigs;

    @JsonProperty(value = "sourceTenantId")
    private String sourceTenantId;

    @JsonProperty(value = "targetTenantId")
    private String targetTenantId;

    @JsonProperty(value = "sourceEnvId")
    private String sourceEnvId;

    @JsonProperty(value = "targetEnvId")
    private String targetEnvId;

    @JsonProperty(value = "deleteAfterDays")
    private int deleteAfterDays;

    @Override
    public String getID() {
        return id;
    }

}
