package com.reltio.services.pms.service.compliance;

import com.reltio.services.pms.common.model.compliance.request.*;
import com.reltio.services.pms.common.model.compliance.response.ApiUsageResponse;
import com.reltio.services.pms.common.model.compliance.response.CpUsageResponse;
import com.reltio.services.pms.common.model.compliance.response.RsuUsageResponse;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByEndpointResponse;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByEnvironmentModel;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByTenantModel;
import com.reltio.services.pms.common.model.compliance.response.cpusage.ApiByMonthResponse;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByMonthResponse;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuBreakdownResponse;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuByTenantResponse;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuUsageByDateResponse;
import com.reltio.services.pms.common.model.compliance.response.TaskUsageResponse;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByDateResponse;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByTenantResponse;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByDateResponse;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByTenantResponse;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuUsageByMonthResponse;
import com.reltio.services.pms.common.sales.TenantPurpose;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * The interface Compliance service.
 */
public interface ComplianceService {


    /**
     * Gets task usage.
     *
     * @param complianceRequest the compliance request
     * @return the task usage
     */
    List<TaskUsageResponse> getTaskUsage(ComplianceRequest complianceRequest);


    /**
     * Gets cp usage.
     *
     * @param cpUsageRequest the cp usage request
     * @param export         the export
     * @return the cp usage
     */
    List<CpUsageResponse> getCpUsage(CpUsageRequest cpUsageRequest,Boolean export);


    /**
     * Gets api usage.
     *
     * @param apiUsageRequest the api usage request
     * @return the api usage
     */
    Collection<ApiUsageResponse> getApiUsage(ApiUsageRequest apiUsageRequest);

    /**
     * Gets rsu usage.
     *
     * @param complianceRequest the rsu usage request
     * @return the rsu usage
     */
    List<RsuUsageResponse> getRsuUsage(ComplianceRequest complianceRequest);

    /**
     * Gets rsu usage by tenant.
     *
     * @param complianceReportRequest the compliance report request
     * @param tenantPurposeMap        the tenant purpose map
     * @param export                  the export
     * @return the rsu usage by tenant
     */
    RsuByTenantResponse getRsuUsageByTenant(ComplianceReportRequest complianceReportRequest, Map<String, TenantPurpose> tenantPurposeMap, Boolean export);


    /**
     * Gets cp usage by date.
     *
     * @param cpUsageRequest the cp usage request
     * @param tenantIdList   the tenant id list
     * @param export         the export
     * @return the cp usage by date
     */
    CpUsageByDateResponse getCpUsageByDate(CpUsageReportRequest cpUsageRequest, List<String> tenantIdList, Boolean export);

    /**
     * Gets cp usage by month.
     *
     * @param cpUsageRequest the cp usage request
     * @param tenantIdList   the tenant id list
     * @return the cp usage by month
     */
    CpUsageByMonthResponse getCpUsageByMonth(CpUsageReportRequest cpUsageRequest, List<String> tenantIdList);

    /**
     * Gets cp usage by tenant.
     *
     * @param complianceReportRequest the compliance report request
     * @param tenantIdList            the tenant id list
     * @param export                  the export
     * @return the cp usage by tenant
     */
    CpUsageByTenantResponse getCpUsageByTenant(CpUsageReportRequest complianceReportRequest, List<String> tenantIdList, Boolean export);

    /**
     * Gets rsu usage breakdown.
     *
     * @param complianceReportRequest the compliance report request
     * @param tenantPurposeMap            the tenant id and purpose map
     * @param export                  the export
     * @return the rsu usage breakdown
     */
    RsuBreakdownResponse getRsuUsageBreakdown (ComplianceReportRequest complianceReportRequest, List<String> tenantPurposeMap, Boolean export);

    /**
     * Gets api usage by date.
     *
     * @param apiUsageRequest the api usage request
     * @param tenantIdList   the tenant id list
     * @param export         the export
     * @return the api usage by date
     */
    ApiUsageByDateResponse getApiUsageByDate(ApiUsageReportRequest apiUsageRequest, List<String> tenantIdList, Boolean export);

    /**
     * Gets api usage by tenant.
     *
     * @param apiUsageReportRequest the api usage report request
     * @param tenantIdList          the tenant id list
     * @param export                the export
     * @return the api usage by tenant
     */
    ApiUsageByTenantResponse getApiUsageByTenant(ApiUsageReportRequest apiUsageReportRequest, List<String> tenantIdList, Boolean export);

    /**
     * Gets api usage by endpoint.
     *
     * @param apiUsageReportRequest the api usage report request
     * @param tenantIdList          the tenant id list
     * @param export                the export
     * @return the api usage by endpoint
     */
    ApiUsageByEndpointResponse getApiUsageByEndpoint(ApiUsageReportRequest apiUsageReportRequest, List<String> tenantIdList, Boolean export);

    /**
     * Gets api usage by date.
     *
     * @param apiUsageRequest the api usage request
     * @param tenantIdList   the tenant id list
     * @return the api usage by date
     */
    ApiByMonthResponse getApiUsageByMonth(ComplianceReportRequest apiUsageRequest, List<String> tenantIdList);


    /**
     * Gets rsu usage by date.
     *
     * @param rsuUsageRequest the cp usage request
     * @param tenantPurposeMap   the tenant id and purpose map
     * @param export         the export
     * @return the cp usage by date
     */
    RsuUsageByDateResponse getRsuUsageByDate(ComplianceReportRequest rsuUsageRequest, Map<String, TenantPurpose> tenantPurposeMap, Boolean export);


    /**
     * Gets billing report by environment.
     *
     * @param billingReportRequest the api usage report request
     * @param tenantPurposeMap          the tenant purpose map
     * @return the api usage by tenant
     */
    List<BillingReportByEnvironmentModel> getBillingByEnvironment(ComplianceReportRequest billingReportRequest, Map<String,TenantPurpose> tenantPurposeMap);

    /**
     * Gets billing report by customer.
     *
     * @param billingReportRequest the task usage report request
     * @param tenantPurposeMap          the tenant purpose map
     * @return the task usage by tenant
     */
    List<BillingReportByTenantModel> getBillingByTenant(ComplianceReportRequest billingReportRequest, Map<String,TenantPurpose> tenantPurposeMap);

    RsuUsageByMonthResponse getRsuUsageByMonth(ComplianceReportRequest rsuUsageReportRequest, Map<String, TenantPurpose> tenantIdWithPurpose);
}

