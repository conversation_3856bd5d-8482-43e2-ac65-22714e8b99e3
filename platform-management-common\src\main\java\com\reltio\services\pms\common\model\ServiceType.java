package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.util.StdConverter;

import java.util.Arrays;

public enum ServiceType {
    WORKFLOW("workflow"),
    MATCH_IQ("ri"),
    RI_API("riapi"),
    DTSS("dtss"),
    CASSANDRA("cassandra"),
    DYNAMO_DB("dynamodb"),
    ES("elasticsearch"),
    API("api"),
    UI("ui"),
    DL("dataload"),
    DATAPROCESS("dataprocess"),
    QUEUEPROCESS("queueprocess"),
    EXPORT("export"),
    WORKFLOW_UI("workflowui"),
    BPMN("bpmn"),
    DQ_UI("dqui"),
    DQ("dq"),
    DATALOADER("dataloader"),
    TPS("throttling"),
    IRS("irs"),
    CLEANSE("cleanse"),
    CONNECTOR("connector"),
    AUTH("auth"),
    TESTLINK("testlink"),
    VALIDATION("validation"),
    CSERVICE("cservice"),
    LCA("lca"),
    SSO("sso"),

    ROUTER("router"),
    EM_UI("environment"),
    REPORT("tenantreports"),
    ELK("elk"),
    GRAFANA("grafana"),
    VORMETRIC("vormetric"),
    GBT("gbt"),
    BQSU("bqsu"),
    HZ("hazelcast"),
    DEVOPSAPI("devopsapi"),
    COLLABORATION("collaboration"),
    LOGSTASH("logstash"),
    CONSOLE("console"),
    GRAPH("graph"),
    TALENA("talena"),
    BASTION("bastion"),
    CBROWSER("cbrowser"),
    SFTP("sftp"),
    DSM("dsm"),
    GENERAL("general"),
    ML("ml"),
    CONFIG_SERVICE("config-service"),
    WORKATO("workato"),
    SPANNERDB("spannerdb"),
    COSMOSDB("cosmosdb"),
    DPH("dph"),
    NOTIFICATION("notification"),
    RDM_CONSOLE("rdm"),
    RDM_API("rdmapi"),
    RDM_UI("rdmui"),
    RDM_WORKFLOW_SERVICE("rdm-workflow-service"),
    ACTIVITYLOG("activitylog"),
    IH("ih"),
    WORKATO_CONSOLE("workato-console"),
    DNB_CONNECTOR_TEST("dnb-connector-test"),
    DNB_CONNECTOR_PROD("dnb-connector-prod"),
    DATA_VALIDATION_SERVICE("dvf"),
    RELTIO_SHIELD_SERVICE("reltio-shield-service"),
    NOTIFICATION_SERVICE("notification-service"),
    SFDC_CONNECTOR_SERVICE("sfdc-connector-service"),
    SAML_CONFIG("saml-config");

    private final String value;

    @JsonValue
    public String getValue() {
        return value;
    }

    ServiceType(String value) {
        this.value = value;
    }

    public static ServiceType convertFromString(String value) {
        return Arrays.stream(ServiceType.values())
                .filter(e -> e.value.equals(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid value '" + value + "'"));

    }

    public static final class ServiceTypeConverter extends StdConverter<String, ServiceType> {

        @Override
        public ServiceType convert(String value) {
            return ServiceType.convertFromString(value);
        }
    }

}
