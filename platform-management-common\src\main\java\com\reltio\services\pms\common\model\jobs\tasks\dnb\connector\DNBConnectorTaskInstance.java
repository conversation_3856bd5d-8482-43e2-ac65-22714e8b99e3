package com.reltio.services.pms.common.model.jobs.tasks.dnb.connector;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.enterprise.contracts.DTSSType;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServiceEnablementBaseTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class DNBConnectorTaskInstance extends ServiceEnablementBaseTaskInstance {

    @JsonProperty
    private final String dataTenantId;

    @JsonProperty
    private final DTSSType dtssSubscriptionType;

   @JsonProperty(value="configureProfileMappingsWhenProfileCreated")
   private final List<Boolean> configureProfileMappingsWhenProfileCreated;

    @JsonCreator
    public DNBConnectorTaskInstance(
            @JsonProperty(value = "taskId") String taskId,
            @JsonProperty(value = "name") String name,
            @JsonProperty(value = "jobId") String jobId,
            @JsonProperty(value = "startTime") Long startTime,
            @JsonProperty(value = "finishTime") Long finishTime,
            @JsonProperty(value = "status") TaskStatus status,
            @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
            @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
            @JsonProperty(value = "executingNodeName") String executingNodeName,
            @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
            @JsonProperty(value = "envId") String envId,
            @JsonProperty(value = "failedTenants") Set<String> failedTenants,
            @JsonProperty(value = "totalProcessedTenants", defaultValue = "0") int totalProcessedTenants,
            @JsonProperty(value = "tenantsForServiceEnablement") Set<String> tenantsForServiceEnablement,
            @JsonProperty(value="events") Map<String,Set<String>> events,
            @JsonProperty(value = "dataTenantId") String dataTenantId,
            @JsonProperty(value = "dtssSubscriptionType") DTSSType dtssSubscriptionType,
            @JsonProperty(value="configureProfileMappingsWhenProfileCreated")List<Boolean> configureProfileMappingsWhenProfileCreated
    ) {
        super(taskId, name, jobId, startTime, finishTime, TaskType.DNB_CONNECTOR_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId, failedTenants, totalProcessedTenants,
                tenantsForServiceEnablement,events);
        this.dataTenantId = dataTenantId;
        this.dtssSubscriptionType = dtssSubscriptionType;
        this.configureProfileMappingsWhenProfileCreated=configureProfileMappingsWhenProfileCreated==null?new ArrayList<>() : new ArrayList<>(configureProfileMappingsWhenProfileCreated);


    }

    public String getDataTenantId() {
        return dataTenantId;
    }

    public DTSSType getDtssSubscriptionType() {
        return dtssSubscriptionType;
    }

    public List<Boolean> getConfigureProfileMappingsWhenProfileCreated(){return configureProfileMappingsWhenProfileCreated;}

    public void addConfigureProfileMappingsWhenProfileCreated(Boolean value){configureProfileMappingsWhenProfileCreated.add(value);}
}
