package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.collection.CollectionUtils;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import com.reltio.services.pms.common.model.CustomerType;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantConfigurationValues;
import com.reltio.services.pms.common.sales.TenantPurpose;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.EnumMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Predicate;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ReltioTenant extends BaseFirestoreEntity {

    @JsonProperty("additionalProducts")
    Map<PMSProductName, BaseProductConfig> additionalProducts = new EnumMap<>(PMSProductName.class);
    @JsonProperty("tenantId")
    private String tenantId;
    @JsonProperty("tenantName")
    private String tenantName;
    @JsonProperty("accountId")
    private String accountId;
    @JsonProperty("subscriptionId")
    private String subscriptionId;
    @JsonProperty("salesConfig")
    private SalesConfig salesConfig;
    @Deprecated
    /* Used tenantPurpose*/
    @JsonProperty("purpose")
    private TenantPurpose purpose;
    @JsonProperty("tenantPurpose")
    private TenantPurpose tenantPurpose;
    @JsonProperty("reltioEnv")
    private String reltioEnv;
    @JsonProperty("deploymentCloud")
    private String deploymentCloud;
    @JsonProperty("deploymentRegion")
    private String deploymentRegion;
    @JsonProperty("isHipaa")
    private boolean isHipaa;
    @JsonProperty("industry")
    private String industry;
    @JsonProperty("productEdition")
    private String productEdition;
    //same as companyName/customerLongName
    @JsonProperty("companyName")
    private String companyName;

    //same as customerId
    @JsonProperty("authCustomerId")
    private String authCustomerId;

    @JsonProperty("owners")
    private Set<String> owners;

    @JsonProperty("contractId")
    private String contractId;

    @JsonProperty("packageId")
    private String packageId;

    @JsonProperty("tenantInternalInfo")
    private TenantInternalInfo tenantInternalInfo;

    @JsonProperty("tenantConfigurationValues")
    private TenantConfigurationValues tenantConfigurationValues;

    @JsonProperty("tenantStatus")
    private TenantStatus tenantStatus;

    @JsonProperty("customerType")
    private CustomerType customerType;

    @JsonProperty("division")
    private String division;

    @JsonProperty("department")
    private String department;

    @JsonProperty("costCenter")
    private String costCenter;

    @JsonProperty("endDate")
    private Date endDate;

    private DeltaStatus deltaStatus;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map<String, Object> changeProperties;

    @JsonProperty("reltioPackageType")
    private ReltioPackageType reltioPackageType;

    @JsonProperty(value = "processing")
    private Boolean processing = Boolean.FALSE;

    @JsonProperty(value = "parentJobId")
    private String parentJobId;

    @JsonProperty("opportunityId")
    private String opportunityId;

    @JsonProperty("initialOwner")
    private String initialOwner;

    @Override
    public String getID() {
        return tenantId;
    }




    public void updateOwnershipDetails(ReltioTenant updatedTenant, ReltioTenant existingTenant) {
        updateField(updatedTenant.getOwners(), existingTenant::setOwners, CollectionUtils::isNotEmpty);
        updateField(updatedTenant.getDivision(), existingTenant::setDivision, Objects::nonNull);
        updateField(updatedTenant.getDepartment(), existingTenant::setDepartment, Objects::nonNull);
        updateField(updatedTenant.getCostCenter(), existingTenant::setCostCenter, Objects::nonNull);
        updateField(updatedTenant.getCustomerType(), existingTenant::setCustomerType, Objects::nonNull);
        updateField(updatedTenant.getEndDate(), existingTenant::setEndDate, Objects::nonNull);
    }

    private static <T> void updateField(T value, Consumer<T> setter, Predicate<T> condition) {
        Optional.ofNullable(value)
                .filter(condition)
                .ifPresent(setter);
    }
}
