package com.reltio.services.pms.clients.reltio.fern;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.reltio.io.IOUtils;
import com.reltio.services.pms.common.IPMSRestTemplate;
import com.reltio.services.pms.service.jobs.tasks.provisioning.mdm.MdmProvisioningTaskService;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

@Service
@Retryable(exceptionExpression = "${retry.shouldRetry:true}", maxAttempts = 5, backoff = @Backoff(delay = 1000, multiplier = 5))
public class FERNClient {
    private static final Logger LOGGER = LogManager.getLogger(FERNClient.class);
    private static final String CONFIG_URL = "%s/service/%s/?environment=%s&tenant=%s";
    private static final String NAMESPACE = "dataModeller";
    private static final String AVAILABLE_FEATURES = "availableFeatures";
    private static final String MATCH_IQ_MODELS = "matchIqModels";
    private static final String DATA = "data";
    private final IPMSRestTemplate rest;

    @Autowired
    public FERNClient(@Qualifier("console") IPMSRestTemplate rest) {
        this.rest = rest;
    }

    public JsonNode getConfiguration(String env, String tenantId, String serviceUrl) {
        String url = getUrl(env, tenantId, serviceUrl);
        return rest.getForObject(url, JsonNode.class);
    }

    public boolean deprovisionFern(String env, String tenantId, String serviceUrl) {
        String url = getUrl(env, tenantId, serviceUrl);
        try {
            JsonNode existingConfig = getConfiguration(env, tenantId, serviceUrl);

            if (existingConfig == null) {
                String file = IOUtils.readResource(MdmProvisioningTaskService.class, "mdm/mlDisablePostRequest.json");
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode disableConfig = objectMapper.readTree(file);
                rest.postForObject(url, disableConfig, JsonNode.class);
            } else {
                JsonNode availableFeatures = existingConfig.path(DATA).path(AVAILABLE_FEATURES);
                if (availableFeatures.isObject()) {
                    ObjectNode updatedFeatures = (ObjectNode) availableFeatures;
                    if (updatedFeatures.has(MATCH_IQ_MODELS)) {
                        updatedFeatures.put(MATCH_IQ_MODELS, false);
                    } else {
                        LOGGER.warn("matchIqModels field not found in availableFeatures.");
                        return false;
                    }
                    ObjectNode dataNode = new ObjectMapper().createObjectNode();
                    dataNode.set(AVAILABLE_FEATURES, updatedFeatures);
                    rest.put(url, dataNode);
                } else {
                    LOGGER.warn("Invalid availableFeatures structure in existing configuration.");
                    return false;
                }
            }
            JsonNode confirmationConfig = getConfiguration(env, tenantId, serviceUrl);
            return  !(confirmationConfig.get(DATA).get(AVAILABLE_FEATURES).get(MATCH_IQ_MODELS).asBoolean());

        } catch (HttpClientErrorException.NotFound ex) {
            LOGGER.warn("Fern configuration not found for this tenant");
            return false;
        } catch (Exception ex) {
            LOGGER.error(String.format("Exception occurred while deprovisioning Fern for tenant %s: %s", tenantId, ex.getMessage()), ex);
            return false;
        }
    }


    private static String getUrl(String env, String tenantId, String serviceUrl) {
        return String.format(CONFIG_URL, serviceUrl, NAMESPACE, env, tenantId);
    }
}
