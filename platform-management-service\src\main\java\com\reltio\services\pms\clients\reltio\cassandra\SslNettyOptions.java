package com.reltio.services.pms.clients.reltio.cassandra;

import com.datastax.driver.core.NettyOptions;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.ssl.SslHandler;

// Base implementation: each time a connection is established, create an SSL handler
// and add it to the pipeline:
public abstract class SslNettyOptions extends NettyOptions {
    @Override
    public void afterChannelInitialized(SocketChannel channel) throws Exception {
        channel.pipeline().addFirst("ssl", createSslHandler(channel));
    }

    protected abstract SslHandler createSslHandler(SocketChannel channel);
}
