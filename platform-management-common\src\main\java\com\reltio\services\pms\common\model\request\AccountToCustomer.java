package com.reltio.services.pms.common.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class AccountToCustomer {
    @JsonProperty("salesAccountId")
    String salesAccountId;
    @JsonProperty("salesContractId")
    String salesContractId;
    @JsonProperty("authCustomerId")
    String authCustomerId;
}
