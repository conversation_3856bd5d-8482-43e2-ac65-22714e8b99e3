package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ConfigOverrideElement {
    @JsonProperty("path")
    private final String path;

    @JsonProperty("key")
    private final String key;

    @JsonProperty("value")
    private final Object value;

    @JsonCreator
    public ConfigOverrideElement(@JsonProperty(value = "path", required = true) String path,
                                 @JsonProperty(value = "key", required = true) String key,
                                 @JsonProperty(value = "value", required = true) Object value) {
        this.path = path;
        this.key = key;
        this.value = value;
    }

    public String getPath() {
        return path;
    }

    public String getKey() {
        return key;
    }

    public Object getValue() {
        return value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ConfigOverrideElement)) {
            return false;
        }
        ConfigOverrideElement that = (ConfigOverrideElement) o;
        return Objects.equals(getPath(), that.getPath()) &&
                Objects.equals(getKey(), that.getKey()) &&
                Objects.equals(getValue(), that.getValue());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getPath(), getKey(), getValue());
    }

    @Override
    public String toString() {
        return "ConfigOverrideElement{" +
                "path='" + path + '\'' +
                ", key='" + key + '\'' +
                ", value=" + value +
                '}';
    }
}

