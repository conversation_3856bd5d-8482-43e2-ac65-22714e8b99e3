package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.ShieldStorageTemplate;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Set;

@Service
public class ShieldNotificationDao extends AbstractRootCollectionDao<ShieldStorageTemplate> {
    private static final String RDM_TENANTS_COLLECTION_NAME = "PMS_SHIELD_DOMAIN";
    private static final Logger LOGGER = Logger.getLogger(ShieldNotificationDao.class);


    @Autowired
    public ShieldNotificationDao(CredentialsProvider provider, @Value("${firestore.env.name}") String deployedEnv, ReltioUserHolder reltioUserHolder) {
        super(provider, RDM_TENANTS_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    protected TypeReference<ShieldStorageTemplate> getTypeReference() {
        return new TypeReference<ShieldStorageTemplate>() {
        };
    }

    public void createShieldStorageRecord(ShieldStorageTemplate template) {
        try {
            createOrUpdate(template);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public ShieldStorageTemplate getShieldStorageTemplate(String taskId) {
        try {
            return get(taskId);
        } catch (InvalidDocumentIdException ex) {
            return null;
        }
    }

}
