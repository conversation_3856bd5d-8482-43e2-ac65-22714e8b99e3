package com.reltio.services.pms.common.model.jobs.tasks.clean.tenant;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@Builder
@Getter
@NoArgsConstructor
public class Parameters {
    private boolean historyData;
    private boolean tenantData;
    private String pipelineId;

    public void setPipelineId(String pipelineId) {
        this.pipelineId = pipelineId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }

        Parameters that = (Parameters) o;
        return Objects.equals(getPipelineId(), that.getPipelineId()) &&
                isTenantData() == that.isTenantData() &&
                isHistoryData() == that.isHistoryData();

    }

    @Override
    public int hashCode() {
        return Objects.hash(isHistoryData(), isTenantData(), getPipelineId());
    }

    @Override
    public String toString() {
        return "Parameters{" +
                "historyData=" + historyData +
                ", tenantData=" + tenantData +
                ", pipelineId='" + pipelineId + '\'' +
                '}';
    }
}
