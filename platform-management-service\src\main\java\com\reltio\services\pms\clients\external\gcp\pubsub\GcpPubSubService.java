package com.reltio.services.pms.clients.external.gcp.pubsub;

import com.google.api.gax.rpc.NotFoundException;
import com.google.cloud.pubsub.v1.SubscriptionAdminClient;
import com.google.cloud.pubsub.v1.SubscriptionAdminSettings;
import com.google.cloud.pubsub.v1.TopicAdminClient;
import com.google.cloud.pubsub.v1.TopicAdminSettings;
import com.google.pubsub.v1.ExpirationPolicy;
import com.google.pubsub.v1.ProjectName;
import com.google.pubsub.v1.ProjectSubscriptionName;
import com.google.pubsub.v1.ProjectTopicName;
import com.google.pubsub.v1.Subscription;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.config.gcp.ProjectsServiceAccountKeyCredentialsProvider;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
public class GcpPubSubService {
    private static final Logger LOG = Logger.getLogger(GcpPubSubService.class);
    private final TopicAdminSettings topicAdminSettings;
    private final SubscriptionAdminSettings subscriptionAdminSettings;

    @Autowired
    public GcpPubSubService(ProjectsServiceAccountKeyCredentialsProvider projectsCredentialsProvider) throws IOException {
        topicAdminSettings =
                TopicAdminSettings.newBuilder()
                        .setCredentialsProvider(projectsCredentialsProvider)
                        .build();
        subscriptionAdminSettings =
                SubscriptionAdminSettings.newBuilder()
                        .setCredentialsProvider(projectsCredentialsProvider)
                        .build();
    }


    public String createTopic(String projectId, String topicId) {
        ProjectTopicName topicName = ProjectTopicName.of(projectId, topicId);
        try (TopicAdminClient topicAdminClient = TopicAdminClient.create(topicAdminSettings)) {
            return topicAdminClient.createTopic(topicName).getName();
        } catch (IOException ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
        }
    }

    public boolean doesTopicExist(String projectId, String topicId) {
        ProjectTopicName topicName = ProjectTopicName.of(projectId, topicId);

        try (TopicAdminClient topicAdminClient = TopicAdminClient.create(topicAdminSettings)) {
            topicAdminClient.getTopic(topicName);
            return true;
        } catch (NotFoundException e) {
            return false;
        } catch (IOException ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
        }
    }

    public boolean doesSubscriptionExist(String projectId, String subscriptionId) {
        ProjectSubscriptionName subscriptionName = ProjectSubscriptionName.of(projectId, subscriptionId);
        try (SubscriptionAdminClient subscriptionAdminClient = SubscriptionAdminClient.create(subscriptionAdminSettings)) {
            subscriptionAdminClient.getSubscription(subscriptionName);
            return true;
        } catch (NotFoundException e) {
            return false;
        } catch (IOException ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
        }
    }

    @Retryable(backoff = @Backoff(delay = 30000))
    public boolean listSubscriptions(String projectId) {
        try (SubscriptionAdminClient subscriptionAdminClient = SubscriptionAdminClient.create(subscriptionAdminSettings)) {
            subscriptionAdminClient.listSubscriptions(ProjectName.of(projectId));
            return true;
        } catch (Exception e) {
            LOG.warn(String.format("Can't list subscriptions in %s due to %s: %s", projectId, e.getMessage(), e));
            throw new PlatformManagementException(PlatformManagementErrorCode.INCORRECT_GCP_PROJECT_NAME,
                    HttpStatus.BAD_REQUEST.value(), e, projectId);
        }
    }

    public String createSubscription(String projectId, String topicId, String subscriptionId) {
        try (SubscriptionAdminClient subscriptionAdminClient = SubscriptionAdminClient.create(subscriptionAdminSettings)) {
            ProjectTopicName topicName = ProjectTopicName.of(projectId, topicId);
            ProjectSubscriptionName subscriptionName = ProjectSubscriptionName.of(projectId, subscriptionId);
            Subscription subscription = Subscription.newBuilder()
                    .setName(subscriptionName.toString())
                    .setTopic(topicName.toString())
                    .setExpirationPolicy(ExpirationPolicy.newBuilder().clearTtl().build())
                    .build();

            return subscriptionAdminClient.createSubscription(subscription).getName();
        } catch (IOException ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
        }
    }

    public void deleteTopic(String projectId, String topicId) {
        ProjectTopicName topicName = ProjectTopicName.of(projectId, topicId);
        try (TopicAdminClient topicAdminClient = TopicAdminClient.create(topicAdminSettings)) {
            topicAdminClient.deleteTopic(topicName);
        } catch (IOException ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
        }
    }

    public void deleteSubscription(String projectId, String subscriptionId) {
        try (SubscriptionAdminClient subscriptionAdminClient = SubscriptionAdminClient.create(subscriptionAdminSettings)) {
            ProjectSubscriptionName subscriptionName = ProjectSubscriptionName.of(projectId, subscriptionId);

            subscriptionAdminClient.deleteSubscription(subscriptionName);
        } catch (IOException ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
        }
    }


    public static String getTopicFullName(String gcpProjectName, String topicSubsName) {
        return ProjectTopicName.of(gcpProjectName, topicSubsName).toString();
    }

    public static String getSubscriptionFullName(String gcpProjectName, String subscriptionName) {
        return ProjectSubscriptionName.of(gcpProjectName, subscriptionName).toString();
    }
}
