package com.reltio.services.pms.clients.external.aws.iam;

import com.reltio.services.pms.clients.external.aws.AwsCredentialsFactory;
import com.reltio.services.pms.clients.external.aws.credentials.PlatformManagementAwsCredentials;
import com.reltio.services.pms.common.model.aws.IamUserAccessKey;
import com.reltio.services.pms.service.Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.json.simple.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.iam.IamClient;
import software.amazon.awssdk.services.iam.model.AccessKey;
import software.amazon.awssdk.services.iam.model.AddUserToGroupRequest;
import software.amazon.awssdk.services.iam.model.AttachGroupPolicyRequest;
import software.amazon.awssdk.services.iam.model.AttachRolePolicyRequest;
import software.amazon.awssdk.services.iam.model.AttachUserPolicyRequest;
import software.amazon.awssdk.services.iam.model.AttachedPolicy;
import software.amazon.awssdk.services.iam.model.CreateAccessKeyRequest;
import software.amazon.awssdk.services.iam.model.CreateGroupRequest;
import software.amazon.awssdk.services.iam.model.CreatePolicyRequest;
import software.amazon.awssdk.services.iam.model.CreatePolicyVersionRequest;
import software.amazon.awssdk.services.iam.model.CreatePolicyVersionResponse;
import software.amazon.awssdk.services.iam.model.CreateRoleRequest;
import software.amazon.awssdk.services.iam.model.CreateUserRequest;
import software.amazon.awssdk.services.iam.model.DeleteAccessKeyRequest;
import software.amazon.awssdk.services.iam.model.DeleteAccessKeyResponse;
import software.amazon.awssdk.services.iam.model.DeleteGroupRequest;
import software.amazon.awssdk.services.iam.model.DeleteGroupResponse;
import software.amazon.awssdk.services.iam.model.DeletePolicyRequest;
import software.amazon.awssdk.services.iam.model.DeletePolicyResponse;
import software.amazon.awssdk.services.iam.model.DeletePolicyVersionRequest;
import software.amazon.awssdk.services.iam.model.DeletePolicyVersionResponse;
import software.amazon.awssdk.services.iam.model.DeleteUserRequest;
import software.amazon.awssdk.services.iam.model.DeleteUserResponse;
import software.amazon.awssdk.services.iam.model.DetachGroupPolicyRequest;
import software.amazon.awssdk.services.iam.model.DetachGroupPolicyResponse;
import software.amazon.awssdk.services.iam.model.DetachUserPolicyRequest;
import software.amazon.awssdk.services.iam.model.EntityType;
import software.amazon.awssdk.services.iam.model.GetPolicyRequest;
import software.amazon.awssdk.services.iam.model.GetPolicyResponse;
import software.amazon.awssdk.services.iam.model.GetPolicyVersionRequest;
import software.amazon.awssdk.services.iam.model.GetPolicyVersionResponse;
import software.amazon.awssdk.services.iam.model.GetUserRequest;
import software.amazon.awssdk.services.iam.model.Group;
import software.amazon.awssdk.services.iam.model.IamException;
import software.amazon.awssdk.services.iam.model.ListAccessKeysRequest;
import software.amazon.awssdk.services.iam.model.ListAccessKeysResponse;
import software.amazon.awssdk.services.iam.model.ListAttachedUserPoliciesRequest;
import software.amazon.awssdk.services.iam.model.ListAttachedUserPoliciesResponse;
import software.amazon.awssdk.services.iam.model.ListEntitiesForPolicyRequest;
import software.amazon.awssdk.services.iam.model.ListEntitiesForPolicyResponse;
import software.amazon.awssdk.services.iam.model.ListGroupPoliciesRequest;
import software.amazon.awssdk.services.iam.model.ListGroupPoliciesResponse;
import software.amazon.awssdk.services.iam.model.ListGroupsForUserRequest;
import software.amazon.awssdk.services.iam.model.ListGroupsForUserResponse;
import software.amazon.awssdk.services.iam.model.ListPolicyVersionsRequest;
import software.amazon.awssdk.services.iam.model.ListPolicyVersionsResponse;
import software.amazon.awssdk.services.iam.model.NoSuchEntityException;
import software.amazon.awssdk.services.iam.model.Policy;
import software.amazon.awssdk.services.iam.model.PolicyUser;
import software.amazon.awssdk.services.iam.model.PutGroupPolicyRequest;
import software.amazon.awssdk.services.iam.model.PutUserPermissionsBoundaryRequest;
import software.amazon.awssdk.services.iam.model.SetDefaultPolicyVersionRequest;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.model.GetCallerIdentityRequest;
import software.amazon.awssdk.services.sts.model.GetCallerIdentityResponse;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class AwsIamService implements IAwsIamService {
    public static final String BLANK = "";
    public static final String POLICY_FORMAT = "arn:aws:iam::%s:policy/%s";
    private final Logger logger = LoggerFactory.getLogger(AwsIamService.class);
    private final IamClient iam;
    private final StsClient sts;
    private String defaultAccount;
    private static final Set<String> AWS_CHINA_REGIONS = Set.of("cn-north-1", "cn-northwest-1");


    public AwsIamService(PlatformManagementAwsCredentials credentials) {
        AwsCredentialsProvider provider = AwsCredentialsFactory.createAwsCredentialsProvider(credentials);
        String awsRegion = credentials.getAWSRegion();
        Region region = AWS_CHINA_REGIONS.contains(awsRegion) ? Region.AWS_CN_GLOBAL : Region.AWS_GLOBAL;
        this.iam = IamClient.builder().credentialsProvider(provider)
                .region(region).build();
        this.sts = StsClient.builder().credentialsProvider(provider)
                .region(Region.of(credentials.getAWSRegion())).build();
    }

    @Override
    public void createPolicy(String policyName, String policyDocument) {
        CreatePolicyRequest request = CreatePolicyRequest.builder()
                .policyName(policyName).policyDocument(policyDocument).build();
        iam.createPolicy(request);
    }

    @Override
    public void createGroupInlinePolicy(String groupName, String policyName, String policyDocument) {
        PutGroupPolicyRequest request = PutGroupPolicyRequest.builder()
                .groupName(groupName)
                .policyName(policyName)
                .policyDocument(policyDocument).build();
        iam.putGroupPolicy(request);
    }

    @Override
    public void createUser(String userName, String boundaryArn) {
        CreateUserRequest request;
        if (boundaryArn != null) {
            request = CreateUserRequest.builder().userName(userName).permissionsBoundary(boundaryArn).build();
        } else {
            request = CreateUserRequest.builder().userName(userName).build();
        }
        iam.createUser(request);
    }

    @Override
    public boolean doesUserExist(String userName) {
        try {
            GetUserRequest getUserRequest = GetUserRequest.builder().userName(userName).build();
            iam.getUser(getUserRequest);
            return true;
        } catch (NoSuchEntityException e) {
            logger.info("User does not exist, for userName {} {} ", userName, e.getMessage());
            return false;
        }
    }

    @Override
    public IamUserAccessKey createAwsKeysForUser(String userName) {
        CreateAccessKeyRequest request = CreateAccessKeyRequest.builder().userName(userName).build();
        AccessKey accessKey = iam.createAccessKey(request).accessKey();
        return new IamUserAccessKey(accessKey.accessKeyId(), accessKey.secretAccessKey());
    }

    @Override
    public void createUserGroup(String groupName) {
        CreateGroupRequest createGroupRequest = CreateGroupRequest.builder().groupName(groupName).build();
        iam.createGroup(createGroupRequest);
    }

    @Override
    public void attachGroupPolicy(String groupName, String policyName, String region) {
        AttachGroupPolicyRequest attachGroupPolicyRequest = AttachGroupPolicyRequest.builder()
                .policyArn(buildPolicyArn(getDefaultAccount(), policyName, region))
                .groupName(groupName).build();
        iam.attachGroupPolicy(attachGroupPolicyRequest);
    }

    @Override
    public void addUserToGroup(String userName, String groupName) {
        AddUserToGroupRequest addUserToGroupRequest = AddUserToGroupRequest.builder()
                .groupName(groupName)
                .userName(userName).build();
        iam.addUserToGroup(addUserToGroupRequest);
    }

    @Override
    public void createRole(String roleName, JSONObject assumeRolePolicyDocument, String policyName, String region) {
        CreateRoleRequest.Builder createRoleRequestBuilder = CreateRoleRequest.builder().roleName(roleName);
        if (assumeRolePolicyDocument != null) {
            createRoleRequestBuilder.assumeRolePolicyDocument(assumeRolePolicyDocument.toString());
        }
        iam.createRole(createRoleRequestBuilder.build());

        AttachRolePolicyRequest attachRolePolicyRequest = AttachRolePolicyRequest.builder()
                .roleName(roleName)
                .policyArn(buildPolicyArn(getDefaultAccount(), policyName, region)).build();
        iam.attachRolePolicy(attachRolePolicyRequest);
    }

    @Override
    public void deleteUserKey(String userName, String accessKey) {
        iam.deleteAccessKey(DeleteAccessKeyRequest.builder().userName(userName).accessKeyId(accessKey).build());
    }

    @Override
    public DetachGroupPolicyResponse detachGroupPolicyByPolicyName(String groupName, String policyName, String region) {
        String policyArn = buildPolicyArn(getDefaultAccount(), policyName, region);
        return detachGroupPolicy(groupName, policyArn);

    }

    @Override
    public DetachGroupPolicyResponse detachGroupPolicy(String groupName, String policyArn) {
        try {
            return iam.detachGroupPolicy(DetachGroupPolicyRequest.builder().policyArn(policyArn).groupName(groupName).build());
        } catch (IamException e) {
            logger.info("Policy does not exist in group {}, for policyARN {} {} ", groupName, policyArn, e.getMessage());
            return null;
        }
    }

    @Override
    public DeleteUserResponse deleteUser(String userName) {
        try {
            return iam.deleteUser(DeleteUserRequest.builder().userName(userName).build());
        } catch (IamException e) {
            logger.info("Usr does not exist, for userName {} {}", userName, e.getMessage());
            return null;
        }
    }

    @Override
    public ListAccessKeysResponse listAccessKeys(String userName) {
        try {
            return iam.listAccessKeys(ListAccessKeysRequest.builder().userName(userName).build());
        } catch (IamException e) {
            logger.info("User does not exist, for userName {} {} ", userName, e.getMessage());
            return null;
        }
    }

    @Override
    public DeleteAccessKeyResponse deleteAccessKey(String userName, String accessKeyId) {
        try {
            return iam.deleteAccessKey(DeleteAccessKeyRequest.builder().userName(userName).accessKeyId(accessKeyId).build());
        } catch (IamException e) {
            logger.info("User does not exist, not able to delete the Access key for userName {} and accessKeyId {} {} ", userName, accessKeyId, e.getMessage());
            return null;
        }
    }

    @Override
    public DeleteGroupResponse deleteGroup(String groupName) {
        try {
            return iam.deleteGroup(DeleteGroupRequest.builder().groupName(groupName).build());
        } catch (IamException e) {
            logger.info("Group does not exist, for groupName {} {} ", groupName, e.getMessage());
            return null;
        }
    }

    @Override
    public DeletePolicyResponse deletePolicyByPolicyName(String policyName, String region) {
        String policyArn = buildPolicyArn(getDefaultAccount(), policyName, region);
        return deletePolicy(policyArn);

    }

    @Override
    public ListPolicyVersionsResponse listPolicyVersions(String policyArn) {
        try {
            return iam.listPolicyVersions(ListPolicyVersionsRequest.builder().policyArn(policyArn).build());
        } catch (IamException e) {
            logger.info("Policy does not exist, not able to find version for policyArn {} {} ", policyArn, e.getMessage());
            return null;
        }
    }

    @Override
    public DeletePolicyVersionResponse deletePolicyVersion(String policyArn, String versionId) {
        try {
            return iam.deletePolicyVersion(DeletePolicyVersionRequest.builder().policyArn(policyArn).versionId(versionId).build());
        } catch (IamException e) {
            logger.info("Policy Version does not exist, for policyArn {} and versionId {} {} ", policyArn, versionId, e.getMessage());
            return null;
        }
    }

    @Override
    public ListPolicyVersionsResponse listPolicyVersionsByPolicyName(String policyName, String region) {
        String policyArn = buildPolicyArn(getDefaultAccount(), policyName, region);
        return listPolicyVersions(policyArn);
    }

    @Override
    public DeletePolicyVersionResponse deletePolicyVersionByPolicyName(String policyName, String versionId,
                                                                       String region) {
        String policyArn = buildPolicyArn(getDefaultAccount(), policyName, region);
        return deletePolicyVersion(policyArn, versionId);
    }

    @Override
    public DeletePolicyResponse deletePolicy(String policyArn) {
        try {
            return iam.deletePolicy(DeletePolicyRequest.builder().policyArn(policyArn).build());
        } catch (IamException e) {
            logger.info("Policy does not exist, for policyArn {} {} ", policyArn, e.getMessage());
            return null;
        }
    }

    @Override
    public List<String> getUserGroups(String userName) {
        try {
            ListGroupsForUserResponse listGroupsForUserResult = iam.listGroupsForUser(ListGroupsForUserRequest.builder()
                    .userName(userName).build());
            List<Group> groups = listGroupsForUserResult.groups();
            return groups.stream().map(Group::groupName).collect(Collectors.toList());
        } catch (IamException e) {
            logger.info("User does not has a group, for userName {} {} ", userName, e.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public void attachUserPolicy(String userName, String policyName, String region) {
        AttachUserPolicyRequest attachGroupPolicyRequest = AttachUserPolicyRequest.builder()
                .policyArn(buildPolicyArn(getDefaultAccount(), policyName, region))
                .userName(userName).build();
        iam.attachUserPolicy(attachGroupPolicyRequest);
    }

    @Override
    public void putUserPermissionsBoundary(String userName, String boundaryArn) {
        PutUserPermissionsBoundaryRequest putUserPermissionsBoundaryRequest = PutUserPermissionsBoundaryRequest.builder()
                .userName(userName).permissionsBoundary(boundaryArn).build();
        iam.putUserPermissionsBoundary(putUserPermissionsBoundaryRequest);
    }

    @Override
    public List<AttachedPolicy> listUserPolicies(String userName) {
        try {
            ListAttachedUserPoliciesResponse listAttachedUserPoliciesResponse = iam.listAttachedUserPolicies(
                    ListAttachedUserPoliciesRequest.builder().userName(userName).build());
            return listAttachedUserPoliciesResponse.attachedPolicies();
        } catch (NoSuchEntityException e) {
            logger.info("User does not has any policy, for userName {} {} ", userName, e.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public List<Group> listGroupsForUser(String userName) {
        try {
            ListGroupsForUserResponse listGroupsForUserResponse = iam.listGroupsForUser(ListGroupsForUserRequest.builder().userName(userName).build());
            return listGroupsForUserResponse.groups();
        } catch (NoSuchEntityException e) {
            logger.info("User does not has any group, for userName {} {} ", userName, e.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> listGroupPolicies(String groupName) {
        try {
            ListGroupPoliciesResponse listGroupsForUserResponse = iam.listGroupPolicies(ListGroupPoliciesRequest.builder().groupName(groupName).build());
            return listGroupsForUserResponse.policyNames();
        } catch (IamException e) {
            logger.info("Group does not has any policy, for userName {} {} ", groupName, e.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public void detachUserPolicy(String userName, String policyArn) {
        try {
            DetachUserPolicyRequest detachUserPolicyRequest = DetachUserPolicyRequest.builder()
                    .policyArn(policyArn).userName(userName).build();
            iam.detachUserPolicy(detachUserPolicyRequest);
        } catch (IamException e) {
            logger.info("user does not has any policy {}, for userName {} {} ", policyArn, userName, e.getMessage());
        }
    }

    @Override
    public void createGroupForPolicy(String groupName) {
        CreateGroupRequest request = CreateGroupRequest.builder().groupName(groupName).build();
        iam.createGroup(request);
    }

    @Override
    public Policy checkPolicyExists(String policyArn) {
        try {
            GetPolicyRequest getPolicyRequest = GetPolicyRequest.builder().policyArn(policyArn).build();
            GetPolicyResponse getPolicyResponse = iam.getPolicy(getPolicyRequest);
            return getPolicyResponse.policy();
        } catch (NoSuchEntityException e) {
            logger.info("Policy does not exist, for policyARN {} {} ", policyArn, e.getMessage());
            return null;
        }
    }

    @Override
    public String getPolicyDocument(String policyArn, String version) {
        GetPolicyVersionRequest getPolicyVersionRequest = GetPolicyVersionRequest.builder()
                .policyArn(policyArn).versionId(version).build();
        GetPolicyVersionResponse getPolicyVersionResponse = iam.getPolicyVersion(getPolicyVersionRequest);
        return getPolicyVersionResponse.policyVersion().document();
    }

    @Override
    public void updatePolicy(String policyArn, String policyDocument) {
        CreatePolicyVersionRequest createPolicyVersionRequest = CreatePolicyVersionRequest.builder()
                .policyArn(policyArn).policyDocument(policyDocument).setAsDefault(Boolean.TRUE).build();
        CreatePolicyVersionResponse createPolicyVersionResponse = iam.createPolicyVersion(createPolicyVersionRequest);
        SetDefaultPolicyVersionRequest setDefaultPolicyVersionRequest = SetDefaultPolicyVersionRequest.builder()
                .policyArn(policyArn).versionId(createPolicyVersionResponse.policyVersion().versionId()).build();
        iam.setDefaultPolicyVersion(setDefaultPolicyVersionRequest);
    }

    private synchronized String getDefaultAccount() {
        if (defaultAccount == null) {
            GetCallerIdentityResponse callerIdentity = sts.getCallerIdentity(GetCallerIdentityRequest.builder().build());
            defaultAccount = callerIdentity.account();
        }
        return defaultAccount;
    }


    private static String buildPolicyArn(String account, String policyName, String region) {
        return String.format(Util.processChinaArn(POLICY_FORMAT, region), account, policyName);
    }

    public String getPolicyArn(String policyName, String region) {
        return buildPolicyArn(getDefaultAccount(), policyName, region);
    }

    /**
     * Get IAM username by policy ARN
     *
     * @param policyArn policy ARN to use for search
     * @return username or blank
     */
    @NonNull
    public String getUserByPolicyArn(String policyArn) {
        ListEntitiesForPolicyRequest listEntitiesForPolicyRequest = ListEntitiesForPolicyRequest.builder()
                .policyArn(policyArn)
                .entityFilter(EntityType.USER)
                .build();
        ListEntitiesForPolicyResponse listEntitiesForPolicyResponse =
                iam.listEntitiesForPolicy(listEntitiesForPolicyRequest);
        List<PolicyUser> users = listEntitiesForPolicyResponse.policyUsers();
        if (CollectionUtils.isEmpty(users)) {
            return BLANK;
        }
        return users.getFirst().userName();
    }

    public  String getAccountId() {
            GetCallerIdentityResponse callerIdentity = sts.getCallerIdentity(GetCallerIdentityRequest.builder().build());
            return  callerIdentity.account();
    }
}
