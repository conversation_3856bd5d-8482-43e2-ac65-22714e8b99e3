package com.reltio.services.pms.common.sales.model.addon;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.tenant.ProductUrl;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@Getter
@Setter
public class RdmProductConfig extends BaseProductConfig {

    @JsonProperty("rdmTenantId")
    String rdmTenantId;

    @JsonProperty("rdmServiceUrl")
    String rdmServiceUrl;

    @JsonProperty("productUrl")
    ProductUrl productUrl;

    public RdmProductConfig() {
        this.pmsProductName = PMSProductName.RDM;
    }

}
