package com.reltio.services.pms.common.sales.model;


import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;
import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class RihSalesPackageConfig extends SalesPackageConfig{

    @JsonProperty("rihAddOns")
    private Map<String,Set<SalesConfig>> salesConfigList;

    @JsonCreator
    public RihSalesPackageConfig(@JsonProperty("salesConfig") SalesConfig salesConfig,
                                 @JsonProperty("rihAddOns") Map<String,Set<SalesConfig>> rihAddOns){
        super(salesConfig);
        this.salesConfigList = rihAddOns;
    }

}
