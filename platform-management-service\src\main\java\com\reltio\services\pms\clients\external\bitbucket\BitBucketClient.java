package com.reltio.services.pms.clients.external.bitbucket;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.*;

public class BitBucketClient {
    private static final Pattern BRANCH_CONFLICT_PATTERN = Pattern.compile("\\{\"type\":\"error\",\"error\":\\{\"message\":\"The parent commit specified \\(\\w*\\) is not the head of branch \\w*.\"}}");


    private static final String BIT_BUCKET_BASE_URL = "https://api.bitbucket.org/2.0";
    private static final String BRANCH_FILE_PATH_TEMPLATE = "%s/repositories/reltio-ondemand/%s/src/%s/%s";
    private static final String BRANCH_DIRECTORY_PATH_TEMPLATE = "%s/repositories/reltio-ondemand/%s/src/%s/%s?pagelen=100";
    private static final String REPOSITORY_COMMIT_PATH_TEMPLATE = "%s/repositories/reltio-ondemand/%s/src";
    private static final String BRANCH_INFO_TEMPLATE = "%s/repositories/reltio-ondemand/%s/refs/branches/%s";
    private static final String DOWNLOADS_TEMPLATE = "%s/repositories/reltio-ondemand/%s/downloads/%s";
    private static final String LIST_DOWNLOADS_TEMPLATE = "%s/repositories/reltio-ondemand/%s/downloads?fields=values.name&pagelen=100";
    private static final String CONTENT_TYPE = "Content-Type";

    public static final String AUTH = "Authorization";

    public static final String AUTH_HEADER_VALUE = "Basic ";

    private final BasicAuthRestTemplate basicAuthRestTemplate;
    private final ObjectMapper objectMapper;

    public BitBucketClient(BasicAuthRestTemplate basicAuthRestTemplate) {
        this.basicAuthRestTemplate = basicAuthRestTemplate;
        objectMapper = new ObjectMapper();
    }

    public String getFile(String repoName, String branch, String path) {
        String url = String.format(BRANCH_FILE_PATH_TEMPLATE, BIT_BUCKET_BASE_URL, repoName, branch, path);
        ResponseEntity<String> forEntity = basicAuthRestTemplate.getForEntity(url, String.class);
        List<String> contentTypes = getContentType(forEntity);
        if (contentTypes.stream().anyMatch(e -> e.contains("application/json"))) {
            throw new BitBucketClientException.PathPointToFolder();
        }
        return forEntity.getBody();
    }

    public byte[] getDownload(String repoName, String filename) {
        String url = String.format(DOWNLOADS_TEMPLATE, BIT_BUCKET_BASE_URL, repoName, filename);
        ResponseEntity<byte[]> forEntity = basicAuthRestTemplate.getForEntity(url, byte[].class);
        return forEntity.getBody();
    }

    public String getLatestFile(String repoName, String prefix) {
        String url = String.format(LIST_DOWNLOADS_TEMPLATE, BIT_BUCKET_BASE_URL, repoName);
        JsonNode jsonNode = basicAuthRestTemplate.getForObject(url, JsonNode.class);
        String latestFile = null;
        if (jsonNode != null && jsonNode.get("values") != null) {
            Set<String> filenames = new HashSet<>(jsonNode.get("values").findValuesAsText("name"));

            Pattern pattern = Pattern.compile("^" + prefix + "_v\\d+(\\.\\d+)*_(\\d{8})\\.zip$");
            LocalDate latestDate = LocalDate.MIN;
            DateTimeFormatter dateFormatter = DateTimeFormatter.BASIC_ISO_DATE;

            for (String filename : filenames) {
                Matcher matcher = pattern.matcher(filename);
                if (matcher.matches()) {
                    try {
                        LocalDate fileDate = LocalDate.parse(matcher.group(2), dateFormatter);
                        if (fileDate.isAfter(latestDate)) {
                            latestDate = fileDate;
                            latestFile = filename;
                        }
                    } catch (Exception e) {
                        throw new PlatformManagementException(
                                PlatformManagementErrorCode.INCORRECT_FORMAT_FOUND,
                                HttpStatus.BAD_REQUEST.value(),
                                "Invalid date format in filename: " + filename
                        );
                    }
                }
            }

        } if (latestFile == null){
            throw new PlatformManagementException(
                    PlatformManagementErrorCode.RESOURCE_FILE_IS_NOT_AVAILABLE,
                    HttpStatus.BAD_REQUEST.value(),
                    String.format("RIH file for %s is not present in the repository", prefix)
            );
        }
        return latestFile;
    }

    public List<DirectoryObject> getDirectory(String repoName, String branch, String path) {
        String url = String.format(BRANCH_DIRECTORY_PATH_TEMPLATE, BIT_BUCKET_BASE_URL, repoName, branch, path);
        List<DirectoryObject> result = new ArrayList<>();
        boolean hasNextElements = true;
        while (hasNextElements) {
            ResponseEntity<String> forEntity = basicAuthRestTemplate.getForEntity(url, String.class);
            List<String> contentTypes = getContentType(forEntity);
            if (contentTypes.stream().anyMatch(e -> e.contains("text/plain"))) {
                throw new BitBucketClientException.PathPointToFile();
            }
            try {
                String response = getResponse(forEntity);
                JsonNode body = objectMapper.readTree(response);
                if (body.has("next")) {
                    url = body.get("next").asText();
                } else {
                    hasNextElements = false;
                }
                result.addAll(objectMapper.convertValue(body.get("values"), new TypeReference<List<DirectoryObject>>() {
                }));
            } catch (IOException e) {
                throw new BitBucketClientException(e);
            }
        }
        return result;
    }

    public String getBranchHash(String repoName, String branch) {
        String url = String.format(BRANCH_INFO_TEMPLATE, BIT_BUCKET_BASE_URL, repoName, branch);
        JsonNode jsonNode = basicAuthRestTemplate.getForObject(url, JsonNode.class);
        return jsonNode != null ? jsonNode.get("target").get("hash").asText() : null;
    }

    public void commitForDeleteFile(String repoName, String branch, String message, String branchHash, Set<String> files) {
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        commitFiles(repoName, branch, message, branchHash, files, body);
    }

    public void commitFiles(String repoName, String branch, String message, String branchHash, Map<String, String> files) {
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        files.forEach(body::add);
        commitFiles(repoName, branch, message, branchHash, files.keySet(), body);
    }

    private void commitFiles(String repoName, String branch, String message, String branchHash, Set<String> files, MultiValueMap<String, Object> body) {
        String url = String.format(REPOSITORY_COMMIT_PATH_TEMPLATE, BIT_BUCKET_BASE_URL, repoName);
        try {
            body.add("message", message);
            body.add("branch", branch);
            body.add("parents", branchHash);
            files.forEach(e -> body.add("files", e));

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(body, headers);
            basicAuthRestTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        } catch (HttpClientErrorException badRequestException) {
            if (BRANCH_CONFLICT_PATTERN.matcher(badRequestException.getResponseBodyAsString()).matches()) {
                throw HttpClientErrorException.create(HttpStatus.CONFLICT, HttpStatus.CONFLICT.getReasonPhrase(), badRequestException.getResponseHeaders(), badRequestException.getResponseBodyAsByteArray(), null);
            } else {
                throw badRequestException;
            }
        }
    }

    private String getResponse(ResponseEntity<String> forEntity) {
        String body = forEntity.getBody();
        if (body == null) {
            throw new BitBucketClientException("No Response from BitBucket");
        }
        return body;
    }

    private List<String> getContentType(ResponseEntity<String> forEntity) {
        List<String> contentTypes = forEntity.getHeaders().get(CONTENT_TYPE);
        if (contentTypes == null) {
            throw new BitBucketClientException("Response from bitbucket can not be parsed ");
        }
        return contentTypes;
    }

    public boolean isFileExist(String repoName, String branch, String path) {
        try {
            getFile(repoName, branch, path);
            return true;
        } catch (HttpClientErrorException.NotFound e) {
            return false;
        }
    }

    public boolean isDirectoryPresent(String repoName, String branch, String path) {
        String url = String.format(BRANCH_DIRECTORY_PATH_TEMPLATE, BIT_BUCKET_BASE_URL, repoName, branch, path);
        try {
            basicAuthRestTemplate.getForEntity(url, String.class);
            return true;
        } catch (HttpClientErrorException e) {
            return false;
        }
    }
}
