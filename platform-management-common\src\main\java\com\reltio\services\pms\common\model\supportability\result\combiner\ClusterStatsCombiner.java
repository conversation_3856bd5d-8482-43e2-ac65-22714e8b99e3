package com.reltio.services.pms.common.model.supportability.result.combiner;

import com.reltio.services.pms.common.model.supportability.ClusterStatsDto;
import com.reltio.services.pms.common.model.supportability.CpuUsage;
import com.reltio.services.pms.common.model.supportability.Memory;
import com.reltio.services.pms.common.model.supportability.MemoryUsage;
import com.reltio.services.pms.common.model.supportability.PodNamesAbove80;
import com.reltio.services.pms.common.model.supportability.Scaling;
import com.reltio.services.pms.common.model.supportability.result.StatsCombiner;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * The type Event stats combiner.
 */
public class ClusterStatsCombiner implements StatsCombiner<ClusterStatsDto> {
    /**
     * mergeAllRestRows method is used to merge all the provided rows from GBQ into single record by sum/avg based on the column in GBQ.
     *
     * @param dtoList the dto list
     * @return the list
     */
    @Override
    public ClusterStatsDto mergeAllRows(List<ClusterStatsDto> dtoList) {
        ClusterStatsDto response = dtoList.get(NumberUtils.INTEGER_ZERO);
        ClusterStatsDto lastResponse = dtoList.get(dtoList.size() - NumberUtils.INTEGER_ONE);
        long startTime = response.getStartTime();
        long endTime = lastResponse.getEndTime();
        int totalRows = dtoList.size();
        Float cpuSum = NumberUtils.FLOAT_ZERO;
        Float cpuMax = NumberUtils.FLOAT_ZERO;
        Float cpuP90Sum = NumberUtils.FLOAT_ZERO;
        Float cpuP95Sum = NumberUtils.FLOAT_ZERO;
        Float podMemorySum = NumberUtils.FLOAT_ZERO;
        Float podMemoryMax = NumberUtils.FLOAT_ZERO;
        Float podMemoryP90Sum = NumberUtils.FLOAT_ZERO;
        Float podMemoryP95Sum = NumberUtils.FLOAT_ZERO;
        Float appMemorySum = NumberUtils.FLOAT_ZERO;
        Float appMemoryMax = NumberUtils.FLOAT_ZERO;
        Float appMemoryP90Sum = NumberUtils.FLOAT_ZERO;
        Float appMemoryP95Sum = NumberUtils.FLOAT_ZERO;
        Float scalingAvgSum = NumberUtils.FLOAT_ZERO;
        Float scalingMax = NumberUtils.FLOAT_ZERO;
        Float scalingP90Sum = NumberUtils.FLOAT_ZERO;
        Float scalingP95Sum = NumberUtils.FLOAT_ZERO;
        long scalingMinReplicas = NumberUtils.INTEGER_ZERO;
        long scalingMaxReplicas = NumberUtils.INTEGER_ZERO;
        Set<String> cpu80 = new HashSet<>();
        Set<String> podMemory80 = new HashSet<>();
        Set<String> appMemory80 = new HashSet<>();
        for (ClusterStatsDto dto : dtoList) {
            cpuSum += dto.getCpu().getAverage();
            cpuMax = Math.max(dto.getCpu().getMax(), cpuMax);
            cpuP90Sum += dto.getCpu().getP90();
            cpuP95Sum += dto.getCpu().getP95();
            cpu80.addAll(dto.getCpu().getCpu80().getNames());
            podMemorySum += dto.getMemory().getPod().getAverage();
            podMemoryMax = Math.max(dto.getMemory().getPod().getMax(), podMemoryMax);
            podMemoryP90Sum += dto.getMemory().getPod().getP90();
            podMemoryP95Sum += dto.getMemory().getPod().getP95();
            podMemory80.addAll(dto.getMemory().getPod().getMemory80().getNames());
            appMemorySum += dto.getMemory().getApp().getAverage();
            appMemoryMax = Math.max(dto.getMemory().getApp().getMax(), appMemoryMax);
            appMemoryP90Sum += dto.getMemory().getApp().getP90();
            appMemoryP95Sum += dto.getMemory().getApp().getP95();
            appMemory80.addAll(dto.getMemory().getApp().getMemory80().getNames());
            scalingAvgSum += dto.getScaling().getAverage();
            scalingMax = Math.max(dto.getScaling().getMax(), scalingMax);
            scalingP90Sum += dto.getScaling().getP90();
            scalingP95Sum += dto.getScaling().getP95();
            scalingMinReplicas = Math.min(dto.getScaling().getMinReplicas(), scalingMinReplicas);
            scalingMaxReplicas = Math.max(dto.getScaling().getMaxReplicas(), scalingMaxReplicas);
        }
        Float cpuAvg = roundOff2Digits(cpuSum / totalRows);
        Float cpuP90Avg = roundOff2Digits(cpuP90Sum / totalRows);
        Float cpuP95Avg = roundOff2Digits(cpuP95Sum / totalRows);
        PodNamesAbove80 cpuPodNamesAbove80 = new PodNamesAbove80(new ArrayList<>(cpu80), cpu80.size());
        CpuUsage cpuUsage = new CpuUsage(cpuAvg, roundOff2Digits(cpuMax), cpuP90Avg, cpuP95Avg, cpuPodNamesAbove80);

        Float podMemoryAvg = roundOff2Digits(podMemorySum / totalRows);
        Float podMemoryP90Avg = roundOff2Digits(podMemoryP90Sum / totalRows);
        Float podMemoryP95Avg = roundOff2Digits(podMemoryP95Sum / totalRows);
        PodNamesAbove80 podMemoryPodNamesAbove80 = new PodNamesAbove80(new ArrayList<>(podMemory80), podMemory80.size());

        Float appMemoryAvg = roundOff2Digits(appMemorySum / totalRows);
        Float appMemoryP90Avg = roundOff2Digits(appMemoryP90Sum / totalRows);
        Float appMemoryP95Avg = roundOff2Digits(appMemoryP95Sum / totalRows);
        PodNamesAbove80 appMemoryPodNamesAbove80 = new PodNamesAbove80(new ArrayList<>(appMemory80), appMemory80.size());

        MemoryUsage podMemoryUsage = new MemoryUsage(podMemoryAvg, roundOff2Digits(podMemoryMax), podMemoryP90Avg, podMemoryP95Avg, podMemoryPodNamesAbove80);
        MemoryUsage appMemoryUsage = new MemoryUsage(appMemoryAvg, roundOff2Digits(appMemoryMax), appMemoryP90Avg, appMemoryP95Avg, appMemoryPodNamesAbove80);
        Memory memory = new Memory(podMemoryUsage, appMemoryUsage);

        Float scalingAvg = scalingAvgSum / totalRows;
        Float scalingP90Avg = scalingP90Sum / totalRows;
        Float scalingP95Avg = scalingP95Sum / totalRows;

        Scaling scaling = new Scaling(scalingAvg, scalingMax, scalingP90Avg, scalingP95Avg, scalingMinReplicas, scalingMaxReplicas);
        ClusterStatsDto dto = new ClusterStatsDto();
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setCpu(cpuUsage);
        dto.setMemory(memory);
        dto.setScaling(scaling);
        return dto;
    }
}
