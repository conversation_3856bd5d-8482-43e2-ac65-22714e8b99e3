package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.common.config.MessagingPayloadType;

import java.util.List;
import java.util.Objects;

public class DestinationConfig {

    @JsonProperty("objectFilter")
    private String objectFilter;

    @JsonProperty("typeFilter")
    private List<String> typeFilter;

    @JsonProperty("payloadType")
    private MessagingPayloadType payloadType;

    public DestinationConfig(@JsonProperty("objectFilter") String objectFilter,@JsonProperty("typeFilter") List<String> typeFilter,@JsonProperty("payloadType") MessagingPayloadType payloadType) {
        this.objectFilter = objectFilter;
        this.typeFilter = typeFilter;
        this.payloadType = payloadType;
    }

    public String getObjectFilter() {
        return objectFilter;
    }


    public List<String> getTypeFilter() {
        return typeFilter;
    }


    public MessagingPayloadType getPayloadType() {
        return payloadType;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DestinationConfig)) return false;
        DestinationConfig that = (DestinationConfig) o;
        return objectFilter.equals(that.objectFilter) && typeFilter.equals(that.typeFilter) && payloadType == that.payloadType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(objectFilter, typeFilter, payloadType);
    }
}
