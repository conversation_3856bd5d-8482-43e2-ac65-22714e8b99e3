package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.sales.model.PmsLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class PmsLockDao extends AbstractRootCollectionDao<PmsLock> {
    private static final String LOCK_COLLECTION_NAME = "PMS_TASKS_LOCKS";
    protected PmsLockDao(CredentialsProvider provider, @Value("${firestore.env.name}") String deployedEnv, ReltioUserHolder reltioUserHolder) {
        super(provider, LOCK_COLLECTION_NAME, deployedEnv, reltioUserHolder);
    }

    @Override
    protected TypeReference getTypeReference() {
        return new TypeReference<PmsLock>(){};
    }


}
