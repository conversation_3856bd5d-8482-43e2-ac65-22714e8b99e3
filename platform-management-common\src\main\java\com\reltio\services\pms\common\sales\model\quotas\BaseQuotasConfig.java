package com.reltio.services.pms.common.sales.model.quotas;

import com.fasterxml.jackson.annotation.*;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.QuantityType;
import com.reltio.services.pms.common.sales.QuotaPeriod;
import com.reltio.services.pms.common.sales.QuotaType;
import com.reltio.services.pms.common.sales.model.AbstractProductConfig;
import com.reltio.services.pms.common.sales.model.PMSQuotaName;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "pmsQuotaName", defaultImpl = BaseQuotasConfig.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseQuotasConfig extends AbstractProductConfig {

    @JsonProperty("pmsProductName")
    protected PMSProductName pmsProductName;

    @JsonProperty("pmsQuotaName")
    protected PMSQuotaName pmsQuotaName;

    @JsonProperty("quantity")
    private Float quantity;

    @JsonProperty("quantityType")
    private QuantityType quantityType = QuantityType.COUNT;
    @JsonProperty("quotaPeriod")
    private QuotaPeriod quotaPeriod;

    @JsonProperty("quotaType")
    private QuotaType quotaType;


    public PMSProductName getPmsProductName() {
        return pmsProductName;
    }

    public void setPmsProductName(PMSProductName pmsProductName) {
        this.pmsProductName = pmsProductName;
    }

    public Float getQuantity() {
        return quantity;
    }

    public void setQuantity(Float quantity) {
        this.quantity = quantity;
    }

    public PMSQuotaName getPmsQuotaName() {
        return pmsQuotaName;
    }

    public void setPmsQuotaName(PMSQuotaName pmsQuotaName) {
        this.pmsQuotaName = pmsQuotaName;
    }

    public QuotaPeriod getQuotaPeriod() {
        return quotaPeriod;
    }

    public void setQuotaPeriod(QuotaPeriod quotaPeriod) {
        this.quotaPeriod = quotaPeriod;
    }

    public QuotaType getQuotaType() {
        return quotaType;
    }

    public void setQuotaType(QuotaType quotaType) {
        this.quotaType = quotaType;
    }

    public QuantityType getQuantityType() {
        return quantityType;
    }

    public void setQuantityType(QuantityType quantityType) {
        this.quantityType = quantityType;
    }
}
