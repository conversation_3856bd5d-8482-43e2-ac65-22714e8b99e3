package com.reltio.services.pms.common.model.jobs.tasks.tenants.configs;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class CloneTenantConfigsTaskInstance extends TaskInstance {

    @JsonProperty(value = "sourceTenantId")
    private final String sourceTenantId;

    @JsonProperty(value = "targetTenantId")
    private final String targetTenantId;

    @JsonProperty(value = "sourceEnvId")
    private final String sourceEnvId;

    @JsonProperty(value = "targetEnvId")
    private final String targetEnvId;

    @JsonProperty(value = "targetStreamingConfig")
    private final String targetStreamingConfig;

    @JsonProperty(value = "isAnalyticsEnabledOnTarget")
    private final boolean isAnalyticsEnabledOnTarget ;

    @JsonProperty(value = "events")
    private final List<String> events;

    @JsonCreator
    public CloneTenantConfigsTaskInstance(@JsonProperty(value = "id") String id,
                                                     @JsonProperty(value = "name") String name,
                                                     @JsonProperty(value = "envId") String envId,
                                                     @JsonProperty(value = "jobId") String jobId,
                                                     @JsonProperty(value = "startTime") Long startTime,
                                                     @JsonProperty(value = "finishTime") Long finishTime,
                                                     @JsonProperty(value = "status") TaskStatus status,
                                                     @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                                     @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                                     @JsonProperty(value = "executingNodeName") String executingNodeName,
                                                     @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                                     @JsonProperty(value = "sourceTenantId") String sourceTenantId,
                                                     @JsonProperty(value = "targetTenantId") String targetTenantId,
                                                     @JsonProperty(value = "sourceEnvId") String sourceEnvId,
                                                     @JsonProperty(value = "targetEnvId") String targetEnvId,
                                                     @JsonProperty(value = "targetStreamingConfig") String targetStreamingConfig,
                                                     @JsonProperty(value = "isAnalyticsEnabledOnTarget") boolean isAnalyticsEnabledOnTarget,
                                                     @JsonProperty(value = "events") List<String> events) {
        super(id, name, jobId, startTime, finishTime, TaskType.CLONE_TENANTS_CONFIGS_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.sourceTenantId = sourceTenantId;
        this.targetTenantId = targetTenantId;
        this.sourceEnvId = sourceEnvId;
        this.targetEnvId = targetEnvId;
        this.targetStreamingConfig = targetStreamingConfig;
        this.isAnalyticsEnabledOnTarget = isAnalyticsEnabledOnTarget;
        this.events = events == null ? new ArrayList<>() : new ArrayList<>(events);
    }

    public void addEvent(String event) {
        events.add(event);
    }
}
