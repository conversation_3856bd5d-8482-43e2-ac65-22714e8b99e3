error.INTERNAL_ERROR=Internal Service Error
error.INTERNAL_ERROR.details=An Internal Service Error occurred: {0}
error.GENERIC_ERROR={0}
error.GENERIC_ERROR.details=An Error occurred: {0}
error.BAD_REQUEST={0}
error.BAD_REQUEST.details=Bad request: {0}
error.ENVIRONMENT_DOES_NOT_EXIST_IN_REPOSITORY=Repository does not contain an environment
error.ENVIRONMENT_DOES_NOT_EXIST_IN_REPOSITORY.details=Repository does not contain an environment
error.ENVIRONMENT_IS_NOT_REGISTERED=Environment {0} is not registered in platform management service
error.ENVIRONMENT_IS_NOT_REGISTERED.details=Environment {0} is not registered in platform management service
error.RESOURCE_FILE_IS_NOT_AVAILABLE=An error occurred on attempt to read physical configuration template
error.RESOURCE_FILE_IS_NOT_AVAILABLE.details=An error occurred on attempt to read physical configuration template
error.CLUSTER_NOT_FOUND=Specified cluster {0} does not exist in platform management service
error.CLUSTER_NOT_FOUND.details=Specified cluster {0} does not exist in platform management service
error.PROPERTY_IS_NOT_SEARCH_SUPPORTED=Cluster search is not supported by {0} property
error.PROPERTY_IS_NOT_SEARCH_SUPPORTED.details=Cluster search is not supported by {0} property
error.ENVIRONMENT_NAMES_NOT_EQUAL=Environment names in the body and request path must be equal
error.ENVIRONMENT_NAMES_NOT_EQUAL.details=Environment names in the body and request path must be equal
error.TEMPLATE_NAMES_NOT_EQUAL=Template names in the body and request path must be equal
error.TEMPLATE_NAMES_NOT_EQUAL.details=Template names in the body and request path must be equal
error.DATA_CLUSTER_NOT_FOUND=Specified data cluster with name {0} does not exist in platform management service
error.DATA_CLUSTER_NOT_FOUND.details=Specified data cluster with name {0} does not exist in platform management service
error.ES_CLUSTER_NOT_FOUND=Specified ES cluster with name {0} does not exist in platform management service
error.ES_CLUSTER_NOT_FOUND.details=Specified ES cluster with name {0} does not exist in platform management service
error.DATALOAD_CLUSTER_NOT_FOUND=Specified dataload cluster with url {0} does not exist in platform management service
error.DATALOAD_CLUSTER_NOT_FOUND.details=Specified dataload cluster with url {0} does not exist in platform management service
error.STORAGE_TEMPLATE_NOT_FOUND=Specified storage template does not exist in platform management service
error.STORAGE_TEMPLATE_NOT_FOUND.details=Specified storage template does not exist in platform management service
error.TENANT_STORAGE_CONFIG_NOT_FOUND=Specified tenant storage configuration does not exist in platform management service:
error.TENANT_STORAGE_CONFIG_NOT_FOUND.details=Specified tenant storage configuration does not exist in platform management service
error.REQUESTER_DOES_NOT_EXIST=Specified tenantRequest with email {0} does not exist
error.REQUESTER_DOES_NOT_EXIST.details=Specified tenantRequest with email {0} does not exist
error.EMAIL_IS_A_MANDATORY_FILED=Email must be present
error.EMAIL_IS_A_MANDATORY_FILED.details=Email must be present
error.NONE_OR_BOTH_PARAMETERS_MUST_BE_SPECIFIED=The request need none or both(property and value) parameters to be processed
error.NONE_OR_BOTH_PARAMETERS_MUST_BE_SPECIFIED.details=The request need none or both(property and value) parameters to be processed

error.PRODUCT_EDITION_NOT_FOUND={0} Product Edition is not found
error.PRODUCT_EDITION_NOT_FOUND.details={0} Product Edition is not found

error.PRODUCT_EDITION_ALREADY_EXIST=Product Edition {0} already exist.
error.PRODUCT_EDITION_ALREADY_EXIST.details=Product Edition {0} already exist. Please update existing edition.

error.PRODUCT_EDITION_BRANCH_DOES_NOT_EXIST=Product Edition branch {0} not found.
error.PRODUCT_EDITION_BRANCH_DOES_NOT_EXIST.details=Product Edition branch {0} not found.

error.PRODUCT_EDITION_L3_DOES_NOT_EXIST=Business Configuration file at path {0} does not exist in bitbucket repository.
error.PRODUCT_EDITION_L3_DOES_NOT_EXIST.details=Business Configuration file at path {0} does not exist in bitbucket repository.

error.PRODUCT_EDITION_L3_NOT_VALID_JSON=Business Configuration at path {0} in bitbucket repository is not a valid json file.
error.PRODUCT_EDITION_L3_NOT_VALID_JSON.details=Business Configuration at path {0} in bitbucket repository is not a valid json file.

error.PRODUCT_EDITION_DVF_NOT_EXIST=Data validation functions file at path {0} does not exist in bitbucket repository.
error.PRODUCT_EDITION_DVF_NOT_EXIST.details=Data validation functions file at path {0} does not exist in bitbucket repository.

error.PRODUCT_EDITION_DVF_NOT_VALID_JSON=Data validation functions at path {0} in bitbucket repository is not a valid json file.
error.PRODUCT_EDITION_DVF_NOT_VALID_JSON.details=Data validation functions at path {0} in bitbucket repository is not a valid json file.

error.PRODUCT_EDITION_LOOKUP_FILE_DOES_NOT_EXIST=Look Ups file at path {0} does not exist in bitbucket repository.
error.PRODUCT_EDITION_LOOKUP_FILE_DOES_NOT_EXIST.details=Look Ups file at path {0} does not exist in bitbucket repository.

error.PRODUCT_EDITION_RDM_LOOKUP_FILE_DOES_NOT_EXIST=Rdm Look Ups file at path {0} does not exist in bitbucket repository.
error.PRODUCT_EDITION_RDM_LOOKUP_FILE_DOES_NOT_EXIST.details=Rdm Look Ups file at path {0} does not exist in bitbucket repository.

error.PRODUCT_EDITION_LOOKUPS_NOT_VALID_JSON=Look Ups file at path {0} in bitbucket repository is not a valid json file.
error.PRODUCT_EDITION_LOOKUPS_NOT_VALID_JSON.details=Look Ups file at path {0} in bitbucket repository is not a valid json file.

error.PRODUCT_EDITION_RDM_LOOKUPS_NOT_VALID_JSON=Rdm Look Ups file at path {0} in bitbucket repository is not a valid json file.
error.PRODUCT_EDITION_RDM_LOOKUPS_NOT_VALID_JSON.details=Rdm Look Ups file at path {0} in bitbucket repository is not a valid json file.

error.PRODUCT_EDITION_SAMPLE_DATA_FILE_DOES_NOT_EXIST=Sample data file at path {0} does not exist in bitbucket repository.
error.PRODUCT_EDITION_SAMPLE_DATA_FILE_DOES_NOT_EXIST.details=Sample data file at path {0} does not exist in bitbucket repository.

error.PRODUCT_EDITION_SAMPLE_DATA_FILE_NOT_VALID_JSON=Sample data file at path {0} in bitbucket repository is not a valid json file.
error.PRODUCT_EDITION_SAMPLE_DATA_FILE_NOT_VALID_JSON.details=Sample data file at path {0} in bitbucket repository is not a valid json file.

error.PRODUCT_EDITION_INVALID=Product Edition Configuration is invalid.
error.PRODUCT_EDITION_INVALID.details=Product Edition Configuration is invalid. Please see inner error for detail information.

error.PIPELINE_NOT_FOUND={0} pipeline is not found
error.PIPELINE_NOT_FOUND.details={0} pipeline is not found

error.PIPELINE_ALREADY_EXIST=Pipeline {0} already exist.
error.PIPELINE_ALREADY_EXIST.details=Pipeline {0} already exist. Please update existing edition.

error.NO_TASK_DETAIL_PROVIDER_FOR_TASK_TYPE=Task detail provider is not defined for task {0} type.
error.NO_TASK_DETAIL_PROVIDER_FOR_TASK_TYPE.details=Task detail provider is not defined for task {0} type.

error.JOB_NOT_FOUND={0} job is not found.
error.JOB_NOT_FOUND.details={0} job is not found.

error.PRODUCT_EDITION_UI_CONFIG_FOLDER_NOT_EXIST=UI Configuration folder at path {0} does not exist in bitbucket repository.
error.PRODUCT_EDITION_UI_CONFIG_FOLDER_NOT_EXIST.details=UI Configuration folder at path {0} does not exist in bitbucket repository.

error.PRODUCT_EDITION_UI_CONFIG_FILE_NOT_VALID_JSON=UI plugin file at path {0} in bitbucket repository is not a valid json file.
error.PRODUCT_EDITION_UI_CONFIG_FILE_NOT_VALID_JSON.details=UI plugin file at path {0} in bitbucket repository is not a valid json file.

error.INTERNAL_DB_ERROR=Internal Database Service Error
error.INTERNAL_DB_ERROR.details=An Internal Database Error occurred: {0}

error.TASK_NOT_FOUND={0} task is not found.
error.TASK_NOT_FOUND.details={0} task is not found.
error.UNKNOWN_EMAIL_CONTENT_TYPE=Specified email content type {0} cannot be processed
error.UNKNOWN_EMAIL_CONTENT_TYPE.details=Specified email content type {0} cannot be processed

error.THE_TASK_IS_ALREADY_RESOLVED=The task is already resolved and its status won't change
error.THE_TASK_IS_ALREADY_RESOLVED.details=The task is already resolved and its status won't change

error.TEMPLATE_EMAIL_IS_UNAVAILABLE=Email template cannot be loaded
error.TEMPLATE_EMAIL_IS_UNAVAILABLE.details=Email template cannot be loaded

error.ENVIRONMENT_DOES_NOT_CORRESPOND_TO_TENANTID=Specified tenant does not belong to specified environment
error.ENVIRONMENT_DOES_NOT_CORRESPOND_TO_TENANTID.details=Specified tenant does not belong to specified environment

error.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED=Auth server returned unexpected response.
error.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED.details=Auth server returned unexpected response.

error.ROLE_IS_NOT_VALID=Role is not valid.
error.ROLE_IS_NOT_VALID.details=Role is not valid. Available roles are: {0}

error.UI_CONFIG_UPDATE_EXCEPTION=UI config update for tenant failed.
error.UI_CONFIG_UPDATE_EXCEPTION.details=UI config update for tenant failed.

error.TERMS_FILE_UNAVAILABLE=Cannot load file with terms of use
error.TERMS_FILE_UNAVAILABLE.details=Cannot load file with terms of use

error.PIPELINE_VALIDATION_ERROR=Pipeline configuration is invalid.
error.PIPELINE_VALIDATION_ERROR.details=Pipeline configuration is invalid.

error.REQUESTER_ALREADY_HAVE_RELTIO_ACCOUNT=Thanks for your interest. But the email address already has a valid Reltio account. So please work with your Customer Success Manager for the additional tenant.
error.REQUESTER_ALREADY_HAVE_RELTIO_ACCOUNT.details=Thanks for your interest. But the email address already has a valid Reltio account. So please work with your Customer Success Manager for the additional tenant.

error.UNABLE_TO_PARSE_PHYSICAL_CONFIGURATION_TEMPLATE=Cannot parse physical configuration template.
error.UNABLE_TO_PARSE_PHYSICAL_CONFIGURATION_TEMPLATE.details=Cannot parse physical configuration template.

error.UNABLE_TO_PREPARE_TENANT_URL=Cannot prepare tenant URL for environment: {0}
error.UNABLE_TO_PREPARE_TENANT_URL.details=Cannot prepare tenant URL for environment: {0}. Please check its url.

error.NO_JOB_EXISTS_FOR_THE_REQUESTER=<p>Thank you for your interest in Reltio Identity 360.</p> <p>A request was already made from this account.</p> <p>We are currently in the process of creating your tenant. You will receive an email shortly to verify your address.</p>
error.NO_JOB_EXISTS_FOR_THE_REQUESTER.details=Thank you for your interest in Reltio Identity 360. A request was already made from this account. We are currently in the process of creating your tenant. You will receive an email shortly to verify your address.

error.THE_JOB_IS_COMPLETED=<p>Thank you for your request. It looks like a request was previously received for this email address.</p><p>Please check your inbox for your welcome email from <b><u><EMAIL>.</u></b><p>Didn't receive your welcome email with login details, or having trouble logging in? Email us <a href="mailto:<EMAIL>" title="mailto:<EMAIL>"><b><u>here</u></b></a>, and we'll resend your welcome email with everything you need to get started.</p>
error.THE_JOB_IS_COMPLETED.details=Thank you for your request. It looks like a request was previously received for this email address.Please check your inbox for your welcome <NAME_EMAIL>.

error.EMAIL_IS_NOT_VERIFIED_YET=<p>Thank you for signing up for Reltio Identity 360. It looks like a request was previously received for this email address.&nbsp;</p><p>Please check your inbox for an email from&nbsp;<strong><u><a href="mailto:<EMAIL>"><EMAIL></a></u></strong> and click the<strong>&nbsp;Verify Email Address</strong> button to get started.</p><p>Didn't receive your verification request or having trouble logging in? Email us <strong><a href="mailto:<EMAIL>">here</a></strong> and we'll ensure you have everything you need.</p><p>Don't forget to check out the&nbsp;<a title="Identity 360 playlist" href="https://www.youtube.com/watch?v=2n-queFl0Jg&amp;list=PLAp-ahcfA6G7j5uq-tDBK9BYmyTr5GEFi" target="_blank" rel="noopener"><strong>Identity 360 playlist</strong></a> for everything you need to get started.</p>
error.EMAIL_IS_NOT_VERIFIED_YET.details=Thank you for signing up for Reltio Identity 360. It looks like a request was previously received for this email address.Please check your inbox for <NAME_EMAIL> and click the Verify Email Address button to get started.

error.PROVISION_IS_IN_PROGRESS=<p>Thank you for your interest in Reltio Identity 360.</p> <p>A request was already made from this account.</p> <p>We are currently in the process of creating your tenant. You will receive an email shortly with your login and tenant information.</p>
error.PROVISION_IS_IN_PROGRESS.details=Thank you for your interest in Reltio Identity 360.  A request was already made from this account. We are currently in the process of creating your tenant. You will receive an email shortly with your login and tenant information.

error.EMAIL_WAS_NOT_VERIFIED=Email verification step was not completed
error.EMAIL_WAS_NOT_VERIFIED.details=Email verification step was not completed. Current task status is {0}

error.THE_JOB_HAS_NOT_SUCCEED=<p>Thank you for your interest in Reltio Identity 360.</p> <p>A request was already made from this account.</p> <p>We are currently in the process of creating your tenant. Please check your email for further instructions.</p>
error.THE_JOB_HAS_NOT_SUCCEED.details=Thank you for your interest in Reltio Identity 360. A request was already made from this account. We are currently in the process of creating your tenant. Please check your email for further instructions.

error.THE_TENANT_HAS_NO_CUSTOMER_ASSIGNED=There is no authCustomer assigned to the tenant
error.THE_TENANT_HAS_NO_CUSTOMER_ASSIGNED.details=There is no authCustomer assigned to the tenant. TenantId : {0}

error.THE_TENANT_BELONGS_TO_SEVERAL_CUSTOMERS=The tenant is assigned to several authCustomers
error.THE_TENANT_BELONGS_TO_SEVERAL_CUSTOMERS.details=The tenant assigned to several authCustomers. TenantId : {0}

error.NO_CLIENT_EXISTS_FOR_THE_CUSTOMER=The authCustomer does not have a client
error.NO_CLIENT_EXISTS_FOR_THE_CUSTOMERS.details=The authCustomer does not have a client. Customer : {0}

error.STRIPE_EXCEPTION=An exception occurred while interacting with Stripe
error.STRIPE_EXCEPTION.details=An exception occurred while interacting with Stripe: {0}

error.PRICE_IS_IN_USE=The price is in use and cannot be deactivated
error.PRICE_IS_IN_USE.details=The price is in use and cannot be deactivated. PreiceId: {0}

error.TIER_ALREADY_EXISTS=The tier already exist
error.TIER_ALREADY_EXISTS.details=The tier already exist. Tier name: {0}. Please use update request or delete the existing tier.

error.TIER_NOT_FOUND=The tier does not exist
error.TIER_NOT_FOUND.details=The tier does not exist. Tier name : {0}

error.PRODUCT_EDITION_PERMISSIONS_DOES_NOT_EXIST=Permissions file at path {0} does not exist in bitbucket repository.
error.PRODUCT_EDITION_PERMISSIONS_DOES_NOT_EXIST.details=Permissions file at path {0} does not exist in bitbucket repository.

error.PRODUCT_EDITION_PERMISSIONS_NOT_VALID_JSON=Permissions at path {0} in bitbucket repository is not a valid json file.
error.PRODUCT_EDITION_PERMISSIONS_NOT_VALID_JSON.details=Permissions at path {0} in bitbucket repository is not a valid json file.

error.OFFERTYPE_IS_REQUIRED=Please specify offerType
error.OFFERTYPE_IS_REQUIRED.details=Please specify offerType

error.TASK_IS_NOT_IN_RUNNABLE_STATE=Task can not be executed as it is in {0} state
error.TASK_IS_NOT_IN_RUNNABLE_STATE.details=Task can not be executed as it is in {0} state

error.NO_NEED_IN_UPDATE=Cluster data is already in sync with repository
error.NO_NEED_IN_UPDATE.details=Cluster data is already in sync with repository

error.SUITABLE_REPLACEMENT_NOT_FOUND=No clusters of type {0} in {1} environment can be used for a new tenant creation
error.SUITABLE_REPLACEMENT_NOT_FOUND.details=No clusters of type {0} in {1} environment can be used for a new tenant creation

error.WRONG_FORMAT_TTL=Provided TTL {0} has wrong format
error.WRONG_FORMAT_TTL.details=Provided TTL {0} has wrong format

error.CUSTOMER_NOT_FOUND=Customer with id {0} not found in platform management service
error.CUSTOMER_NOT_FOUND.details=Customer with id {0} not found in platform management service

error.USER_NOT_FOUND=User {0} does not exist.
error.USER_NOT_FOUND.details=User {0} does not exist.


error.ROLES_NOT_CONFIGURED=Roles must be configured in {0} using PMS Config
error.ROLES_NOT_CONFIGURED.details=Roles must be configured in {0} using PMS Config using createAccessLevelRolesMapping

error.UNABLE_TO_CREATE_SERVICE_ACCOUNT=Cannot create a service account due to {0}
error.UNABLE_TO_CREATE_SERVICE_ACCOUNT.details=Cannot create a service account due to {0}

error.UNABLE_TO_CREATE_SERVICE_ACCOUNT_KEY=Cannot create a service account key due to {0}
error.UNABLE_TO_CREATE_SERVICE_ACCOUNT_KEY.details=Cannot create a service account key due to {0}

error.UNABLE_TO_GET_SERVICE_ACCOUNT=Cannot retrieve the service account due to {0}
error.UNABLE_TO_GET_SERVICE_ACCOUNT.details=Cannot retrieve the service account due to {0}

error.UNABLE_TO_MANIPULATE_WITH_SERVICE_ACCOUNT_KEY=Cannot manipulate with a service account key due to {0}
error.UNABLE_TO_MANIPULATE_WITH_SERVICE_ACCOUNT_KEY.details=Cannot manipulate with a service account key due to {0}

error.UNABLE_TO_DELETE_SERVICE_ACCOUNT=Cannot delete a service account due to {0}
error.UNABLE_TO_DELETE_SERVICE_ACCOUNT.details=Cannot delete a service account due to {0}

error.TTL_MAPPING_ALREADY_EXISTS=TTL mapping with id {0} already exists in platform management service
error.TTL_MAPPING_ALREADY_EXISTS.details=TTL mapping with id {0} already exists in platform management service

error.TTL_MAPPING_DOES_NOT_EXIST=TTL mapping with id {0} not found in platform management service
error.TTL_MAPPING_DOES_NOT_EXIST.details=TTL mapping with id {0} not found in platform management service

error.NO_SUCH_ACCESS_LEVEL=Access level mapping with id {0} not found in platform management service
error.NO_SUCH_ACCESS_LEVEL.details=Access level mapping with id {0} not found in platform management service

error.MATCH_CLUSTER_NOT_FOUND=Specified match cluster with name {0} does not exist in platform management service
error.MATCH_CLUSTER_NOT_FOUND.details=Specified match cluster with name {0} does not exist in platform management service

error.FREE_TRIER_JOB_ALREADY_CREATED=There can be only one job created for any requester with any offer type.
error.FREE_TRIER_JOB_ALREADY_CREATED.details=There can be one only job created for any requester with any offer type. Requested job already exist with id {0}.

error.NO_LOOKUP_FOR_CONFIGURED_FOR_PRODUCT_EDITION=There is no configured lookUps for the product edition {0}
error.NO_LOOKUP_FOR_CONFIGURED_FOR_PRODUCT_EDITION.details=There is no configured lookUps for the product edition {0}

error.RDM_LOOKUP_PATH_NOT_CONFIGURED_IN_PRODUCT_EDITION=no rdm lookup path is configured in product edition {0}.
error.RDM_LOOKUP_PATH_NOT_CONFIGURED_IN_PRODUCT_EDITION.details=no rdm lookup path is configured in product edition {0}.

error.CLEANSE_REGION_MAPPING_ALREADY_EXISTS=CLEANSE REGION mapping with id {0} already exists in platform management service
error.CLEANSE_REGION_MAPPING_ALREADY_EXISTS.details=CLEANSE REGION mapping with id {0} already exists in platform management service

error.CLEANSE_REGION_MAPPING_DOES_NOT_EXIST=CLEANSE REGION mapping with id {0} not found in platform management service
error.CLEANSE_REGION_MAPPING_DOES_NOT_EXIST.details=CLEANSE REGION mapping with id {0} not found in platform management service

error.REQUIRED_TASK_PARAMETER_IS_NULL=One or more of required task parameters is null: {0}
error.REQUIRED_TASK_PARAMETER_IS_NULL.details=One or more of required task parameters is null: {0}

error.JOB_STATUS_CANNOT_BE_CHANGED=Job status of {0} is {1} and cannot be updated.
error.JOB_STATUS_CANNOT_BE_CHANGED.details=Job status of {0} is {1} and cannot be updated.

error.PIPELINE_WITHOUT_TASKS=New pipeline must include tasks.
error.PIPELINE_WITHOUT_TASKS.details=New pipeline must include tasks.


error.RDM_TASK_CONFIG_NOT_FOUND_IN_PIPELINE=No rdm task found in pipeline
error.RDM_TASK_CONFIG_NOT_FOUND_IN_PIPELINE.details=No rdm task found in pipeline.

error.TENANT_IS_NOT_PRESENT_IN_DATABASE=The tenant does not exist in the database, tenantId: {0}
error.TENANT_IS_NOT_PRESENT_IN_DATABASE.details=The tenant does not exist in the database, tenantId: {0}

error.UNKNOWN_TASK_PARAMETER=The task does not have {0} parameter
error.UNKNOWN_TASK_PARAMETER.details=The task does not have {0} parameter

error.CONFIG_NAMES_IN_PATH_AND_BODY_MUST_BE_EQUAL=Config names in the path and in the request body are not equal. {0}
error.CONFIG_NAMES_IN_PATH_AND_BODY_MUST_BE_EQUAL.details=Config names in the path and in the request body are not equal. {0}

error.MDM_TASK_CONFIG_NOT_FOUND_IN_PIPELINE=No Mdm task found in pipeline
error.MDM_TASK_CONFIG_NOT_FOUND_IN_PIPELINE.details=No Mdm task found in pipeline.

error.PRODUCT_EDITION_IS_NOT_THERE_IN_PIPELINE_AND_TASK_PARAM=Product edition is not there in tenant creation request as well as pipeline config.
error.PRODUCT_EDITION_IS_NOT_THERE_IN_PIPELINE_AND_TASK_PARAM.details=Product edition is not there in tenant creation request as well as pipeline config.Please provide it in request.

error.ENVIRONMENT_DOES_NOT_SUPPORT_TENANT_SIZE=Max tenant size for environment is {0}.
error.ENVIRONMENT_DOES_NOT_SUPPORT_TENANT_SIZE.details=Max tenant size for environment is not matching.

error.EMAIL_IN_PATH_AND_BODY_MUST_BE_EQUAL=Email in the path and the body do not match.
error.EMAIL_IN_PATH_AND_BODY_MUST_BE_EQUAL.details=Email in the path and the body do not match.

error.TTL_MAPPING_NAME_IS_MANDATORY_IN_PROD_PIPELINE=The ttl mapping is required for prod environment mdm task configuration.
error.TTL_MAPPING_NAME_IS_MANDATORY_IN_PROD_PIPELINE.details=The ttl mapping is required for prod environment mdm task configuration.

error.TTL_MAPPING_DOES_NOT_CONTAIN_TTL_FOR_INDUSTRY=The ttl mapping {0} does't contain ttl for industry {1}.
error.TTL_MAPPING_DOES_NOT_CONTAIN_TTL_FOR_INDUSTRY.details=The ttl mapping {0} does't contain ttl for industry {1}.

error.DEFAULT_TTL_IS_NOT_CONFIGURED_FOR_NON_PROD_ENV=There should be default ttl configured for non prod environments.
error.DEFAULT_TTL_IS_NOT_CONFIGURED_FOR_NON_PROD_ENV.details=There should be default ttl configured for non prod environments.

error.NEED_AT_LEAST_ONE_OWNER_FOR_TENANT=There should be at least one owner for tenant. Owners cannot be null or empty.
error.NEED_AT_LEAST_ONE_OWNER_FOR_TENANT.details=There should be at least one owner for tenant. Owners cannot be null or empty.

error.TENANTS_NOT_FROM_SAME_CUSTOMER=Tenants {0} and {1} are not from same authCustomer.
error.TENANTS_NOT_FROM_SAME_CUSTOMER.details=Tenants {0} and {1} are not from same authCustomer.

error.DUPLICATE_TASK_TYPE=Pipeline {0} should not contain duplicate task type {1}.
error.DUPLICATE_TASK_TYPE.details=Pipeline {0} should not contain duplicate task type {1}.

error.TASK_NOT_SUPPORTED_IN_PIPELINE_AT_ENDPOINT=Pipeline with task type {0} is not supported with this endpoint.
error.TASK_NOT_SUPPORTED_IN_PIPELINE_AT_ENDPOINT.details=Pipeline with task type {0} is not supported with this endpoint.

error.INVALID_STREAMING_CLOUD={0} is not valid cloud for streaming. AWS, Azure and GCP are only supported clouds.
error.INVALID_STREAMING_CLOUD.details={0} is not valid cloud for streaming. AWS, Azure and GCP are only supported clouds.

error.GCP_REGION_MAPPING_ABSENT=There is no GCP to AWS region mapping defined for GCP region {0}.Please add the missing mapping.
error.GCP_REGION_MAPPING_ABSENT.details=There is no GCP to AWS region mapping defined for GCP region {0}.Please add the missing mapping.

error.PROCESS_PARAMETER_IS_NOT_VALID=The process {0} is not an valid option for address cleanser.
error.PROCESS_PARAMETER_IS_NOT_VALID.details=The process {0} is not an valid option for address cleanser.

error.WRONG_PIPELINE_TASK_TYPE =Wrong pipeline task type {0}
error.WRONG_PIPELINE_TASK_TYPE.details =Wrong pipeline task type {0}

error.TENANT_PURPOSE_REQUIRED_FOR_ENABLEMENT_OF_NON_EXISTING_TENANT=Tenant Purpose Required for enablement of tenant which is does not exist in PMS.
error.TENANT_PURPOSE_REQUIRED_FOR_ENABLEMENT_OF_NON_EXISTING_TENANT.details=Tenant Purpose Required for enablement of tenant which is does not exist in PMS.

error.MDM_TENANTS_NOT_FOUND_IN_JOB_REQUEST=MDM Tenants not found in job request.
error.MDM_TENANTS_NOT_FOUND_IN_JOB_REQUEST.details=MDM Tenants not found in job request.

error.RDM_TENANT_NOT_FOUND_IN_JOB_REQUEST=RDM Tenant not found in job request.
error.RDM_TENANT_NOT_FOUND_IN_JOB_REQUEST.details=RDM Tenant not found in job request.

error.GENERATOR_CONFIG_DOES_NOT_EXSIST=Generator config does not exsist for {0}.
error.GENERATOR_CONFIG_DOES_NOT_EXSIST.details=Generator config does not exsist for {0}.

error.MULTIPLE_AUTH_USER_EXIST_REQUESTER=<p>Thank you for your request. It looks like you have one or more user with your email and we can not create new user for you. Please work with your Customer Success Manager for managing your users</p>
error.MULTIPLE_AUTH_USER_EXIST_REQUESTER.details=<p>Thank you for your request. It looks like you have one or more user with your email and we can not create new user for you. Please work with your Customer Success Manager for managing your users</p>

error.MULTIPLE_AUTH_USER_EXIST_OWNER=There are multiple users matching email {0}. Please use one of the email the following emails for this owner: {1}.
error.MULTIPLE_AUTH_USER_EXIST_OWNER.details=There are multiple users matching email {0}. Please use one of the email the following emails for this owner: {1}.

error.TENANT_EXIST_WITH_GIVEN_ID=Tenant with given tenant id already exist and can not used for creating new tenant.Please use api to generate new tenant id.
error.TENANT_EXIST_WITH_GIVEN_ID.details=Tenant with given tenant id already exist and can not used for creating new tenant.Please use api to generate new tenant id.

error.CUSTOMER_ID_IS_NOT_VALID=Customer ID is not valid and can contain only alphanumeric.
error.CUSTOMER_ID_IS_NOT_VALID.details=Customer ID is not valid and can contain only alphanumeric.

error.DTSS_CONFIG_DOES_NOT_EXSIST=DTSS config does not exsist for {0}.
error.DTSS_CONFIG_DOES_NOT_EXSIST.details=DTSS config does not exsist for {0}.
error.CUSTOMER_ID_LENGTH_IS_NOT_IN_RANGE=Length of Customer ID should lie between 2 to 20.
error.CUSTOMER_ID_LENGTH_IS_NOT_IN_RANGE.details=Length of Customer ID should lie between 2 to 20.

error.TENANTS_NOT_FROM_SAME_CUSTOMER_DURING_SHILED_ENABLMENT=All Tenants Must be from same authCustomer.
error.TENANTS_NOT_FROM_SAME_CUSTOMER_DURING_SHILED_ENABLMENT.details=All Tenants Must be from same authCustomer.

error.AZURE_REGION_MAPPING_ABSENT=There is no Azure to AWS region mapping defined for Azure region {0}.Please add the missing mapping.
error.AZURE_REGION_MAPPING_ABSENT.details=There is no Azure to AWS region mapping defined for Azure region {0}.Please add the missing mapping.

error.CONTRACT_NOT_FOUND=The contract with id {0} does not exist in PMS.
error.CONTRACT_NOT_FOUND.details=The contract with id {0} does not exist in PMS.

error.INCORRECT_FORMAT_FOUND=Incorrect value for either of the parameter startDateBefore/startDateAfter/giventime/endDateAfter/endDateBefore.
error.INCORRECT_FORMAT_FOUND.details=Incorrect value for either of the parameter startDateBefore/startDateAfter/giventime/endDateAfter/endDateBefore.

error.DATE_CANNOT_BE_PARSED=Provided date is not parsable.
error.DATE_CANNOT_BE_PARSED.details=Provided date is not parsable.

error.BUSINESS_CUSTOMER_NOT_FOUND=The authCustomer with accountId {0} does not exist in PMS.
error.BUSINESS_CUSTOMER_NOT_FOUND.details=The contract with acocuntId {0} does not exist in PMS.

error.BUSINESS_CUSTOMER_WITH_CUSTOMERID_NOT_FOUND=The authCustomer with customerId {0} does not exist in PMS.
error.BUSINESS_CUSTOMER_WITH_CUSTOMERID_NOT_FOUND.details=The authCustomer with customerId {0} does not exist in PMS.

error.CONTRACT_HAS_CRITICAL_ERRORS=The contract has critical errors. {0}
error.CONTRACT_HAS_CRITICAL_ERRORS.details=The contract has critical errors. Please fix them and try again. {0}

error.PROFILE_NOT_FOUND_FOR_TENANT=Profile not found for the tenantId {0}.
error.PROFILE_NOT_FOUND_FOR_TENANT.details=Profile not found for the tenantId {0}.

error.TENANT_NOT_SUBSCRIBED=Customer tenantId {0} has not subscribed to any dataTenant.
error.TENANT_NOT_SUBSCRIBED.details=Customer tenantId {0} has not subscribed to any dataTenant.

error.CLEAN_TENANT_TASK_CONFIG_NOT_FOUND_IN_PIPELINE=No Clean tenant task found in pipeline
error.CLEAN_TENANT_TASK_CONFIG_NOT_FOUND_IN_PIPELINE.details=No Clean tenant task found in pipeline.

error.UNABLE_TO_GET_JOB_DETAILS=Unable to get job details for tenant with tenantId {0}.
error.UNABLE_TO_GET_JOB_DETAILS.details=Unable to get job details for tenant with tenantId {0}.

error.CLEANABILITY_NOT_ENABLED=Cleanability for the accountId {0} is not enabled, please contact support team.
error.CLEANABILITY_NOT_ENABLED.details=Cleanability for the accountId {0} is not enabled, please contact support team.

error.INCORRECT_TENANT_TYPE=RDM tenant id {0} given but only MDM tenants are allowed. Please input a valid MDM tenant id.
error.INCORRECT_TENANT_TYPE.details=RDM tenant id {0} given but only MDM tenants are allowed. Please input a valid MDM tenant id.

error.CONTRACT_WITH_THIS_ACCOUNTID_NOT_FOUND=No contract with accountId {0} found.
error.CONTRACT_WITH_THIS_ACCOUNTID_NOT_FOUND.details=No contract with accountId {0} found.

error.CONTRACT_WITH_THIS_CUSTOMERID_NOT_FOUND=No contract with customerId {0} found.
error.CONTRACT_WITH_THIS_CUSTOMERID_NOT_FOUND.details=No contract with customerId {0} found.

error.CONTRACT_WITH_THIS_CUSTOMERNAME_NOT_FOUND=No contract with customerName {0} found.
error.CONTRACT_WITH_THIS_CUSTOMERNAME_NOT_FOUND.details=No contract with customerName {0} found.

error.TENANT_NOT_ISOLATED=MDM tenant id {0} is not isolated.
error.TENANT_NOT_ISOLATED.details=MDM tenant id {0} is not isolated.

error.CANNOT_PARSE_PHYSICAL_CONFIGURATION=Physical configuration cannot be parsed
error.CANNOT_PARSE_PHYSICAL_CONFIGURATION.details=Physical configuration cannot be parsed

error.DUPLICATE_DTSS_SUBSCRIPTION_TYPE_OR_DATA_TENANT =Pipeline {0} should not contain duplicate dtssSubscriptionType/dataTenantId {1}.
error.DUPLICATE_DTSS_SUBSCRIPTION_TYPE_OR_DATA_TENANT.details=Pipeline {0} should not contain duplicate dtssSubscriptionType/dataTenantId {1}.

error.TENANT_NOT_FOUND_IN_PLATFORM =Tenant is not found in platform.
error.TENANT_NOT_FOUND_IN_PLATFORM.details=Tenant is not found in platform.

error.CLEAN_UP_TASK_FAILED =Clean up task with {0} failed;
error.CLEAN_UP_TASK_FAILED.details=Clean up task with {0} failed;
error.DELETE_MDM_TASK_FAILED =Delete MDM Tenant task with id {0} failed;
error.DELETE_MDM_TASK_FAILED.details =Delete MDM Tenant task with id {0} failed;
error.PROD_TENANT_DATA_DELETION_NOT_SUPPORTED =Production tenant data deletion is not supported currently.
error.PROD_TENANT_DATA_DELETION_NOT_SUPPORTED.details=Production tenant data deletion is not supported currently.

error.TENANT_SYNC_REQUIRED =Tenant is not presnt in PMS db, contact support team for tenant sync process.
error.TENANT_SYNC_REQUIRED.details=Tenant is not presnt in PMS db, contact support team for tenant sync process.

error.DNB_MAPPING_DOES_NOT_EXIST =DnB mapping does not exist for {0}.
error.DNB_MAPPING_DOES_NOT_EXIST.details =DnB mapping does not exist for {0}.

error.BOTH_RDMTENANTTOREUSE_AND_RDMTENANTID_CANNOT_BE_USED =Both rdmTenantToReuse and rdmTenantId cannot be used, Give only one.
error.BOTH_RDMTENANTTOREUSE_AND_RDMTENANTID_CANNOT_BE_USED.details=Both rdmTenantToReuse and rdmTenantId cannot be used, Give only one.

error.MULTIPLE_OWNERS_ARE_NOT_ALLOWED_IN_TRAINING_CUSTOMER_TYPE=More than 1 owner for Training Customer Type, Give only one.
error.MULTIPLE_OWNERS_ARE_NOT_ALLOWED_IN_TRAINING_CUSTOMER_TYPE.details=More than 1 owner for Training Customer Type, Give only one.

error.NO_JOB_FOUND_FOR_THIS_TENANTID =No Job found for this TenantId {0}.
error.NO_JOB_FOUND_FOR_THIS_TENANTID.details =No Job found for this TenantId {0}.

error.SUBSCRIPTIONID_IS_WRONG=Request you to provide subscriptionId not subscriptionNum {0}.
error.SUBSCRIPTIONID_IS_WRONG.details=Request you to provide subscriptionId not subscriptionNum {0}.
error.CONTRACT_OR_SUBSCRIPTION_NOT_FOUND=The contract with id {0} OR subscriptionId {1} does not exist in PMS or deactive.
error.CONTRACT_OR_SUBSCRIPTION_NOT_FOUND.details=The contract with id {0} OR subscriptionId {1} does not exist in PMS or deactive.
error.NO_SUBSCRIPTION_NUM=There is no subscriptionNum {0}
error.NO_SUBSCRIPTION_NUM.details=There is no subscriptionNum {0}
error.NO_SUBSCRIPTION_ID=There is no subscriptionId {0}
error.NO_SUBSCRIPTION_ID.details=There is no subscriptionId {0}

error.ZENDESK_TICKET_NOT_FOUND=The zendesk ticket does not exist, zendeskId: {0}.
error.ZENDESK_TICKET_NOT_FOUND.details=The zendesk ticket does not exist, zendeskId: {0}.

error.TENANT_FOR_ZENDESK_NOT_FOUND=The tenant does not exist, tenantId: {0}.
error.TENANT_FOR_ZENDESK_NOT_FOUND.details=The tenant does not exist, tenantId: {0}.

error.USER_FOR_ZENDESK_NOT_FOUND=The user does not exist, username: {0}.
error.USER_FOR_ZENDESK_NOT_FOUND.details=The user does not exist, username: {0}.

error.ZENDESK_TENANT_IS_HIPAA=Non US user {0} cannot be given access to HIPAA tenant {1}.
error.ZENDESK_TENANT_IS_HIPAA.details=Non US user {0} cannot be given access to HIPAA tenant {1}.

error.USER_NOT_RELTIO=Email domain must be Reltio. User must be part of Reltio to be given access, username: {0}.
error.USER_NOT_RELTIO.details=Email domain must be Reltio. User must be part of Reltio to be given access, username: {0}.

error.EMAIL_NOT_VALID=Email is not valid, username: {0}.
error.EMAIL_NOT_VALID.details=Email is not valid, username: {0}.

error.NOT_A_VALID_STORAGE=Not a valid Storage Type : {0}.
error.NOT_A_VALID_STORAGE.details=Not a valid Storage Type : {0}.

error.THERE_IS_NO_CLUSTERS_SATISFYING_STORAGE_PRIORITY_LIST=There is no correct cluster for provisioning: {0} for environment {1}.
error.THERE_IS_NO_CLUSTERS_SATISFYING_STORAGE_PRIORITY_LIST.details=There is no correct cluster for provisioning: {0} for environment {1}.

error.INVALID_ZENDESK_TICKET_STATUS=Zendesk ticket status is {0}. Zendesk ticket status cannot be closed, solved, or deleted.
error.INVALID_ZENDESK_TICKET_STATUS.details=Zendesk ticket status is {0}. Zendesk ticket status cannot be closed, solved, or deleted.

error.USER_DOES_NOT_HAVE_ROLE_TO_REMOVE=User does not have role {0} to remove.
error.USER_DOES_NOT_HAVE_ROLE_TO_REMOVE.details=User does not have role {0} to remove.

error.UI_CONFIG_PATH_DOES_NOT_EXSIST=UI config path does not exsist for {0}.
error.UI_CONFIG_PATH_DOES_NOT_EXSIST.details=UI config path does not exsist for {0}.

error.NO_REQUESTS_FOR_ZENDESK_ID_TO_REVOKE=No requests corresponding to inputted zendesk Id {0} to revoke access.
error.NO_REQUESTS_FOR_ZENDESK_ID_TO_REVOKE.details=No requests corresponding to inputted zendesk Id {0} to revoke access.

error.NO_OKTA_USER_PROFILE=User {0} does not have okta profile.
error.NO_OKTA_USER_PROFILE.details=User {0} does not have okta profile.

error.GBQ_CONFIG_IS_NOT_DEFINED_FOR_TENANT=A GBQ configuration is not defined for the tenant.
error.GBQ_CONFIG_IS_NOT_DEFINED_FOR_TENANT.details=A GBQ configuration is not defined for the tenant.

error.SUPPORTABILITY_DB_ERROR=Request to Supportability DB failed.
error.SUPPORTABILITY_DB_ERROR.details=Request to Supportability DB failed.

error.CONTRACT_NOT_IN_EXPIRED_STATE=Contract {0} is not in expired state.
error.CONTRACT_NOT_IN_EXPIRED_STATE.details=Contract {0} is not in expired state.

error.COMPLIANCE_DB_ERROR=Request to Compliance DB failed.
error.COMPLIANCE_DB_ERROR.details=Request to Compliance DB failed.

error.CONFIG_IS_NOT_AVAILABLE=Config is not available for tenantId {0} in config-service.
error.CONFIG_IS_NOT_AVAILABLE.details=Config is not available for tenantId {0} in config-service.

error.NOT_ABLE_TO_UPDATE_CONFIG_SERVICE=Not able to update Config Service.
error.NOT_ABLE_TO_UPDATE_CONFIG_SERVICE.details=Not able to update Config Service.

error.INCORRECT_TENANT_STATE=Tenant {0} has improper maintenance mode value {1}.
error.INCORRECT_TENANT_STATE.details=Tenant {0} has improper maintenance mode value {1}.

error.CANNOT_CREATE_JOB_EARLY=Cannot create job 30 minutes before last cleanup job which is not failed.
error.CANNOT_CREATE_JOB_EARLY.details=Cannot create job 30 minutes before last cleanup job which is not failed..

error.CAN_NOT_CREATE_NEW_TENANT_IN_CONTRACT_STAGE_CHANGE =Can not create new tenant when contract stage is Change.
error.CAN_NOT_CREATE_NEW_TENANT_IN_CONTRACT_STAGE_CHANGE.details =Can not create new tenant when contract stage is Change.

error.PRODUCT_EDITION_NOT_FOUND_IN_CONTRACT =Product Edition not found in Contract.
error.PRODUCT_EDITION_NOT_FOUND_IN_CONTRACT.details =Product Edition not found in Job Contract.

error.NOT_ABLE_TO_FIND_ENVIRONMENT =Not able to find environment.
error.NOT_ABLE_TO_FIND_ENVIRONMENT.details =Not able to find environment.

error.DOC_ID_IS_EMPTY=Document Id is empty,please provide the valid doc Id.
error.DOC_ID_IS_EMPTY.details=Document Id is empty,please provide the valid doc Id.

error.CUSTOMER_CONTACT_IS_NOT_PRESENT_IN_SALESFORCE_CONTRACT=Customer contact is not there in Salesforce Contract.
error.CUSTOMER_CONTACT_IS_NOT_PRESENT_IN_SALESFORCE_CONTRACT.details=Customer contact is not there in Salesforce Contract.

error.INCORRECT_GCP_PROJECT_NAME=Current deployment does not support {0} gcp project.
error.INCORRECT_GCP_PROJECT_NAME.details=Current deployment does not support {0} gcp project.

error.DNB_MAPPING_OR_DTS_CONFIG_NOT_AVAILABLE_FOR_PRODUCT_EDITION=DNB mapping or DTSS config not available for Product Edition {0}.
error.DNB_MAPPING_OR_DTS_CONFIG_NOT_AVAILABLE_FOR_PRODUCT_EDITION.details=DNB mapping or DTSS config not available for Product Edition {0}.

error.INVALID_AZURE_CLOUD=Azure is not supported clouds for DataSet.
error.INVALID_AZURE_CLOUD.details=Azure is not supported clouds for DataSet.

error.SERVICE_NODE_NOT_FOUND={0} service node is not found.
error.SERVICE_NODE_NOT_FOUND.details={0} service node is not found.

error.RESPONSE_FROM_NOTIFICATION_SERVICE_CANNOT_BE_PROCESSED=Invalid response from notification service

error.ERROR_GETTING_PROFILES_FOR_THIS_TENANT=Not able to get profiles for tenantId {0}.
error.ERROR_GETTING_PROFILES_FOR_THIS_TENANT.details=Not able to get profiles for tenantId {0}.

error.TENANT_IS_NOT_REGISTERED_SALESFORCE=Tenant id {0} is not registered.
error.TENANT_IS_NOT_REGISTERED_SALESFORCE.details=Tenant id {0} is not registered.

error.ERROR_WHILE_DE_REGISTERING_TENANT_FROM_DNB=Error occurred while de registering tenant {0} from dnb.
error.ERROR_WHILE_DE_REGISTERING_TENANT_FROM_DNB.details=Error occurred while de registering tenant {0} from dnb.

error.TRAINING_TENANT_ALREADY_EXISTS_FOR_USER=Training tenant already exists for user {0}.
error.TRAINING_TENANT_ALREADY_EXISTS_FOR_USER.details=Training tenant already exists for user {0}.

error.SALES_PACKAGE_MAPPING_NOT_EXIST=Sales Package mapping with id {0} not found in platform management service {0}.
error.SALES_PACKAGE_MAPPING_NOT_EXIST.details=Sales Package mapping with id {0} not found in platform management service {0}.

error.RELTIO_PACKAGE_TYPE_PRODUCT_CODE_CONFIG_NOT_EXIST=Reltio Package Type Product Config not found in platform management service {0}.
error.RELTIO_PACKAGE_TYPE_PRODUCT_CODE_CONFIG_NOT_EXIST.details=Reltio Package Type Product Config not found in platform management service {0}.

error.PACKAGE_TYPE_PRODUCT_CODE_NOT_VALID =Provided Package Type Product Code is not valid Product Code from Sales Force {0}.
error.PACKAGE_TYPE_PRODUCT_CODE_NOT_VALID.details =Provided Package Type Product Code is not valid Product Code from Sales Force {0}.

error.NO_SUCH_PRODUCT_EDITION_EXISTS_IN_PMS=No such Product Edition Exists in PMS for values {0} and {1}.
error.NO_SUCH_PRODUCT_EDITION_EXISTS_IN_PMS.details=No such Product Edition Exists in PMS for values {0} and {1} . Please provide correct values .

error.NEED_BOTH_SUBJECT_AND_BODY=We need both subject and body.
error.NEED_BOTH_SUBJECT_AND_BODY.details=We need both subject and body.

error.DUPLICATE_PROVISIONING_REQUEST=Duplicate Provisioning Request
error.DUPLICATE_PROVISIONING_REQUEST.details=This is a duplicate provisioning request for emails {0}.

error.ERROR_WHILE_LOCKING=There was an error while locking
error.ERROR_WHILE_LOCKING.details=There was an error while locking. Try increasing the timeout.

error.FAILED_TO_ACQUIRE_LOCK=Failed to acquire lock
error.FAILED_TO_ACQUIRE_LOCK.details=Failed to acquire lock as the previous lock was not released.

error.SECONDARY_NAME_DOES_NOT_EXIST=No secondary name exists for the environment {0}.
error.SECONDARY_NAME_DOES_NOT_EXIST.details=No secondary name exists for the environment {0}.

error.UNABLE_TO_FETCH_RESPONSE=Unable to fetch response
error.UNABLE_TO_FETCH_RESPONSE.details=Unable to fetch response

error.MULTIPLE_OWNERS_NOT_ALLOWED_FOR_TRAINING_TENANT=Multiple owners are not allowed for training tenant.
error.MULTIPLE_OWNERS_NOT_ALLOWED_FOR_TRAINING_TENANT.details=Multiple owners are not allowed for training tenant.

error.DATA_TENANT_IS_NOT_CORRECT=Data tenant is not correct.
error.DATA_TENANT_IS_NOT_CORRECT.details=Data tenant is not correct : {0}.

error.THE_CLIENT_ALREADY_EXISTS=Client already exist.
error.THE_CLIENT_ALREADY_EXISTS.details=Client already exist.

error.FILTER_AND_ORDER_FIELDS_MUST_BE_DIFFERENT=Filter and order fields must be different.
error.FILTER_AND_ORDER_FIELDS_MUST_BE_DIFFERENT.details=Filter and order fields must be different.

error.CANT_DELETE_GBT_TABLE=Can't delete GBT table.
error.CANT_DELETE_GBT_TABLE.details=Can't delete GBT table.

error.RETRY_EXCEPTION=Retry Exception.
error.RETRY_EXCEPTION.details=Retry Exception occurred {0}.

error.CONFIG_IS_NOT_AVAILABLE_IN_CONFIG_SERVICE=Configuration not available in Config service.
error.CONFIG_IS_NOT_AVAILABLE_IN_CONFIG_SERVICE.details=Configuration not available in Config service for {0}.

error.TENANT_PROVISIONING_CUSTOMER_TYPE_CONFIG_NOT_FOUND=Tenant Provisioning Customer Type Config not found.
error.TENANT_PROVISIONING_CUSTOMER_TYPE_CONFIG_NOT_FOUND.details=Tenant Provisioning Customer Type Config not found : {0}.

error.TENANT_PROVISIONING_CLOUD_CONFIG_NOT_FOUND=Tenant Provisioning Cloud Config not found.
error.TENANT_PROVISIONING_CLOUD_CONFIG_NOT_FOUND.details=Tenant Provisioning Cloud Config not found for : {0}.

error.SERVICE_URL_IS_NOT_AVAILABLE=Service URL not available.
error.SERVICE_URL_IS_NOT_AVAILABLE.details=Service URL not available for : {0} , env {1} .

error.NO_JOB_EXISTS_FOR_PARENT_JOB_ID_IN_PMS = No Job Exists For Parent Job ID : {0}.
error.NO_JOB_EXISTS_FOR_PARENT_JOB_ID_IN_PMS.details = No Job Exists For Parent Job ID : {0} , Please check your Parent Job ID

error.NO_JOB_HAS_DRAFT_STATUS_FOR_PARENT_JOB_ID = No Job Has DRAFT Status For Parent Job ID : {0}.
error.NO_JOB_HAS_DRAFT_STATUS_FOR_PARENT_JOB_ID.details = No Job Has DRAFT status For Parent Job ID : {0}. Job status cannot be updated

error.JOB_NOT_IN_DRAFT_STATE = One or more jobs are not in DRAFT status.
error.JOB_NOT_IN_DRAFT_STATE.details = One or more jobs are not in DRAFT status for Parent Job I'd : {0}. Bulk Job cannot be deleted

error.CANNOT_EXCEED_LENGTH = The key or value exceeds length of 500 characters.
error.CANNOT_EXCEED_LENGTH.details = The key or value exceeds length of 500 characters for key {0} , value {0}

error.JOB_CREATION_FAILED = Job creation failed.
error.JOB_CREATION_FAILED.details = Job creation failed.

error.UNABLE_TO_MAP_PHYSICAL_CONFIGURATION_PARAMETERS = Unable to map physical configuration parameters with response template.
error.UNABLE_TO_MAP_PHYSICAL_CONFIGURATION_PARAMETERS.details = Unable to map physical configuration parameters with response template.

error.INCORRECT_PATH = Incorrect path {0} .
error.INCORRECT_PATH.details = Incorrect path {0} . Please provide correct path .

error.UPDATED_PARAMS_INPUT_MAP_IS_EMPTY_OR_NULL = Updated params input map is empty or null.
error.UPDATED_PARAMS_INPUT_MAP_IS_EMPTY_OR_NULL.details = Updated params input map is empty or null. Please provide correct input data.

error.PARAM_TO_BE_UPDATED_IS_NOT_PRESENT_IN_PMS_CONFIG = Cannot update param {0}. Please make sure the param is present in pms config collection.
error.PARAM_TO_BE_UPDATED_IS_NOT_PRESENT_IN_PMS_CONFIG.details = Cannot update param {0}. Please make sure the param is present in pms config collection.

error.CANNOT_UPDATE_A_READ_ONLY_PARAM = Cannot update a read only param {0}.
error.CANNOT_UPDATE_A_READ_ONLY_PARAM.details = Cannot update a read only param {0}. Please provide correct input data.

error.PHYSICAL_CONFIG_KEY_ABSENT_IN_PMS_CONFIG = No physical config key present for param {0} in pms config.
error.PHYSICAL_CONFIG_KEY_ABSENT_IN_PMS_CONFIG.details = No physical config key present for param {0} in pms config. Please make sure to add it before proceeding.

error.NOT_AN_ALLOWED_VALUE = Provided value {0} for param {1} is not allowed.
error.NOT_AN_ALLOWED_VALUE.details = Provided value {0} for param {1} is not allowed. Please make sure to provide only allowed values from pms config.

error.NO_UNIQUE_IDENTIFIER = No unique identifier present for param {0} in pms config.
error.NO_UNIQUE_IDENTIFIER.details = No unique identifier present for param {0} in pms config.

error.NO_UPDATE_VERB_PRESENT_IN_PMS_CONFIG = No update verb present in pms config for param {0}.
error.NO_UPDATE_VERB_PRESENT_IN_PMS_CONFIG.details = No update verb present in pms config {0}. Please make sure to add it in pms config.

error.NO_PARENT_NODE_PRESENT_IN_PHYSICAL_CONFIG = Cannot update nested param {0}. No parent node exists in physical config.
error.NO_PARENT_NODE_PRESENT_IN_PHYSICAL_CONFIG.details = Cannot update nested param {0}. No parent node exists in physical config.

error.NO_MATCHING_ARRAY_ELEMENT_FOUND = Cannot update param {0}.No matching element found in array with unique identifier {1} : {2}.
error.NO_MATCHING_ARRAY_ELEMENT_FOUND.details = Cannot update param {0}.No matching element found in array with unique identifier {1} : {2}.

error.TENANT_PROVISIONING_ENV_CONFIG_NOT_FOUND=Tenant Provisioning Env Config not found.
error.TENANT_PROVISIONING_ENV_CONFIG_NOT_FOUND.details=Tenant Provisioning Env Config not found for : {0}.

error.MULTIPLE_TENANT_IDS_ARE_NOT_ALLOWED_WITH_CUSTOM_QUEUE_NAMES = Multiple tenant ids are not allowed with custom queue names.
error.MULTIPLE_TENANT_IDS_ARE_NOT_ALLOWED_WITH_CUSTOM_QUEUE_NAMES.details = Multiple tenant ids are not allowed with custom queue names. Please use a single tenantId for job creation .

error.NO_MATCHING_STREAMING_CONFIG_FOUND_FOR_CLOUD_PROVIDER = No matching streaming config found for cloud provider : {0} , in the tenant's physical configuration.
error.NO_MATCHING_STREAMING_CONFIG_FOUND_FOR_CLOUD_PROVIDER.details = No matching streaming config found for cloud provider : {0}  , in the tenant's physical configuration.

error.STREAMING_QUEUES_ARE_NOT_ENABLED_FOR_THE_TENANT = Streaming queues are not enabled for the tenant.
error.STREAMING_QUEUES_ARE_NOT_ENABLED_FOR_THE_TENANT.details = Streaming queues are not enabled for the tenant.

error.DELETE_TENANT_FROM_SYSTEM_KEYSPACE_FAILED = Delete tenant {0} from system keyspace failed.
error.DELETE_TENANT_FROM_SYSTEM_KEYSPACE_FAILED.details = Delete tenant {0} from system keyspace failed.

error.INVALID_FILTER_COMBINATION = Only endDate is allowed as a filter when it is provided.
error.INVALID_DATE_FORMAT = Invalid date format for endDate. Use 'yyyyMMdd'.

error.ERROR_WHILE_DELETING_AZURE_QUEUE_RESOURCES = Error occurred while deleting azure queue resources.
error.ERROR_WHILE_DELETING_AZURE_QUEUE_RESOURCES.details = Error occurred while deleting azure queue resources.

error.DE_PROVISION_JOB_ALREADY_EXISTS = De provision job already exists for the tenant with jobId {0}.
error.DE_PROVISION_JOB_ALREADY_EXISTS.details = De provision job already exists for the tenant with jobId {0}.Please check the status of job before retrying.

error.UI_CONFIG_GET_EXCEPTION = UI config not found.
error.UI_CONFIG_GET_EXCEPTION.details = No UI config found for the provided parameters.

error.CANNOT_CREATE_DATA_BACKUP_JOB_WITH_SPECIFIED_STORAGES = Data backup job is only allowed with tenant storages as dynamodb or spannerdb .
error.CANNOT_CREATE_DATA_BACKUP_JOB_WITH_SPECIFIED_STORAGES.details = Data backup job is only allowed with tenant storages as dynamodb or spannerdb . Please make sure you have the correct storages .

error.CANNOT_CREATE_DATA_BACKUP_JOB_WITH_DIFFERENT_PHYSICAL_CONFIGURATION_PARAMETERS = Data backup job is not allowed with different values of physical configuration parameters indexOvStrategy or matchingStrategy .
error.CANNOT_CREATE_DATA_BACKUP_JOB_WITH_DIFFERENT_PHYSICAL_CONFIGURATION_PARAMETERS.details = Data backup job is not allowed with different values of physical configuration parameters indexOvStrategy or matchingStrategy . Please make sure to use "forceOverridePhyConfig" as true to override the parameters in target tenant physical configuration.

error.CANNOT_CREATE_BACKUP_JOB_WITH_MISSING_PARAMS = One or more mandatory params (sourceEnvId, sourceTenantId, ticketId) are null or blank .
error.CANNOT_CREATE_BACKUP_JOB_WITH_MISSING_PARAMS.details = One or more mandatory params (sourceEnvId, sourceTenantId, ticketId) are null or blank . Please provide valid values .

error.DATA_BACKUP_JOB_ALREADY_EXISTS_WITH_GIVEN_SOURCE_TENANT_ID_AND_TICKET_ID = Data backup job already exists for the given source tenant id and ticket id with job id {0} and task id {1} .
error.DATA_BACKUP_JOB_ALREADY_EXISTS_WITH_GIVEN_SOURCE_TENANT_ID_AND_TICKET_ID.details = Data backup job already exists for the given source tenant id and ticket id with job id {0} and task id {1} . Please check job status and use check data backup api for more details .

error.DATA_RESTORE_JOB_ALREADY_EXISTS_WITH_GIVEN_SOURCE_TENANT_ID_AND_TICKET_ID = Data restore job already exists for the given target tenant id and ticket id with job id {0} and task id {1} .
error.DATA_RESTORE_JOB_ALREADY_EXISTS_WITH_GIVEN_SOURCE_TENANT_ID_AND_TICKET_ID.details = Data restore job already exists for the given target tenant id and ticket id . Please check job status and use check data restore api for more details .

error.CANNOT_CREATE_DATA_RESTORE_JOB_WITH_INCORRECT_TENANT_STATUS = Cannot create data restore job ,target tenant has either maintenance enabled already or has with incorrect status.
error.CANNOT_CREATE_DATA_RESTORE_JOB_WITH_INCORRECT_TENANT_STATUS.details = Cannot create data restore job ,target tenant has either maintenance enabled already or has with incorrect status.

error.CANNOT_CREATE_DATA_RESTORE_JOB_WHEN_TENANT_HAS_RUNNING_TASKS = Cannot create data restore job when target tenant has running tasks.
error.CANNOT_CREATE_DATA_RESTORE_JOB_WHEN_TENANT_HAS_RUNNING_TASKS.details = Cannot create data restore job when target tenant has running tasks.

error.CANNOT_CREATE_DATA_CLONE_JOB_WITH_DIFFERENT_PHYSICAL_CONFIGURATION_PARAMETERS = Data clone job is not allowed with different values of physical configuration parameters indexOvStrategy or matchingStrategy .
error.CANNOT_CREATE_DATA_CLONE_JOB_WITH_DIFFERENT_PHYSICAL_CONFIGURATION_PARAMETERS.details = Data clone job is not allowed with different values of physical configuration parameters indexOvStrategy or matchingStrategy . Please make sure to use "forceOverridePhyConfig" as true to override the parameters in target tenant physical configuration.

error.QUEUE_ALREADY_EXIST = Queue {0} already exist.
error.QUEUE_ALREADY_EXIST.details = Queue {0} already exist.

error.RETENTION_PERIOD_VALUE_IS_NOT_VALID = Retention period value is not valid.
error.RETENTION_PERIOD_VALUE_IS_NOT_VALID.details = Retention period value is not valid. Value should be between 30 and 90 days .

error.HISTORY_BACKUP_JOB_ALREADY_EXISTS_WITH_GIVEN_SOURCE_TENANT_ID_AND_TICKET_ID = History backup job already exists for the given source tenant id and ticket id with job id {0} and task id {1} .
error.HISTORY_BACKUP_JOB_ALREADY_EXISTS_WITH_GIVEN_SOURCE_TENANT_ID_AND_TICKET_ID.details = History backup job already exists for the given source tenant id and ticket id with job id {0} and task id {1} . Please check job status and use check history backup api for more details .

error.HISTORY_RESTORE_JOB_ALREADY_EXISTS_WITH_GIVEN_SOURCE_TENANT_ID_AND_TICKET_ID = History restore job already exists for the given target tenant id and ticket id with job id {0} and task id {1} .
error.HISTORY_RESTORE_JOB_ALREADY_EXISTS_WITH_GIVEN_SOURCE_TENANT_ID_AND_TICKET_ID.details = Data restore job already exists for the given target tenant id and ticket id . Please check job status and use check history restore api for more details .

error.ERROR_WHILE_CREATING_GBT_BACKUP = An error occurred while creating backup for the gbt table.
error.ERROR_WHILE_CREATING_GBT_BACKUP.details = An error occurred while creating backup for the gbt table.

error.ERROR_WHILE_RESTORING_HISTORY_FROM_GBT_BACKUP = An error occurred while restoring history from gbt backup.
error.ERROR_WHILE_RESTORING_HISTORY_FROM_GBT_BACKUP.details = An error occurred while restoring history from gbt backup.

error.HISTORY_STORAGE_SPECIFIED_FOR_THE_TENANT_IS_NOT_GBT = History storage specified for the tenant is not GBT.
error.HISTORY_STORAGE_SPECIFIED_FOR_THE_TENANT_IS_NOT_GBT.details = History storage specified for the tenant is not GBT.


