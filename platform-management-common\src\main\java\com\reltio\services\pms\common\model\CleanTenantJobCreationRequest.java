package com.reltio.services.pms.common.model;

import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.Parameters;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

@AllArgsConstructor
@Builder
@Getter
@NoArgsConstructor
public class CleanTenantJobCreationRequest {
    private  String tenantName;
    private Parameters parameters;
    private boolean qaAutomation;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        if (!super.equals(o)) {
            return false;
        }

        CleanTenantJobCreationRequest that = (CleanTenantJobCreationRequest) o;
        return Objects.equals(getTenantName(), that.getTenantName()) &&
                Objects.equals(getParameters(), that.getParameters());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getTenantName(), getParameters());
    }

    @Override
    public String toString() {
        return "CleanTenantJobCreationRequest{" +
                ", tenantName='" + tenantName + '\'' +
                ", parameters=" + parameters +
                '}';
    }
}
