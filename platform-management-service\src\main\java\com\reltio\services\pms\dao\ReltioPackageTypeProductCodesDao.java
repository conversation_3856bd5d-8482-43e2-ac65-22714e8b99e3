package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.config.ReltioPackageTypeProductCodes;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Set;

/**
 * The type Reltio package type product codes dao.
 */
@Service
public class ReltioPackageTypeProductCodesDao extends AbstractRootCollectionDao<ReltioPackageTypeProductCodes> {
    /**
     * The constant CONFIG_COLLECTION_NAME.
     */
    private static final String CONFIG_COLLECTION_NAME = "PMS_CONFIG";
    /**
     * The constant LOGGER.
     */
    private static final Logger LOGGER = Logger.getLogger(ReltioPackageTypeProductCodesDao.class);

    /**
     * Instantiates a new Reltio package type product codes dao.
     *
     * @param provider         the provider
     * @param deployedEnv      the deployed env
     * @param reltioUserHolder the reltio user holder
     */
    @Autowired
    public ReltioPackageTypeProductCodesDao(CredentialsProvider provider,
                                            @Value("${firestore.env.name}") String deployedEnv,
                                            ReltioUserHolder reltioUserHolder) {
        super(provider, CONFIG_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties = Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties, LOGGER);
    }

    /**
     * Gets type reference.
     *
     * @return the type reference
     */
    @Override
    protected TypeReference<ReltioPackageTypeProductCodes> getTypeReference() {
        return new TypeReference<ReltioPackageTypeProductCodes>() {
        };
    }
}
