package com.reltio.services.pms.clients.external.aws.s3;


import com.reltio.services.pms.clients.external.aws.credentials.AccessSecretKeyCredentialsProviderImageHosting;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.model.GetCallerIdentityRequest;
import software.amazon.awssdk.services.sts.model.GetCallerIdentityResponse;


@Service
public class S3Service {

    private static final String S3_ARN_FORMAT = "arn:aws:s3::%s:%s/*";

    private final AccessSecretKeyCredentialsProviderImageHosting s3AccountCredentials;

    private final S3ClientConfig s3ClientConfig;

    private final StsClient sts;

    private String defaultAccount;

    private static final Logger LOG = Logger.getLogger(S3Service.class);


    @Autowired
    public S3Service(AccessSecretKeyCredentialsProviderImageHosting s3AccountCredentials, S3ClientConfig s3ClientConfig) {
        this.s3AccountCredentials = s3AccountCredentials;
        this.s3ClientConfig = s3ClientConfig;
        AwsCredentialsProvider awsCredentialsProvider = StaticCredentialsProvider.create(s3AccountCredentials);
        this.sts = StsClient.builder()
                .credentialsProvider(awsCredentialsProvider)
                .region(Region.of(s3AccountCredentials.getAWSRegion()))
                .build();
    }

    public boolean doesBucketExist(String bucketName, String region) {
        try (S3Client s3client = s3ClientConfig.createClient(region)) {
            try {
                s3client.headBucket(HeadBucketRequest.builder().bucket(bucketName).build());
                return true;
            } catch (Exception ex) {
                if (ex instanceof AwsServiceException && ((AwsServiceException) ex).statusCode() == 404) {
                    return false;
                } else {
                    throw ex;
                }
            }
        }
    }

    public void createBucket(String bucketName, String region) {
        try (S3Client s3client = s3ClientConfig.createClient(region)) {
            s3client.createBucket(CreateBucketRequest.builder().bucket(bucketName).build());
        }
    }

    public void createFolder(String region, String bucketName, String folderName) {
        try (S3Client s3client = s3ClientConfig.createClient(region)) {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(folderName)
                    .build();
            s3client.putObject(putObjectRequest, RequestBody.empty());
        }
    }

    public String getURL(String region) {
        return String.format("https://s3.%s.amazonaws.com", region);
    }

    public String getBucketARN(String bucketName) {
        return String.format(S3_ARN_FORMAT, getDefaultAccount(), bucketName);
    }

    private synchronized String getDefaultAccount() {
        if (defaultAccount == null) {
            GetCallerIdentityRequest getCallerIdentityRequest = GetCallerIdentityRequest.builder().build();
            GetCallerIdentityResponse result = sts.getCallerIdentity(getCallerIdentityRequest);
            defaultAccount = result.account();
        }
        return defaultAccount;
    }

    public void makeObjectReadPublic(String bucketName, S3Client s3client) {
        PublicAccessBlockConfiguration publicAccessBlockConfiguration = PublicAccessBlockConfiguration.builder()
                .blockPublicAcls(false).ignorePublicAcls(false)
                .build();

        PutPublicAccessBlockRequest putPublicAccessBlockRequest = PutPublicAccessBlockRequest.builder()
                .bucket(bucketName)
                .publicAccessBlockConfiguration(publicAccessBlockConfiguration)
                .build();
        s3client.putPublicAccessBlock(putPublicAccessBlockRequest);

    }

    public void modifyObjectOwnership(String bucketName,S3Client s3client){
        OwnershipControls ownershipControls = OwnershipControls.builder()
                .rules(OwnershipControlsRule.builder()
                        .objectOwnership(ObjectOwnership.BUCKET_OWNER_PREFERRED)
                        .build())
                .build();

        PutBucketOwnershipControlsRequest ownershipControlsRequest = PutBucketOwnershipControlsRequest.builder()
                .bucket(bucketName)
                .ownershipControls(ownershipControls)
                .build();

        s3client.putBucketOwnershipControls(ownershipControlsRequest);
    }

    public void enableAclForBucket(String region,String bucketName) {
        try (S3Client s3client = s3ClientConfig.createClient(region)) {
            makeObjectReadPublic(bucketName,s3client);
            modifyObjectOwnership(bucketName,s3client);
            PutBucketAclRequest putBucketAclRequest = PutBucketAclRequest.builder()
                    .bucket(bucketName)
                    .acl(BucketCannedACL.PUBLIC_READ)
                    .build();

            s3client.putBucketAcl(putBucketAclRequest);
        }
    }

    public void deleteS3Bucket(String region,String bucketName) {
        try (S3Client s3client = s3ClientConfig.createClient(region)) {
            deleteAllObjects(s3client, bucketName);

            DeleteBucketRequest deleteBucketRequest = DeleteBucketRequest.builder()
                    .bucket(bucketName)
                    .build();
            s3client.deleteBucket(deleteBucketRequest);
        }catch (NoSuchBucketException e) {
            LOG.info("Bucket " + bucketName + " does not exist");
        }
    }

    private static void deleteAllObjects(S3Client s3Client, String bucketName) {
        // Delete all objects
        ListObjectsV2Request listObjectsReq = ListObjectsV2Request.builder()
                .bucket(bucketName)
                .build();

        ListObjectsV2Response listObjectsResp = s3Client.listObjectsV2(listObjectsReq);
        for (S3Object object : listObjectsResp.contents()) {
            s3Client.deleteObject(DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(object.key())
                    .build());
        }

        // Delete object versions if the bucket is versioned
        ListObjectVersionsRequest listVersionsReq = ListObjectVersionsRequest.builder()
                .bucket(bucketName)
                .build();

        ListObjectVersionsResponse listVersionsResp = s3Client.listObjectVersions(listVersionsReq);
        for (ObjectVersion version : listVersionsResp.versions()) {
            s3Client.deleteObject(DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(version.key())
                    .versionId(version.versionId())
                    .build());
        }
    }
}
