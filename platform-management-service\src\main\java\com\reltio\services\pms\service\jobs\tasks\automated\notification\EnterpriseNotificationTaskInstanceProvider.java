package com.reltio.services.pms.service.jobs.tasks.automated.notification;

import com.reltio.services.pms.common.model.jobs.tasks.notification.EnterpriseNotificationTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.notification.EnterpriseNotificationTaskInstanceParams;
import com.reltio.services.pms.common.model.pipeline.tasks.EnterpriseNotificationPipelineTaskConfig;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.jobs.tasks.TaskInstanceProvider;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class EnterpriseNotificationTaskInstanceProvider implements TaskInstanceProvider<EnterpriseNotificationTaskInstance, EnterpriseNotificationPipelineTaskConfig, EnterpriseNotificationTaskInstanceParams> {
    @Override
    public TaskType getTaskType() {
        return TaskType.ENTERPRISE_NOTIFICATION_TASK;
    }

    @Override
    public EnterpriseNotificationTaskInstance getTaskDetail(String envId, String jobId, EnterpriseNotificationPipelineTaskConfig pipelineTaskConfig, EnterpriseNotificationTaskInstanceParams taskInstanceParams) {
        return new EnterpriseNotificationTaskInstance(
                UUID.randomUUID().toString(),
                TaskType.ENTERPRISE_NOTIFICATION_TASK_NAME,
                jobId,
                null,
                null,
                null,
                null,
                null,
                null,
                 null,
                taskInstanceParams.getOwners(),
                envId
        );
    }
}
