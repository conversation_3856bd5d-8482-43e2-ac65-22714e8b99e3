package com.reltio.services.pms.clients.reltio.mdm;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.reltio.services.pms.common.IPMSRestTemplate;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.util.Iterator;
import java.util.Map;
import java.util.Objects;

@Service
@Retryable(exceptionExpression = "${retry.shouldRetry:true}", maxAttempts = 5, backoff = @Backoff(delay = 1000, multiplier = 5))
public class MLEnablementClient {
    private static final Logger LOGGER = LogManager.getLogger(MLEnablementClient.class);
    private final IPMSRestTemplate rest;
    private static final String CONFIG_URL = "%s/service/%s/?environment=%s&tenant=%s";
    private static final String DEFAULT = "&default=true";
    private static final String NAMESPACE = "dataModeller";
    private static final String AVAILABLE_FEATURES = "availableFeatures";

    @Autowired
    public MLEnablementClient(@Qualifier("console") IPMSRestTemplate rest) {
        this.rest = rest;
    }

    public JsonNode getConfiguration(String env, String tenantId, String serviceUrl) {
        String url = getUrl(env, tenantId, serviceUrl) + DEFAULT;
        return rest.getForObject(url, JsonNode.class);
    }

    public JsonNode getConfigForTenant(String env, String tenantId, String serviceUrl) {
        try {
            JsonNode response = getConfiguration(env, tenantId, serviceUrl);
            Objects.requireNonNull(response, "Got Null as response from conifig-service");
            JsonNode jsonNode1 = Objects.requireNonNull(response).get("data").get(AVAILABLE_FEATURES);

            ((ObjectNode) response).remove("data");
            ((ObjectNode) response).remove("createdAt");
            ((ObjectNode) response).remove("createdBy");
            ((ObjectNode) response).putObject(AVAILABLE_FEATURES);
            JsonNode availableFeatures = response.get(AVAILABLE_FEATURES);

            Iterator<Map.Entry<String, JsonNode>> fields = jsonNode1.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> field = fields.next();
                ((ObjectNode) availableFeatures).put(field.getKey(), field.getValue());
            }
            ((ObjectNode) availableFeatures).put("matchIqModels", true);
            return response;
        } catch (HttpClientErrorException.NotFound ex) {
            LOGGER.warn("config not available");
            return null;
        } catch (Exception ex) {
            LOGGER.warn(String.format("Exception raised while getting config %s", ex));
            throw new PlatformManagementException(PlatformManagementErrorCode.CONFIG_IS_NOT_AVAILABLE, HttpStatus.NOT_FOUND.value(), ex, tenantId);
        }
    }

    public void postConfigForTenant(String env, String tenantId, JsonNode request, String serviceUrl) {
        String url = getUrl(env, tenantId, serviceUrl);
        try {
            rest.postForObject(url, request, JsonNode.class);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CONFIG_IS_NOT_AVAILABLE, HttpStatus.NOT_FOUND.value(), ex, tenantId);
        }
    }

    public void putConfigForTenant(String env, String tenantId, JsonNode request, String serviceUrl){
        String url = getUrl(env, tenantId, serviceUrl);
        rest.put(url, request);
    }

    private String getUrl(String env, String tenantId, String serviceUrl) {
        return  String.format(CONFIG_URL, serviceUrl, NAMESPACE, env, tenantId);
    }

}
