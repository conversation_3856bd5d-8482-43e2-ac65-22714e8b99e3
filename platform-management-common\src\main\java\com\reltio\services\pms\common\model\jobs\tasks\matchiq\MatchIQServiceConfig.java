package com.reltio.services.pms.common.model.jobs.tasks.matchiq;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.pipeline.tasks.FeatureToggles;

import java.util.List;
import java.util.Objects;

public class MatchIQServiceConfig {
    @JsonProperty(value = "matchServiceUrl")
    private final String matchServiceUrl;

    @JsonProperty(value = "sharedModelId")
    private final String sharedModelId;

    @JsonProperty(value = "finalFeatureToggles")
    private final FeatureToggles finalFeatureToggles;

    @JsonProperty(value = "actionThresholds")
    private final List<ActionThreshold> actionThresholds;

    @JsonCreator
    public MatchIQServiceConfig(@JsonProperty(value = "matchServiceUrl") String matchServiceUrl,
                                @JsonProperty(value = "sharedModelId") String sharedModelId,
                                @JsonProperty(value = "finalFeatureToggles", required = true)  FeatureToggles finalFeatureToggles,
                                @JsonProperty(value = "actionThresholds", required = true) List<ActionThreshold> actionThresholds) {
        this.matchServiceUrl = matchServiceUrl;
        this.sharedModelId = sharedModelId;
        this.finalFeatureToggles = validateFeatureToggles(finalFeatureToggles);
        this.actionThresholds = validateActionThresholds(actionThresholds);
    }

    public List<ActionThreshold> validateActionThresholds(List<ActionThreshold> actionThresholds){
        if (actionThresholds == null) {
            throw new NullPointerException("Action Threshold cannot be null.");
        } else if (actionThresholds.isEmpty()){
            throw new IllegalArgumentException("Action Threshold cannot be an empty list.");
        }
        return actionThresholds;
    }

    public FeatureToggles validateFeatureToggles(FeatureToggles featureToggles) {
        if (featureToggles == null) {
            throw new NullPointerException("finalFeatureToggles cannot be null.");
        }
        return featureToggles;
    }

    public String getMatchServiceUrl() {
        return matchServiceUrl;
    }

    public String getSharedModelId() {
        return sharedModelId;
    }

    public FeatureToggles getFinalFeatureToggles() {
        return finalFeatureToggles;
    }

    public List<ActionThreshold> getActionThresholds() {
        return actionThresholds;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MatchIQServiceConfig)) {
            return false;
        }

        MatchIQServiceConfig that = (MatchIQServiceConfig) o;
        return Objects.equals(getMatchServiceUrl(), that.getMatchServiceUrl()) &&
                Objects.equals(getSharedModelId(), that.getSharedModelId()) &&
                Objects.equals(getFinalFeatureToggles(), that.getFinalFeatureToggles()) &&
                Objects.equals(getActionThresholds(), that.getActionThresholds());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getMatchServiceUrl(), getSharedModelId(), getFinalFeatureToggles(), getActionThresholds());
    }

    @Override
    public String toString() {
        return "MatchIQServiceConfig{" +
                "matchServiceUrl='" + matchServiceUrl + '\'' +
                ", sharedModelId='" + sharedModelId + '\'' +
                ", finalFeatureToggles=" + finalFeatureToggles +
                ", actionThresholds=" + actionThresholds +
                '}';
    }
}