package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.sales.model.HistoryRestore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class HistoryRestoreDao extends AbstractRootCollectionDao<HistoryRestore> {
    private static final String HISTORY_BACKUP_COLLECTION_NAME = "PMS_RESTORE_HISTORY";

    @Autowired
    public HistoryRestoreDao(CredentialsProvider provider, @Value("${firestore.env.name}") String deployedEnv, ReltioUserHolder reltioUserHolder) {
        super(provider, HISTORY_BACKUP_COLLECTION_NAME, deployedEnv, reltioUserHolder);
    }

    @Override
    protected TypeReference<HistoryRestore> getTypeReference() {
        return new TypeReference<>() {
        };
    }
}
