package com.reltio.services.pms.common.model.proxy.generic;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.proxy.ActionStatus;
import com.reltio.services.pms.common.model.proxy.ExecutionStatus;
import com.reltio.services.pms.common.model.proxy.SecureAction;
import com.reltio.services.pms.common.model.proxy.SecureActionHandlerName;

import java.util.Objects;

public class GenericSecureAction extends SecureAction {

    private final SecureActionHandlerName secureActionHandlerName;

    public GenericSecureAction(@JsonProperty("secureKey") String secureKey,
                               @JsonProperty("environmentId") String environmentId,
                               @JsonProperty("jobId") String jobId,
                               @JsonProperty("taskId") String taskId,
                               @JsonProperty("status") ActionStatus status,
                               @JsonProperty("executionStatus") ExecutionStatus executionStatus,
                               @JsonProperty("secureActionHandlerName") SecureActionHandlerName secureActionHandlerName) {
        super(secureKey, environmentId, jobId, taskId, status, executionStatus);
        this.secureActionHandlerName = secureActionHandlerName;
    }

    public SecureActionHandlerName getSecureActionHandlerName() {
        return secureActionHandlerName;
    }


    @Override
    public boolean equals(Object object) {
        if(Objects.isNull(object)) {
            return false;
        }
        if (this == object) {
            return true;
        }
        if (!(this.getClass().equals(object.getClass()))) {
            return false;
        }
        GenericSecureAction that = (GenericSecureAction) object;
        return getSecureActionHandlerName() == that.getSecureActionHandlerName();
    }

    @Override
    public int hashCode() {
        return Objects.hash(getSecureActionHandlerName());
    }

    @Override
    public String toString() {
        return "GenericSecureAction{" +
                "secureActionHandlerName=" + secureActionHandlerName +
                "} " + super.toString();
    }
}
