package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.Getter;

@Getter
public class StreamingDeProvisionPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonProperty(value = "gcpProjectName")
    private String gcpProjectName;

    @JsonCreator
    public StreamingDeProvisionPipelineTaskConfig(@JsonProperty(value = "name") String name,
                                                  @JsonProperty(value = "gcpProjectName") String gcpProjectName) {
        super(name, TaskType.STREAMING_DE_PROVISION_TASK);
        this.gcpProjectName = gcpProjectName;
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.QUEUES;
    }

    @Override
    public String toString() {
        return "StreamingPipelineTaskConfig{}";
    }

}
