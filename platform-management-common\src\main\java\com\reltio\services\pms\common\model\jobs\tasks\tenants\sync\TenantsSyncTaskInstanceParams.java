package com.reltio.services.pms.common.model.jobs.tasks.tenants.sync;

import com.reltio.services.pms.common.model.jobs.tasks.TaskInstanceParams;
import com.reltio.services.pms.common.sales.TenantPurpose;

import java.util.List;

public interface TenantsSyncTaskInstanceParams extends TaskInstanceParams {

    TenantPurpose getTenantPurpose();

    List<String> getTenantsList();

    boolean isProcessAllTenants();
}
