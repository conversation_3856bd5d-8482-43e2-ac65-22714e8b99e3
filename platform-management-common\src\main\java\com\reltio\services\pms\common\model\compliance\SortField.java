package com.reltio.services.pms.common.model.compliance;
/**
 * The enum Sort field.
 */
public enum SortField {
    /**
     * Report date sort field.
     */
    ReportDate,
    /**
     * Cp usage sort field.
     */
    CpUsage,
    /**
     * Tenant Id sort field.
     */
    TenantId,
    /**
     * Environment sort field.
     */
    Environment,
    /**
     * Total Rsu sort field
     */
    TotalRsu,
    /**
     * Total Size sort field
     */
    TotalSize,
    /**
     * Data Size sort field
     */
    DataSize,
    /**
     * Match Data sort field
     */
    MatchData,
    /**
     * History Data sort field
     */
    HistoryData,
    /**
     * Activity Data sort field
     */
    ActivityData,
    /**
     * Index data sort field
     */
    IndexData,
    /**
     * Interaction size sort field
     */
    InteractionSize,
    /**
     * Rsu Usage  sort field
     */
    RsuUsage,
    /**
     * Api usage sort field.
     */
    ApiUsage,

    /**
     * Entity type sort field.
     */
    EntityType,

    /**
     * Entity count sort field.
     */
    EntityCount,

    /**
     * Billing start date sort field.
     */
    BillingStartDate,

    /**
     * Billing End data sort field.
     */
    BillingEndDate,
    /**
     * TaskQuota sort field.
     */
    TaskQuota,
    /**
     * Customer sort field.
     */
    Customer,
    /**
     * CpQuota sort field.
     */
    CpQuota,
    /**
     * TaskUsage sort field.
     */
    TaskUsage,
    /**
     * TaskPercentage sort field.
     */
    TaskPercentage,
    /**
     * HandlerMapping sort field.
     */
    HandlerMapping,
    /**
     * HTTPMethod sort field.
     */
    HTTPMethod,
    /**
     * SourceSystem sort field.
     */
    SourceSystem,

}
