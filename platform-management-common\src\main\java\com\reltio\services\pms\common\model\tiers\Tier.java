package com.reltio.services.pms.common.model.tiers;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;

import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Tier extends BaseFirestoreEntity {

    @JsonProperty("name")
    private String name;

    @JsonProperty("label")
    private String label;

    @JsonProperty("productEdition")
    private String productEdition;

    @JsonProperty("entitlement")
    private Map<String, String> entitlement;

    @JsonProperty("paymentType")
    private PaymentType paymentType;

    @JsonProperty("stripeProductId")
    private String stripeProductId;

    @JsonProperty("billingCycle")
    private List<Billing> billingCycle;

    @JsonProperty("notificationEmails")
    private List<String> notificationEmails;

    @JsonProperty("quotas")
    private Map<String, Integer> quotas;

    @JsonCreator
    public Tier(@JsonProperty("name") String name,
                @JsonProperty("label") String label,
                @JsonProperty("productEdition") String productEdition,
                @JsonProperty("entitlement") Map<String, String> entitlement,
                @JsonProperty("paymentType") PaymentType paymentType,
                @JsonProperty("stripeProductId") String stripeProductId,
                @JsonProperty("billingCycle") List<Billing> billingCycle,
                @JsonProperty("notificationEmails") List<String> notificationEmails,
                @JsonProperty("quotas") Map<String, Integer> quotas) {
        this.name = name;
        this.label = label;
        this.productEdition = productEdition;
        this.entitlement = entitlement;
        this.paymentType = paymentType;
        this.stripeProductId = stripeProductId;
        this.billingCycle = billingCycle;
        this.notificationEmails = notificationEmails;
        this.quotas = quotas;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getProductEdition() {
        return productEdition;
    }

    public void setProductEdition(String productEdition) {
        this.productEdition = productEdition;
    }

    public PaymentType getPaymentType() {
        return paymentType;
    }

    public void setStripeProductId(String stripeProductId) {
        this.stripeProductId = stripeProductId;
    }

    public String getStripeProductId() {
        return stripeProductId;
    }

    public List<Billing> getBillingCycle() {
        return billingCycle;
    }

    public void setBillingCycle(List<Billing> billingCycle) {
        this.billingCycle = billingCycle;
    }

    @Override
    public String getID() {
        return getName();
    }

    public static class Billing{
        @JsonProperty("recurring")
        private PaymentCycle recurring;

        @JsonProperty("stripePriceId")
        private String stripePriceId;

        @JsonProperty("priceTiers")
        private List<PriceTier> priceTiers;

        @JsonCreator
        public Billing(@JsonProperty("recurring") PaymentCycle recurring,
                       @JsonProperty("stripePriceId") String stripePriceId,
                       @JsonProperty("priceTiers") List<PriceTier> priceTiers) {
            this.recurring = recurring;
            this.priceTiers = priceTiers;
            this.stripePriceId = stripePriceId;
        }

        public PaymentCycle getRecurring() {
            return recurring;
        }

        public List<PriceTier> getPriceTiers() {
            return priceTiers;
        }

        public void setPriceTiers(List<PriceTier> priceTiers) {
            this.priceTiers = priceTiers;
        }

        public void setStripePriceId(String stripePriceId) {
            this.stripePriceId = stripePriceId;
        }

        public String getStripePriceId() {
            return stripePriceId;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            Billing billing = (Billing) o;

            if (recurring != billing.recurring) return false;
            return priceTiers.containsAll(billing.priceTiers) && billing.priceTiers.containsAll(priceTiers);
        }

        @Override
        public int hashCode() {
            int result = recurring.hashCode();
            result = 31 * result + priceTiers.hashCode();
            return result;
        }
    }

}
