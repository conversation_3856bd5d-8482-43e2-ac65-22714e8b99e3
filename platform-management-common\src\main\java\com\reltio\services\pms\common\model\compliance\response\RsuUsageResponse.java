package com.reltio.services.pms.common.model.compliance.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class RsuUsageResponse {
    @JsonProperty("reportDate")
    private String reportDate;

    @JsonProperty("environment")
    private String environment;

    @JsonProperty("tenantId")
    private String tenantId;

    @JsonProperty("totalRsu")
    private Long totalRsu;
    @JsonProperty("totalSize")
    private Long totalSize;
    @JsonProperty("dataSize")
    private Long dataSize;
    @JsonProperty("matchData")
    private Long matchData;
    @JsonProperty("historyData")
    private Long historyData;
    @JsonProperty("activityData")
    private Long activityData;
    @JsonProperty("indexData")
    private Long indexData;

    @JsonProperty("interactionSize")
    private Long interactionSize;

    @JsonProperty("fullCount")
    private Long fullCount;

}
