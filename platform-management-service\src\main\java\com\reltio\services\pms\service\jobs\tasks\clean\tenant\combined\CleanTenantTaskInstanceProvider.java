package com.reltio.services.pms.service.jobs.tasks.clean.tenant.combined;

import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.AnalyticsCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.DataCleanUpOptions;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.ExternalServicesCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.MetaDataCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.combined.CleanTenantTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.combined.CleanTenantTaskInstanceParams;
import com.reltio.services.pms.common.model.pipeline.tasks.CleanTenantTaskConfig;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.ValidationErrorCollector;
import com.reltio.services.pms.service.environment.EnvironmentService;
import com.reltio.services.pms.service.jobs.tasks.TaskInstanceProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Service
@Deprecated
public class CleanTenantTaskInstanceProvider implements TaskInstanceProvider<CleanTenantTaskInstance, CleanTenantTaskConfig, CleanTenantTaskInstanceParams> {

    private final EnvironmentService environmentService;

    @Autowired
    public CleanTenantTaskInstanceProvider(EnvironmentService environmentService) {
        this.environmentService = environmentService;
    }

    @Override
    public Class<? super CleanTenantTaskInstanceParams> getTaskInstanceParamType() {
        return TaskInstanceProvider.super.getTaskInstanceParamType();
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.DELETE_TENANT_DATA_TASK;
    }

    @Override
    public void validateTaskConfig(String envId, CleanTenantTaskConfig pipelineTaskConfig, ValidationErrorCollector validationErrorCollector) {
        TaskInstanceProvider.super.validateTaskConfig(envId, pipelineTaskConfig, validationErrorCollector);
        validateServiceUrl(envId, pipelineTaskConfig);

    }

    @Override
    public void validateTaskInstance(CleanTenantTaskInstance taskInstance, ValidationErrorCollector validationErrorCollector) {
        TaskInstanceProvider.super.validateTaskInstance(taskInstance, validationErrorCollector);
    }

    @Override
    public CleanTenantTaskInstance getTaskDetail(String envId, String jobId, CleanTenantTaskConfig pipelineTaskConfig, CleanTenantTaskInstanceParams taskInstanceParams) {

        DataCleanUpOptions dataCleanUpOptions = pipelineTaskConfig.getDataCleanUpOptions();
        dataCleanUpOptions.setGraphClean(dataCleanUpOptions.isGraphClean() && taskInstanceParams.getParameters().isTenantData());
        dataCleanUpOptions.setActivities(dataCleanUpOptions.isActivities() && taskInstanceParams.getParameters().isHistoryData());
        dataCleanUpOptions.setHistory(dataCleanUpOptions.isHistory() && taskInstanceParams.getParameters().isHistoryData());
        dataCleanUpOptions.setPrimary(dataCleanUpOptions.isPrimary() && taskInstanceParams.getParameters().isTenantData());
        dataCleanUpOptions.setSecondary(dataCleanUpOptions.isSecondary() && taskInstanceParams.getParameters().isTenantData());
        dataCleanUpOptions.setInternalQueue(dataCleanUpOptions.isInternalQueue() && taskInstanceParams.getParameters().isTenantData());

        AnalyticsCleanUp analyticsCleanUp = pipelineTaskConfig.getAnalyticsCleanUp();
        analyticsCleanUp.setRiq(analyticsCleanUp.isRiq() && taskInstanceParams.getParameters().isTenantData());
        analyticsCleanUp.setGbQClean(analyticsCleanUp.isGbQClean() && taskInstanceParams.getParameters().isTenantData());
        analyticsCleanUp.setMatchIQ(analyticsCleanUp.isMatchIQ() && taskInstanceParams.getParameters().isTenantData());

        MetaDataCleanUp metaDataCleanUp = pipelineTaskConfig.getMetaDataCleanUp();
        metaDataCleanUp.setLookUpsClean(metaDataCleanUp.isLookUpsClean() && taskInstanceParams.getParameters().isTenantData());

        boolean externalQueue = (pipelineTaskConfig.isExternalQueue() && taskInstanceParams.getParameters().isTenantData());

        ExternalServicesCleanUp externalServicesCleanUp = pipelineTaskConfig.getExternalServicesCleanUp();
        externalServicesCleanUp.setWorkflow(externalServicesCleanUp.isWorkflow() && taskInstanceParams.getParameters().isTenantData());
        validateServiceUrl(envId, pipelineTaskConfig);

        return new CleanTenantTaskInstance(
                UUID.randomUUID().toString(),
                TaskType.CLEAN_TENANT_TASK_NAME, jobId, 0L, null, null, null, null, null, null,
                envId,
                dataCleanUpOptions,
                externalServicesCleanUp,
                externalQueue,
                metaDataCleanUp,
                analyticsCleanUp,
                taskInstanceParams.getTenantName(),
                Collections.emptyList(),
                Collections.emptyList(),
                taskInstanceParams.getParameters(),
                taskInstanceParams.isQaAutomation()
        );
    }


    private void validateServiceUrl(String environmentName, CleanTenantTaskConfig cleanTenantTaskConfig) {
        List<ServiceType> serviceTypes = new ArrayList<>();
        if (cleanTenantTaskConfig.getAnalyticsCleanUp().isMatchIQ()) {
            serviceTypes.add(ServiceType.ML);
        }
        if (cleanTenantTaskConfig.getAnalyticsCleanUp().isRiq()) {
            serviceTypes.add(ServiceType.RI_API);
        }
        if (cleanTenantTaskConfig.getDataCleanUpOptions().isInternalQueue()) {
            serviceTypes.add(ServiceType.IRS);
        }
        environmentService.validateServiceUrl(serviceTypes, environmentName);
    }
}
