package com.reltio.services.pms.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * GBQ configuration
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GBQConfiguration {
    private boolean enabled;
    private long limitBytes;

    /**
     * GBQ configuration
     *
     * @param enabled    - enable\disable GBQ
     * @param limitBytes - limit size in bytes
     */
    @JsonCreator
    public GBQConfiguration(@JsonProperty(value = "enabled", required = true) boolean enabled,
                            @JsonProperty(value = "limitBytes", required = true) long limitBytes) {
        this.enabled = enabled;
        this.limitBytes = limitBytes;
    }
}
