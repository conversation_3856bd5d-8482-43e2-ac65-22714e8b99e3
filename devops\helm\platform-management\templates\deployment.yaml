apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: {{.Values.serviceName}}
    environment: {{ .Release.Namespace }}
    fullName: {{ .Release.Namespace }}-{{.Values.serviceName}}
    version: {{ .Values.version }}
  name: {{.Values.serviceName}}
  namespace: {{ .Release.Namespace }}
spec:
  minReadySeconds: 10
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: {{ .Values.maxUnavailable }}
      maxSurge: {{ .Values.maxSurge }}
  selector:
    matchLabels:
      app: {{.Values.serviceName}}
  replicas: {{ .Values.replicaCount }}
  template:
    metadata:
      labels:
        app: {{.Values.serviceName}}
    spec:
    {{- if  .Values.azureOcean2 }}
      tolerations:
        - key: "kubernetes.azure.com/scalesetpriority"
          operator: "Equal"
          value: "spot"
          effect: "NoSchedule"
    {{- end }}
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            fullName: {{ .Release.Namespace }}-{{.Values.serviceName}}
      - maxSkew: 1
        topologyKey: topology.kubernetes.io/zone
        whenUnsatisfiable: DoNotSchedule
        labelSelector:
          matchLabels:
            fullName: {{ .Release.Namespace }}-{{.Values.serviceName}}
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: kubernetes.io/arch
                    operator: In
                    values:
                      - "{{ .Values.arch | default "amd64" }}"
        {{- if .Values.onDemand }}
                  - key: spotinst.io/node-lifecycle
                    operator: In
                    values:
                      - od
        {{- else if .Values.spotinstPercent }}
                  - key: spotinstPercent
                    operator: In
                    values:
                      - "{{.Values.spotinstPercent}}"
        {{- else if .Values.awsInstanceLifecycle }}
                  - key: awsInstanceLifecycle
                    operator: In
                    values:
                      - "{{.Values.awsInstanceLifecycle}}"
        {{- else }}
        {{- end }}
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                {{- if not .Values.spotinstPercent }}
                  - key: fullName
                    operator: In
                    values:
                      - {{ .Release.Namespace }}-{{.Values.serviceName}}
                {{- else}}
                  - key: spotinst.io/restrict-scale-down
                    operator: In
                    values:
                      - "true"
                {{- end}}
              topologyKey: kubernetes.io/hostname
      imagePullSecrets:
      - name: {{ .Values.regcred }}
      volumes:
      - name: config-volume
        projected:
          sources:
          - secret:
              name: {{ .Release.Namespace }}-{{.Values.serviceName}}-properties
              items:
              - key: {{ .Release.Namespace }}-{{ .Values.serviceName }}.properties
                path: {{ .Release.Namespace }}-{{ .Values.serviceName }}.properties
      - name: persistent-storage
        persistentVolumeClaim:
          claimName: {{ .Values.efsClaim }}
      {{- if hasKey .Values "awsIamSaRole" }}
      serviceAccount: {{ .Values.serviceName }}-sa
      serviceAccountName: {{.Values.serviceName}}-sa
      {{- end }}
      containers:
      - image: "{{ .Values.imageRepository }}"
        imagePullPolicy: {{ .Values.imagePullPolicy }}
        name: {{ .Chart.Name }}
        securityContext:
          runAsNonRoot: true
          runAsUser: 10000
        livenessProbe:
          httpGet:
            scheme: HTTP
            path: /healthcheck
            port: 9090
          initialDelaySeconds: 50
          periodSeconds: 60
          timeoutSeconds: 60
          failureThreshold: 5
        readinessProbe:
          httpGet:
            scheme: HTTP
            path: /healthcheck
            port: 9090
          initialDelaySeconds: 50
          periodSeconds: 6
          failureThreshold: 1
          timeoutSeconds: 5
        env:
        - name: POD_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: PLATFORM_MANAGEMENT_CONFIG_NAME
          value: {{ .Release.Namespace }}-{{ .Values.serviceName }}.properties
        - name: OTEL_AGENT_HOST
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: OTEL_AGENT_PORT
          value: "4318"
        - name: OTEL_TRACES_EXPORTER
          value: none
        - name: OTEL_EXPORTER_OTLP_METRICS_PROTOCOL
          value: http/protobuf
        - name: OTEL_EXPORTER_OTLP_METRICS_ENDPOINT
          value: http://$(OTEL_AGENT_HOST):$(OTEL_AGENT_PORT)/v1/metrics
        - name: OTEL_SERVICE_NAME
          value: {{ .Release.Namespace }}-{{.Values.serviceName}}
        - name: OTEL_RESOURCE_ATTRIBUTES
          value: "deployment.environment={{ .Release.Namespace }}"
        - name: VAULT_TOKEN
          valueFrom:
            secretKeyRef:
              name: vault-secret-{{ .Values.serviceName }}
              key: vault-token
        - name: VAULT_URL
          valueFrom:
            secretKeyRef:
              name: vault-secret-{{ .Values.serviceName }}
              key: vault-endpoint
        - name: VAULT_NAMESPACE
          valueFrom:
            secretKeyRef:
              name: vault-secret-{{ .Values.serviceName }}
              key: vault-namespace
        - name: JAVA_OPTS
          value:  "-Xmx{{ required "JAVA_HEAP_SIZE must be defined" .Values.JAVA_HEAP_SIZE }} \
                -DFS_DIR=/usr/src/application/config \
                {{- if .Values.otelEnabled }}
                -javaagent:/usr/src/application/apm/opentelemetry-javaagent.jar \
                {{- end }}
                --add-opens java.base/java.util=ALL-UNNAMED \
                --add-opens java.base/java.lang=ALL-UNNAMED \
                -XX:+DisableExplicitGC \
                -XX:+HeapDumpOnOutOfMemoryError \
                -XX:HeapDumpPath=/heapdumps/$(POD_ID).hprof \
                -Xlog:gc*:file=./logs/gc.log:time,level,tags \
                -XX:+UnlockDiagnosticVMOptions \
                -XX:+LogVMOutput \
                -XX:LogFile=./logs/jvm.log \
                {{ default "" .Values.additionalJavaOpts }}"
        volumeMounts:
        - mountPath: /usr/src/application/config
          name: config-volume
          readOnly: true
        - mountPath: /heapdumps
          name: persistent-storage
          subPath: heapdump/{{ .Release.Namespace }}
        ports:
        - containerPort: 8080
          name: "http"
        - containerPort: 9090
          name: "http-health"
        resources:
          requests:
            cpu: {{ .Values.requestsCpu }}
            memory: {{ .Values.requestsMemory }}
            ephemeral-storage: {{ .Values.ephemeralStorageRequest }}
          limits:
            cpu: {{ .Values.limitsCpu }}
            memory: {{ .Values.limitsMemory }}
            ephemeral-storage: {{ .Values.ephemeralStorageLimit }}
