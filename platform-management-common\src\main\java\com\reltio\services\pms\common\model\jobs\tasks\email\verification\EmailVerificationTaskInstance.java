package com.reltio.services.pms.common.model.jobs.tasks.email.verification;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.Requester;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

import java.util.Objects;

public class EmailVerificationTaskInstance extends TaskInstance {

    @JsonProperty("requesterEmail")
    private Requester requester;

    @JsonProperty("secureActionId")
    private String secureActionId;

    @JsonProperty("reminderCount")
    private Integer reminderCount;

    @JsonCreator
    public EmailVerificationTaskInstance(@JsonProperty(value = "id") String id,
                                         @JsonProperty(value = "name") String name,
                                         @JsonProperty(value = "jobId") String jobId,
                                         @JsonProperty(value = "startTime") Long startTime,
                                         @JsonProperty(value = "finishTime") Long finishTime,
                                         @JsonProperty(value = "status") TaskStatus status,
                                         @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                         @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                         @JsonProperty(value = "requesterEmail") Requester requester,
                                         @JsonProperty(value = "envId") String envId,
                                         @JsonProperty("secureActionId") String secureActionId,
                                         @JsonProperty(value = "reminderCount") Integer reminderCount) {
        super(id, name, jobId, startTime, finishTime, TaskType.EMAIL_VERIFICATION_TASK, status, lastUpdatedTime, taskFailureContext,
                null, null, envId);
        this.requester = requester;
        this.secureActionId = secureActionId;
        this.reminderCount = reminderCount;
    }

    public Requester getRequester() {
        return requester;
    }

    public void setRequester(Requester requester) {
        this.requester = requester;
    }

    public String getSecureActionId() {
        return secureActionId;
    }

    public void setSecureActionId(String secureActionId) {
        this.secureActionId = secureActionId;
    }

    public Integer getReminderCount() {
        return reminderCount;
    }

    public void setReminderCount(Integer reminderCount) {
        this.reminderCount = reminderCount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof EmailVerificationTaskInstance)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        EmailVerificationTaskInstance that = (EmailVerificationTaskInstance) o;
        return Objects.equals(getRequester(), that.getRequester());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getRequester());
    }
}
