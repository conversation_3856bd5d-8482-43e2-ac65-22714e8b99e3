package com.reltio.services.pms.common.model.jobs.tasks.notification;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.Requester;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

import java.util.List;

public class FreeTierEmailNotificationTaskInstance extends TaskInstance {

    private final Requester requester;
    private final String linkToFile;
    private final String tenantId;
    private final boolean requesterExistsOnAuth;

    private final List<String> cc;
    private final String overwriteToEmail;
    @JsonCreator
    public FreeTierEmailNotificationTaskInstance(@JsonProperty(value = "taskId") String taskId,
                                                 @JsonProperty(value = "name") String name,
                                                 @JsonProperty(value = "jobId") String jobId,
                                                 @JsonProperty(value = "startTime") Long startTime,
                                                 @JsonProperty(value = "finishTime") Long finishTime,
                                                 @JsonProperty(value = "status") TaskStatus status,
                                                 @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                                 @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                                 @JsonProperty(value = "executingNodeName") String executingNodeName,
                                                 @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                                 @JsonProperty(value = "envId") String envId,
                                                 @JsonProperty(value = "requester") Requester requester,
                                                 @JsonProperty(value = "linkToFile") String linkToFile,
                                                 @JsonProperty(value = "tenantId") String tenantId,
                                                 @JsonProperty(value = "requesterExistsOnAuth") boolean requesterExistsOnAuth,
                                                 @JsonProperty(value = "cc") List<String> cc,
                                                 @JsonProperty(value = "overwriteToEmail") String overwriteToEmail) {
        super(taskId, name, jobId, startTime, finishTime, TaskType.FREE_TIER_EMAIL_NOTIFICATION_TASK, status, lastUpdatedTime,
                taskFailureContext, executingNodeName, serviceNodeStatus, envId);
        this.requester = requester;
        this.linkToFile = linkToFile;
        this.tenantId = tenantId;
        this.requesterExistsOnAuth = requesterExistsOnAuth;
        this.cc = cc;
        this.overwriteToEmail = overwriteToEmail;
    }

    public Requester getRequester() {
        return requester;
    }

    public String getLinkToFile() {
        return linkToFile;
    }

    public String getTenantId() {
        return tenantId;
    }

    public boolean isRequesterExistsOnAuth() {
        return requesterExistsOnAuth;
    }

    public List<String> getCc() {
        return cc;
    }

    public String getOverwriteToEmail() {
        return overwriteToEmail;
    }
}
