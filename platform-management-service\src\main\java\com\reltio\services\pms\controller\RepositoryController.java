package com.reltio.services.pms.controller;

import com.reltio.services.pms.service.repo.RepositoryService;
import com.reltio.services.pms.service.repo.common.RepositoryServiceFactory;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * The type Repository controller.
 */
@RestController
@RequestMapping(value = "/api/v1/repository", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Repository")
@Validated
public class RepositoryController {

    private static final String regex = "^[a-zA-Z0-9-_]+$";


    private final RepositoryServiceFactory serviceFactory;

    /**
     * Instantiates a new Repository controller.
     *
     * @param serviceFactory the service factory
     */
    @Autowired
    public RepositoryController(RepositoryServiceFactory serviceFactory) {
        this.serviceFactory = serviceFactory;
    }

    /**
     * Clone repo.
     *
     * @param repoName   the repo name
     * @param branchName the branch name
     */
    @PostMapping("/{repoName}/branch/{branchName}")
    public void cloneRepo(@PathVariable String repoName, @PathVariable String branchName, @RequestParam(defaultValue = "false") boolean forceClone) {
        String repoPath = validateInput(repoName, branchName);
        RepositoryService repositoryService = serviceFactory.getService(repoName);
        repositoryService.cloneRepository(repoPath, branchName,forceClone);
    }

    /**
     * Delete repo.
     *
     * @param repoName   the repo name
     * @param branchName the branch name
     */
    @DeleteMapping("/{repoName}/branch/{branchName}")
    public void deleteRepo(@PathVariable String repoName, @PathVariable String branchName) {
        String repoPath = validateInput(repoName, branchName);
        RepositoryService repositoryService = serviceFactory.getService(repoName);
        repositoryService.deleteRepo(repoPath, branchName);
    }

    private String validateInput(String repoName, String branchName) {
        if (!repoName.matches(regex) || !branchName.matches(regex)) {
            throw new IllegalArgumentException("Invalid repository name or branch name");
        }
        return repoName;
    }
}
