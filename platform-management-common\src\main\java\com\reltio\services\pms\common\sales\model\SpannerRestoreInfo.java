package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class SpannerRestoreInfo extends BaseFirestoreEntity {

    @JsonProperty(value = "databaseId")
    private String databaseId;

    @JsonProperty(value = "backupId")
    private String backupId;

    @JsonProperty(value = "sourceTenantId")
    private String sourceTenantId;

    @JsonProperty(value = "sourceEnvId")
    private String sourceEnvId;

    @JsonProperty(value = "targetTenantId")
    private String targetTenantId;

    @JsonProperty(value = "targetEnvId")
    private String targetEnvId;

    @JsonProperty(value = "ticketId")
    private String ticketId;

    @JsonProperty(value = "creationTime")
    private Timestamp creationTime;

    @JsonProperty(value = "listOfErrors")
    private List<String> listOfErrors;

    @Override
    public String getID() {
        return databaseId;
    }

}