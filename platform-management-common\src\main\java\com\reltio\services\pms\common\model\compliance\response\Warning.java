package com.reltio.services.pms.common.model.compliance.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class Warning {

    @JsonProperty("missingDefaultUrls")
    private Set<String> missingDefaultUrls = new HashSet<>();
    @JsonProperty("storagePriorityList")
    private Set<String> storagePriorityList = new HashSet<>();
}
