package com.reltio.services.pms.common.model.compliance.request;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * The type Compliance report request.
 */
@NoArgsConstructor
@Getter
@Setter
public class CpUsageReportRequest extends ComplianceReportRequest {

    /**
     * The endpoint
     */
    String entityType;

    /**
     * The matchRuleEntities
     */
    String entityRule;

    public CpUsageReportRequest(String startDate, String endDate, Integer size, Integer offset, String sortOrder, String sortField, List<String> accountIds, String tenantId, String tenantPurpose, Boolean includeAllTenants, String entityType, String entityRule) {
        super(startDate, endDate, size, offset, sortOrder, sortField, accountIds, tenantId, tenantPurpose,includeAllTenants);
        this.entityType = entityType;
        this.entityRule = entityRule;
    }
}
