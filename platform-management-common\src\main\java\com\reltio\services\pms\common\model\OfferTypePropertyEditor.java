package com.reltio.services.pms.common.model;

import java.beans.PropertyEditorSupport;

public class OfferTypePropertyEditor extends PropertyEditorSupport {

    @Override
    public String getAsText() {
        OfferType offerType = (OfferType) getValue();
        return offerType == null ? null : offerType.getValue();
    }

    @Override
    public void setAsText(String text){
        setValue(OfferType.convertFromString(text));
    }

}
