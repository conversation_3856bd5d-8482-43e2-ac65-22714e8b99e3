package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import lombok.Getter;
import lombok.Setter;

import java.util.EnumSet;

@Getter
public class TenantCreationRequest extends CreateJobRequest {
    @Setter
    @JsonProperty("tenantSize")
    private TenantSize tenantSize;

    @JsonProperty("mdmTenantId")
    private final String mdmTenantId;

    @JsonProperty(value = "isQaAutomation")
    private final Boolean isQaAutomation;

    @JsonProperty(value = "newInstanceId")
    private String newInstanceId;

    @JsonProperty("useSpannerCloudFunction")
    private Boolean useSpannerCloudFunction;

    @JsonProperty(value = "reltioPackageType")
    private final ReltioPackageType reltioPackageType;

    @JsonCreator
    public TenantCreationRequest(@JsonProperty("pipelineId") String pipelineId,
                                 @JsonProperty("tenantSize") TenantSize tenantSize,
                                 @JsonProperty("mdmTenantId") String mdmTenantId,
                                 @JsonProperty("isQaAutomation") Boolean isQaAutomation,
                                 @JsonProperty("newInstanceId") String newInstanceId,
                                 @JsonProperty("useSpannerCloudFunction") Boolean useSpannerCloudFunction,
                                 @JsonProperty("reltioPackageType") ReltioPackageType reltioPackageType,
                                 @JsonProperty("skipTasks") EnumSet<PMSProductName> skipTasks) {
        super(pipelineId, skipTasks);
        this.tenantSize = tenantSize;
        this.mdmTenantId = mdmTenantId;
        this.isQaAutomation = isQaAutomation != null && isQaAutomation;
        this.useSpannerCloudFunction = useSpannerCloudFunction == null ? Boolean.TRUE : useSpannerCloudFunction;
        this.newInstanceId = newInstanceId;
        this.reltioPackageType = reltioPackageType == null ? ReltioPackageType.MDM : reltioPackageType;
    }
}
