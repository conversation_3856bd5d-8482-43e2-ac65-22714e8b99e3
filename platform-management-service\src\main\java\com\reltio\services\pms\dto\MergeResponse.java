package com.reltio.services.pms.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MergeResponse {
    private JsonNode mergedConfig;
    private JsonNode conflicts;
    public boolean hasConflicts() {
        return conflicts != null && !conflicts.isEmpty();
    }
}