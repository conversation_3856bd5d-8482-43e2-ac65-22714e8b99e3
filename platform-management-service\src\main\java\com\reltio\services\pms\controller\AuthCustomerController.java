package com.reltio.services.pms.controller;

import com.reltio.services.pms.common.model.auth.AuthCustomer;
import com.reltio.services.pms.service.AuthCustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;

/**
 * The type Auth customer controller.
 */
@RestController
@RequestMapping(value = "/api/v1/auth", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Auth Customer", description = "APIs related to Auth Customers")
public class AuthCustomerController {

    private final AuthCustomerService authCustomerService;

    @Autowired
    public AuthCustomerController(AuthCustomerService authCustomerService) {
        this.authCustomerService = authCustomerService;
    }


    /**
     * Gets auth customers details.
     *
     * @param size   the size
     * @param offset the offset
     * @return the api usage
     */
    @GetMapping("")
    @Operation(summary = "Get Auth Customers", description = "Retrieves a list of Auth Customers based on pagination parameters")
    public Collection<AuthCustomer> getAuthCustomers(@RequestParam Integer size, @RequestParam Integer offset) {
        return authCustomerService.getAuthCustomers(size, offset);
    }


}
