package com.reltio.services.pms.security;

import com.reltio.services.pms.common.model.Environment;
import org.springframework.lang.NonNull;

import java.util.List;

/**
 * PMSPermissionsManager
 * Created by a<PERSON><PERSON>ov
 */
public interface PMSPermissionsManager {

    boolean hasWriteAccessToEnvironment(@NonNull String env);
    boolean hasReadAccessToEnvironment(@NonNull String env);
    List<Environment> getListOfEnvironments(boolean hasWriteAccess);
    boolean hasAccessToEnvironment(@NonNull String env, boolean environmentAdmin);
}
