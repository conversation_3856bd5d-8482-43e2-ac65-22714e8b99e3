package com.reltio.services.pms.clients.reltio.dnb;

public class DNBConnectorClientException extends RuntimeException {

    public DNBConnectorClientException() {
    }

    public DNBConnectorClientException(String message) {
        super(message);
    }

    public DNBConnectorClientException(String message, Throwable cause) {
        super(message, cause);
    }

    public DNBConnectorClientException(Throwable cause) {
        super(cause);
    }

    public DNBConnectorClientException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
