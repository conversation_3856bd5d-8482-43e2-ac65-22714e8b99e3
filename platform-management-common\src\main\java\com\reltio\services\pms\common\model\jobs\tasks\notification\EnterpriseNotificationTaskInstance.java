package com.reltio.services.pms.common.model.jobs.tasks.notification;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

import java.util.List;

public class EnterpriseNotificationTaskInstance extends TaskInstance {

    private List<String> owners;

    @JsonCreator
    public EnterpriseNotificationTaskInstance(
            @JsonProperty(value = "taskId") String taskId,
            @JsonProperty(value = "name") String name,
            @JsonProperty(value = "jobId") String jobId,
            @JsonProperty(value = "startTime") Long startTime,
            @JsonProperty(value = "finishTime") Long finishTime,
            @JsonProperty(value = "status") TaskStatus status,
            @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
            @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
            @JsonProperty(value = "executingNodeName") String executingNodeName,
            @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
            @JsonProperty(value = "owners") List<String> owners,
            @JsonProperty(value = "envId") String envId
    ) {
        super(taskId, name, jobId, startTime, finishTime, TaskType.ENTERPRISE_NOTIFICATION_TASK, status, lastUpdatedTime,
                taskFailureContext, executingNodeName, serviceNodeStatus, envId);
        this.owners = owners;
    }

    public List<String> getOwners() {
        return owners;
    }

}
