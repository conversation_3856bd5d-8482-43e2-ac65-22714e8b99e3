package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.Query;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.Requester;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.util.Collection;
import java.util.Collections;
import java.util.Set;

@Service
public class RequesterDao extends AbstractRootCollectionDao<Requester> {

    private static final String REQUESTER_COLLECTION_NAME = "PMS_REQUESTERS";
    private static final Logger LOGGER = Logger.getLogger(RequesterDao.class);

    @Autowired
    public RequesterDao(CredentialsProvider provider,
                        @Value("${firestore.env.name}") String deployedEnv, ReltioUserHolder reltioUserHolder) {
        super(provider, REQUESTER_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    protected TypeReference<Requester> getTypeReference() {
        return new TypeReference<Requester>() {
        };
    }

    public Collection<Requester> getRequesters(String property, String value, Integer size, Integer offset) {
        Query query = getBaseCollection().whereEqualTo(property, value).limit(size).offset(offset);

        return getResultFromQuery(query);
    }
}
