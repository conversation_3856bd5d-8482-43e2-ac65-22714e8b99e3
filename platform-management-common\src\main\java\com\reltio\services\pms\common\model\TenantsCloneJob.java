package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.jobs.Job;
import com.reltio.services.pms.common.model.jobs.JobStatus;
import com.reltio.services.pms.common.model.pipeline.SkipReason;
import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.Getter;

import java.util.EnumMap;
import java.util.EnumSet;
import java.util.List;


@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
public class TenantsCloneJob extends Job {

    @JsonProperty(value = "sourceTenantId")
    private final String sourceTenantId;

    @JsonProperty(value = "targetTenantId")
    private final String targetTenantId;

    @JsonProperty(value = "sourceEnvId")
    private final String sourceEnvId;

    @JsonProperty(value = "targetEnvId")
    private final String targetEnvId;

    @JsonProperty(value = "isCloneJob")
    private final boolean isCloneJob;

    public TenantsCloneJob(String parentJobId ,
                           String jobId,
                           String pipelineId,
                           Long startTime,
                           Long finishTime,
                           JobStatus status,
                           List<String> tasks,
                           String envId,
                           EnumMap<SkipReason, EnumSet<PMSProductName>> skippedTasks,
                           List<String> owners,
                           String sourceTenantId,
                           String targetTenantId,
                           String sourceEnvId,
                           String targetEnvId,
                           boolean isCloneJob) {
        super(parentJobId,jobId, pipelineId, startTime, finishTime, status, tasks, envId, skippedTasks,owners,Boolean.FALSE);
        this.sourceTenantId = sourceTenantId;
        this.targetTenantId = targetTenantId;
        this.sourceEnvId = sourceEnvId;
        this.targetEnvId = targetEnvId;
        this.isCloneJob = isCloneJob;

    }
}
