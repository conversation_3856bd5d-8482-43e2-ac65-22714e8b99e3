package com.reltio.services.pms.convertors.sales;

import com.reltio.collection.CollectionUtils;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.service.SalesPackageService;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * The type Abstract tenant add on product provider.
 *
 * @param <Q> the type parameter
 */
public abstract class AbstractTenantAddOnProductProvider<Q extends BaseProductConfig> implements TenantAddOnProductProvider<BaseProductConfig> {

    /**
     * The Sales package service.
     */
    protected final SalesPackageService salesPackageService;

    /**
     * Instantiates a new Abstract tenant add on product provider.
     *
     * @param salesPackageService the sales package service
     */
    protected AbstractTenantAddOnProductProvider(SalesPackageService salesPackageService) {
        this.salesPackageService = salesPackageService;
    }

    /**
     * Gets product config by tenant code.
     *
     * @param packageSaleConfigs the package sale configs
     * @return the product config by tenant code
     */
    @Override
    public Map<String, BaseProductConfig> getProductConfigByTenantCode(Map<String, Set<SalesConfig>> packageSaleConfigs) {
        Map<String, BaseProductConfig> tenantProductConfig = new HashMap<>();
        if (CollectionUtils.isEmpty(packageSaleConfigs)) {
            return Collections.emptyMap();
        }

        Set<String> tenantCodes = getTenantProductCodes(packageSaleConfigs);
        Map<String, Set<String>> productCodesByTenant = getProductCodesByTenant();
        Set<SalesConfig> allSalesConfigs;
        for (String tenantProductCode : tenantCodes) {
            Set<String> productCodes = productCodesByTenant.get(tenantProductCode);
            allSalesConfigs = new HashSet<>();
            if (productCodes != null) {
                for (String productCode : productCodes) {
                    Set<SalesConfig> salesConfigs = packageSaleConfigs.get(productCode);
                    if (CollectionUtils.isNotEmpty(salesConfigs)) {
                        allSalesConfigs.addAll(salesConfigs);
                    }
                }

                BaseProductConfig productConfig = getProductConfig(allSalesConfigs, tenantCodes, tenantProductCode);
                if (productConfig != null) {
                    tenantProductConfig.put(tenantProductCode, productConfig);
                }
            }
        }

        return tenantProductConfig;
    }

    /**
     * Gets tenant product codes.
     *
     * @param salesConfigs the sales configs
     * @return the tenant product codes
     */
    @Override
    public Set<String> getTenantProductCodes(Map<String, Set<SalesConfig>> salesConfigs) {
        Set<String> tenantProductCodes = new HashSet<>();
        Set<String> tenantPurposeProductCodes = salesPackageService.getSalesForcePurpose();
        for (String productCode : tenantPurposeProductCodes) {
            Set<SalesConfig> salesConfigSet = salesConfigs.get(productCode);
            if (CollectionUtils.isNotEmpty(salesConfigSet)) {
                tenantProductCodes.add(productCode);
            }
        }
        return tenantProductCodes;
    }

    /**
     * Gets quantity.
     *
     * @param productConfig the product config
     * @return the quantity
     */
    protected Integer getQuantity(BaseProductConfig productConfig) {
        Long quantity = 0l;
        if (productConfig.getSalesConfigs() != null) {
            for (SalesConfig salesConfig : productConfig.getSalesConfigs()) {
                quantity += salesConfig.getQuantity();
            }
        }
        return quantity.intValue();
    }

}
