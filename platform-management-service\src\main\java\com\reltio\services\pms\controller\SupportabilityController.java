package com.reltio.services.pms.controller;

import com.reltio.services.pms.common.model.supportability.Facet;
import com.reltio.services.pms.common.model.supportability.StatsDto;
import com.reltio.services.pms.common.model.supportability.request.ClusterParams;
import com.reltio.services.pms.common.model.supportability.request.EventParams;
import com.reltio.services.pms.common.model.supportability.request.ProcessorTypeGroupEventParams;
import com.reltio.services.pms.common.model.supportability.request.RestParams;
import com.reltio.services.pms.common.model.supportability.request.TableParams;
import com.reltio.services.pms.common.model.supportability.request.TasksParams;
import com.reltio.services.pms.common.model.supportability.request.TenantHealthParams;
import com.reltio.services.pms.controller.api.SupportabilityResources;
import com.reltio.services.pms.service.supportability.MetricsService;
import com.reltio.services.pms.validator.StatsParamsValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * SupportabilityController
 * Created by apylkov
 */
@RestController
public class SupportabilityController implements SupportabilityResources {
    private final MetricsService metricsService;

    @Autowired
    public SupportabilityController(MetricsService metricsService) {
        this.metricsService = metricsService;
    }

    @Override
    public ResponseEntity<Map<String, List<StatsDto>>> getRestMetrics(Long startTime, Long endTime, String tenantId,
                                                                      String operationType, String clusterType, Facet facet) {
        RestParams restParams = new RestParams(startTime, endTime, tenantId, operationType, clusterType, facet);
        StatsParamsValidator.validate(restParams);
        return ResponseEntity.ok().body(metricsService.getRestApiCalls(restParams));
    }

    @Override
    public ResponseEntity<List<StatsDto>> getTasksMetrics(Long startTime, Long endTime, String tenantId, Facet facet) {
        TasksParams params = new TasksParams(startTime, endTime, tenantId, facet);
        StatsParamsValidator.validate(params);
        return ResponseEntity.ok().body(metricsService.getTasksStats(params).getOrDefault("", Collections.emptyList()));
    }

    @Override
    public ResponseEntity<Map<String, List<StatsDto>>> getEventsMetrics(Long startTime, Long endTime, String tenantId,
                                                                      String queueType, String consumerType, Facet facet) {
        EventParams eventParams = new EventParams(startTime, endTime, tenantId, queueType, consumerType, facet);
        StatsParamsValidator.validate(eventParams);
        return ResponseEntity.ok().body(metricsService.getEventStats(eventParams));
    }

    @Override
    public ResponseEntity<Map<String, List<StatsDto>>> getProcessorGroupEventsMetrics(Long startTime, Long endTime, String tenantId,
                                                                        String queueType, String consumerType, String processorTypeGroup, Facet facet) {
        ProcessorTypeGroupEventParams eventParams = new ProcessorTypeGroupEventParams(startTime, endTime, tenantId, queueType, consumerType, processorTypeGroup, facet);
        StatsParamsValidator.validate(eventParams);
        return ResponseEntity.ok().body(metricsService.getProcessorTypeGroupEventStats(eventParams));
    }

    @Override
    public ResponseEntity<Map<String, List<StatsDto>>> getClusterMetrics(@RequestParam Long startTime,
                                                                  @RequestParam Long endTime,
                                                                  @RequestParam String tenantId,
                                                                  @RequestParam String clusterType,
                                                                  @RequestParam(required = false) Facet facet) {
        ClusterParams clusterParams = new ClusterParams(startTime, endTime, tenantId, clusterType, facet);
        StatsParamsValidator.validate(clusterParams);
        return ResponseEntity.ok().body(metricsService.getClusterStats(clusterParams));
    }

    @Override
    public ResponseEntity<Map<String, List<StatsDto>>> getTenantHealthMetrics(@RequestParam Long startTime,
                                                                         @RequestParam Long endTime,
                                                                         @RequestParam String tenantId,
                                                                         @RequestParam(required = false) String healthCheckId,
                                                                         @RequestParam(required = false) Facet facet) {
        // Default grouping by hour instead of min
        facet = (facet == null) ? Facet.hour: facet;
        TenantHealthParams tenantHealthParams = new TenantHealthParams(startTime, endTime, tenantId, healthCheckId, facet);
        StatsParamsValidator.validate(tenantHealthParams);
        return ResponseEntity.ok().body(metricsService.getTenantHealthStats(tenantHealthParams));
    }

    @Override
    public ResponseEntity<Map<String, List<StatsDto>>> getTablesMetrics(@RequestParam Long startTime,
                                                                        @RequestParam Long endTime,
                                                                        @RequestParam String tenantId,
                                                                        @RequestParam(required = false) String tableName,
                                                                        @RequestParam(required = false) Facet facet) {
        TableParams tableParams = new TableParams(startTime, endTime, tenantId, tableName, facet);
        StatsParamsValidator.validate(tableParams);
        return ResponseEntity.ok().body(metricsService.getDynamoDBStats(tableParams));
    }
}
