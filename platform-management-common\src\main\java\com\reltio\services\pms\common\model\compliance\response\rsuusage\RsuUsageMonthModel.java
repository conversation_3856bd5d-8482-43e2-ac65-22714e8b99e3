package com.reltio.services.pms.common.model.compliance.response.rsuusage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * The type Rsu usage by Month model.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class RsuUsageMonthModel {

    /**
     * The Year.
     */
    @JsonProperty("year")
    String year;
    /**
     * The Month.
     */
    @JsonProperty("month")
    String month;
    /**
     * The RSU usage.
     */
    @JsonProperty("maxRsuUsage")
    String maxRsuUsage;
    /**
     * The Average RSU usage.
     */
    @JsonProperty("averageRsuUsage")
    String averageRsuUsage;
}
