package com.reltio.services.pms.dao;

import com.google.api.core.ApiFuture;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.CollectionReference;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.FirestoreOptions;
import com.google.cloud.firestore.WriteBatch;
import com.google.cloud.firestore.CollectionGroup;
import com.google.common.annotations.VisibleForTesting;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import com.reltio.services.pms.common.sales.model.PmsLock;
import org.springframework.http.HttpStatus;

import javax.annotation.PreDestroy;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static com.reltio.services.pms.common.exception.PlatformManagementErrorCode.FAILED_TO_ACQUIRE_LOCK;

/**
 * Provides the generic functions to support the root level collections and corresponding document management.
 *
 * @param <T>
 */
public abstract class AbstractRootCollectionDao<T extends BaseFirestoreEntity> extends AbstractDao<T> {

    private static final String FORMAT_SPECIFIER = "%s_%s";

    private static final String LOCK_ID = "lockId";

    private static final String IS_LOCKED = "isLocked";

    private static final String LOCKED_BY = "lockedBy";

    private final Firestore db;

    private final String pmsCollectionName;

    protected AbstractRootCollectionDao(CredentialsProvider provider, String collectionPrefix, String deployedEnv, ReltioUserHolder reltioUserHolder) {
        super(reltioUserHolder);
        this.db = FirestoreOptions.newBuilder().setCredentialsProvider(provider).build().getService();
        this.pmsCollectionName = String.format(FORMAT_SPECIFIER, collectionPrefix, deployedEnv);
    }

    @PreDestroy
    public void destroy() throws Exception {
        db.close();
    }

    public T createOrUpdate(T entity) {
        if (isExists(entity.getID())) {
            return update(entity);
        }
        return create(entity);
    }

    public T create(T entity) {
        return create(getBaseCollection(), entity);
    }

    public T override(T entity) {
        return override(getBaseCollection(), entity);
    }

    public T update(T entity) {
        return update(getBaseCollection(), entity);
    }


    public T get(String docId) throws InvalidDocumentIdException {
        return get(getBaseCollection(), docId);
    }

    public boolean isExists(String docId) {
        return isExists(getBaseCollection(), docId);
    }

    public Collection<T> getAll() {
        return getAll(getBaseCollection());
    }

    public Iterator<T> getIterator() {
        return getIterator(getBaseCollection());
    }

    public void delete(String docId) {
        delete(getBaseCollection(), docId);
    }

    public final DocumentReference getDocumentReference(String docId) {
        return getBaseCollection().document(docId);
    }

    /**
     * This method used to get the base collection and use it to  create document which not fall under the standard environment category.
     *
     * @return FireStore Collection Reference
     */
    protected final CollectionReference getBaseCollection() {
        return db.collection(pmsCollectionName);
    }

    @VisibleForTesting
    public final void deleteCollection() {
        CollectionReference c = getBaseCollection();
        c.getFirestore().recursiveDelete(c);
    }

    public Collection<T> getAllWithSizeAndOffset(Integer size, Integer offset) {
        return getAllWithSizeAndOffset(getBaseCollection(), size, offset);
    }

    public Long getAllCount() {
        try {
            return getBaseCollection().count().get().get().getCount();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    public Collection<T> getAllFilteredByFeaturesWithSizeAndOffset(Map<String, String> features, Integer size, Integer offset) {
        return getAllFilteredByFeaturesWithSizeAndOffset(getBaseCollection(), features, size, offset);
    }

    public List<T> createOrUpdateBatch(List<T> entity) {
        return batch(getBaseCollection(), getBatch(), entity);
    }

    protected final WriteBatch getBatch() {
        return db.batch();
    }

    public CollectionGroup getCurrentCollectionGroup() {
        return db.collectionGroup(pmsCollectionName);
    }

    public boolean acquireLock(PmsLock pmsLock) {
        DocumentReference lockRef = db.collection(pmsCollectionName).document(pmsLock.getLockId());
        final int maxRetires = 3;
        int retryCount = 0;
        while (retryCount <= maxRetires) {
            //Try to acquire the lock after every 10 seconds for 30 times
            for (int retry = 0; retry < 30; retry++) {
                try {
                    ApiFuture<Boolean> transaction = db.runTransaction(t -> {
                        DocumentSnapshot lockSnapshot = t.get(lockRef).get();
                        Boolean lockSnapShotExists = lockSnapshot.exists();
                        Boolean isLocked = (Boolean) lockSnapshot.get(IS_LOCKED);
                        // Check if the lock is available
                        if (Boolean.FALSE.equals(lockSnapShotExists) || Boolean.FALSE.equals(isLocked)) {
                            // Acquire the lock
                            Map<String, Object> lockData = createLockObject(pmsLock);
                            t.set(lockRef, lockData);
                            return true;
                        } else {
                            return false;
                        }
                    });

                    if (Boolean.TRUE.equals(transaction.get(20, TimeUnit.SECONDS))) {
                        return true;
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } catch(ExecutionException | TimeoutException e) {
                    throw new PlatformManagementException(PlatformManagementErrorCode.ERROR_WHILE_LOCKING, HttpStatus.INTERNAL_SERVER_ERROR.value(),e);
                }
                // Wait for 10 seconds before retrying
                try {
                    Thread.sleep(10000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            retryCount++;
        }
        throw new PlatformManagementException(FAILED_TO_ACQUIRE_LOCK, HttpStatus.BAD_REQUEST.value());
    }

    public void releaseLock(String lockDocId) {
        DocumentReference lockRef = db.collection(pmsCollectionName).document(lockDocId);

        db.runTransaction(t -> {
            DocumentSnapshot lockSnapshot = t.get(lockRef).get();
            if (Boolean.TRUE.equals(lockSnapshot.get(IS_LOCKED))) {
                Map<String, Object> lockData = new HashMap<>();
                lockData.put(LOCK_ID,lockDocId);
                lockData.put(IS_LOCKED, false);
                lockData.put(LOCKED_BY, null);
                t.set(lockRef, lockData);
            }
            return true;
        });
    }

    private Map<String,Object> createLockObject(PmsLock pmsLock){
        Map<String,Object> lockData = new HashMap<>();
        lockData.put(LOCK_ID,pmsLock.getLockId());
        lockData.put(IS_LOCKED, pmsLock.getIsLocked());
        lockData.put(LOCKED_BY, pmsLock.getLockedBy());
        return lockData;

    }

}
