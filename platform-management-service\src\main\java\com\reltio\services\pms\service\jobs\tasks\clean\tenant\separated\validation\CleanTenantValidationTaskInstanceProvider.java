package com.reltio.services.pms.service.jobs.tasks.clean.tenant.separated.validation;

import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.combined.CleanTenantTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.separated.validation.CleanTenantValidationTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.CleanTenantValidationTaskConfig;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.jobs.tasks.TaskInstanceProvider;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.UUID;

@Service
public class CleanTenantValidationTaskInstanceProvider implements TaskInstanceProvider<CleanTenantValidationTaskInstance, CleanTenantValidationTaskConfig, CleanTenantTaskInstanceParams> {
    @Override
    public TaskType getTaskType() {
        return TaskType.DELETE_TENANT_DATA_VALIDATION_TASK;
    }

    @Override
    public CleanTenantValidationTaskInstance getTaskDetail(String envId, String jobId, CleanTenantValidationTaskConfig pipelineTaskConfig, CleanTenantTaskInstanceParams taskInstanceParams) {
        return new CleanTenantValidationTaskInstance(
                UUID.randomUUID().toString(),
                TaskType.CLEAN_TENANT_VALIDATION_TASK_NAME, jobId,
                0L,
                null,
                null,
                null,
                null,
                null,
                null,
                envId,
                taskInstanceParams.getTenantName(),
                Collections.emptyList(),
                Collections.emptyList(),
                taskInstanceParams.getParameters());
    }
}
