package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.Requester;
import com.reltio.services.pms.common.model.RequesterWithOffers;
import com.reltio.services.pms.common.model.TenantRequest;
import com.reltio.services.pms.common.model.tenant.Subscription;
import com.reltio.services.pms.service.RequesterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = {"/api/v1/pms/requesters", "/api/v1/requesters"}, produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Requesters")
public class RequesterController {

    private static final Logger LOG = Logger.getLogger(RequesterController.class);

    private final RequesterService requesterService;

    @Autowired
    public RequesterController(RequesterService requesterService) {
        this.requesterService = requesterService;
    }

    @GetMapping("/{requesterEmail}")
    @ReltioSecured(resourceClass = Pms.Config.Freetier.class)
    public Requester getRequester(@PathVariable String requesterEmail,
                                  @RequestParam(required = false, defaultValue = "false") boolean withOffers) {
        Requester requester = requesterService.getRequester(requesterEmail);
        if (withOffers) {
            return getRequesterWithOffers(requester);
        }
        return requester;
    }

    @GetMapping("")
    @ReltioSecured(resourceClass = Pms.Config.Freetier.class)
    public Collection<Requester> getRequester(@RequestParam(required = false) String property,
                                              @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                              @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
                                              @RequestParam(required = false) String value,
                                              @RequestParam(required = false, defaultValue = "false") boolean withOffers) {
        Collection<Requester> requesters;
        if (property == null && value == null) {
            requesters = requesterService.getAllRequesters(size, offset);
        } else if (property != null && value != null) {
            requesters = requesterService.getRequesters(property, value, size, offset);
        } else {
            throw new PlatformManagementException(PlatformManagementErrorCode.NONE_OR_BOTH_PARAMETERS_MUST_BE_SPECIFIED, HttpStatus.BAD_REQUEST.value());
        }
        if (withOffers) {
            return requesters.stream().map(this::getRequesterWithOffers).collect(Collectors.toList());
        }
        return requesters;
    }

    @Operation(requestBody = @RequestBody(content = @Content(mediaType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)))
    @PostMapping(value = "", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Requester createOrUpdateRequester(TenantRequest tenantRequest) {
        if (tenantRequest.getOfferType() == null) {
            throw new PlatformManagementException(PlatformManagementErrorCode.OFFERTYPE_IS_REQUIRED, HttpStatus.BAD_REQUEST.value());
        }
        Requester requesterWithOfferTypes = new Requester(tenantRequest);
        Subscription offer = new Subscription(tenantRequest);
        return requesterService.handleNewTenantRequest(requesterWithOfferTypes, offer);
    }

    @PutMapping(value = "/{requesterEmail}", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Config.Freetier.class)
    public Requester updateRequester(@PathVariable String requesterEmail, @org.springframework.web.bind.annotation.RequestBody Requester requester) {
        if (!requester.getEmail().equals(requesterEmail)) {
            throw new PlatformManagementException(PlatformManagementErrorCode.EMAIL_IN_PATH_AND_BODY_MUST_BE_EQUAL, HttpStatus.BAD_REQUEST.value());
        }
        return requesterService.updateRequester(requester);
    }

    private RequesterWithOffers getRequesterWithOffers(Requester requester) {
        Collection<Subscription> subscriptions = requesterService.getRequesterOfferTypes(requester.getEmail());
        return new RequesterWithOffers(requester, subscriptions);
    }
}
