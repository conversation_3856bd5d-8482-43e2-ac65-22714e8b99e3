package com.reltio.services.pms.service.jobs.tasks.contracts.sync;

import com.reltio.services.pms.common.model.jobs.tasks.contracts.sync.ContractsSyncTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.TaskFactory;
import com.reltio.services.pms.service.sales.ContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ContractsSyncTaskFactory implements TaskFactory<ContractsSyncTaskInstance, ContractsSyncTaskExecutionService> {

    private final ContractService contractService;

    @Autowired
    public ContractsSyncTaskFactory(ContractService contractService) {
        this.contractService = contractService;
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.CONTRACTS_SYNC_TASK;
    }

    @Override
    public ContractsSyncTaskExecutionService createTask(String jobId, ContractsSyncTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService) {
        return new ContractsSyncTaskExecutionService(jobId, taskDetail, grafanaDashboardGBQService, contractService);
    }
}
