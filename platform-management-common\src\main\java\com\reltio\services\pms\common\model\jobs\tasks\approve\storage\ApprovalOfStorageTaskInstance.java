package com.reltio.services.pms.common.model.jobs.tasks.approve.storage;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.common.config.LcaConfig;
import com.reltio.devops.common.environment.ServicePurpose;
import com.reltio.devops.common.environment.ServiceType;

import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.approve.ApprovalStatus;
import com.reltio.services.pms.common.model.jobs.tasks.approve.ApprovalTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.TenantPurpose;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

public class ApprovalOfStorageTaskInstance extends ApprovalTaskInstance {

    public static final int LCA_NATIVE_TIME_OUT = 100;
    public static final int LCA_LAMBDA_TIME_OUT = 100;
    public static final int LCA_DVF_TIME_OUT = 1000;

    private String defaultStorageTemplate;
    private String customerName;
    private String tenantId;
    private TenantSize tenantSize;
    private String secureActionId;

    @JsonProperty(value = "dataStorageArn")
    private final String dataStorageArn;

    @JsonProperty(value = "matchStorageArn")
    private final String matchStorageArn;

    @JsonProperty(value = "tenantPurpose")
    private final TenantPurpose tenantPurpose;

    @JsonProperty("storagePriorityList")
    private final Map<ServicePurpose, List<ServiceType>> storagePriorityList ;

    @JsonProperty("lcaConfig")
    private final LcaConfig lcaConfig;

    @JsonCreator
    public ApprovalOfStorageTaskInstance(@JsonProperty(value = "id") String id,
                                         @JsonProperty(value = "name") String name,
                                         @JsonProperty(value = "jobId") String jobId,
                                         @JsonProperty(value = "startTime") Long startTime,
                                         @JsonProperty(value = "finishTime") Long finishTime,
                                         @JsonProperty(value = "status") TaskStatus status,
                                         @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                         @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                         @JsonProperty(value = "appoverEmails") Set<String> approverEmails,
                                         @JsonProperty(value = "resolution") ApprovalStatus approvalStatus,
                                         @JsonProperty(value = "envId") String envId,
                                         @JsonProperty(value = "defaultStorageTemplate") String defaultStorageTemplate,
                                         @JsonProperty(value = "customerName") String customerName,
                                         @JsonProperty(value = "tenantId") String tenantId,
                                         @JsonProperty(value = "tenantSize") TenantSize tenantSize,
                                         @JsonProperty("secureActionId") String secureActionId,
                                         @JsonProperty(value = "dataStorageArn") String dataStorageArn,
                                         @JsonProperty(value = "matchStorageArn") String matchStorageArn,
                                         @JsonProperty(value = "tenantPurpose") TenantPurpose tenantPurpose,
                                         @JsonProperty(value = "storagePriorityList" ) Map<ServicePurpose, List<ServiceType>> storagePriorityList,
                                         @JsonProperty(value = "lcaConfig" ) LcaConfig lcaConfig) {
        super(id, name, jobId, startTime, finishTime, status, lastUpdatedTime, taskFailureContext, approverEmails,
                approvalStatus, null, envId);
        this.dataStorageArn = dataStorageArn;
        this.matchStorageArn = matchStorageArn;
        this.tenantPurpose = tenantPurpose;
        this.storagePriorityList = storagePriorityList;
        super.setType(TaskType.APPROVAL_OF_STORAGE);
        this.defaultStorageTemplate = defaultStorageTemplate;
        this.customerName = customerName;
        this.tenantId = tenantId;
        this.tenantSize = tenantSize;
        this.secureActionId = secureActionId;
        this.lcaConfig = Objects.isNull(lcaConfig) ? lcaConfig : getLcaConfigCopy(lcaConfig);
    }

    private LcaConfig getLcaConfigCopy(LcaConfig lcaConfig){
        LcaConfig config = new LcaConfig();
        if(Objects.isNull(lcaConfig)) {
            config.setDvfTimeOut(LCA_DVF_TIME_OUT);
            config.setLambdaTimeOut(LCA_LAMBDA_TIME_OUT);
            config.setNativeTimeOut(LCA_NATIVE_TIME_OUT);
            return config;
        }
        config.setLambdaTimeOut(lcaConfig.getLambdaTimeOut());
        config.setNativeTimeOut(lcaConfig.getNativeTimeOut());
        config.setDvfTimeOut(lcaConfig.getDvfTimeOut());
        return config;
    }

    public String getDefaultStorageTemplate() {
        return defaultStorageTemplate;
    }

    public void setDefaultStorageTemplate(String defaultStorageTemplate) {
        this.defaultStorageTemplate = defaultStorageTemplate;
    }

    public String getCustomerName() {
        return customerName;
    }

    public String getTenantId() {
        return tenantId;
    }

    public TenantSize getTenantSize() {
        return tenantSize;
    }

    public String getSecureActionId() {
        return secureActionId;
    }

    public void setSecureActionId(String secureActionId) {
        this.secureActionId = secureActionId;
    }

    public String getDataStorageArn() {
        return dataStorageArn;
    }

    public String getMatchStorageArn() {
        return matchStorageArn;
    }

    public TenantPurpose getTenantPurpose() {
        return tenantPurpose;
    }

    public Map<ServicePurpose, List<ServiceType>> getStoragePriorityList() {
        return Collections.unmodifiableMap(storagePriorityList);
    }

    public LcaConfig getLcaConfig() {
        return getLcaConfigCopy(lcaConfig);
    }
}
