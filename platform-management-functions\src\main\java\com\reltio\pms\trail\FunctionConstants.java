package com.reltio.pms.trail;

public class FunctionConstants {

    public static final String PROJECT_ID = "RELTIO_GCP_PROJECT_ID";
    public static final String SECRET_NAME = "RELTIO_SECRET_NAME";
    public static final String SECRET_VERSION = "RELTIO_SECRET_VERSION";
    public static final String AUTH_URL = "RELTIO_AUTH_URL";
    public static final String PMS_JOBS_URL = "PMS_JOBS_URL";
    public static final String PIPELINE_ID = "TRIAL_PIPELINE_ID";
    public static final String TESTDRIVE_PIPELINE_ID = "TESTDRIVE_PIPELINE_ID";
    public static final String TERMS_FILE_PATH = "GCS_TERMS_PATH"; //gs://<>

    //Requesters Fields
    public static final String REQ_JOB_ID = "trialJobId";
    public static final String REQ_EMAIL_VERIFIED = "emailVerified";
    public static final String REQ_TERMS_ACCEPTED = "termsAccepted";
    public static final String REQ_EMAIL = "email";
    public static final String REQ_TERMS_FILE = "termsFilePath";

    //Firestore Values
    public static final String FS_STRING_VALUE = "stringValue";
    public static final String FS_BOOLEAN_VALUE = "booleanValue";
    public static final String ENVIRONMENT = "envId";
    public static final String DEFAULT_ENVIRONMENT = "DEFAULT_ENVIRONMENT";
}
