package com.reltio.services.pms.common.model.jobs.tasks.imagehosting;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.node.JsonNodeType;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServiceEnablementBaseTaskInstance;
import com.reltio.services.pms.common.sales.TenantPurpose;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class ImageHostingTaskInstanceDeserializer extends StdDeserializer<ImageHostingTaskInstance> {
    private final ObjectMapper mapper;

    public static final String EVENTS = "events";

    public ImageHostingTaskInstanceDeserializer() {
        this(null);
    }

    public ImageHostingTaskInstanceDeserializer(Class<?> vc) {
        super(vc);
        mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Override
    public ImageHostingTaskInstance deserialize(JsonParser jsonParser, DeserializationContext ctxt) throws IOException {
        JsonNode node = jsonParser.getCodec().readTree(jsonParser);
        TenantPurpose tenantPurpose = null;
        List<Boolean> createFoldersIfFailedWhileBucketCreated = new ArrayList<>();
        String tenantId = null;
        String customerId = null;
        if (node.hasNonNull("tenantId")) {
            tenantId = node.get("tenantId").textValue();
        }
        if (node.hasNonNull("customerId")) {
            customerId = node.get("customerId").textValue();
        }
        if (node.hasNonNull("tenantPurpose")) {
            tenantPurpose = TenantPurpose.valueOf(node.get("tenantPurpose").textValue());
        }
        if (node.hasNonNull("createFoldersIfFailedWhileBucketCreated")) {
            ObjectReader reader = mapper.readerFor(new TypeReference<List<String>>() {
            });
            createFoldersIfFailedWhileBucketCreated = reader.readValue(node.get("createFoldersIfFailedWhileBucketCreated"));
        }
        Map<String, Set<String>> events = getEvents(node, tenantId);
        if (node.hasNonNull("type")) {
            ((ObjectNode) node).remove("type");
        }
        ServiceEnablementBaseTaskInstance taskInstance = mapper.convertValue(node, ServiceEnablementBaseTaskInstance.class);

        return new ImageHostingTaskInstance(taskInstance.getTaskId(), taskInstance.getName(), taskInstance.getJobId(),
                taskInstance.getStartTime(), taskInstance.getFinishTime(), taskInstance.getStatus(),
                taskInstance.getLastUpdatedTime(), taskInstance.getTaskFailureContext(), taskInstance.getExecutingNodeName(),
                taskInstance.getServiceNodeStatus(), taskInstance.getEnvId(), tenantId, customerId, tenantPurpose,
                taskInstance.getFailedTenants(), taskInstance.getTotalProcessedTenants(),
                taskInstance.getTenantsForServiceEnablement(), events, createFoldersIfFailedWhileBucketCreated);
    }

    private Map<String, Set<String>> getEvents(JsonNode node, String tenantId) throws IOException {
        Map<String, Set<String>> events = new HashMap<>();
        if (node.hasNonNull(EVENTS)) {
            if (node.get(EVENTS).getNodeType() == JsonNodeType.ARRAY) {
                ObjectReader reader = mapper.readerFor(new TypeReference<Set<String>>() {
                });
                events.put(tenantId, reader.readValue(node.get(EVENTS)));
            } else if (node.get(EVENTS).getNodeType() == JsonNodeType.OBJECT) {
                ObjectReader reader = mapper.readerFor(new TypeReference<Map<String, Set<String>>>() {
                });
                events = reader.readValue(node.get(EVENTS));
            }
            ((ObjectNode) node).remove(EVENTS);
        }
        return events;
    }


}