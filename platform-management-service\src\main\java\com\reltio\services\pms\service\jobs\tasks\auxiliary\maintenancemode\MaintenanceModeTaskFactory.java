package com.reltio.services.pms.service.jobs.tasks.auxiliary.maintenancemode;

import com.reltio.services.pms.clients.reltio.mdm.MDMCleanTenantClient;
import com.reltio.services.pms.clients.reltio.mdm.MDMClient;
import com.reltio.services.pms.common.model.jobs.tasks.maintenance.MaintenanceModeTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.TaskFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MaintenanceModeTaskFactory implements TaskFactory<MaintenanceModeTaskInstance, MaintenanceModeTaskExecutionService> {

    private final MDMCleanTenantClient mdmCleanTenantClient;
    private final MDMClient mdmClient;


    @Autowired
    public MaintenanceModeTaskFactory(MDMCleanTenantClient mdmCleanTenantClient,
                                      MDMClient mdmClient) {
        this.mdmCleanTenantClient = mdmCleanTenantClient;
        this.mdmClient = mdmClient;
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.MAINTENANCE_MODE_TASK;
    }

    @Override
    public MaintenanceModeTaskExecutionService createTask(String jobId, MaintenanceModeTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService) {
        return new MaintenanceModeTaskExecutionService(jobId, taskDetail, grafanaDashboardGBQService, mdmCleanTenantClient, mdmClient);
    }
}
