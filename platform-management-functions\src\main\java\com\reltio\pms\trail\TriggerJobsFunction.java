package com.reltio.pms.trail;

import com.google.api.core.ApiFuture;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.FieldValue;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.FirestoreOptions;
import com.google.cloud.firestore.SetOptions;
import com.google.cloud.functions.BackgroundFunction;
import com.google.cloud.functions.Context;
import com.google.gson.Gson;
import com.reltio.pms.trail.domain.AuthSecrets;
import com.reltio.pms.trail.service.ReltioAPIService;
import com.reltio.pms.trail.service.impl.SecretsServiceImpl;
import com.reltio.pms.trail.service.impl.SimpleReltioAPIServiceImpl;
import com.reltio.pms.trail.service.impl.TokenGeneratorServiceImpl;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.reltio.pms.trail.FunctionConstants.*;

public class TriggerJobsFunction implements BackgroundFunction<TriggerJobsFunction.FirestoreEvent> {

    private static final Gson gson = new Gson();

    private static final Logger logger = Logger.getLogger(TriggerJobsFunction.class.getName());

    private final Firestore firestore;
    private final SecretsServiceImpl secretsService;
    private ReltioAPIService reltioAPIService;

    public TriggerJobsFunction() {
        this(FirestoreOptions.getDefaultInstance().getService(), new SecretsServiceImpl(), null);
    }

    public TriggerJobsFunction(Firestore firestore, SecretsServiceImpl secretsService, ReltioAPIService reltioAPIService) {
        this.firestore = firestore;
        this.secretsService = secretsService;
        this.reltioAPIService = reltioAPIService;
    }

    @Override
    public void accept(FirestoreEvent firestoreEvent, Context context) throws Exception {

        String affectedDoc = context.resource().split("/documents/")[1].replace("\"", "");
        String reqEmail = affectedDoc.split("/OFFER_TYPES/")[0].replace("\"", "").split("/")[1];
        String[] docPath = affectedDoc.split("/OFFER_TYPES/")[1].replace("\"", "").split("-");
        String offerType = docPath[0];
        logger.log(Level.INFO, "OfferType is " + offerType);
        String tenantPurpose = docPath.length > 1 ? docPath[1].toUpperCase() : "FREE";
        logger.log(Level.INFO, "TenantPurpose is " + tenantPurpose);

        if (getFirestoreStringValue(firestoreEvent.value, REQ_JOB_ID) != null) {
            logger.log(Level.INFO, "Requester already have the Job associated with that. No action needed here :" + affectedDoc);
            return;
        }
        Boolean termsAccepted = getFirestoreBooleanValue(firestoreEvent.value, REQ_TERMS_ACCEPTED);
        if (!Boolean.TRUE.equals(termsAccepted)) {
            return;
        }

        DocumentReference docRef = firestore.document(affectedDoc);
        ApiFuture<Boolean> runTransaction = firestore.runTransaction(transaction -> {
            // retrieve document and increment population field
            DocumentSnapshot snapshot = transaction.get(docRef).get();
            String jobId = snapshot.get(REQ_JOB_ID, String.class);
            if (jobId != null && !jobId.isEmpty())
                return false;
            transaction.update(docRef, REQ_JOB_ID, "TEMP_JOB_ID");
            return true;
        });

        Boolean lockAcquired = runTransaction.get();
        if (lockAcquired) {
            try {
                AuthSecrets authSecrets = secretsService.getSecrets();
                reltioAPIService = reltioAPIService == null ?
                        new SimpleReltioAPIServiceImpl(new TokenGeneratorServiceImpl(authSecrets.getClientId(),
                                authSecrets.getClientSecret(), System.getenv().get(AUTH_URL))) : reltioAPIService;
                logger.log(Level.INFO, "Calling Create Job API");
                String termsFilePath = getFirestoreStringValue(firestoreEvent.value, REQ_TERMS_FILE);
                String url = getFirestoreStringValue(firestoreEvent.value, ENVIRONMENT) != null ?
                        System.getenv().get(PMS_JOBS_URL).replace(ENVIRONMENT, getFirestoreStringValue(firestoreEvent.value, ENVIRONMENT)) :
                        System.getenv().get(PMS_JOBS_URL).replace(ENVIRONMENT, System.getenv().get(DEFAULT_ENVIRONMENT));
                String response = reltioAPIService.post(url, getJobBody(reqEmail,
                        offerType, tenantPurpose, termsFilePath));
                logger.log(Level.INFO, "Create Job API completed: " + response);
                JobResponse jsonNode = gson.fromJson(response, JobResponse.class);
                String jobId = jsonNode.jobId;
                updateJobId(jobId, affectedDoc);
                logger.log(Level.INFO, "The Job Creation for Requester Completed Successfully: " + reqEmail);
            } catch (Exception e) {
                logger.log(Level.SEVERE,
                        "The Job creation failed for " + reqEmail + ". due to " + e.getMessage());
                deleteJobId(affectedDoc);
            }
        }
    }

    private void deleteJobId(String affectedDoc) {
        try {
            logger.log(Level.INFO, "Deleting the Firestore with Job ID");
            Map<String, Object> updates = new HashMap<>();
            updates.put(REQ_JOB_ID, FieldValue.delete());
            firestore.document(affectedDoc).update(updates).get();
            logger.log(Level.INFO, "Completed deleting JOB ID in the Firestore");
        } catch (ExecutionException | InterruptedException e) {
            logger.log(Level.SEVERE, "Error updating Firestore document: " + e.getMessage(), e);
        }
    }

    private void updateJobId(String jobId, String affectedDoc) {
        Map<String, String> newFields = new HashMap<>();
        newFields.put(REQ_JOB_ID, jobId);
        try {
            logger.log(Level.INFO, "Updating the Firestore with Job ID");
            firestore.document(affectedDoc).set(newFields, SetOptions.merge()).get();
            logger.log(Level.INFO, "Completed updating the Firestore with Job ID");
        } catch (ExecutionException | InterruptedException e) {
            logger.log(Level.SEVERE, "Error updating Firestore document: " + e.getMessage(), e);
        }
    }

    private static String getJobBody(String reqEmail, String offerType, String tenantPurpose, String termsFilePath) {
        JobRequest jobRequest = new JobRequest();
        jobRequest.requesterEmail = reqEmail;
        jobRequest.pipelineId = tenantPurpose.equals("TESTDRIVE") ? System.getenv().get(TESTDRIVE_PIPELINE_ID) :
                System.getenv().get(PIPELINE_ID);
        jobRequest.linkTermsFile = StringUtils.isEmpty(termsFilePath) ? System.getenv().get(TERMS_FILE_PATH) : termsFilePath;
        logger.log(Level.INFO, String.format("termsFilePath %s", termsFilePath));
        jobRequest.offerType = offerType;
        jobRequest.tenantPurpose = tenantPurpose;
        logger.log(Level.INFO, String.format("Body %s", jobRequest.linkTermsFile));
        return gson.toJson(jobRequest);
    }


    private String getFirestoreStringValue(FirestoreValue fsDoc, String fieldName) {
        String strValue = null;
        if (fsDoc != null && fsDoc.fields != null) {
            Map<String, Object> fieldValues = fsDoc.fields.get(fieldName);
            if (fieldValues != null) {
                strValue = (String) fieldValues.get(FS_STRING_VALUE);
            }
        }
        return strValue;
    }

    private Boolean getFirestoreBooleanValue(FirestoreValue fsDoc, String fieldName) {
        Boolean boolValue = null;
        if (fsDoc != null && fsDoc.fields != null) {
            Map<String, Object> fieldValues = fsDoc.fields.get(fieldName);
            if (fieldValues != null) {
                boolValue = (Boolean) fieldValues.get(FS_BOOLEAN_VALUE);
            }
        }
        return boolValue;
    }

    public static class FirestoreEvent {
        FirestoreValue value;
        FirestoreValue oldValue;

        public void setValue(FirestoreValue value) {
            this.value = value;
        }
    }

    public static class FirestoreValue {
        String createTime;
        String name;
        Map<String, Map<String, Object>> fields;

        public void setFields(Map<String, Map<String, Object>> fields) {
            this.fields = fields;
        }
    }

    public static class JobRequest {
        String pipelineId;
        String requesterEmail;
        String linkTermsFile;
        String offerType;

        String tenantPurpose;
    }

    public static class JobResponse {
        String jobId;
    }
}
