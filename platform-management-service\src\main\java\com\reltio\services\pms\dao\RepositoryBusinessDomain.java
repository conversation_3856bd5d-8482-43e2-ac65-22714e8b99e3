package com.reltio.services.pms.dao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class RepositoryBusinessDomain extends BaseFirestoreEntity {

    @JsonProperty("path")
    private String path;

    @JsonProperty("configData")
    private String configData;

    @JsonProperty("branchCommitHash")
    private String branchCommitHash;

    @Override
    public String getID() {
        return (getPath() != null) ? getPath().replace("/", "_") : "";
    }
}
