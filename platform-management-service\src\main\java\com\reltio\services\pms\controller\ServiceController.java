package com.reltio.services.pms.controller;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableSet;
import com.reltio.utils.SecurityUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.commons.configuration.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/v1", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Service Controller")
public class ServiceController {
    protected final Configuration configuration;

    @Autowired
    public ServiceController(Configuration configuration) {
        this.configuration = configuration;
    }


    @GetMapping(value = "/systemConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    public @ResponseBody
    ObjectNode systemConfig() {
        ObjectNode json = new ObjectNode(JsonNodeFactory.instance);
        SecurityUtils.secureFields(configuration, null, ImmutableSet.of(
                "aws.access.key", "aws.secret.key", "streaming.azure.applicationId", "streaming.azure.clientId", "streaming.azure.tenantId", "streaming.azure.clientSecret",
                "bitbucket.userName", "cassandra.pass", "cassandra.user", "compliance.gbq.key", "devtest.publisher.service.account.credentials",
                "google.recaptcha.secret.key", "image.hosting.account.access.key", "image.hosting.account.secret.key", "AWS_SECRET_KEY",
                "loqate.api.key", "mail.password", "mail.username", "matchIq.service.account.credentials", "okta.api.token", "aws.ih.cleaner.secretkey",
                "aws.ih.cleaner.accesskey", "projects.service.account.credentials", "publisher.service.account.credentials",
                "reltio.auth.client.id", "reltio.auth.client.secret", "reltio.auth.internal.token.key", "reltio.auth.password", "reltio.auth.username",
                "reltio.metrics.service.signalfx.orgtoken", "rih.client.secret", "rih.password", "rih.username", "service.account.credentials",
                "spanner.provisioner.credentials", "spanner.cloner.credentials", "stripe.api.key", "supportability.gbq.key", "workato.rih.apiToken", "zendesk.url", "zendesk.token", "zendesk.username",
                "git.password", "auth.pass", "auth.user", "bitbucket.pwd", "salesforce.username", "salesforce.password", "salesforce.client.id", "salesforce.client.secret",
                "gbq.logger.credentials"
        )).forEach((key, value) -> json.put(key, value.toString()));
        return json;
    }
}
