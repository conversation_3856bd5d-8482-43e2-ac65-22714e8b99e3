package com.reltio.services.pms.config.repos;

import com.reltio.services.pms.validator.PropertiesValidator;
import lombok.Getter;
import lombok.Setter;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Setter
@Getter
@Configuration
public class CloudNativeDbRepoInformation {
    private String userName;
    private String password;
    private String repoName;
    private String branch;
    private String environmentType;
    private static final Logger LOGGER = Logger.getLogger(CloudNativeDbRepoInformation.class);

    public CloudNativeDbRepoInformation(@Value("${bitBucket.userName}") String userName,
                                        @Value("${bitbucket.pwd}") String password,
                                        @Value("${cloud.native.db.bitbucket.repoName:#{null}}") String repoName,
                                        @Value("${cloud.native.db.bitbucket.branch:#{null}}") String branch,
                                        @Value("${reltio.environments.bitBucket.folder}") String environmentType) {
        this.userName = userName;
        this.password = password;
        this.repoName = repoName;
        this.branch = branch;
        this.environmentType = environmentType;
        Set<String> properties=new HashSet<>(Arrays.asList(userName,password,repoName,branch));
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

}

