package com.reltio.services.pms.common.sales.model;

import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class TenantsManagementLogsFilters {
    private String requesterEmail;
    private String tenantId;
    private String envId;
    private Long createdTime;
    private List<Map<String, Object>> updatedParams;
}