package com.reltio.services.pms.common.model.compliance.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The type Cp usage entity response.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public class CpUsageEntityResponse extends CpUsageResponse{
    /**
     * The Match rule entity type.
     */
    @JsonProperty("matchRuleEntityType")
    public String matchRuleEntityType;

    /**
     * The Match rule entity count.
     */
    @JsonProperty("matchRuleEntityCount")
    public String matchRuleEntityCount;

    /**
     * The Total count.
     */
    @JsonProperty("totalCount")
    public String totalCount;


    /**
     * Instantiates a new Cp usage entity response.
     *
     * @param reportDate           the report date
     * @param environment          the environment
     * @param tenantId             the tenant id
     * @param matchRuleEntityType  the match rule entity type
     * @param matchRuleEntityCount the match rule entity count
     * @param totalCount           the total count
     */
    public CpUsageEntityResponse(String reportDate, String environment, String tenantId, String matchRuleEntityType, String matchRuleEntityCount,String totalCount) {
        super(reportDate, environment, tenantId);
        this.matchRuleEntityType = matchRuleEntityType;
        this.matchRuleEntityCount = matchRuleEntityCount;
        this.totalCount= totalCount;
    }
}
