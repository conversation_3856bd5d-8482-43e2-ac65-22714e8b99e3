package com.reltio.services.pms.service.compliance.util;

import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.SalesAccount;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;

public class ComplianceUtil {

    /**
     * Gets purpose.
     *
     * @param tenantId         the tenant id
     * @param tenantCollection the tenant collection
     * @return the purpose
     */
    protected String getPurpose(String tenantId, Collection<ReltioTenant> tenantCollection) {
        Predicate<ReltioTenant> tenantPredicate = tenant -> Objects.equals(tenant.getTenantId(), tenantId);
        Optional<ReltioTenant> tenantDetailsOptional = tenantCollection.stream().filter(Objects::nonNull).filter(tenantPredicate).findFirst();
        if (tenantDetailsOptional.isPresent() && Objects.nonNull(tenantDetailsOptional.get().getTenantPurpose())) {
            return tenantDetailsOptional.get().getTenantPurpose().name();
        } else if (tenantDetailsOptional.isPresent() && Objects.nonNull((tenantDetailsOptional.get().getPurpose()))) {
            return tenantDetailsOptional.get().getPurpose().name();
        }
        return StringUtils.EMPTY;
    }


    /**
     * Gets customer name.
     *
     * @param tenantId          the tenant id
     * @param tenantCollection  the tenant collection
     * @param accountCollection the account collection
     * @return the customer name
     */
    protected String getCustomerName(String tenantId, Collection<ReltioTenant> tenantCollection, Collection<SalesAccount> accountCollection) {
        Optional<ReltioTenant> tenantDetailsOptional = tenantCollection.stream().filter(Objects::nonNull).filter(tenant -> Objects.equals(tenant.getTenantId(), tenantId)).findFirst();
        if (tenantDetailsOptional.isPresent() && StringUtils.isNotBlank(tenantDetailsOptional.get().getAccountId())) {
            Predicate<SalesAccount> accountPredicate = accountDetails -> Objects.equals(accountDetails.getAccountId(), tenantDetailsOptional.get().getAccountId());
            return accountCollection.stream().filter(accountPredicate).map(SalesAccount::getName).findFirst().orElse(StringUtils.EMPTY);
        }
        return StringUtils.EMPTY;
    }

}
