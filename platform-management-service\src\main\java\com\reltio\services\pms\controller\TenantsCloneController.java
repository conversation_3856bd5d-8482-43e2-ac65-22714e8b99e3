package com.reltio.services.pms.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.reltio.services.pms.common.exception.SpannerBackupOperationException;
import com.reltio.services.pms.common.exception.SpannerDatabaseOperationException;
import com.reltio.services.pms.common.model.TenantsCloneJob;
import com.reltio.services.pms.common.model.TenantsCloneJobRequest;
import com.reltio.services.pms.common.sales.model.Backup;
import com.reltio.services.pms.dto.SpannerBackupStatus;
import com.reltio.services.pms.dto.SpannerDatabaseStatus;
import com.reltio.services.pms.service.jobs.tasks.TenantsCloneJobService;
import com.reltio.services.pms.service.jobs.tasks.tenants.data.clone.DynamoDbBackupService;
import com.reltio.services.pms.service.jobs.tasks.tenants.data.clone.SpannerCommonUtilService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

@RestController
@RequestMapping(value = "/api/v1/cloneTenant", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Tenants Clone Controller")
public class TenantsCloneController {
    private final TenantsCloneJobService jobService;

    private final DynamoDbBackupService dynamoDbBackupService;
    private final SpannerCommonUtilService spannerCommonUtilService;

    @Autowired
    public TenantsCloneController(TenantsCloneJobService jobService, DynamoDbBackupService dynamoDbBackupService, SpannerCommonUtilService spannerCommonUtilService) {
        this.jobService = jobService;
        this.dynamoDbBackupService = dynamoDbBackupService;
        this.spannerCommonUtilService = spannerCommonUtilService;
    }

    @PostMapping(value = "/createJob", consumes = MediaType.APPLICATION_JSON_VALUE)
    public TenantsCloneJob cloneJob(@RequestBody TenantsCloneJobRequest request) {
        return jobService.createJob(request);
    }

    @GetMapping(value = "/dynamoBackupDetails/{sourceTenantId}")
    public Collection<Backup> getDynamoBackupDetails(@PathVariable String sourceTenantId ,
                                                     @RequestParam(required = false)String ticketId) {
        return dynamoDbBackupService.getDynamoBackupDetails(sourceTenantId, ticketId);
    }

    @GetMapping(value = "/getAllDynamoBackups")
    public Collection<Backup> getAllDynamoBackups(@RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                                  @RequestParam(value = "size", required = false, defaultValue = "20") Integer size) {
        return dynamoDbBackupService.getAllBackups(size,offset);
    }

    @PostMapping(value = "/spannerBackupStatus")
    public SpannerBackupStatus spannerBackupStatus(@RequestBody JsonNode node) {
        if (node == null || !node.hasNonNull("tenantId") || !node.hasNonNull("envId") || !node.hasNonNull("ticketId")) {
            throw new SpannerBackupOperationException("Invalid input: 'tenantId', 'envId', and 'ticketId' must be provided in request body.");
        }
        return spannerCommonUtilService.checkBackupStatus(node.get("tenantId").asText(), node.get("envId").asText(), node.get("ticketId").asText());
    }

    @PostMapping(value = "/spannerDatabaseStatus")
    public SpannerDatabaseStatus spannerDatabaseStatus(@RequestBody JsonNode node) {
        if (node == null || !node.hasNonNull("tenantId") || !node.hasNonNull("envId")) {
            throw new SpannerDatabaseOperationException("Invalid input: 'tenantId' and 'envId' must be provided in request body.");
        }
        return spannerCommonUtilService.checkDatabaseStatus(node.get("tenantId").asText(), node.get("envId").asText());
    }

    @PostMapping(value = "/spannerDatabases")
    public List<String> spannerDatabases(@RequestBody JsonNode node) {
        if (node == null || !node.hasNonNull("tenantId") || !node.hasNonNull("envId")) {
            throw new SpannerDatabaseOperationException("Invalid input: 'tenantId' and 'envId' must be provided in request body.");
        }
        return spannerCommonUtilService.listDatabase(node.get("tenantId").asText(), node.get("envId").asText());
    }

    @PostMapping(value = "/spannerBackups")
    public List<String> spannerBackups(@RequestBody JsonNode node) {
        if (node == null || !node.hasNonNull("tenantId") || !node.hasNonNull("envId")) {
            throw new SpannerBackupOperationException("Invalid input: 'tenantId' and 'envId' must be provided in request body.");
        }
        return spannerCommonUtilService.listBackups(node.get("tenantId").asText(), node.get("envId").asText());
    }
}


