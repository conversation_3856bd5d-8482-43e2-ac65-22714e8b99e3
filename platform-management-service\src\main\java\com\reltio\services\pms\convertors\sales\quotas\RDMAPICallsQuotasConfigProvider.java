package com.reltio.services.pms.convertors.sales.quotas;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.QuantityType;
import com.reltio.services.pms.common.sales.QuotaPeriod;
import com.reltio.services.pms.common.sales.QuotaType;
import com.reltio.services.pms.common.sales.model.PMSQuotaName;
import com.reltio.services.pms.common.sales.model.SalesForceProductName;
import com.reltio.services.pms.common.sales.model.quotas.BaseQuotasConfig;
import com.reltio.services.pms.service.SalesPackageService;
import org.springframework.stereotype.Service;

import java.util.Set;
@Service
public class RDMAPICallsQuotasConfigProvider extends AbstractQuotasConfigProvider<BaseQuotasConfig> {

    public RDMAPICallsQuotasConfigProvider(SalesPackageService salesPackageService) {
        super(salesPackageService);
    }

    @Override
    public PMSQuotaName getQuotaName() {
        return PMSQuotaName.RDM_API_CALLS;
    }

    @Override
    public QuotaType getQuotaType() {
        return QuotaType.TENANT_LEVEL;
    }

    @Override
    public QuotaPeriod getQuotaPeriod() {
        return QuotaPeriod.DAILY;
    }

    @Override
    public QuantityType getQuantityType() {
        return QuantityType.MILLIONS;
    }

    @Override
    public PMSProductName getPMSProductName() {
        return PMSProductName.RDM;
    }

    @Override
    public Set<String> getQuotaProductCodes() {
        return salesPackageService.getSalesForceQuotas(SalesForceProductName.RDM_API_CALLS);
    }
}
