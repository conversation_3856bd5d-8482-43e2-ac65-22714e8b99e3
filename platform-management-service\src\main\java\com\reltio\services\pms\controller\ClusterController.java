package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.auth.domain.ReltioPrivileges;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.Cluster;
import com.reltio.services.pms.service.environment.ClusterService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.DeleteMapping;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping(value = "/api/v1/clusters", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Clusters")
@ReltioSecured(resourceClass = Pms.Environment.Config.Clusters.class)
public class ClusterController {

    private static final Logger LOG = Logger.getLogger(ClusterController.class);

    private final ClusterService clusterService;

    @Autowired
    public ClusterController(ClusterService clusterService) {
        this.clusterService = clusterService;
    }

    @GetMapping("/byName")
    public Cluster getClusterByName(@RequestParam(value = "name") String name,
                                    @RequestParam(value = "cloud") String cloud,
                                    @RequestParam(value = "region") String region) {
        return clusterService.getCluster(name.toLowerCase(), cloud.toLowerCase(), region.toLowerCase());
    }

    @GetMapping("")
    public Collection<Cluster> getClusterByProperty(@RequestParam(value = "property", required = false) String property,
                                                    @RequestParam(value = "value", required = false) String value) {
        Collection<Cluster> clusters;
        if (property != null && value != null) {
            if ("tenants".equals(property.toLowerCase())) {
                clusters = clusterService.getClusterWithFilter(property.toLowerCase(), value);
            } else {
                clusters = clusterService.getClusterWithFilter(property.toLowerCase(), value.toLowerCase());
            }
        } else if (property == null && value == null) {
            clusters = clusterService.getAllClusters();
        } else {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value());
        }
        return clusters;
    }

    @PostMapping(value = "/update", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.Config.Clusters.class,privileges = ReltioPrivileges.UPDATE)
    public Cluster updateClusters(@RequestBody Cluster cluster) {
        return clusterService.updateCluster(cluster.toLowerCase());
    }


    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.Config.Clusters.class,privileges = ReltioPrivileges.CREATE)
    public Cluster createClusters(@RequestBody Cluster cluster) {
        return clusterService.createCluster(cluster.toLowerCase());
    }


    @PutMapping(value = "/{id}/tenants/", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.Config.Clusters.class,privileges = ReltioPrivileges.UPDATE)
    public Set<String> updateTenants(@PathVariable(value = "id") String id,@RequestBody Set<String> tenants) {
         return clusterService.updateTenants(tenants,id);
    }


    @PutMapping(value = "/{id}/environments/", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.Config.Clusters.class,privileges = ReltioPrivileges.UPDATE)
    public Set<String> updateEnvironment(@PathVariable(value = "id") String id,@RequestBody Set<String> environments) {
        return clusterService.updateEnvironments(environments,id);
    }

    @PutMapping(value = "/{id}/urls/", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.Config.Clusters.class,privileges = ReltioPrivileges.UPDATE)
    public List<String> updateUrl(@PathVariable(value = "id") String id,@RequestBody List<String> urls) {
       return clusterService.updateUrl(urls,id);
    }


    @PutMapping(value = "{id}/customers/", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.Config.Clusters.class,privileges = ReltioPrivileges.UPDATE)
    public Set<String> updateCustomers( @PathVariable(value = "id") String id,@RequestBody Set<String> customers) {
        return clusterService.updateCustomers(customers,id);
    }

    @DeleteMapping(value = "/{id}")
    public Cluster deleteCluster(@PathVariable String id) {
        return clusterService.deleteCluster(id);
    }

}
