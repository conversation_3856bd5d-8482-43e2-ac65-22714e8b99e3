package com.reltio.services.pms.common.model.jobs.tasks.notification;

import com.reltio.services.pms.common.model.Requester;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstanceParams;

import java.util.List;

public interface FreeTierEmailNotificationTaskInstanceParams extends TaskInstanceParams {

    Requester getRequester();

    String getLinkTermsFile();

    String getTenantId();

    boolean isRequesterExistsOnAuth();

    List<String> getCc();

    String getOverwriteToEmail();
}
