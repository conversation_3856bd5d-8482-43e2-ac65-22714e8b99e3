package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;


@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public  class TrainingTenant extends BaseFirestoreEntity {
    @JsonProperty("userMail")
    String userMail;

    @JsonProperty("jobId")
    private String jobId;

    @Override
    public String getID(){
        return this.userMail;
    }


}
