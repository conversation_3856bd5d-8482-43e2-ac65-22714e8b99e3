package com.reltio.services.pms.common.model;

import com.reltio.services.pms.common.sales.model.TenantStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class TenantFilter {
    private Integer offset;
    private Integer size;
    private TenantStatus status;
    private String department;
    private String division;
    private String costCenter;
    private String customerType;
    private String deploymentCloud;
    private String owners;
    private String environment;
    private String parentJobId;
    private String endDate;
}
