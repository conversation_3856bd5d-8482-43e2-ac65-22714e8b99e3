package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;

import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class TrainingTenantCreationRequest extends EnterpriseTenantCreationRequest {


    public TrainingTenantCreationRequest(@JsonProperty(value = "pipelineId", required = true) String pipelineId,
                                         @JsonProperty(value = "customerName") String customerName,
                                         @JsonProperty(value = "cleanseRegions") List<String> cleanseRegions,
                                         @JsonProperty(value = "owners", required = true) List<String> owners,
                                         @JsonProperty(value = "configureDefaultQueue") Boolean configureDefaultQueue,
                                         @JsonProperty(value = "rdmTenantToReuse") String rdmTenantToReuse,
                                         @JsonProperty(value = "clientNames") Set<String> clientNames,
                                         @JsonProperty(value = "productEdition", required = true) String productEdition,
                                         @JsonProperty(value = "streamingCloud") String streamingCloud,
                                         @JsonProperty(value = "loqateProcesses") String loqateProcesses,
                                         @JsonProperty(value = "mdmTenantId") String mdmTenantId,
                                         @JsonProperty(value = "shortDescription") String shortDescription,
                                         @JsonProperty(value = "tenantRecordType", defaultValue = "Customer Tenant") String tenantRecordType,
                                         @JsonProperty(value = "ownedByReltioDept") String ownedByReltioDept,
                                         @JsonProperty(value = "rdmTenantId") String rdmTenantId,
                                         @JsonProperty(value = "rih") Map<String, String> rihKey,
                                         @JsonProperty(value = "rdmHostNameUrl") String rdmHostNameUrl,
                                         @JsonProperty(value = "newInstanceId") String newInstanceId,
                                         @JsonProperty(value = "queueNames") Set<String> queueNames,
                                         @JsonProperty(value = "endDate") String endDate,
                                         @JsonProperty(value = "reltioPackageType") ReltioPackageType reltioPackageType,
                                         @JsonProperty("skipTasks") EnumSet<PMSProductName> skipTasks) {
        super(pipelineId, customerName, "Training", TenantSize.XX_SMALL, "default", cleanseRegions, owners, configureDefaultQueue,
                rdmTenantToReuse, TenantPurpose.TRAINING, clientNames, productEdition, false, streamingCloud, loqateProcesses, mdmTenantId,
                null, null, null, null, shortDescription, tenantRecordType, ownedByReltioDept,
                rdmTenantId, rihKey, rdmHostNameUrl, false, newInstanceId, queueNames, CustomerType.TRAINING, "Sales", "Training & Enablement",
                "55 Training & Support", endDate, reltioPackageType, skipTasks, Boolean.TRUE, true, null);
    }

}
