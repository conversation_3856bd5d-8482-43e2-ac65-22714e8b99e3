package com.reltio.services.pms.clients.reltio;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.reltio.services.pms.clients.reltio.dnb.DNBConnectorClient;
import com.reltio.services.pms.clients.reltio.dtss.DTSSClient;
import com.reltio.services.pms.clients.reltio.imagehosting.ImageHostingServiceClient;
import com.reltio.services.pms.clients.reltio.mdm.MDMBusinessLayerClient;
import com.reltio.services.pms.clients.reltio.workflow.WorkflowServiceClient;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.service.jobs.tasks.provisioning.matchiq.MatchIQClient;
import com.reltio.services.pms.service.jobs.tasks.provisioning.workato.Customer;
import com.reltio.services.pms.service.jobs.tasks.provisioning.workato.WorkatoClientException;
import com.reltio.services.pms.service.jobs.tasks.provisioning.workato.WorkatoEnablementClient;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class EnablementChecker {

    private final MatchIQClient matchIQClient;
    private final ImageHostingServiceClient imageHostingServiceClient;
    private final WorkflowServiceClient workflowServiceClient;
    private final DTSSClient dtssClient;
    private final WorkatoEnablementClient workatoEnablementClient;
    private final DNBConnectorClient dnbConnectorClient;

    private final MDMBusinessLayerClient mdmBusinessLayerClient;
    private static final Logger LOGGER = Logger.getLogger(EnablementChecker.class);
    @Autowired
    public EnablementChecker(
            MatchIQClient matchIQClient,
            ImageHostingServiceClient imageHostingServiceClient,
            WorkflowServiceClient workflowServiceClient,
            DTSSClient dtssClient,
            WorkatoEnablementClient workatoEnablementClient,
            DNBConnectorClient dnbConnectorClient, MDMBusinessLayerClient mdmBusinessLayerClient) {
        this.matchIQClient = matchIQClient;
        this.imageHostingServiceClient = imageHostingServiceClient;
        this.workflowServiceClient = workflowServiceClient;
        this.dtssClient = dtssClient;
        this.workatoEnablementClient = workatoEnablementClient;
        this.dnbConnectorClient = dnbConnectorClient;
        this.mdmBusinessLayerClient = mdmBusinessLayerClient;
    }

    public boolean matchIqEnabled(String tenantId, String env) {
        return matchIQClient.isEnabledForTenant(tenantId, env);
    }

    public boolean imageHostingEnabled(String tenantId, String env) {
        return imageHostingServiceClient.isEnabledForTenant(tenantId, env);
    }

    public boolean workflowEnabled(String tenantId, String env) {
        String response = workflowServiceClient.fetchWorkflowRegistration(env);
        return response.contains(tenantId);
    }

    public boolean dtssEnabled(String tenantId, String env) {
        return dtssClient.isEnabledForTenant(tenantId, env);
    }

    public List<String> dtssSubscribed(String tenantId, String env) {
        return dtssClient.isTenantSubscribed(env, tenantId, Boolean.TRUE, Boolean.TRUE);
    }

    public boolean workatoEnabled(String url, String tenantId, String envId, String custId, String rihEnv) throws JsonProcessingException {
        try {
            List<Customer> allEnvCustomer = workatoEnablementClient.getAllEnvironments(url, rihEnv);
            return allEnvCustomer.stream().anyMatch(c -> c.getReltioTenant().equals(tenantId) && c.getReltioEnvironment().equals(envId) && c.getName().equals(custId));
        } catch (WorkatoClientException ex) {
            LOGGER.warn("Error while checking Workato enabled", ex);
            return false;
        }
    }

    public String dnbTenant(String envId, String tenantId, TenantPurpose purpose) {
        return dnbConnectorClient.getDataTenantByTenant(envId, tenantId, purpose);
    }

    public List<String> getGeneratorsForTenant(String envId, String tenantId) {
        return mdmBusinessLayerClient.getGenerators(envId).stream().filter(v -> v.contains(tenantId)).collect(Collectors.toList());
    }
}
