package com.reltio.services.pms.clients.external.gcp;

import com.google.api.gax.core.FixedCredentialsProvider;
import com.google.auth.Credentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.List;


@Component
public class GoogleCredentialsProvider {

    private GoogleCredentialsProvider() {
        //nothing to set
    }

    public static Credentials getCredentials(String value, List<String> scopes) throws IOException {
        InputStream in = getKeyStream(value);
        return FixedCredentialsProvider.create(ServiceAccountCredentials.fromStream(in).createScoped(scopes)).getCredentials();
    }


    private static InputStream getKeyStream(String keyOrPath) throws IOException {

        Path path = Paths.get(FilenameUtils.getName(keyOrPath));

        if (path.toFile().exists()) {
            return Files.newInputStream(path);
        }
        byte in[] = Base64.getDecoder().decode(keyOrPath);
        return new ByteArrayInputStream(in);
    }

}
