package com.reltio.services.pms.common.model.jobs.tasks.maintenance;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class MaintenanceModeTaskInstance extends TaskInstance {

    private final String tenantName;

    private final Boolean valueToSet;

    private final int secondsDelay;

    private List<String> events;

    private Long modeChangeTimestamp;

    @JsonCreator
    public MaintenanceModeTaskInstance(@JsonProperty(value = "taskId") String taskId,
                                       @JsonProperty(value = "name") String name,
                                       @JsonProperty(value = "jobId") String jobId,
                                       @JsonProperty(value = "startTime") Long startTime,
                                       @JsonProperty(value = "finishTime") Long finishTime,
                                       @JsonProperty(value = "status") TaskStatus status,
                                       @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                       @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                       @JsonProperty(value = "executingNodeName") String executingNodeName,
                                       @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                       @JsonProperty(value = "envId") String envId,
                                       @JsonProperty(value = "tenantName") String tenantName,
                                       @JsonProperty(value = "valueToSet") Boolean valueToSet,
                                       @JsonProperty(value = "secondsDelay") int secondsDelay,
                                       @JsonProperty(value = "events") List<String> events,
                                       @JsonProperty(value = "modeChangeTimestamp") Long modeChangeTimestamp
    ) {
        super(taskId, name, jobId, startTime, finishTime, TaskType.MAINTENANCE_MODE_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.tenantName = tenantName;
        this.valueToSet = valueToSet;
        this.secondsDelay = secondsDelay;
        this.events = events == null ? new ArrayList<>() : new ArrayList<>(events);
        this.modeChangeTimestamp = modeChangeTimestamp;
    }

    public void addEvent(String event) {
        events.add(event);
    }

}
