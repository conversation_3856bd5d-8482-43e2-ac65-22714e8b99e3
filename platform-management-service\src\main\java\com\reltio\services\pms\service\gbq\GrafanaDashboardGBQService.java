package com.reltio.services.pms.service.gbq;

import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.BigQueryException;
import com.google.cloud.bigquery.BigQueryOptions;
import com.google.cloud.bigquery.JobException;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.Schema;
import com.google.cloud.bigquery.StandardTableDefinition;
import com.google.cloud.bigquery.Table;
import com.google.cloud.bigquery.TableId;
import com.google.cloud.bigquery.TableInfo;
import com.google.cloud.bigquery.TableResult;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.gbq.GrafanaDashboardGBQModel;
import com.reltio.services.pms.common.model.jobs.Job;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.dao.JobDao;
import com.reltio.services.pms.service.compliance.impl.gbq.QueryBuilder;
import org.apache.commons.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.reltio.services.pms.clients.external.gcp.GoogleCredentialsProvider.getCredentials;


@Service
public class GrafanaDashboardGBQService {
    public static final List<String> SCOPES = Collections.singletonList("https://www.googleapis.com/auth/bigquery");
    private final Logger LOG = LoggerFactory.getLogger(GrafanaDashboardGBQService.class);
    private final String projectId;
    private final String datasetId;
    private final String tableName;
    private final String gbqCredentials;
    private final JobDao jobDao;
    private BigQuery bigQuery;

    public GrafanaDashboardGBQService(Configuration configuration, JobDao jobDao) {
        this.projectId = configuration.getString("gbq.logger.project.id");
        this.datasetId = configuration.getString("gbq.job.task.dataset");
        this.tableName = configuration.getString("gbq.job.task.table");
        this.gbqCredentials = configuration.getString("gbq.logger.credentials");
        this.jobDao = jobDao;
    }

    public boolean gbqLoggingEnabled() {
        return this.gbqCredentials != null;
    }

    public boolean isRecordPresentForJob(String jobId, Long startTime) {
        String sTime = getTableName(startTime);
        if (sTime == null) {
            return false;
        }
        String table = tableName + "_" + sTime;
        return dmlOnGBQ(getJobQuery(), getRecordParams(jobId, table), table);
    }

    private String getJobQuery() {
        return "SELECT jobId, status, startedTime, endTime, createdBy \n" +
                " FROM `${projectId}.${dataset}.${tableName}` \n" +
                " WHERE jobId='${jobId}' ";
    }

    public boolean insertRecordsInGBQ(Job job) {
        String sTime = getTableName(job.getStartTime());
        if (sTime == null) {
            return false;
        }
        String table = tableName + "_" + sTime;
        return dmlOnGBQ(insertJobQuery(), insertParams(job.getJobId(), table, job.getStatus().name(), job.getFinishTime(), job.getCreatedBy(), job.getStartTime()), table);
    }

    private String getTableName(Long epochMilli) {
        if (epochMilli != null) {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(epochMilli), ZoneId.systemDefault());
            return localDateTime.format(DateTimeFormatter.ISO_LOCAL_DATE).replace("-", "_");
        }
        return null;
    }

    private boolean dmlOnGBQ(String query, Map<String, Object> params, String table) {
        getBQService();
        createdTable(table);
        QueryBuilder queryBuilder = new QueryBuilder(query, params);
        QueryJobConfiguration job = QueryJobConfiguration.newBuilder(queryBuilder.build()).build();
        try {
            TableResult results = bigQuery.query(job);
            if (results.getTotalRows() >= 1) {
                return true;
            }
        } catch (InterruptedException | JobException | BigQueryException exception) {
            if (exception instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            LOG.error("Call DB service to get stats failed with exception: ", exception);
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), exception, exception.getMessage());
        }
        return false;
    }

    private String insertJobQuery() {
        return "INSERT INTO `${projectId}.${dataset}.${tableName}` \n" +
                " (status, endTime, jobId, createdBy, startedTime) \n" +
                " VALUES ('${status}', ${endTime}, '${jobId}','${createdBy}',${startedTime})";
    }

    private Map<String, Object> insertParams(String jobId, String tableName, String status, Long endTime, String createdBy, Long startedTime) {
        Map<String, Object> params = addUpdateParams(jobId, tableName, status, endTime);
        params.put("createdBy", createdBy);
        params.put("startedTime", startedTime);
        return params;
    }

    private void getBQService() {
        if (bigQuery == null) {
            try {
                this.bigQuery = BigQueryOptions.newBuilder().setProjectId(projectId).setCredentials(getCredentials(gbqCredentials, SCOPES)).build().getService();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    //created Table if not present
    private void createdTable(String tableName) {
        TableId tableId = TableId.of(datasetId, tableName);
        Table table = bigQuery.getTable(tableId);
        if (Objects.isNull(table)) {
            Schema schema = GrafanaDashboardGBQModel.getSchema();
            bigQuery.create(TableInfo.newBuilder(tableId, StandardTableDefinition.of(schema)).build());
        }
    }

    private Map<String, Object> addUpdateParams(String jobId, String tableName, String status, Long endTime) {
        Map<String, Object> params = getRecordParams(jobId, tableName);
        params.put("status", status);
        params.put("endTime", endTime == null ? 0 : endTime);
        return params;
    }

    private Map<String, Object> getRecordParams(String jobId, String tableName) {
        Map<String, Object> params = getDefaultParams(tableName);
        params.put("jobId", jobId);
        return params;
    }

    private Map<String, Object> getDefaultParams(String tableName) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("dataset", datasetId);
        params.put("tableName", tableName);
        return params;
    }

    public boolean insertRecordsInGBQ(TaskInstance task) {
        Job job;
        try {
            job = jobDao.get(task.getEnvId(), task.getJobId());
        } catch (com.reltio.services.pms.common.exception.InvalidDocumentIdException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.JOB_NOT_FOUND, HttpStatus.NOT_FOUND.value(), task.getJobId());
        }
        return insertTaskRecordsInGBQ(job, task);
    }

    private boolean insertTaskRecordsInGBQ(Job job, TaskInstance task) {
        String sTime = getTableName(job.getStartTime());
        if (sTime == null) {
            return false;
        }
        String table = tableName + "_" + sTime;
        if (isRecordPresentForJob(job.getJobId(), job.getStartTime())) {
            Map<String, Object> params = getRecordParams(job.getJobId(), table);
            return dmlOnGBQ(UpdateJobAndTAskQuery(), insertTaskParams(params, task), table);
        } else {
            Map<String, Object> params = insertParams(job.getJobId(), table, job.getStatus().name(), job.getFinishTime(), job.getCreatedBy(), job.getStartTime());
            return dmlOnGBQ(insertJobAndTAskQuery(), insertTaskParams(params, task), table);
        }
    }

    private String insertJobAndTAskQuery() {
        return "INSERT INTO `${projectId}.${dataset}.${tableName}` \n" +
                " (jobId, startedTime, endTime, createdBy, status, taskIds) \n" +
                " VALUES ('${jobId}', ${startedTime}, ${endTime}, '${createdBy}', '${status}', " + "[('${taskId}', " +
                "PARSE_NUMERIC('${taskStartedTime}'), PARSE_NUMERIC('${taskEndTime}'),'${taskCreatedBy}', " +
                "'${taskStatus}', '${taskType}')])";
    }

    private String UpdateJobAndTAskQuery() {
        return "UPDATE `${projectId}.${dataset}.${tableName}` \n" +
                " SET taskIds = ARRAY(select t FROM  UNNEST(taskIds) t \n" +
                " UNION ALL \n" +
                " select('${taskId}', ${taskStartedTime}, ${taskEndTime},'${taskCreatedBy}', '${taskStatus}', '${taskType}')) \n " +
                " WHERE jobId='${jobId}'";
    }

    private Map<String, Object> insertTaskParams(Map<String, Object> params, TaskInstance task) {
        params.put("taskId", task.getTaskId());
        params.put("taskStartedTime", task.getStartTime());
        params.put("taskEndTime", task.getFinishTime() == null ? 0 : task.getFinishTime());
        params.put("taskCreatedBy", task.getCreatedBy() == null ? " " : task.getCreatedBy());
        params.put("taskStatus", task.getStatus());
        params.put("taskType", task.getType().name());

        return params;
    }

    public boolean updatedRecord(String jobId, String status, Long endTime, Long startTime) {
        String sTime = getTableName(startTime);
        if (sTime == null) {
            return false;
        }
        String table = tableName + "_" + sTime;
        return dmlOnGBQ(updateJobQuery(), addUpdateParams(jobId, table, status, endTime), table);
    }

    private String updateJobQuery() {
        return "UPDATE `${projectId}.${dataset}.${tableName}`\n" +
                " SET status = '${status}', endTime= ${endTime} \n" +
                " WHERE jobId='${jobId}'";
    }

}
