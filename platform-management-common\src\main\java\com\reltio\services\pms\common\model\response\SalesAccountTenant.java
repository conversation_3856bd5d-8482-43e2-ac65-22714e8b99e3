package com.reltio.services.pms.common.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class SalesAccountTenant {

    @JsonProperty("tenantId")
    String tenantId;

    @JsonProperty("tenantPurpose")
    String tenantPurpose;

    @JsonProperty("reltioEnv")
    String reltioEnv;

    @JsonProperty("deploymentCloud")
    String deploymentCloud;
}
