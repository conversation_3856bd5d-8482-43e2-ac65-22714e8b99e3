package com.reltio.services.pms.common.model.jobs.tasks.clean.tenant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataCleanUpOptions {
    @JsonProperty(value = "primary")
    private boolean primary;

    @JsonProperty(value = "history")
    private boolean history;

    @JsonProperty(value = "activities")
    private boolean activities;

    @JsonProperty(value = "secondary")
    private boolean secondary;

    @JsonProperty(value = "internalQueue")
    private boolean internalQueue;

    @JsonProperty(value = "graphClean")
    private boolean graphClean;

    @JsonCreator
    public DataCleanUpOptions(
            @JsonProperty(value = "primary") boolean primary,
            @JsonProperty(value = "history") boolean history,
            @JsonProperty(value = "activities") boolean activities,
            @JsonProperty(value = "secondary") boolean secondary,
            @JsonProperty(value = "internalQueue") boolean internalQueue,
            @JsonProperty(value = "graphClean") boolean graphClean) {
        this.primary = primary;
        this.history = history;
        this.activities = activities;
        this.secondary = secondary;
        this.internalQueue = internalQueue;
        this.graphClean = graphClean;
    }

    public boolean isPrimary() {
        return primary;
    }

    public boolean isHistory() {
        return history;
    }

    public boolean isActivities() {
        return activities;
    }

    public boolean isSecondary() {
        return secondary;
    }

    public boolean isInternalQueue() {
        return internalQueue;
    }

    public boolean isGraphClean() {
        return graphClean;
    }

    public void setPrimary(boolean primary) {
        this.primary = primary;
    }

    public void setHistory(boolean history) {
        this.history = history;
    }

    public void setActivities(boolean activities) {
        this.activities = activities;
    }

    public void setSecondary(boolean secondary) {
        this.secondary = secondary;
    }

    public void setInternalQueue(boolean internalQueue) {
        this.internalQueue = internalQueue;
    }

    public void setGraphClean(boolean graphClean) {
        this.graphClean = graphClean;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if(o == null || this.getClass() != o.getClass()) return false;

        DataCleanUpOptions that = (DataCleanUpOptions) o;
        return isPrimary() == that.isPrimary() &&
                isHistory() == that.isHistory() &&
                isActivities() == that.isActivities() &&
                isSecondary() == that.isSecondary() &&
                isInternalQueue() == that.isInternalQueue() &&
                isGraphClean() == that.isGraphClean();
    }

    @Override
    public int hashCode() {
        return Objects.hash(isPrimary(), isHistory(), isActivities(), isSecondary(), isInternalQueue(), isGraphClean());
    }

    @Override
    public String toString() {
        return "DataCleanUpOptions{" +
                "primary=" + primary +
                ", history=" + history +
                ", activities=" + activities +
                ", secondary=" + secondary +
                ", internalQueue=" + internalQueue +
                ", graphClean=" + graphClean +
                '}';
    }
}
