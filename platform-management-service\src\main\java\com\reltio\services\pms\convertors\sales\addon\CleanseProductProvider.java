package com.reltio.services.pms.convertors.sales.addon;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.common.sales.model.addon.CleanseProcessType;
import com.reltio.services.pms.common.sales.model.addon.CleanseProductConfig;
import com.reltio.services.pms.convertors.sales.AbstractTenantAddOnProductProvider;
import com.reltio.services.pms.service.PMSConfigService;
import com.reltio.services.pms.service.SalesPackageService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class CleanseProductProvider extends AbstractTenantAddOnProductProvider<CleanseProductConfig> {

    private static final String ALL_REGIONS_PRODUCT_CODE = "LOQ-WW-NT";
    private static final String SERP_PRODUCT_CODE = "LOQ-SERP-NT";
    private static final String CASS_PRODUCT_CODE = "LOQ-CASS-NT";
    private static final String RGEO_PRODUCT_CODE = "LOQ-RVGC-NT";

    private final PMSConfigService configService;

    private final String cleanseMappingName;


    public CleanseProductProvider(PMSConfigService configService, @Value("${loqate.cleanse.mappingName}") String cleanseMappingName, SalesPackageService salesPackageService) {
        super(salesPackageService);
        this.configService = configService;
        this.cleanseMappingName = cleanseMappingName;
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.CLEANSE;
    }

    @Override
    public Map<String, Set<String>> getProductCodesByTenant() {
        return salesPackageService.getSalesAddOnsByProductCodes(PMSProductName.CLEANSE);
    }

    @Override
    public CleanseProductConfig getProductConfig(Set<SalesConfig> salesConfigs, Set<String> tenantCodes, String currentTenantCode) {
        if (salesConfigs.isEmpty()) {
            return null;
        }

        CleanseProductConfig cleanseProductConfig = new CleanseProductConfig();
        cleanseProductConfig.setPmsProductName(getProductName());
        cleanseProductConfig.addAllSalesConfigs(salesConfigs);
        Set<CleanseProcessType> processTypes = new HashSet<>();
        Set<String> regions = new HashSet<>();

        Map<String, List<String>> productToRegionsMapping = configService.getCleanseRegions(cleanseMappingName).getcleanseRegions();

        for (SalesConfig salesConfig : salesConfigs) {
            addProcessTypes(CASS_PRODUCT_CODE, CleanseProcessType.CASS, salesConfig, processTypes);
            addProcessTypes(SERP_PRODUCT_CODE, CleanseProcessType.SERP, salesConfig, processTypes);
            addProcessTypes(RGEO_PRODUCT_CODE, CleanseProcessType.RGEO, salesConfig, processTypes);
            addRegions(salesConfig, regions, productToRegionsMapping);

            if (ALL_REGIONS_PRODUCT_CODE.equals(salesConfig.getProductCode())) {
                cleanseProductConfig.setAllowAllRegions(true);
            }
        }

        cleanseProductConfig.setRegions(regions);
        cleanseProductConfig.setProcessTypes(processTypes);

        return cleanseProductConfig;
    }


    private void addProcessTypes(String productCode, CleanseProcessType processType, SalesConfig salesConfig, Set<CleanseProcessType> processTypes) {
        if (salesConfig.getProductCode().equals(productCode)) {
            processTypes.add(processType);
        }
    }

    private void addRegions(SalesConfig salesConfig, Set<String> regions, Map<String, List<String>> productToRegionsMapping) {
        List<String> productRegions = productToRegionsMapping.get(salesConfig.getProductCode());
        if (productRegions != null) {
            regions.addAll(productRegions);
        }
    }

    public String getCleanseMappingName() {
        return cleanseMappingName;
    }
}
