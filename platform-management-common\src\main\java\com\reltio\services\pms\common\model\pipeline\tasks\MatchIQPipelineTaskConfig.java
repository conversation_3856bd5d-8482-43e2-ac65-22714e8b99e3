package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;

public class MatchIQPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonCreator
    public MatchIQPipelineTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.MATCH_IQ_TASK);
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.MATCHIQ;
    }

    @Override
    public boolean visibleInContract() {
        return true;
    }

}
