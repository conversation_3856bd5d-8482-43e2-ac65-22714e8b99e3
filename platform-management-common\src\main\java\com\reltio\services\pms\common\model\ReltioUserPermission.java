package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class ReltioUserPermission {
    @JsonProperty("roles")
    private Map<String, Set<String>> roles;
    @JsonProperty("permissions")
    private Map<String, Map<String, Set<String>>> permissions = new HashMap<>();

    @JsonProperty("permissionsByService")
    private Map<String, Map<String, Map<String, Set<String>>>> permissionsByService = new HashMap<>();


    @JsonCreator
    public ReltioUserPermission(@JsonProperty("roles") Map<String, Set<String>> roles,
                                @JsonProperty("permissions") Map<String, Map<String, Set<String>>> permissions,
                                @JsonProperty("permissionsByService") Map<String, Map<String, Map<String, Set<String>>>> permissionsByService) {
        this.roles = roles;
        this.permissions = permissions;
        this.permissionsByService = permissionsByService;
    }

    public Map<String, Set<String>> getRoles() {
        return roles;
    }

    public Map<String, Map<String, Set<String>>> getPermissions() {
        return permissions;
    }

    public Map<String, Map<String, Map<String, Set<String>>>> getPermissionsByService() {
        return permissionsByService;
    }
}
