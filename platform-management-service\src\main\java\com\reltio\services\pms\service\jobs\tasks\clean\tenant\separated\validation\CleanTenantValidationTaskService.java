package com.reltio.services.pms.service.jobs.tasks.clean.tenant.separated.validation;

import com.reltio.common.config.TenantConfiguration;
import com.reltio.services.pms.clients.reltio.mdm.MDMClient;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.separated.validation.CleanTenantValidationTaskInstance;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.AbstractTaskExecutionService;
import com.reltio.services.pms.validator.ResourcesValidator;
import org.apache.log4j.Logger;
import org.springframework.http.HttpStatus;

public class CleanTenantValidationTaskService extends AbstractTaskExecutionService<CleanTenantValidationTaskInstance> {
    private static final Logger LOG = Logger.getLogger(CleanTenantValidationTaskService.class);
    private static final String RESOURCE_VALIDATION_COMPLETED = "Resource validation completed.";
    private static final String RETRIEVED_TENANT_PHYSICAL_CONFIG = "Retrieving tenant physical config completed.";
    private static final String CHECKING_TENANT_ISOLATION_COMPLETED = "Checking tenant isolation completed.";
    private final MDMClient mdmClient;
    private final ResourcesValidator resourcesValidator;

    protected CleanTenantValidationTaskService(String envId,
                                               CleanTenantValidationTaskInstance taskDetail,
                                               GrafanaDashboardGBQService grafanaDashboardGBQService,
                                               MDMClient mdmClient,
                                               ResourcesValidator resourcesValidator) {
        super(envId, taskDetail, grafanaDashboardGBQService);
        this.mdmClient = mdmClient;
        this.resourcesValidator = resourcesValidator;
    }

    @Override
    public void executeTask() {
        LOG.info(String.format("Delete tenant data jobId: %s", taskDetail.getJobId()));

        TenantConfiguration tenantConfiguration = mdmClient.getTenantConfiguration(taskDetail.getEnvId(), taskDetail.getTenantName());
        addEventToTask(RETRIEVED_TENANT_PHYSICAL_CONFIG);
        validateTenant(tenantConfiguration);
        addEventToTask(CHECKING_TENANT_ISOLATION_COMPLETED);
        resourcesValidator.validateResources(taskDetail.getEnvId(), taskDetail.getTenantName());
        addEventToTask(RESOURCE_VALIDATION_COMPLETED);
        taskDetail.setStatus(TaskStatus.COMPLETED);
    }

    private void validateTenant(TenantConfiguration tenantConfiguration) {
        if (!tenantConfiguration.getDataStorageConfig().getIsolated()) {
            throw new PlatformManagementException(PlatformManagementErrorCode.TENANT_NOT_ISOLATED, HttpStatus.BAD_REQUEST.value(), taskDetail.getTenantName());
        }
    }

    protected void addEventToTask(String event) {
        taskDetail.addEvent(event);
    }

}

