package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.jobs.tasks.shield.ShieldNotificationStatus;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ShieldStorageTemplate extends BaseFirestoreEntity {

    @JsonProperty("domain")
    private String domain;

    @JsonProperty("domainId")
    private String domainId;


    @JsonProperty("taskId")
    private String taskId;

    @JsonProperty("shieldNotificationStatus")
    private ShieldNotificationStatus shieldNotificationStatus;

    @JsonProperty("shieldKey")
    private String shieldKey;

    @JsonProperty("shieldPolicy")
    private String shieldPolicy;


    @JsonCreator
    public ShieldStorageTemplate(@JsonProperty("domain") String domain,
                                 @JsonProperty("domainId") String domainId,
                                 @JsonProperty("taskId") String taskId,
                                 @JsonProperty("shieldNotificationStatus") ShieldNotificationStatus shieldNotificationStatus,
                                 @JsonProperty("shieldKey") String shieldKey,
                                 @JsonProperty("shieldPolicy") String shieldPolicy) {
        this.domain = domain;
        this.domainId = domainId;
        this.taskId = taskId;
        this.shieldNotificationStatus = shieldNotificationStatus;
        this.shieldKey = shieldKey;
        this.shieldPolicy = shieldPolicy;

    }

    @Override
    public String getID() {
        return taskId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getDomainId() {
        return domainId;
    }

    public void setDomainId(String domainId) {
        this.domainId = domainId;
    }

    public String getTaskId() {
        return taskId;
    }

    public ShieldNotificationStatus getShieldNotificationStatus() {
        return shieldNotificationStatus;
    }

    public void setShieldNotificationStatus(ShieldNotificationStatus shieldNotificationStatus) {
        this.shieldNotificationStatus = shieldNotificationStatus;
    }

    public String getShieldKey() {
        return shieldKey;
    }

    public void setShieldKey(String shieldKey) {
        this.shieldKey = shieldKey;
    }

    public void setShieldPolicy(String shieldPolicy) {
        this.shieldPolicy = shieldPolicy;
    }
}
