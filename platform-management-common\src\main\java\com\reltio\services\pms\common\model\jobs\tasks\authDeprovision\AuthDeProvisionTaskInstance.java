package com.reltio.services.pms.common.model.jobs.tasks.authDeprovision;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public class AuthDeProvisionTaskInstance extends TaskInstance {

    @JsonProperty(value = "tenantId")
    private final String tenantId;

    @JsonProperty(value = "events")
    private final List<String> events;

    @JsonProperty("deleteAssociatedUsers")
    private boolean deleteAssociatedUsers;

    @JsonProperty("removeAssociatedRdms")
    private boolean removeAssociatedRdms;

    @JsonProperty("removeGroupAssignment")
    private boolean removeGroupAssignment;

    @JsonProperty("authCustomerId")
    private String authCustomerId;

    @JsonProperty("fullCustomerDeprovision")
    private boolean fullCustomerDeprovision;

    @JsonProperty("usersToDelete")
    private List<String> usersToDelete;

    @JsonProperty("failOnError")
    private boolean failOnError;

    @JsonProperty("protectedUsers")
    private List<String> protectedUsers;

    @JsonProperty("protectedCustomers")
    private List<String> protectedCustomers;

    @JsonProperty("deletedUsers")
    private List<String> deletedUsers;

    @JsonProperty("deprovisionedUsers")
    private List<String> deprovisionedUsers;

    @JsonProperty("deprovisionedCustomers")
    private List<String> deprovisionedCustomers;

    @JsonProperty("deletedClients")
    private List<String> deletedClients;

    @JsonProperty("deprovisionedGroups")
    private List<String> deprovisionedGroups;

    @JsonProperty("skippedUsers")
    private List<String> skippedUsers;

    @JsonCreator
    public AuthDeProvisionTaskInstance(@JsonProperty(value = "taskId") String taskId,
                                       @JsonProperty(value = "name") String name,
                                       @JsonProperty(value = "jobId") String jobId,
                                       @JsonProperty(value = "startTime") Long startTime,
                                       @JsonProperty(value = "finishTime") Long finishTime,
                                       @JsonProperty(value = "status") TaskStatus status,
                                       @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                       @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                       @JsonProperty(value = "executingNodeName") String executingNodeName,
                                       @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                       @JsonProperty(value = "envId") String envId,
                                       @JsonProperty(value = "tenantId") String tenantId,
                                       @JsonProperty(value = "events") List<String> events,
                                       @JsonProperty("deleteAssociatedUsers") Boolean deleteAssociatedUsers,
                                       @JsonProperty("removeAssociatedRdms") Boolean removeAssociatedRdms,
                                       @JsonProperty("removeGroupAssignment") Boolean removeGroupAssignment,
                                       @JsonProperty("authCustomerId") String authCustomerId,
                                       @JsonProperty("fullCustomerDeprovision") Boolean fullCustomerDeprovision,
                                       @JsonProperty("usersToDelete") List<String> usersToDelete,
                                       @JsonProperty("failOnError") Boolean failOnError,
                                       @JsonProperty("protectedUsers") List<String> protectedUsers,
                                       @JsonProperty("protectedCustomers") List<String> protectedCustomers) {

        super(taskId, name, jobId, startTime, finishTime, TaskType.AUTH_DE_PROVISION_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.tenantId = tenantId;
        this.events = events == null ? new ArrayList<>() : new ArrayList<>(events);
        this.deleteAssociatedUsers = deleteAssociatedUsers != null && deleteAssociatedUsers;
        this.removeAssociatedRdms = removeAssociatedRdms != null && removeAssociatedRdms;
        this.removeGroupAssignment = removeGroupAssignment != null && removeGroupAssignment;
        this.authCustomerId = authCustomerId;
        this.fullCustomerDeprovision = fullCustomerDeprovision != null && fullCustomerDeprovision;
        this.usersToDelete = usersToDelete == null ? new ArrayList<>() : new ArrayList<>(usersToDelete);
        this.failOnError = failOnError == null || failOnError;
        this.protectedUsers = protectedUsers == null ? new ArrayList<>() : new ArrayList<>(protectedUsers);
        this.protectedCustomers = protectedCustomers == null ? new ArrayList<>() : new ArrayList<>(protectedCustomers);
        this.deletedUsers= deletedUsers == null ? new ArrayList<>() : new ArrayList<>(deletedUsers);
        this.deprovisionedUsers = deprovisionedUsers == null ? new ArrayList<>() : new ArrayList<>(deprovisionedUsers);
        this.deprovisionedCustomers = deprovisionedCustomers == null ? new ArrayList<>() : new ArrayList<>(deprovisionedCustomers);
        this.deletedClients = deletedClients == null ? new ArrayList<>() : new ArrayList<>(deletedClients);
        this.deprovisionedGroups = new ArrayList<>();
        this.skippedUsers = new ArrayList<>();
    }

    public void addDeletedUsers(String user) {
        this.deletedUsers.add(user);
    }

    public void addDeprovisionedUsers(String user) {
        this.deprovisionedUsers.add(user);
    }

    public void addDeprovisionedCustomer(String customer) {
        this.deprovisionedCustomers.add(customer);
    }

    public void addDeletedClients(String clientId) {
        this.deletedClients.add(clientId);
    }

    public void addDeprovisionedGroup(String groupId) {
        this.deprovisionedGroups.add(groupId);
    }

    public void addSkippedUser(String user) {
        this.skippedUsers.add(user);
    }

    public void addEvent(String event) {
        events.add(event);
    }
}
