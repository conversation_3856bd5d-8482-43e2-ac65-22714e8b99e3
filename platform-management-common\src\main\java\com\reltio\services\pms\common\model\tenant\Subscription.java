package com.reltio.services.pms.common.model.tenant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import com.reltio.services.pms.common.model.OfferType;
import com.reltio.services.pms.common.model.TenantRequest;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Subscription extends BaseFirestoreEntity {

    @JsonProperty("tenantId")
    private String tenantId;

    @JsonProperty("offerType")
    private String offerType;

    @JsonProperty("offerFunction")
    private OfferFunction offerFunction;

    @JsonProperty("envId")
    private String envId;

    @JsonProperty("termsAccepted")
    private Boolean termsAccepted;

    @JsonProperty("termsFilePath")
    private String termsFilePath;

    @JsonProperty("customerId")
    private String customerId;

    @JsonProperty("jobId")
    private String jobId;

    private TenantPurpose tenantPurpose;

    @JsonProperty("reltioPackageType")
    private ReltioPackageType reltioPackageType;

    @JsonCreator
    public Subscription(@JsonProperty("tenantId") String tenantId,
                        @JsonProperty("offerType") String offerType,
                        @JsonProperty("offerFunction") OfferFunction offerFunction,
                        @JsonProperty("envId") String envId,
                        @JsonProperty("termsAccepted") Boolean termsAccepted,
                        @JsonProperty("termsFilePath") String termsFilePath,
                        @JsonProperty("customerId") String customerId,
                        @JsonProperty("jobId") String jobId,
                        @JsonProperty("tenantPurpose") TenantPurpose tenantPurpose,
                        @JsonProperty("reltioPackageType")ReltioPackageType reltioPackageType){
        this.tenantId = tenantId;
        this.offerType = offerType;
        this.offerFunction = offerFunction;
        this.envId = envId;
        this.termsAccepted = termsAccepted;
        this.termsFilePath = termsFilePath;
        this.customerId = customerId;
        this.jobId = jobId;
        this.tenantPurpose = tenantPurpose == null ? TenantPurpose.FREE : tenantPurpose;
        this.reltioPackageType=reltioPackageType == null ? ReltioPackageType.MDM : reltioPackageType;
    }

    public Subscription(TenantRequest tenantRequest) {
        this.offerType = tenantRequest.getOfferType();
        this.offerFunction = tenantRequest.getOfferFunction();
        this.envId = tenantRequest.getEnvId();
        this.termsAccepted = tenantRequest.getTermsAccepted();
        this.termsFilePath = tenantRequest.getTermsFilePath();
        this.tenantPurpose = tenantRequest.getPurpose();
        this.reltioPackageType=tenantRequest.getReltioPackageType();
    }

    @Override
    public String getID() {
        return String.format("%s-%s",offerType, tenantPurpose.name().toLowerCase());
    }

}
