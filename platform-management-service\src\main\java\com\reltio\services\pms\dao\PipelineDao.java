package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.pipeline.Pipeline;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PipelineDao extends AbstractLevel1CollectionDao<Pipeline> {
    private static final String PIPELINE_COLLECTION_NAME = "PIPELINES";

    @Autowired
    public PipelineDao(CredentialsProvider provider, EnvironmentDao environmentDao, ReltioUserHolder reltioUserHolder) {
        super(provider, environmentDao, PIPELINE_COLLECTION_NAME, reltioUserHolder);
    }

    @Override
    protected TypeReference<Pipeline> getTypeReference() {
        return new TypeReference<Pipeline>() {
        };
    }
}
