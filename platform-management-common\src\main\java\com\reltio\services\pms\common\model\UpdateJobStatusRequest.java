package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.jobs.JobStatus;

import java.util.Objects;

public class UpdateJobStatusRequest {

    @JsonProperty("jobId")
    private final String jobId;

    @JsonProperty("status")
    private final JobStatus status;

    @JsonCreator
    public UpdateJobStatusRequest(
            @JsonProperty(value = "jobId", required = true) String jobId,
            @JsonProperty(value = "status", required = true) JobStatus status) {
        this.jobId = jobId;
        this.status = status;
    }

    public String getJobId() {
        return jobId;
    }

    public JobStatus getStatus() {
        return status;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof UpdateJobStatusRequest)) {
            return false;
        }
        UpdateJobStatusRequest that = (UpdateJobStatusRequest) o;
        return Objects.equals(getJobId(), that.getJobId()) &&
                getStatus() == that.getStatus();
    }

    @Override
    public int hashCode() {
        return Objects.hash(getJobId(), getStatus());
    }

    @Override
    public String toString() {
        return "UpdateJobStatusRequest{" +
                "jobId='" + jobId + '\'' +
                ", status=" + status +
                '}';
    }
}