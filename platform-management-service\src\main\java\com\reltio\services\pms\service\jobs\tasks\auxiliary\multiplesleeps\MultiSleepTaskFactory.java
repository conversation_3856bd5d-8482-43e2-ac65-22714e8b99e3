package com.reltio.services.pms.service.jobs.tasks.auxiliary.multiplesleeps;

import com.reltio.services.pms.common.model.jobs.tasks.MultiSleepTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.TaskFactory;
import org.springframework.stereotype.Service;

@Service
public class MultiSleepTaskFactory implements TaskFactory<MultiSleepTaskInstance, MultiSleepTaskExecutionService> {
    @Override
    public TaskType getTaskType() {
        return TaskType.MULTI_SLEEP_TASK;
    }

    @Override
    public MultiSleepTaskExecutionService createTask(String jobId, MultiSleepTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService) {
        return new MultiSleepTaskExecutionService(jobId, taskDetail, grafanaDashboardGBQService);
    }
}
