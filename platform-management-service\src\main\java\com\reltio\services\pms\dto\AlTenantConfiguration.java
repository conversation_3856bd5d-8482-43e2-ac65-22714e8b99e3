package com.reltio.services.pms.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * Activity Log Tenant configuration
 */
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlTenantConfiguration {

    @Setter
    @JsonProperty("tenantId")
    private String tenantId;

    private ElasticsearchConfiguration elasticsearch;

    private CassandraConfiguration cassandra;

    private GBQConfiguration bigQuery;

    /**
     * Activity Log Tenant configuration constructor
     *
     * @param tenantId      - tenant id
     * @param elasticsearch - ElasticSearch details
     * @param cassandra     - Cassandra details
     * @param bigQuery      - GBQ details
     */
    @JsonCreator
    public AlTenantConfiguration(@JsonProperty(value = "tenantId", required = true) String tenantId,
                                 @JsonProperty(value = "elasticsearch", required = true) ElasticsearchConfiguration elasticsearch,
                                 @JsonProperty(value = "cassandra", required = true) CassandraConfiguration cassandra,
                                 @JsonProperty(value = "bigQuery") GBQConfiguration bigQuery) {
        this.cassandra = cassandra;
        this.tenantId = tenantId;
        this.elasticsearch = elasticsearch;
        this.bigQuery = bigQuery;
    }
}
