package com.reltio.services.pms.common.model.supportability.result.combiner;

import com.reltio.services.pms.common.model.supportability.EventStatsDto;
import com.reltio.services.pms.common.model.supportability.Processors;
import com.reltio.services.pms.common.model.supportability.result.StatsCombiner;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * The type Event stats combiner.
 */
public class EventStatsCombiner implements StatsCombiner<EventStatsDto> {
    /**
     * mergeAllRestRows method is used to merge all the provided rows from GBQ into single record by sum/avg based on the column in GBQ.
     *
     * @param dtoList the dto list
     * @return the list
     */
    @Override
    public EventStatsDto mergeAllRows(List<EventStatsDto> dtoList) {
        EventStatsDto response = dtoList.get(NumberUtils.INTEGER_ZERO);
        EventStatsDto lastResponse = dtoList.get(dtoList.size() - NumberUtils.INTEGER_ONE);
        long startTime = response.getStartTime();
        long endTime = lastResponse.getEndTime();
        int totalRows = dtoList.size();
        long totalCount = NumberUtils.INTEGER_ZERO;
        long failedCount = NumberUtils.INTEGER_ZERO;
        long pendingCount = NumberUtils.INTEGER_ZERO;
        long successfulCount = NumberUtils.INTEGER_ZERO;
        long p50Sum = NumberUtils.INTEGER_ZERO;
        long p90Sum = NumberUtils.INTEGER_ZERO;
        long p95Sum = NumberUtils.INTEGER_ZERO;
        long p99Sum = NumberUtils.INTEGER_ZERO;
        Set<Processors> byCount = new HashSet<>();
        Set<Processors> byLatency = new HashSet<>();
        for (EventStatsDto dto : dtoList) {
            byCount.addAll(dto.getProcessorsByCount());
            byLatency.addAll(dto.getProcessorsByLatency());
            totalCount += dto.getTotalCount();
            failedCount += dto.getFailedCount();
            pendingCount += dto.getPendingCount();
            successfulCount += dto.getSuccessfulCount();
            p50Sum += dto.getP50();
            p90Sum += dto.getP90();
            p95Sum += dto.getP95();
            p99Sum += dto.getP99();
        }
        long p50Avg = p50Sum / totalRows;
        long p90Avg = p90Sum / totalRows;
        long p95Avg = p95Sum / totalRows;
        long p99Avg = p99Sum / totalRows;
        EventStatsDto dto = new EventStatsDto();
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setTotalCount(totalCount);
        dto.setFailedCount(failedCount);
        dto.setPendingCount(pendingCount);
        dto.setSuccessfulCount(successfulCount);
        dto.setConsumerType(response.getConsumerType());
        dto.setP50(p50Avg);
        dto.setP90(p90Avg);
        dto.setP95(p95Avg);
        dto.setP99(p99Avg);
        List<Processors> distinctByCount = byCount.stream().filter(filter -> Objects.nonNull(filter.getFailedCount()))
                .collect(Collectors.collectingAndThen(Collectors.toMap(Processors::getFailedCount, Function.identity(), (r1, r2) -> r1.getFailedCount() >= r2.getFailedCount() ? r1 : r2), map -> new ArrayList<>(map.values())));
        List<Processors> distinctByLatency = byLatency.stream().filter(filter -> Objects.nonNull(filter.getAvg()))
                .collect(Collectors.collectingAndThen(Collectors.toMap(Processors::getAvg, Function.identity(), (r1, r2) -> r1.getAvg() >= r2.getAvg() ? r1 : r2), map -> new ArrayList<>(map.values())));
        dto.setProcessorsByCount(distinctByCount.stream().sorted(Comparator.comparingLong(Processors::getFailedCount).reversed())
                .limit(5).collect(Collectors.toList()));
        dto.setProcessorsByLatency(distinctByLatency.stream().sorted(Comparator.comparingLong(Processors::getAvg).reversed())
                .limit(5).collect(Collectors.toList()));
        return dto;
    }
}
