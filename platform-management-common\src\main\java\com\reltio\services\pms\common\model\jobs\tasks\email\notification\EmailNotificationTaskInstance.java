package com.reltio.services.pms.common.model.jobs.tasks.email.notification;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServiceEnablementBaseTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;

import java.util.Map;
import java.util.Set;

@Getter
public class EmailNotificationTaskInstance extends ServiceEnablementBaseTaskInstance {

    private final String subject;

    private final String body;

    private final Set<String> additionalRecipients;

    @JsonCreator
    public EmailNotificationTaskInstance(@JsonProperty(value = "id") String id,
                                         @JsonProperty(value = "name") String name,
                                         @JsonProperty(value = "jobId") String jobId,
                                         @JsonProperty(value = "startTime") Long startTime,
                                         @JsonProperty(value = "finishTime") Long finishTime,
                                         @JsonProperty(value = "status") TaskStatus status,
                                         @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                         @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                         @JsonProperty(value = "executingNodeName") String executingNodeName,
                                         @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                         @JsonProperty(value = "envId") String envId,
                                         @JsonProperty(value = "failedTenants") Set<String> failedTenants,
                                         @JsonProperty(value = "totalProcessedTenants", defaultValue = "0") int totalProcessedTenants,
                                         @JsonProperty(value = "tenantsForServiceEnablement") Set<String> tenantsForServiceEnablement,
                                         @JsonProperty(value = "events") Map<String, Set<String>> events,
                                         @JsonProperty(value = "subject") String subject,
                                         @JsonProperty(value = "body") String body,
                                         @JsonProperty(value = "additionalRecipients") Set<String> additionalRecipients) {
        super(id, name, jobId, startTime, finishTime, TaskType.EMAIL_NOTIFICATION_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId, failedTenants, totalProcessedTenants,
                tenantsForServiceEnablement, events);
        this.subject = subject;
        this.body = body;
        this.additionalRecipients = additionalRecipients;
    }
}
