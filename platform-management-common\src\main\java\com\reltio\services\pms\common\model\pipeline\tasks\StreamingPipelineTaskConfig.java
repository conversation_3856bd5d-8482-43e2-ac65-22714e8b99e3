package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.config.DestinationConfig;
import com.reltio.services.pms.common.sales.PMSProductName;

import java.util.Objects;

public class StreamingPipelineTaskConfig extends AbstractPipelineTaskConfig {
    @JsonProperty(value = "gcpProjectName")
    private final String gcpProjectName;

    @JsonProperty(value = "createQueueIfExisting")
    private final boolean createQueueIfExisting;

    @JsonProperty(value = "destinationConfig")
    private final DestinationConfig destinationConfig;

    @JsonProperty(value = "enabledByDefault")
    private final boolean enabledByDefault;

    @JsonCreator
    public StreamingPipelineTaskConfig(@JsonProperty(value = "name") String name,
                                       @JsonProperty(value = "gcpProjectName") String gcpProjectName,
                                       @JsonProperty(value = "createQueueIfExisting", defaultValue = "false") boolean createQueueIfExisting, @JsonProperty(value = "destinationConfig") DestinationConfig destinationConfig,@JsonProperty(value = "enabledByDefault") boolean enabledByDefault) {
        super(name, TaskType.STREAMING_TASK);
        this.gcpProjectName = gcpProjectName;
        this.createQueueIfExisting = createQueueIfExisting;
        this.destinationConfig = destinationConfig;
        this.enabledByDefault = enabledByDefault;
    }

    public boolean isEnabledByDefault() {
        return enabledByDefault;
    }

    public String getGcpProjectName() {
        return gcpProjectName;
    }

    public boolean isCreateQueueIfExisting() {
        return createQueueIfExisting;
    }

    @Override
    public String toString() {
        return "StreamingPipelineTaskConfig{}";
    }

    public DestinationConfig getDestinationConfig() {
        return destinationConfig;
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.QUEUES;
    }
    @Override
    public boolean visibleInContract() {
        return true;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof StreamingPipelineTaskConfig)) return false;
        if (!super.equals(o)) return false;
        StreamingPipelineTaskConfig that = (StreamingPipelineTaskConfig) o;
        return isEnabledByDefault() == that.isEnabledByDefault() &&  isCreateQueueIfExisting() == that.isCreateQueueIfExisting() && getGcpProjectName().equals(that.getGcpProjectName()) && getDestinationConfig().equals(that.getDestinationConfig() );
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getGcpProjectName(), isCreateQueueIfExisting(), getDestinationConfig(),isEnabledByDefault());
    }

}
