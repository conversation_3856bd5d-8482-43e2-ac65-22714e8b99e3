package com.reltio.services.pms.common.model.jobs.tasks.auth;

import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServicesEnablementBaseTaskInstanceParams;

import java.util.Set;

public interface AuthTaskInstanceParams extends ServicesEnablementBaseTaskInstanceParams {

    Set<String> getEmailsToAuthorizeAccess();

    String getTenantId();

    String getRdmTenantId();

    String getCustomerId();

    Set<String> getClients();

    Boolean reuseRdmTenant();
}
