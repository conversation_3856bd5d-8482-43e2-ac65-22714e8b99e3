package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class DeleteDynamoDbBackupConfig extends AbstractPipelineTaskConfig{

    @JsonCreator
    public DeleteDynamoDbBackupConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.DELETE_DYNAMO_DB_BACKUP_TASK);
    }
}
