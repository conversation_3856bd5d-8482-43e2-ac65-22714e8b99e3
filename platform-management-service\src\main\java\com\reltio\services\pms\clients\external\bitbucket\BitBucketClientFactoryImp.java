package com.reltio.services.pms.clients.external.bitbucket;

import org.springframework.stereotype.Service;

@Service
public class BitBucketClientFactoryImp implements BitBucketClientFactory {

    @Override
    public BitBucketClient createClient(String userName, String password) {
        BasicAuthRestTemplate restTemplate = new BasicAuthRestTemplate(userName, password);
        return new BitBucketClient(restTemplate);
    }
}
