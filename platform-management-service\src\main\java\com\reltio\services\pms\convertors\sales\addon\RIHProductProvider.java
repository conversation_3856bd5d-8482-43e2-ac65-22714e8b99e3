package com.reltio.services.pms.convertors.sales.addon;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.convertors.sales.AbstractTenantAddOnProductProvider;
import com.reltio.services.pms.service.SalesPackageService;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

@Service
public class RIHProductProvider extends AbstractTenantAddOnProductProvider<BaseProductConfig> {



    public RIHProductProvider(SalesPackageService salesPackageService) {
        super(salesPackageService);
    }


    @Override
    public PMSProductName getProductName() {
        return PMSProductName.RIH;
    }

    @Override
    public Map<String, Set<String>> getProductCodesByTenant() {
        return salesPackageService.getSalesAddOnsByProductCodes(PMSProductName.RIH);
    }

    @Override
    public BaseProductConfig getProductConfig(Set<SalesConfig> salesConfigs, Set<String> tenantCodes, String currentTenantCode) {
        if (salesConfigs.isEmpty()) {
            return null;
        }

        BaseProductConfig rihProductConfig = new BaseProductConfig();
        rihProductConfig.setPmsProductName(getProductName());
        rihProductConfig.addAllSalesConfigs(salesConfigs);
        rihProductConfig.setQuantity(getQuantity(rihProductConfig));
        return rihProductConfig;
    }

}
