package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

public class MultiSleepPipelineTaskConfig extends AbstractPipelineTaskConfig {
    private final Long sleepTime;
    private final Long noOfSleep;

    @JsonCreator
    protected MultiSleepPipelineTaskConfig(
            @JsonProperty(value = "name") String name,
            @JsonProperty(value = "type") TaskType type,
            @JsonProperty(value = "sleepTime") Long sleepTime,
            @JsonProperty(value = "noOfSleep") Long noOfSleep) {
        super(name, TaskType.MULTI_SLEEP_TASK);
        this.sleepTime = sleepTime;
        this.noOfSleep = noOfSleep;
    }


    public Long getSleepTime() {
        return sleepTime;
    }

    public Long getNoOfSleep() {
        return noOfSleep;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MultiSleepPipelineTaskConfig)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        MultiSleepPipelineTaskConfig that = (MultiSleepPipelineTaskConfig) o;
        return Objects.equals(getSleepTime(), that.getSleepTime()) &&
                Objects.equals(getNoOfSleep(), that.getNoOfSleep());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getSleepTime(), getNoOfSleep());
    }

    @Override
    public String toString() {
        return "MultiSleepPipelineTaskConfig{" +
                "sleepTime=" + sleepTime +
                ", noOfSleep=" + noOfSleep +
                "} " + super.toString();
    }
}
