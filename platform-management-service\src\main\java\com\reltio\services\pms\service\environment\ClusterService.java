package com.reltio.services.pms.service.environment;

import com.google.common.collect.Sets;
import com.reltio.devops.common.environment.model.ServiceModel;
import com.reltio.devops.common.environment.service.EnvironmentFactory;
import com.reltio.devops.common.environment.service.EnvironmentModelLoaderService;
import com.reltio.devops.common.environment.service.EnvironmentService;
import com.reltio.devops.common.environment.service.EnvironmentServiceImpl;
import com.reltio.devops.common.environment.service.K8SServicesLoader;
import com.reltio.devops.common.git.JGitService;
import com.reltio.devops.common.mail.MailService;
import com.reltio.devops.http.ServiceFactory;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.Cluster;
import com.reltio.services.pms.dao.ClusterDao;
import com.reltio.services.pms.dao.EnvironmentDao;
import org.apache.commons.configuration.Configuration;
import org.apache.log4j.Logger;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.reltio.services.pms.constant.GitConstant.DEFAULT_MASTER_BRANCH_NAME;
import static com.reltio.services.pms.constant.GitConstant.GIT_BRANCH;
import static com.reltio.services.pms.constant.GitConstant.GIT_K8S_BRANCH;
import static com.reltio.services.pms.constant.GitConstant.GIT_K8S_CONFIG_REPO;
import static com.reltio.services.pms.constant.GitConstant.GIT_K8S_REPO;
import static com.reltio.services.pms.constant.GitConstant.GIT_REPO;

@Service
public class ClusterService {
    private static final Logger LOG = Logger.getLogger(ClusterService.class);
    private final ClusterDao clusterDao;
    private final EnvironmentDao environmentDao;
    private EnvironmentService environmentService;
    private final Configuration configuration;
    private final ServiceFactory serviceFactory;

    @Autowired
    public ClusterService(ClusterDao clusterDao, EnvironmentDao environmentDao,
                          EnvironmentService environmentService,
                          Configuration configuration,
                          ServiceFactory serviceFactory) {
        this.clusterDao = clusterDao;
        this.environmentDao = environmentDao;
        this.environmentService = environmentService;
        this.configuration = configuration;
        this.serviceFactory = serviceFactory;
    }

    public List<Cluster> updateEnvironmentClusters(String env) {
        loadEnvironments();
        List<ServiceModel> clusters = environmentService.getEnvironmentByName(env).getEnvironmentModel().getServices();
        return updateCluster(env, clusters);
    }

    public String getCloud(String env) {
        return environmentService.getEnvironmentByName(env).getCloudProvider().toString().toLowerCase(Locale.ROOT);
    }

    protected List<Cluster> updateCluster(String env, List<ServiceModel> services) {
        List<Cluster> allClusters = new ArrayList<>();
        for (ServiceModel service : services) {
            Optional<Cluster> c = getSafe(new Cluster(service, env).getID());
            Cluster result;
            if (c.isPresent()) {
                result = c.get();
                result.merge(service, env);
            } else {
                result = new Cluster(service, env);
            }
            allClusters.add(result.toLowerCase());
            clusterDao.update(result.toLowerCase());
        }
        return allClusters;
    }

    public Cluster updateCluster(Cluster cluster) {
        for (String environment : cluster.getEnvironments()) {
            if (!environmentDao.isExists(environment)) {
                LOG.warn(String.format("Environment %s is not registered", environment));
            }
        }

        try {
            Cluster existingCluster = clusterDao.get(cluster.getID());
            Set<String> envs = existingCluster.getEnvironments();
            envs.addAll(cluster.getEnvironments());
            cluster.setEnvironments(envs);
        } catch (InvalidDocumentIdException e) {
            LOG.info("Haven't found such cluster, so creating a brand new one: " + cluster.getID());
        }
        clusterDao.update(cluster);
        return cluster;
    }

    public Cluster getCluster(String name, String cloud, String region) {
        String id = (region != null && !"unknown".equals(region)) ? name.concat("@").concat(cloud).concat("-").concat(region).toLowerCase() : name.concat("@").concat(cloud).toLowerCase();
        try {
            return clusterDao.get(id);
        } catch (InvalidDocumentIdException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CLUSTER_NOT_FOUND, HttpStatus.BAD_REQUEST.value(), id);
        }
    }


    @Cacheable(value = "clusters", unless = "#id==\"general@aws\"")
    public Cluster get(String id) {
        try {
            return clusterDao.get(id);
        } catch (InvalidDocumentIdException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CLUSTER_NOT_FOUND, HttpStatus.BAD_REQUEST.value(), id);
        }
    }

    public Optional<Cluster> getSafe(String id) {
        try {
            return Optional.of(clusterDao.get(id));
        } catch (InvalidDocumentIdException e) {
            return Optional.empty();
        }
    }

    public Collection<Cluster> getClusterWithFilter(String property, String value) {
        return clusterDao.getClustersWithFilter(property, value);
    }

    public List<String> getCassandraNodesByClusterNameAndEnv(String clusterName, String envName) {
        return clusterDao.getClustersWithFilter("name", clusterName).stream()
                .filter(cluster -> cluster.getEnvironments().contains(envName) && cluster.getType().equals("cassandra"))
                .findFirst().orElseThrow(() -> new IllegalArgumentException(String.format("Can not find cluster %s", clusterName))).getUrlList();
    }

    public Collection<Cluster> getAllClusters() {
        return clusterDao.getAll();
    }

    public void deleteClusterFromEnv(String envName, String id) {
        try {
            Cluster cluster = clusterDao.get(id);
            if (cluster.getEnvironments().size() == 1 && cluster.getEnvironments().iterator().next().equals(envName)) {
                clusterDao.delete(id);
            } else {
                clusterDao.deleteEnvironment(id, envName);
            }
        } catch (InvalidDocumentIdException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CLUSTER_NOT_FOUND, HttpStatus.NOT_FOUND.value(), id);
        }
    }

    public void deleteOldClustersFromEnv(Set<String> existingIds, String envName) {
        Set<String> allIds = clusterDao.getClusterIdsWithFilter("environments", envName);
        Set<String> oldClusters = Sets.difference(allIds, existingIds);
        for (String id : oldClusters) {
            deleteClusterFromEnv(envName, id);
        }
    }

    public void validateCluster(String name, String hosts, String env) {
        Collection<Cluster> clusters = getClusterWithFilter("name", name).stream().filter(cluster -> cluster.getEnvironments().contains(env)).collect(Collectors.toSet());
        if (clusters.size() == 0) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CLUSTER_NOT_FOUND, HttpStatus.BAD_REQUEST.value());
        } else {
            Set<String> urls = new HashSet<>(clusters.iterator().next().getUrlList());
            Set<String> incomingHosts = new HashSet<>(Arrays.asList(hosts.split(";")));
            if (Sets.difference(incomingHosts, urls).size() > 0) {
                throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.BAD_REQUEST.value());
            }
        }
    }


    public boolean clusterExsists(String cluster) {
        return clusterDao.isExists(cluster);
    }

    public List<String> updateUrl(List<String> urls, String name) {
        Cluster cluster = get(name);
        cluster.setUrlList(urls);
        clusterDao.update(cluster);
        return cluster.getUrlList();

    }

    public Set<String> updateCustomers(Set<String> customers, String name) {
        Cluster cluster = get(name);
        cluster.setCustomers(customers);
        clusterDao.update(cluster);
        return cluster.getCustomers();

    }

    public Set<String> updateEnvironments(Set<String> environments, String name) {
        Cluster cluster = get(name);
        cluster.setEnvironments(environments);
        clusterDao.update(cluster);
        return cluster.getEnvironments();

    }

    public Set<String> updateTenants(Set<String> tenants, String name) {
        Cluster cluster = get(name);
        cluster.setTenants(tenants);
        clusterDao.update(cluster);
        return cluster.getTenants();

    }

    public Cluster createCluster(Cluster cluster) {
        for (String environment : cluster.getEnvironments()) {
            if (!environmentDao.isExists(environment)) {
                LOG.warn(String.format("Environment %s is not registered", environment));
            }
        }
        return clusterDao.create(cluster);

    }

    public Cluster deleteCluster(String clusterId) {
        try {
            Cluster cluster = clusterDao.get(clusterId);
            clusterDao.delete(clusterId);
            return cluster;
        } catch (InvalidDocumentIdException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CLUSTER_NOT_FOUND, HttpStatus.NOT_FOUND.value(), clusterId, e.getMessage());
        }
    }

    protected void loadEnvironments() {
        try {
            EnvironmentModelLoaderService environmentModelLoaderService = new EnvironmentModelLoaderService(
                    new JGitService(configuration, configuration.getString(GIT_REPO), configuration.getString(GIT_BRANCH, DEFAULT_MASTER_BRANCH_NAME)),
                    new K8SServicesLoader(
                            new JGitService(configuration, configuration.getString(GIT_K8S_REPO), configuration.getString(GIT_K8S_BRANCH, DEFAULT_MASTER_BRANCH_NAME)),
                            new JGitService(configuration, configuration.getString(GIT_K8S_CONFIG_REPO), configuration.getString("6", DEFAULT_MASTER_BRANCH_NAME))),
                    configuration,
                    new MailService(configuration));
            synchronized (this) {
                environmentService = new EnvironmentServiceImpl(
                        configuration,
                        environmentModelLoaderService,
                        serviceFactory,
                        new EnvironmentFactory());
                environmentService.loadEnvironmentsOnStart();
            }
        } catch (GitAPIException | IOException ex) {
            LOG.warn(String.format("Problem during loading environment -> %s", ex));
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
        }
    }
}