package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.jobs.Job;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;

import java.util.Collection;
import java.util.Objects;

public class JobWithTask extends Job {

    @JsonProperty("taskDetails")
    Collection<TaskInstance> taskInstances;


    @JsonCreator
    public JobWithTask() {

    }

    public JobWithTask(Job job, Collection<TaskInstance> taskInstances) {
        super(job);
        this.taskInstances = taskInstances;
    }


    public Collection<TaskInstance> getTaskInstances() {
        return taskInstances;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof JobWithTask)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        JobWithTask that = (JobWithTask) o;
        return Objects.equals(taskInstances, that.taskInstances);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), taskInstances);
    }

    @Override
    public String toString() {
        return "JobWithTask{" +
                "taskDetails=" + taskInstances +
                "} " + super.toString();
    }
}
