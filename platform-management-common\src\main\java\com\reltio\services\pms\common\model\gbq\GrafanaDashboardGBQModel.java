package com.reltio.services.pms.common.model.gbq;

import com.google.cloud.bigquery.Field;
import com.google.cloud.bigquery.Field.Mode;
import com.google.cloud.bigquery.Schema;
import com.google.cloud.bigquery.StandardSQLTypeName;
import lombok.Getter;

public class GrafanaDashboardGBQModel {
    @Getter
    private static final Schema schema = Schema.of(
            Field.of("jobId", StandardSQLTypeName.STRING),
            Field.of("startedTime", StandardSQLTypeName.NUMERIC),
            Field.of("endTime", StandardSQLTypeName.NUMERIC),
            Field.of("createdBy", StandardSQLTypeName.STRING),
            Field.of("status", StandardSQLTypeName.STRING),
            Field.newBuilder(
                            "taskIds", StandardSQLTypeName.STRUCT,
                            Field.of("taskId", StandardSQLTypeName.STRING),
                            Field.of("startedTime", StandardSQLTypeName.NUMERIC),
                            Field.of("endTime", StandardSQLTypeName.NUMERIC),
                            Field.of("createdBy", StandardSQLTypeName.STRING),
                            Field.of("status", StandardSQLTypeName.STRING),
                            Field.of("taskType", StandardSQLTypeName.STRING))
                    .setMode(Mode.REPEATED).build());

    private GrafanaDashboardGBQModel() {
    }
}
