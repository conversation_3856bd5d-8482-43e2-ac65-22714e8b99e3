package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.Query;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.jobs.JobIndexItem;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.Set;

@Service
public class JobIndexItemDao extends AbstractRootCollectionDao<JobIndexItem> {
    private static final String CONFIG_COLLECTION_NAME = "PMS_JOB_INDEX";
    private static final Logger LOGGER = Logger.getLogger(JobIndexItemDao.class);

    @Autowired
    public JobIndexItemDao(CredentialsProvider provider,
                           @Value("${firestore.env.name}") String deployedEnv,
                           ReltioUserHolder reltioUserHolder) {
        super(provider, CONFIG_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    protected TypeReference<JobIndexItem> getTypeReference() {
        return new TypeReference<JobIndexItem>() {
        };
    }

    public Collection<JobIndexItem> getJobsIndexItemsByContract(String contractId) {
        Query query = getBaseCollection().whereEqualTo("contractId", contractId);
        return getResultFromQuery(query);
    }

    public Collection<JobIndexItem> getJobsIndexItemsByTenant(String tenantId) {
        Query query = getBaseCollection().whereArrayContains("affectedTenants", tenantId);
        return getResultFromQuery(query);
    }
}
