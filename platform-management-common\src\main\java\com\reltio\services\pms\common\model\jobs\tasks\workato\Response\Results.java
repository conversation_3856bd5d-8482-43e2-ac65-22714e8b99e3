package com.reltio.services.pms.common.model.jobs.tasks.workato.Response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

public class Results {

    @JsonProperty("target")
    Map<String, String> target;

    @JsonProperty("resut")
    Map<String, Boolean> resut;

    @JsonProperty("error")
    String error;

    public Results(@JsonProperty("target") Map<String, String> target,
                   @JsonProperty("resut") Map<String, Boolean> resut,
                   @JsonProperty("error") String error) {
        this.target = target;
        this.resut = resut;
        this.error = error;
    }

    public Map<String, String> getTarget() {
        return target;
    }

    public Map<String, Boolean> getResut() {
        return resut;
    }

    public String getError() {
        return error;
    }
}
