package com.reltio.services.pms.clients.external.aws.credentials;

import com.reltio.services.pms.validator.PropertiesValidator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.apache.log4j.Logger;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Component
public class AccessSecretKeyCredentialsProviderImageHosting implements PlatformManagementAwsCredentials {

    private final String accessKey;
    private final String secretKey;
    private final String awsRoleArn;
    private final String region;
    private static final Logger LOGGER = Logger.getLogger(AccessSecretKeyCredentialsProviderImageHosting.class);

    public AccessSecretKeyCredentialsProviderImageHosting(
            @Value("${image.hosting.account.access.key}") String accessKey,
            @Value("${image.hosting.account.secret.key}") String secretKey,
            @Value("${image.hosting.account.access.awsIAMRole}") String awsRoleArn) {
        this.accessKey = accessKey;
        this.secretKey = secretKey;
        this.awsRoleArn = awsRoleArn;
        this.region = "aws-global";
        if (!accessKey.isEmpty() || !secretKey.isEmpty()) {
            PropertiesValidator.validateProperties(
                    new HashSet<>(Arrays.asList(accessKey, secretKey)),
                    LOGGER
            );
        }
    }

    @Override
    public String accessKeyId() {
        return accessKey;
    }

    @Override
    public String secretAccessKey() {
        return secretKey;
    }

    @Override
    public String awsRoleArn(){
        return awsRoleArn;
    }

    @Override
    public String getAWSRegion() {
        return region;
    }
}
