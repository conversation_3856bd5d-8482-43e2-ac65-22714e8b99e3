package com.reltio.services.pms.common.model.compliance.response.rsuusage;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class RsuUsageByMonthResponse {

    @JsonProperty("rsuUsageByMonthModelList")
    List<RsuUsageMonthModel> rsuUsageMonthModelList;

    @JsonProperty("totalCount")
    Long totalCount;
}
