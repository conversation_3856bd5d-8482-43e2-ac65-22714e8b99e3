package com.reltio.services.pms.common.model.supportability.result.combiner;

import com.reltio.collection.CollectionUtils;
import com.reltio.services.pms.common.model.supportability.TasksStatsDto;
import com.reltio.services.pms.common.model.supportability.result.StatsCombiner;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * The type Task stats combiner.
 */
public class TaskStatsCombiner implements StatsCombiner<TasksStatsDto> {

    /**
     * Combine list.
     *
     * @param dtoList the dto list
     * @return the list
     */
    @Override
    public List<TasksStatsDto> combine(List<TasksStatsDto> dtoList) {
        List<TasksStatsDto> valueList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dtoList)) {
            valueList.add(mergeAllRows(dtoList));
        }
        return valueList;
    }

    /**
     * Merge all task rows tasks stats dto.
     *
     * @param dtoList the dto list
     * @return the tasks stats dto
     */
    @Override
    public TasksStatsDto mergeAllRows(List<TasksStatsDto> dtoList) {
        TasksStatsDto response = dtoList.get(NumberUtils.INTEGER_ZERO);
        TasksStatsDto lastResponse = dtoList.get(dtoList.size() - NumberUtils.INTEGER_ONE);
        long startTime = response.getStartTime();
        long endTime = lastResponse.getEndTime();
        long completedSum = NumberUtils.INTEGER_ZERO;
        long processingSum = NumberUtils.INTEGER_ZERO;
        long scheduledSum = NumberUtils.INTEGER_ZERO;
        long failedSum = NumberUtils.INTEGER_ZERO;
        long canceledSum = NumberUtils.INTEGER_ZERO;
        for (TasksStatsDto dto : dtoList) {
            completedSum += dto.getCompleted();
            processingSum += dto.getProcessing();
            scheduledSum += dto.getScheduled();
            failedSum += dto.getFailed();
            canceledSum += dto.getCanceled();
        }
        TasksStatsDto dto = new TasksStatsDto();
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setCompleted(completedSum);
        dto.setProcessing(processingSum);
        dto.setScheduled(scheduledSum);
        dto.setFailed(failedSum);
        dto.setCanceled(canceledSum);
        return dto;
    }
}
