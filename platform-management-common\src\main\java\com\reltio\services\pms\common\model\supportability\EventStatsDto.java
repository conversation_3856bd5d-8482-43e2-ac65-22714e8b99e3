package com.reltio.services.pms.common.model.supportability;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Objects;

/**
 * EventModel
 * Created by aravuru
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class EventStatsDto implements StatsDto {
    @JsonProperty("startTime")
    private Long startTime;
    @JsonProperty("endTime")
    private Long endTime;
    @JsonProperty("totalCount")
    private Long totalCount;
    @JsonProperty("failedCount")
    private Long failedCount;
    @JsonProperty("pendingCount")
    private Long pendingCount;
    @JsonProperty("successfulCount")
    private Long successfulCount;
    @JsonProperty("p50")
    private Long p50;
    @JsonProperty("p90")
    private Long p90;
    @JsonProperty("p95")
    private Long p95;
    @JsonProperty("p99")
    private Long p99;
    @JsonProperty("consumerType")
    private String consumerType;
    @JsonProperty("top5processorsByCount")
    private List<Processors> processorsByCount;

    @JsonProperty("top5processorsByLatency")
    private List<Processors> processorsByLatency;

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        EventStatsDto restStatsDto = (EventStatsDto) o;
        return Objects.equals(getStartTime(), restStatsDto.getStartTime()) &&
                Objects.equals(getEndTime(), restStatsDto.getEndTime()) &&
                Objects.equals(getTotalCount(), restStatsDto.getTotalCount()) &&
                Objects.equals(getFailedCount(), restStatsDto.getFailedCount()) &&
                Objects.equals(getPendingCount(), restStatsDto.getPendingCount()) &&
                Objects.equals(getSuccessfulCount(), restStatsDto.getSuccessfulCount()) &&
                Objects.equals(getP50(), restStatsDto.getP50()) &&
                Objects.equals(getP90(), restStatsDto.getP90()) &&
                Objects.equals(getP95(), restStatsDto.getP95()) &&
                Objects.equals(getP99(), restStatsDto.getP99()) &&
                Objects.equals(getConsumerType(), restStatsDto.getConsumerType());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getStartTime(), getEndTime(), getTotalCount(), getFailedCount(), getPendingCount(), getSuccessfulCount(),
                getP50(), getP90(), getP95(), getP99(), getConsumerType());
    }
}
