package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.DepartmentDetails;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Set;

@Getter
@Setter
public class CustomerTypeConfigEntry {
    @JsonProperty("allowedDepartments")
    private Set<DepartmentDetails> allowedDepartments;
    @JsonProperty("params")
    private List<FieldConfig> params;
}
