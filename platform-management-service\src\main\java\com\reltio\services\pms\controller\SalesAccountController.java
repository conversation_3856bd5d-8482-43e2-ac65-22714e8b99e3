package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.common.StringUtils;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.response.SalesAccountTenant;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.SalesAccount;
import com.reltio.services.pms.service.AuthCustomerService;
import com.reltio.services.pms.service.SalesAccountService;
import com.reltio.services.pms.service.reltiotenant.TenantsService;
import com.reltio.services.pms.service.sales.ContractService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/api/v1/salesAccounts", produces = MediaType.APPLICATION_JSON_VALUE)
@ReltioSecured(resourceClass = Pms.Sales.class)
@Tag(name= "Sales Accounts")
public class SalesAccountController {
    private final SalesAccountService salesAccountService;
    private final AuthCustomerService authCustomerService;
    private final TenantsService tenantsService;

    private final ContractService contractService;

    @Autowired
    public SalesAccountController(SalesAccountService salesAccountService,
                                  AuthCustomerService authCustomerService,
                                  TenantsService tenantsService,
                                  ContractService contractService) {
        this.salesAccountService = salesAccountService;
        this.authCustomerService = authCustomerService;
        this.tenantsService = tenantsService;
        this.contractService = contractService;
    }

    /*
    @GetMapping("/{custId}/contracts")
    public Collection<ReltioContract> getCustomersContracts(@PathVariable(value = "custId") String custId) {
        return contractService.getContractByCustomerId(custId);
    }

    @GetMapping("/{custId}/tenants")
    public Collection<Tenant> getCustomersTenants(@PathVariable(value = "custId") String custId) {
        return tenantsService.getTenantsForCustomerId(custId);
    }

    @GetMapping("/{custId}/rdmTenants")
    public Collection<RdmTenant> getCustomersRdmTenants(@PathVariable(value = "custId") String custId) {
        return tenantsService.getRDMTenantsForCustomerId(custId);
    }

    @GetMapping("/{custId}/jobs")
    public Collection<Job> getJobsForCustomer(@PathVariable(value = "custId") String customerId) {
        List<Job> jobs = new ArrayList<>();
        for (ReltioContract c : contractService.getContractByCustomerId(customerId)) {
            jobs.addAll(jobService.getJobsForContract(c.getContractId()));
        }
        return jobs;
    }

    @GetMapping("/accountId/{accountId}/contracts")
    public Collection<ReltioContract> getContractsByAccountId(@PathVariable(value = "accountId") String accountId) {
        return contractService.getContractsByAccountId(accountId);
    }

    @GetMapping("/accountId/{accountId}/tenants")
    public Collection<Tenant> getTenantsByAccountId(@PathVariable(value = "accountId") String accountId) {
        return tenantsService.getTenantsByAccountId(accountId);
    }

    @GetMapping("/accountId/{accountId}/rdmTenants")
    public Collection<RdmTenant> getRdmTenantsByAccountId(@PathVariable(value = "accountId") String accountId) {
        return tenantsService.getRdmTenantsByAccountId(accountId);
    }

  @GetMapping("/accountId/{accountId}/jobs")
    public Collection<Job> getJobsByAccountId(@PathVariable(value = "accountId") String accountId) {
        List<Job> jobs = new ArrayList<>();
        for (ReltioContract c : contractService.getContractsByAccountId(accountId)) {
            jobs.addAll(jobService.getJobsForContract(c.getContractId()));
        }
        return jobs;
    }
*/

    @PostMapping("/_syncAll")
    public List<String> syncAllSalesAccounts() {
        try {
            return contractService.syncAllCustomers();
        }catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }
    @GetMapping("")
    public Collection<SalesAccount> getSalesAccounts(@RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                                     @RequestParam(value = "size", required = false, defaultValue = "20") Integer size) {
        return salesAccountService.getSalesAccounts(size, offset);
    }

    @GetMapping("/_total")
    public Long getSalesAccountsCount() {
        return salesAccountService.getSalesAccountsCount();
    }

    @GetMapping("/tenants/_total")
    public Long getCustomerTenantsCount() {
        return salesAccountService.getCustomerTenantsCount();
    }

    @GetMapping("/tenants/_totalByEnv")
    public Map<String, Long> getCustomerTenantsCountByEnv() {
        return salesAccountService.getCustomerTenantsCountByEnv();
    }


    //TODO have only one method need for updateCleanableFlag and the API need to be /{accountId}?cleanableFlag={cleanableFlag} or
    @PutMapping(value = "/{accountId}/_allowDataDelete")
    public SalesAccount provideCleanabilityToBusinessCustomer(@PathVariable String accountId) {
        return salesAccountService.updateCleanableFlag(accountId, true);
    }

    @PutMapping(value = "/{accountId}/_denyDataDelete")
    public SalesAccount removeCleanabilityFromBusinessCustomer(@PathVariable String accountId) {
        return salesAccountService.updateCleanableFlag(accountId, false);
    }

    @GetMapping(value = "/{salesAccountId}")
    public SalesAccount getSalesAccount(@PathVariable String salesAccountId) {
        return salesAccountService.getSalesAccount(salesAccountId);
    }

    @GetMapping(value = "/{salesAccountId}/tenants")
    public Collection<SalesAccountTenant> getSalesAccountTenants(@PathVariable String salesAccountId, @RequestParam(required = false) Boolean hasContractId) {
        List<String> tenantIds = authCustomerService.getTenantIdForSalesAccountId(salesAccountId);
        Collection<ReltioTenant> reltioTenants = tenantsService.getTenantsByAccountIdAndTenantIds(salesAccountId, tenantIds);
        if(hasContractId == null) {
            return reltioTenants.parallelStream()
                    .map(t -> new SalesAccountTenant(t.getTenantId(), t.getTenantPurpose().toString(), t.getReltioEnv(), t.getDeploymentCloud()))
                    .collect(Collectors.toList());
        } else if(hasContractId){
            return reltioTenants.parallelStream()
                    .filter(t->t.getContractId() != null && !t.getContractId().isEmpty())
                    .map(t -> new SalesAccountTenant(t.getTenantId(), t.getTenantPurpose().toString(), t.getReltioEnv(), t.getDeploymentCloud()))
                    .collect(Collectors.toList());
        } else {
            return reltioTenants.parallelStream()
                    .filter(t->t.getContractId() == null || t.getContractId().isEmpty())
                    .map(t -> new SalesAccountTenant(t.getTenantId(), t.getTenantPurpose().toString(), t.getReltioEnv(), t.getDeploymentCloud()))
                    .collect(Collectors.toList());
        }
    }

    @PostMapping(value = "")
    public SalesAccount businessCustomer(@RequestBody SalesAccount businessCustomer) {
        return salesAccountService.createSalesAccount(businessCustomer);
    }

    @PutMapping("/{accountId}/customerId/{customerId}")
    public SalesAccount updateBusinessCustomerOrContract(@PathVariable String customerId, @PathVariable String accountId) {
        if (StringUtils.isEmpty(customerId) || StringUtils.isEmpty(accountId)) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "customerId or accountId is missing", customerId + "-" + accountId);
        }
        try {
            return salesAccountService.updateBusinessCustomerOrContract(customerId, accountId);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.BUSINESS_CUSTOMER_NOT_FOUND, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, customerId);
        }
    }

}
