package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.reltio.devops.common.environment.CloudProvider;
import com.reltio.devops.common.environment.ServicePurpose;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
public class Environment extends BaseFirestoreEntity {

    public final static List<ServiceType> defaultUrlList = Arrays.asList(ServiceType.AUTH, ServiceType.CONFIG_SERVICE, ServiceType.DATA_VALIDATION_SERVICE,
            ServiceType.DNB_CONNECTOR_PROD, ServiceType.DNB_CONNECTOR_TEST, ServiceType.DPH, ServiceType.IH, ServiceType.IRS, ServiceType.ML,
            ServiceType.NOTIFICATION, ServiceType.NOTIFICATION_SERVICE, ServiceType.RDM_API, ServiceType.RELTIO_SHIELD_SERVICE, ServiceType.RI_API,
            ServiceType.SAML_CONFIG, ServiceType.SFDC_CONNECTOR_SERVICE, ServiceType.WORKATO, ServiceType.WORKATO_CONSOLE, ServiceType.WORKFLOW,
            ServiceType.WORKFLOW_UI);
    private final static List<ServiceType> supervisedServices = Arrays.asList(ServiceType.DTSS,
            ServiceType.MATCH_IQ, ServiceType.ML, ServiceType.RDM_API, ServiceType.RI_API, ServiceType.WORKFLOW, ServiceType.EM_UI, ServiceType.CONSOLE, ServiceType.WORKFLOW_UI, ServiceType.RDM_UI, ServiceType.IRS, ServiceType.CONFIG_SERVICE, ServiceType.AUTH
            , ServiceType.RDM_WORKFLOW_SERVICE, ServiceType.ACTIVITYLOG, ServiceType.WORKATO_CONSOLE, ServiceType.IH, ServiceType.DNB_CONNECTOR_PROD, ServiceType.DNB_CONNECTOR_TEST,
            ServiceType.RELTIO_SHIELD_SERVICE, ServiceType.DATA_VALIDATION_SERVICE, ServiceType.NOTIFICATION_SERVICE, ServiceType.SAML_CONFIG);
    @JsonProperty("region")
    private final String region;
    @JsonProperty("name")
    private String name;
    @JsonProperty("url")
    private String url;
    @JsonProperty("dataSetLocation")
    private DataSetLocation dataSetLocation;

    @JsonProperty("cloud")
    private CloudProvider cloud;

    @JsonProperty("masterHash")
    private String masterHash;

    @JsonProperty("clusters")
    private Set<String> clusters;

    @JsonProperty("defaultUrls")
    private Map<ServiceType, String> defaultUrls;

    @JsonProperty("template")
    private JsonNode template;

    @JsonProperty("environmentParam")
    private EnvironmentParam environmentParam;

    @JsonProperty("secondryName")
    private String secondryName;

    @JsonProperty("securityCompliance")
    private String securityCompliance;

    @JsonProperty("skip")
    private Boolean skip;

    @JsonProperty("storagePriorityList")
    private Map<ServicePurpose, List<com.reltio.devops.common.environment.ServiceType>> storagePriorityList;

    @JsonProperty("environmentAccessType")
    private EnvironmentAccessType environmentAccessType;


    @JsonCreator
    public Environment(@JsonProperty("name") String name,
                       @JsonProperty("url") String url,
                       @JsonProperty("region") String region,
                       @JsonProperty("dataSetLocation") DataSetLocation dataSetLocation,
                       @JsonProperty("cloud") CloudProvider cloud,
                       @JsonProperty("masterHash") String masterHash,
                       @JsonProperty("clusters") Set<String> clusters,
                       @JsonProperty("defaultUrls") Map<ServiceType, String> defaultUrls,
                       @JsonProperty("template") JsonNode template,
                       @JsonProperty("environmentParam") EnvironmentParam environmentParam,
                       @JsonProperty("secondryName") String secondryName,
                       @JsonProperty("securityCompliance") String securityCompliance,
                       @JsonProperty("skip") Boolean skip,
                       @JsonProperty("storagePriorityList") Map<ServicePurpose, List<com.reltio.devops.common.environment.ServiceType>> storagePriorityList,
                       @JsonProperty("environmentAccessType") EnvironmentAccessType environmentAccessType) {
        this.url = url;
        this.name = name.toLowerCase();
        this.region = region;
        this.dataSetLocation = dataSetLocation;
        this.cloud = cloud;
        this.masterHash = masterHash;
        this.clusters = clusters;
        this.defaultUrls = defaultUrls;
        this.template = template;
        this.environmentParam = environmentParam;
        this.secondryName = secondryName;
        this.securityCompliance = securityCompliance;
        this.skip = skip != null && skip;
        this.storagePriorityList = storagePriorityList;
        this.environmentAccessType = Optional.ofNullable(environmentAccessType).orElse(EnvironmentAccessType.CUSTOMER);
    }

    public static List<ServiceType> getSupervisedServices() {
        return supervisedServices;
    }

    public Environment setUrl(String url) {
        this.url = url;
        return this;
    }

    public Environment setSkip(boolean skip) {
        this.skip = skip;
        return this;
    }

    public void setSecondryName(String secondryName) {
        this.secondryName = secondryName;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setMasterHash(String masterHash) {
        this.masterHash = masterHash;
    }

    public void setDefaultUrls(Map<ServiceType, String> defaultUrls) {
        this.defaultUrls = defaultUrls;
    }

    public void setStoragePriorityList(Map<ServicePurpose, List<com.reltio.devops.common.environment.ServiceType>> storagePriorityList) {
        this.storagePriorityList = storagePriorityList;
    }

    public void setDataSetLocation(DataSetLocation dataSetLocation) {
        this.dataSetLocation = dataSetLocation;
    }

    public void setCloud(CloudProvider cloud) {
        this.cloud = cloud;
    }

    public EnvironmentParam getEnvironmentParam() {
        return environmentParam;
    }

    public void setEnvironmentParam(EnvironmentParam environmentParam) {
        this.environmentParam = environmentParam;
    }

    public void setSecurityCompliance(String securityCompliance) {
        this.securityCompliance = securityCompliance;
    }

    public void addDefaultUrls(Map<ServiceType, String> incomingUrls) {
        if (getDefaultUrls() != null && !getDefaultUrls().isEmpty()) {
            Map<ServiceType, String> result = Stream.concat(getDefaultUrls().entrySet().stream(), incomingUrls.entrySet().stream())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (value1, value2) -> value2));
            this.setDefaultUrls(result);
        } else {
            this.setDefaultUrls(incomingUrls);
        }
    }

    @Override
    public String getID() {
        return name;
    }

    public void setClusters(Set<String> clusters) {
        this.clusters = clusters;
    }

    public void setTemplate(JsonNode template) {
        this.template = template;
    }

    public boolean isHipaa() {
        return (securityCompliance != null && securityCompliance.equals("hipaa"));
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Environment that = (Environment) o;
        return Objects.equals(getName(), that.getName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getName());
    }


    @Override
    public String toString() {
        return "Environment{" +
                "name='" + name + '\'' +
                ", url='" + url + '\'' +
                ", region='" + region + '\'' +
                ", masterHash='" + masterHash + '\'' +
                ", clusters=" + clusters +
                ", defaultUrls=" + defaultUrls +
                ", template=" + template +
                ", environmentParam=" + environmentParam +
                ", secondryName='" + secondryName + '\'' +
                ", securityCompliance='" + securityCompliance + '\'' +
                ", skip=" + skip +
                ", storagePriorityList=" + storagePriorityList +
                '}';
    }
}
