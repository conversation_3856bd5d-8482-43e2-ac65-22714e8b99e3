package com.reltio.services.pms.convertors.sales.quotas;

import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.common.sales.model.quotas.BaseQuotasConfig;
import com.reltio.services.pms.service.SalesPackageService;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public abstract class AbstractQuotasConfigProvider<Q extends BaseQuotasConfig> implements QuotasConfigProvider<BaseQuotasConfig> {


    protected final SalesPackageService salesPackageService;

    protected AbstractQuotasConfigProvider(SalesPackageService salesPackageService) {
        this.salesPackageService = salesPackageService;
    }


    @Override
    public BaseQuotasConfig getQuotasConfig(Map<String, Set<SalesConfig>> salesConfigByProductCode) {

        Set<SalesConfig> salesConfigs = new HashSet<>();

        Set<SalesConfig> tempSalesConfigs;
        for (String quotaProductCode : getQuotaProductCodes()) {
            tempSalesConfigs = salesConfigByProductCode.get(quotaProductCode);
            if (tempSalesConfigs != null) {
                salesConfigs.addAll(tempSalesConfigs);
            }
        }
        return getQuotasConfig(salesConfigs);
    }

    BaseQuotasConfig getQuotasConfig(Set<SalesConfig> salesConfigs) {
        if (salesConfigs.isEmpty()) {
            return null;
        }
        BaseQuotasConfig baseQuotasConfig = new BaseQuotasConfig();
        baseQuotasConfig.setQuotaPeriod(getQuotaPeriod());
        baseQuotasConfig.setPmsQuotaName(getQuotaName());
        baseQuotasConfig.setPmsProductName(getPMSProductName());
        baseQuotasConfig.setQuantityType(getQuantityType());
        baseQuotasConfig.setQuotaType(getQuotaType());
        baseQuotasConfig.setQuantity(getQuantity(salesConfigs));

        return baseQuotasConfig;
    }

    protected Float getQuantity(Set<SalesConfig> salesConfigs) {
        float quantity = 0.0f;
        if (salesConfigs != null) {
            for (SalesConfig salesConfig : salesConfigs) {
                if (salesConfig.isActive()) {
                    quantity += salesConfig.getQuantity().floatValue();
                }
            }
        }
        return quantity;
    }
}
