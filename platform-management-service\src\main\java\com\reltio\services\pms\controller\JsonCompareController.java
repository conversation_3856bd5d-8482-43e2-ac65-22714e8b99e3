package com.reltio.services.pms.controller;

import com.reltio.services.pms.common.model.JsonCompareRequest;
import com.reltio.services.pms.service.jsoncompare.entities.CompareMode;
import com.reltio.services.pms.service.jsoncompare.JsonCompareService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.EnumSet;
import java.util.Set;

@RestController
@RequestMapping("/api/v1/jsontool")
@Tag(name = "JSON Compare")
public class JsonCompareController {

    private static final Logger LOGGER = LoggerFactory.getLogger(JsonCompareController.class);

    private final JsonCompareService jsonCompareService;

    @Autowired
    public JsonCompareController(final JsonCompareService jsonCompareService) {
        this.jsonCompareService = jsonCompareService;
    }

    @PostMapping(value = "/compare")
    public ResponseEntity<String> compare(@RequestBody JsonCompareRequest request) {
        try {
            Set<CompareMode> compareModes = EnumSet.of(CompareMode.JSON_ARRAY_PRIMARY_KEY_CHECK, CompareMode.REGEX_DISABLED);
            String responseBody = jsonCompareService.findDiffsAndCreateResponse(request, compareModes);
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN_VALUE);
            return new ResponseEntity<>(responseBody, headers, HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("Error while converting differences to response", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


}