package com.reltio.services.pms.dao;


import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class BusinessDomainContentDao extends AbstractLevel1CollectionDao<RepositoryBusinessDomain> {

    private static final String BUSINESS_DOMAIN_CONTENT_COLLECTION = "REPOSITORY_BUSINESS_DOMAIN";

    @Autowired
    public BusinessDomainContentDao(CredentialsProvider provider,
                                    RepositoryDao repositoryDao,
                                    ReltioUserHolder reltioUserHolder) {
        super(provider, repositoryDao, BUSINESS_DOMAIN_CONTENT_COLLECTION, reltioUserHolder);
    }

    @Override
    protected TypeReference<RepositoryBusinessDomain> getTypeReference() {
        return new TypeReference<RepositoryBusinessDomain>() {};
    }

}
