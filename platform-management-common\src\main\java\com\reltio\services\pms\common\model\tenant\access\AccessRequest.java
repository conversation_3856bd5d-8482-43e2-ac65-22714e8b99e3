package com.reltio.services.pms.common.model.tenant.access;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccessRequest extends BaseFirestoreEntity {

    @JsonProperty("requestId")
    private String requestId;

    @JsonProperty("userEmail")
    private String userEmail;

    @JsonProperty("tenantId")
    private String tenantId;

    @JsonProperty("zendeskId")
    private String zendeskId;

    @JsonProperty("rolesAssigned")
    Set<String> rolesAssigned;

    @JsonProperty("status")
    private AccessStatus status;

    @JsonProperty("strackTrace")
    private RequestFailureContext stackTrace;

    @JsonProperty("tenantType")
    private TenantType tenantType;

    @JsonCreator
    public AccessRequest(@JsonProperty("requestId") String requestId,
                        @JsonProperty("userEmail") String userEmail,
                        @JsonProperty("tenantId") String tenantId,
                        @JsonProperty("zendeskId") String zendeskId,
                         @JsonProperty("rolesAssigned") Set<String> rolesAssigned,
                        @JsonProperty("status") AccessStatus status,
                         @JsonProperty("strackTrace") RequestFailureContext stackTrace,
                         @JsonProperty("tenantType") TenantType tenantType) {
        this.requestId = requestId;
        this.userEmail = userEmail;
        this.tenantId = tenantId;
        this.zendeskId = zendeskId;
        this.rolesAssigned = rolesAssigned != null ? rolesAssigned : new HashSet<>();
        this.status = status;
        this.stackTrace = stackTrace;
        this.tenantType = tenantType;
    }

    @Override
    public String getID() {
        return requestId;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getZendeskId() {
        return zendeskId;
    }

    public void setZendeskId(String zendeskId) {
        this.zendeskId = zendeskId;
    }

    public Set<String> getRolesAssigned() {
        return rolesAssigned;
    }

    public void setRolesAssigned(Set<String> rolesAssigned) {
        this.rolesAssigned = rolesAssigned;
    }

    public AccessStatus getStatus() {
        return status;
    }

    public void setStatus(AccessStatus status) {
        this.status = status;
    }

    public RequestFailureContext getStackTrace() {
        return stackTrace;
    }

    public void setStackTrace(RequestFailureContext stackTrace) {
        this.stackTrace = stackTrace;
    }

    public TenantType getTenantType() {
        return tenantType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(getRequestId(), getUserEmail(), getTenantId(), getZendeskId(), getRolesAssigned(), getStatus(), getStackTrace());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AccessRequest that = (AccessRequest) o;

        if (!requestId.equals(that.requestId)) return false;
        if (!userEmail.equals(that.userEmail)) return false;
        if (!tenantId.equals(that.tenantId)) return false;
        if (!zendeskId.equals(that.zendeskId)) return false;
        if(status != that.status) return false;
        return stackTrace.equals(that.stackTrace);
    }

    public static class RequestFailureContext {

        @JsonProperty("stackTraceElements")
        private List<String> stackTraceElements;

        @JsonProperty("messages")
        private List<String> messages;

        @JsonCreator
        public RequestFailureContext(@JsonProperty("stackTraceElements") List<String> stackTraceElements,
                                     @JsonProperty("messages") List<String> messages) {
            this.stackTraceElements = stackTraceElements;
            this.messages = messages;
        }

        public List<String> getStackTraceElements() {
            return stackTraceElements;
        }

        public void setStackTraceElements(List<String> stackTraceElements) {
            this.stackTraceElements = stackTraceElements;
        }

        public List<String> getMessages() {
            return messages;
        }

        public void setMessages(List<String> messages) {
            this.messages = messages;
        }

    }

}
