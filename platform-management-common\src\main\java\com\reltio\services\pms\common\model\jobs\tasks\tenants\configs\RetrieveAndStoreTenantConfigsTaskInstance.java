package com.reltio.services.pms.common.model.jobs.tasks.tenants.configs;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class RetrieveAndStoreTenantConfigsTaskInstance extends TaskInstance {

    @JsonProperty(value = "sourceTenantId")
    private final String sourceTenantId;

    @JsonProperty(value = "targetTenantId")
    private final String targetTenantId;

    @JsonProperty(value = "sourceEnvId")
    private final String sourceEnvId;

    @JsonProperty(value = "targetEnvId")
    private final String targetEnvId;

    @JsonProperty(value = "events")
    private final List<String> events;

    @JsonProperty(value = "deleteAfterDays")
    private final int deleteAfterDays;


    @JsonCreator
    public RetrieveAndStoreTenantConfigsTaskInstance(@JsonProperty(value = "id") String id,
                           @JsonProperty(value = "name") String name,
                           @JsonProperty(value = "envId") String envId,
                           @JsonProperty(value = "jobId") String jobId,
                           @JsonProperty(value = "startTime") Long startTime,
                           @JsonProperty(value = "finishTime") Long finishTime,
                           @JsonProperty(value = "status") TaskStatus status,
                           @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                           @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                           @JsonProperty(value = "executingNodeName") String executingNodeName,
                           @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                           @JsonProperty(value = "sourceTenantId") String sourceTenantId,
                           @JsonProperty(value = "targetTenantId") String targetTenantId,
                           @JsonProperty(value = "sourceEnvId") String sourceEnvId,
                           @JsonProperty(value = "targetEnvId") String targetEnvId,
                           @JsonProperty(value = "events") List<String> events,
                           @JsonProperty(value = "deleteAfterDays") int deleteAfterDays){

        super(id, name, jobId, startTime, finishTime, TaskType.RETRIEVE_AND_STORE_TENANT_CONFIGS_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.sourceTenantId = sourceTenantId;
        this.targetTenantId = targetTenantId;
        this.sourceEnvId = sourceEnvId;
        this.targetEnvId = targetEnvId;
        this.events = events == null ? new ArrayList<>() : new ArrayList<>(events);
        this.deleteAfterDays = deleteAfterDays;
    }

    public void addEvent(String event) {
        events.add(event);
    }
}
