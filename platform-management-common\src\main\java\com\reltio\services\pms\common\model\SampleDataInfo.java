package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class SampleDataInfo {

    @JsonProperty("entitiesDataPath")
    private final List<String> entitiesDataPath;

    @JsonProperty("relationsDataPath")
    private final List<String> relationsDataPath;

    @JsonProperty("interactionDataPath")
    private final List<String> interactionDataPath;


    @JsonCreator
    public SampleDataInfo(@JsonProperty("entitiesDataPath") List<String> entitiesDataPath,
                          @JsonProperty("relationsDataPath") List<String> relationsDataPath,
                          @JsonProperty("interactionDataPath") List<String> interactionDataPath) {
        this.entitiesDataPath = entitiesDataPath != null ? entitiesDataPath : Collections.emptyList();
        this.relationsDataPath = relationsDataPath != null ? relationsDataPath : Collections.emptyList();
        this.interactionDataPath = interactionDataPath !=null ? interactionDataPath: Collections.emptyList();
    }

    public List<String> getEntitiesDataPath() {
        return entitiesDataPath;
    }

    public List<String> getRelationsDataPath() {
        return relationsDataPath;
    }

    public List<String> getInteractionDataPath() {
        return interactionDataPath;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SampleDataInfo)) {
            return false;
        }
        SampleDataInfo that = (SampleDataInfo) o;
        return Objects.equals(getEntitiesDataPath(), that.getEntitiesDataPath()) &&
                Objects.equals(getRelationsDataPath(), that.getRelationsDataPath()) &&
                Objects.equals(getInteractionDataPath(), that.getInteractionDataPath());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getEntitiesDataPath(), getRelationsDataPath(), getInteractionDataPath());
    }

    @Override
    public String toString() {
        return "SampleDataInfo{" +
                "entitiesDataPath=" + entitiesDataPath +
                ", relationsDataPath=" + relationsDataPath +
                ", interactionDataPath=" + interactionDataPath +
                '}';
    }
}
