package com.reltio.services.pms.common.model.client;


public class ClientBuilder {
    private final String clientId;

    private String clientSecret;

    private String authClientCloudKey;

    public ClientBuilder(String clientId) {
        this.clientId = clientId;
    }

    public ClientBuilder setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
        return this;
    }

    public ClientBuilder setAuthClientCloudKey(String authClientCloudKey) {
        this.authClientCloudKey = authClientCloudKey;
        return this;
    }

    public Client build() {
        return new Client(this.clientId, this.clientSecret, this.authClientCloudKey);
    }
}
