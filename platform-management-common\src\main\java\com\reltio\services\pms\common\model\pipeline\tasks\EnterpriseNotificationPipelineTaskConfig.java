package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import static com.reltio.services.pms.common.model.pipeline.tasks.TaskType.ENTERPRISE_NOTIFICATION_TASK_NAME;

public class EnterpriseNotificationPipelineTaskConfig extends AbstractPipelineTaskConfig{

    @JsonCreator
    public EnterpriseNotificationPipelineTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.ENTERPRISE_NOTIFICATION_TASK);
    }

    @Override
    public String toString() {
        return ENTERPRISE_NOTIFICATION_TASK_NAME+"{}";
    }
}
