package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

public class IndustryTTL extends PMSConfig {

    @JsonProperty("industryTTL")
    private Map<String, String> industryTTL;

    @JsonCreator
    public IndustryTTL(@JsonProperty("configName") String configName,
                       @JsonProperty("industryTTL") Map<String, String> industryTTL) {
        super(configName);
        this.industryTTL = industryTTL;
    }

    public Map<String, String> getIndustryTTL() {
        return industryTTL;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        IndustryTTL that = (IndustryTTL) o;

        return industryTTL.equals(that.industryTTL);
    }

    @Override
    public int hashCode() {
        return industryTTL.hashCode();
    }
}
