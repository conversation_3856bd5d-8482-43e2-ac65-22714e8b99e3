{"type": "PLATFORM", "tenantId": "#tenantId#", "tenantName": "#tenantName#", "customerName": "<PERSON><PERSON><PERSON>", "analyticsConfig": {"analyticsEnabled": false}, "dataStorageConfig": {"useMatchingMultiTenantFix": false, "isolated": true, "historyGBTMigration": false, "tableProperties": {}, "dataKeyspaceConfig": {"keyspaceName": "#tenantKeySpace#DHM", "host": "#dataHost#", "clusterName": "#dataCluster#", "replicationFactor": 3, "cassandraVersion": "CASSANDRA_1_1", "fetchBufferSize": 150, "maxInsertSizeInMb": 32}, "historyBigTableConfig": {"memory": false, "project": "#historyProject#", "instanceId": "#historyInstance#", "tableName": "#historyTableName#", "ttl": "P1M"}, "matchKeyspaceConfig": {"keyspaceName": "#tenantKeySpace#M", "host": "#matchHosts#", "clusterName": "#matchCluster#", "replicationFactor": 3, "cassandraVersion": "CASSANDRA_1_1", "fetchBufferSize": 150, "maxInsertSizeInMb": 32}, "entitiesCF": "ENTITIES", "entityLinksCF": "ENTITY_LINKS", "entitiesCompressedCF": "ENTITIES_COMPRESSED_VALUES", "entityMergeTreeCF": "ENTITY_MERGE_TREE", "relationsCF": "RELATIONS", "relationsOneHopsCF": "RELATIONS_ONE_HOP_NEW", "relationsStartEndCF": "RELATIONS_START_END", "relationMergeTreeCF": "RELATION_MERGE_TREE", "relationsCompressedCF": "RELATIONS_COMPRESSED_VALUES", "entityGroupsCF": "ENTITY_GROUPS", "groupsCF": "GROUPS", "groupMembersCF": "GROUP_MEMBERS", "entityGraphsCF": "ENTITY_GRAPHS", "graphsCF": "GRAPHS", "graphMembersCF": "GRAPH_MEMBERS", "userPreferencesCF": "USER_PREFERENCES", "categoriesCF": "CATEGORIES", "categoriesLinksCF": "CATEGORY_LINKS", "activityRecordsCF": "ACTIVITY_RECORDS", "activitiesCF": "ACTIVITIES", "activitiesV2CF": "ACTIVITIES_V2", "businessProcessesCF": "BUSINESS_PROCESSES", "entityHistoryExCF": "ENTITY_HISTORY_EX", "entityHistoryCacheCF": "ENTITY_HISTORY_CACHE", "configurationHistoryCF": "CONFIGURATION_HISTORY", "attributesDeltaHistoryCF": "ATTRIBUTES_DELTA_HISTORY", "physicalConfigurationHistoryCF": "PHYSICAL_CONFIGURATION_HISTORY", "matchCF": "MATCH_TOKENS", "matchDocumentsCF": "MATCH_DOCUMENTS", "potentialMatchesCF": "POTENTIAL_DUPLICATES", "autoMatchesCF": "AUTO_MATCHES", "notMatchCF": "NOT_MATCH", "autoNotMatchesCF": "AUTO_NOT_MATCHES", "manuallyMarkedAsMatchCF": "AS_MATCH", "entityMatchesCF": "MATCHES", "interactionsCF": "INTERACTIONS", "entityToInteractionsCF": "ENTITY_TO_INTERACTIONS", "newInteractionsCF": "INTERACTIONS_NEW", "externalCrosswalkCF": "EXTERNAL_CROSSWALKS", "externalRelationCrosswalkCF": "EXTERNAL_RELATION_CROSSWALKS", "cacheStatusCF": "CACHE_STATUS", "analyticsAttributesCF": "ANALYTICS_ATTRIBUTES", "changeRequestsCF": "CHANGE_REQUESTS", "changeRequestIndexCF": "CHANGE_REQUEST_INDEX", "historyDataStorageMode": "PRIMARY"}, "searchStorageConfiguration": {"esClusterName": "#esCluster#", "esHosts": "#esHost#", "numberOfShards": 1, "numberOrReplicas": 1, "maxNumberOfResults": 200, "maxNumberOfIndexRequests": 10, "indexRelations": false, "indexOvStrategy": "#indexOvStrategy#", "indexActivityDelta": false, "indexMLRules": false, "indexDocumentSources": false, "indexOnlySearchableAttributes": false, "indexRDMLookups": false, "preserveCursor": false, "maxAttributeLength": 256, "backpressureEnabled": true, "enableBufferedCluster": false, "refreshIndexOnInserts": false, "esIndexName": "#dataks#-index", "esVersion": 6}, "eventQueuesConfig": {"crud": {"defaultProcessors": ["analytics", "analytics_pubsub", "crud_activity_log_long_term_storage", "crud_analytics", "crud_history", "crud_merge", "crud_search_activities", "crud_search_entities", "crud_search_others", "crud_streaming"], "processors": {"CRUD_INTERNAL_MATCH": ["crud_internal_match"]}, "processorsParams": {"defaultProcessorsParams": {"disabled": false, "debug": false, "maxThreadsPerCluster": -1}}}}, "matchingConfiguration": {"strategy": "#matchingStrategy#", "resolveLookupStrategy": "LOOKUP_CODE", "generateMatchTokensMapping": false, "generateTokensForExactOrAllNull": false, "generateSuspectByNegativeRules": true, "stripDiacritics": false, "hashTokens": true, "tokenCollisionsLimit": 300}, "objectSizeLimits": {"maxAttributeValues": 200, "maxAttributeValueSize": 10240, "maxCrosswalks": 200, "maxSubNestedAttributesPerEntity": 20000, "maxSubReferenceAttributesPerEntity": 20000, "maxReferenceCrosswalksPerEntity": 2000}, "defaultCrosswalk": "<PERSON><PERSON><PERSON>", "defaultTenant": "#tenantId#", "resolveLookupCode": false, "maxChangedObjects": -1, "maxInteractionsToFetchPerEntity": 100000, "maxFetchSizeForOneHopRow": 500000, "maxOneHopTotalFetchSize": 1000000, "objectsInTreeLimit": 1500, "traverseStepLimit": 500, "rdmConfig": {"serviceUri": "https://rdm.reltio.com/", "oauthInstance": "https://auth.reltio.com/oauth/token", "cache": {"enabled": true, "type": "TranscodeInMemory"}, "publishRDMErrors": false}, "ignoreMultipleContributorsError": false, "logRequestsBody": false, "maxTaskPartsCount": 8, "updateAttributesEntitiesBatchSize": 200, "useEntityLinksCF": true, "resolveEntityLinksUsingEntityLinksCF": false, "maxLookupsCacheSize": 300000, "permanentLookupsStore": false, "useActivityLogInTasks": false, "gdprComplianceRequired": false, "exportConfig": {"exportVersion": "v2", "fileCompression": "GZIP", "storageType": "S3", "analyticsClusterSizeFactor": 0.9, "smartExport": {"maxNumberOfObjectsForEsFilteringFactor": 0.5, "maxNumberOfObjectsForAlwaysEsFiltering": 200000, "maxNumberOfObjectsForEsFiltering": 8000000, "maxNumberOfObjectsPerTask": 1000000, "taskPartsCountForUnknownEstimatedTotal": 4, "maxNumberOfActivityItemsPerTask": 5000000, "relationsToEntitiesRatio": 0.5, "maxNumberOfRelationsForEsFiltering": 8000000, "maxNumberOfRelationsForAlwaysEsFiltering": 200000}, "writeManifest": true, "tasksLimit": 20, "appendExtension": true}, "updateAttributeUpdateDatesOnActualChanges": false, "enforceAsyncThrottling": true, "enforceSyncThrottling": true, "enforcePriorityThrottling": true, "maxHistoryBufferSize": 50000, "userPreferencesCacheEnabled": true, "physicalConfigValidationEnabled": true, "businessConfigValidationEnabled": true, "activityLogConfig": {"ttl": "PT744H", "returnObjectLabelsInScan": true, "convertNullsToEmptyLabelsInCassandra": false, "longTermStorage": {"enabled": false, "gbqLimitBytes": 1099511627776}}, "enableNestedPartialOverride": false, "enableTenantPerRoleSecurity": false, "historyCache": {"historyCacheEnabled": false, "historyCacheTaskPartCount": 1}, "lcaConfig": {"nativeTimeOut": 100, "lambdaTimeOut": 100, "dvfTimeOut": 1000}}