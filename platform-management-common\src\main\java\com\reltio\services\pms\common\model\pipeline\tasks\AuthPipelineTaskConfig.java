package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import com.reltio.services.pms.common.model.jobs.tasks.auth.AuthTaskConstants;
import com.reltio.services.pms.common.model.jobs.tasks.auth.AuthTaskInstance;
import com.reltio.services.pms.common.sales.PMSProductName;

import java.util.List;

@JsonDeserialize(using = AuthPipelineTaskConfigDeserializer.class)
public class AuthPipelineTaskConfig extends AbstractPipelineTaskConfig {

    private final AuthTaskInstance.CustomerConfig customerConfig;
    private final AuthTaskInstance.ClientConfig clientConfig;
    private final AuthTaskInstance.UserConfig userConfig;
    private final List<AuthTaskInstance.GroupConfig> groupConfig;




    @JsonCreator
    public AuthPipelineTaskConfig(@JsonProperty(value = AuthTaskConstants.PIPELINE_NAME) String name,
                                  @JsonProperty(value = AuthTaskConstants.CUSTOMER_CONFIG) AuthTaskInstance.CustomerConfig customerConfig,
                                  @JsonProperty(value = AuthTaskConstants.CLIENT_CONFIG) AuthTaskInstance.ClientConfig clientConfig,
                                  @JsonProperty(value = AuthTaskConstants.USER_CONFIG) AuthTaskInstance.UserConfig userConfig,
                                  @JsonProperty(value = AuthTaskConstants.GROUP_CONFIG) List<AuthTaskInstance.GroupConfig> groupConfig) {
        super(name, TaskType.AUTH_TASK);
        this.customerConfig = customerConfig;
        this.clientConfig = clientConfig;
        this.userConfig = userConfig;
        this.groupConfig = groupConfig;
    }

    public AuthTaskInstance.CustomerConfig getCustomerConfig() {
        return customerConfig;
    }

    public AuthTaskInstance.ClientConfig getClientConfig() {
        return clientConfig;
    }

    public AuthTaskInstance.UserConfig getUserConfig() {
        return userConfig;
    }

    public List<AuthTaskInstance.GroupConfig> getGroupConfig(){return groupConfig;}


    @Override
    public PMSProductName getProductName() {
        return PMSProductName.AUTH_ACCESS;
    }


    @Override
    public String toString() {
        return "AuthPipelineTaskConfig{" + ", customerConfig=" + customerConfig + ", clientConfig=" + clientConfig + ", userConfig=" + userConfig + "groupConfig=" + groupConfig +'}';
    }
}
