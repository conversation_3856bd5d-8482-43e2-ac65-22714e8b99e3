package com.reltio.services.pms.common.model.jobs.tasks.tenants.data.clone;

import com.reltio.services.pms.common.model.jobs.tasks.TaskInstanceParams;

public interface DataRestoreTaskInstanceParams extends TaskInstanceParams {
    String getSourceTenantId();
    String getSourceEnvId();
    String getTargetTenantId();
    String getTargetEnvId();
    String getTicketId();
    String getTargetAccountBackupVaultName();
    boolean isForceOverridePhyConfig();
}
