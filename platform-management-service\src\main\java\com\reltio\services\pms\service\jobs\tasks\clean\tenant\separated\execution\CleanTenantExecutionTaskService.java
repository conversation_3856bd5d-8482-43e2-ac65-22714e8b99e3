package com.reltio.services.pms.service.jobs.tasks.clean.tenant.separated.execution;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.annotations.VisibleForTesting;
import com.reltio.common.config.CassandraConfigWithDSMapping;
import com.reltio.common.config.DataStorageMapping;
import com.reltio.common.config.DataStorageType;
import com.reltio.common.config.TenantConfiguration;
import com.reltio.common.config.TenantDatabaseConfiguration;
import com.reltio.sdk.credentials.AWSRoleFeatureSupportUtil;
import com.reltio.services.pms.clients.external.gcp.pubsub.GBTClient;
import com.reltio.services.pms.clients.reltio.cassandra.CassandraCleaner;
import com.reltio.services.pms.clients.reltio.irs.IRSClient;
import com.reltio.services.pms.clients.reltio.mdm.MDMCleanTenantClient;
import com.reltio.services.pms.clients.reltio.mdm.MDMClient;
import com.reltio.services.pms.clients.reltio.workflow.WorkflowServiceClient;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.common.model.gcp.GcpBigTable;
import com.reltio.services.pms.common.model.jobs.JobStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.separated.execution.CleanTenantExecutionTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.deprovision.DeleteMDMTenantExecutionTaskInstance;
import com.reltio.services.pms.common.model.secrets.SecretDto;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.TenantStatus;
import com.reltio.services.pms.common.sales.model.addon.RdmProductConfig;
import com.reltio.services.pms.service.PmsLockService;
import com.reltio.services.pms.service.SecretService;
import com.reltio.services.pms.service.environment.EnvironmentService;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.JobService;
import com.reltio.services.pms.service.jobs.tasks.AbstractMultiStepTaskExecutionService;
import com.reltio.services.pms.service.jobs.tasks.deprovision.auth.AuthDeProvisionHelper;
import com.reltio.services.pms.service.jobs.tasks.provisioning.matchiq.MatchIQClient;
import com.reltio.services.pms.service.jobs.tasks.provisioning.matchiq.RIQClient;
import com.reltio.services.pms.service.reltiotenant.RdmTenantsService;
import com.reltio.services.pms.service.reltiotenant.TenantUIConfigService;
import com.reltio.services.pms.service.reltiotenant.TenantsService;
import org.apache.commons.configuration.Configuration;
import org.apache.log4j.Logger;
import org.springframework.http.HttpStatus;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.reltio.services.pms.clients.reltio.cassandra.PhysicalConfigurationHelper.isDataStorageCassandra;
import static com.reltio.services.pms.clients.reltio.cassandra.PhysicalConfigurationHelper.isDataStorageCosmosDB;
import static com.reltio.services.pms.clients.reltio.cassandra.PhysicalConfigurationHelper.isDataStorageDynamoDB;
import static com.reltio.services.pms.clients.reltio.cassandra.PhysicalConfigurationHelper.isDataStorageSpannerDB;

public class CleanTenantExecutionTaskService extends AbstractMultiStepTaskExecutionService<CleanTenantExecutionTaskInstance> {
    private static final Logger LOG = Logger.getLogger(CleanTenantExecutionTaskService.class);
    private static final String CRUD_QUEUE_CLEAN_UP_COMPLETED = "CRUD queue cleanup completed.";
    private static final String MATCH_QUEUE_CLEAN_UP_COMPLETED = "Match queue cleanup completed.";
    private static final String DYNAMO_DB_CLEAN_UP_COMPLETED = "DynamoDB cleanup completed.";
    private static final String SPANNER_DB_CLEAN_UP_COMPLETED = "SpannerDB cleanup completed.";
    private static final String COSMOS_DB_CLEAN_UP_COMPLETED = "CosmosDB cleanup completed.";
    private static final String GRAPH_CLEAN_UP_COMPLETED = "Graph cleanup completed.";
    private static final String STREAMING_DESTINATION_CLEAN_UP_COMPLETED = "Streaming destination cleanup completed.";
    private static final String ACTIVITY_LOG_CLEAN_UP_COMPLETED = "Activity log cleanup completed.";
    private static final String WORKFLOW_CLEAN_UP_COMPLETED = "Workflow cleanup completed.";
    private static final String ELASTIC_SEARCH_CLEAN_UP_COMPLETED = "Elastic search cleanup completed.";
    private static final String CASSANDRA_CLEAN_UP_COMPLETED = "Cassandra cleanup completed.";
    private static final String GBT_CLEAN_UP_COMPLETED = "GBT cleanup completed.";
    private static final String MATCH_IQ_CLEAN_UP_COMPLETED = "Match IQ cleanup completed.";
    private static final String TENANT_PHYSICAL_CONFIG_UPDATED = "Physical config updated.";
    private static final String RETRIEVED_TENANT_PHYSICAL_CONFIG = "Retrieving tenant physical config completed.";
    private static final String IRSQUEUE_CLEAN_UP_COMPLETED = "IRS Queue cleanup completed.";
    private static final String LOOKUP_CLEAN_UP_COMPLETED = "Lookup cleanup completed.";
    private static final String RIQ_CLEAN_UP_COMPLETED = "RIQ cleanup completed.";
    private static final String DELETE_FROM_SYSTEM_KEYSPACE_COMPLETED = "Delete tenant from System keyspace completed.";
    private static final String DELETE_ILM_COMPLETED = "Delete ILM policies completed.";
    private static final String DELETE_UI_COMPLETED = "Delete UI config completed.";
    private static final String DYNAMO_DB_ACCESS_KEY_NAME = "system_keyspace_dynamodb_access_key";
    private static final String DYNAMO_DB_SECRET_KEY_NAME = "system_keyspace_dynamodb_secret_key";
    private static final String SPANNER_DB_GCP_ENCODED_KEY = "system_keyspace_spannerdb_gcp_encoded_key";
    private static final String COSMOS_DB_ACCOUNT_KEY = "system_keyspace_cosmosdb_account_key";
    private static final String AWS_ACCESS_KEY_ID_SYSTEM_PROPERTY = "aws.accessKeyId";
    private static final String AWS_SECRET_KEY_SYSTEM_PROPERTY = "aws.secretKey";
    private static final String GCP_PRIVATE_KEY_SYSTEM_PROPERTY = "GCP_PRIVATE_KEY_ENCODED";
    private static final String SYSTEM_COSMOS_ACCOUNT_KEY = "SYSTEM.COSMOS.KEY";
    private static final String DYNAMO = "dynamo";
    private static final String SPANNER = "spanner";
    private static final String COSMOS = "cosmos";
    private static final String SYSTEM_KEYSPACE_LOCK = "SYSTEM_KEYSPACE_LOCK";
    private static final Set<String> CASSANDRA_HOSTS_PROPS = Set.of("CASSANDRA_HOSTS", "COMMON.CASSANDRA_HOSTS");
    private static final String BASE_AWS_ROLE_ARN = "BASE_AWS_ROLE_ARN";
    private final MDMClient mdmClient;
    private final MDMCleanTenantClient mdmCleanTenantClient;
    private final WorkflowServiceClient workflowServiceClient;
    private final GBTClient gbtClient;
    private final CassandraCleaner cassandraCleaner;
    private final MatchIQClient matchIQClient;
    private final EnvironmentService environmentService;
    private final RIQClient riqClient;
    private final IRSClient irsClient;
    private final boolean isDelete;
    private final JobService jobService;
    private final boolean isFailOnError;
    private final TenantUIConfigService tenantUIConfigService;
    private final AuthDeProvisionHelper authDeProvisionHelper;
    private final RdmTenantsService rdmTenantsService;
    private final SecretService secretService;
    private final Configuration configuration;

    public CleanTenantExecutionTaskService(String envId,
                                           CleanTenantExecutionTaskInstance taskDetail,
                                           GrafanaDashboardGBQService grafanaDashboardGBQService,
                                           MDMClient mdmClient,
                                           WorkflowServiceClient workflowServiceClient,
                                           GBTClient gbtClient,
                                           CassandraCleaner cassandraCleaner,
                                           MatchIQClient matchIQClient,
                                           EnvironmentService environmentService,
                                           RIQClient riqClient,
                                           IRSClient irsClient,
                                           MDMCleanTenantClient mdmCleanTenantClient,
                                           JobService jobService,
                                           TenantUIConfigService tenantUIConfigService,
                                           TenantsService tenantService,
                                           AuthDeProvisionHelper authDeProvisionHelper,
                                           RdmTenantsService rdmTenantsService,
                                           SecretService secretService,
                                           PmsLockService pmsLockService,
                                           Configuration configuration) {
        super(envId, taskDetail, grafanaDashboardGBQService, tenantService, pmsLockService);
        this.mdmClient = mdmClient;
        this.mdmCleanTenantClient = mdmCleanTenantClient;
        this.workflowServiceClient = workflowServiceClient;
        this.gbtClient = gbtClient;
        this.cassandraCleaner = cassandraCleaner;
        this.matchIQClient = matchIQClient;
        this.environmentService = environmentService;
        this.riqClient = riqClient;
        this.irsClient = irsClient;
        this.isDelete = (taskDetail instanceof DeleteMDMTenantExecutionTaskInstance);
        this.jobService = jobService;
        this.isFailOnError = !isDelete || ((DeleteMDMTenantExecutionTaskInstance) taskDetail).isFailOnError();
        this.tenantUIConfigService = tenantUIConfigService;
        this.authDeProvisionHelper = authDeProvisionHelper;
        this.rdmTenantsService = rdmTenantsService;
        this.secretService = secretService;
        this.configuration = configuration;
    }

    @Override
    public void executeTask() {
        LOG.info(String.format("Delete tenant data jobId: %s", taskDetail.getJobId()));
        String tenantId = taskDetail.getTenantName();
        TenantConfiguration tenantConfiguration = mdmClient.getTenantConfiguration(taskDetail.getEnvId(), tenantId);
        if (Objects.nonNull(tenantConfiguration.getMaintenance()) && tenantConfiguration.getMaintenance().equals(Boolean.FALSE)) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INCORRECT_TENANT_STATE,
                    HttpStatus.BAD_REQUEST.value(), tenantId, Boolean.FALSE.toString());
        }
        Set<DataStorageType> storageTypes = getAllStorages(tenantConfiguration);
        if (!taskDetail.getEvents().contains(RETRIEVED_TENANT_PHYSICAL_CONFIG)) {
            addEventToTask(RETRIEVED_TENANT_PHYSICAL_CONFIG);
        }
        try {
            cleanQueues();
            if (!isDelete) {
                cleanAnalyticsCleanup();
                cleanExternalService();
            }
            cleanActivitiesAndHistory(tenantConfiguration);
            cleanPrimary(tenantConfiguration, storageTypes);
            cleanElasticSearchIndices();
            if (taskDetail.getMetaDataCleanUp().isLookUpsClean()) {
                runOneStep(() -> mdmCleanTenantClient.cleanLookup(taskDetail.getEnvId(), tenantId), isFailOnError, LOOKUP_CLEAN_UP_COMPLETED);
            }
            if (isDelete) {
                performDeleteSteps(tenantId);
            } else {
                runOneStep(() -> mdmClient.updateTenantPhysicalConfigOnSpecificDataLoad(taskDetail.getEnvId(), tenantConfiguration, tenantId), isFailOnError, TENANT_PHYSICAL_CONFIG_UPDATED);
            }
        } catch (Exception ex) {
            if(Objects.nonNull(mdmClient.getTenantConfigurationReturnsNullIfTenantNotFound(taskDetail.getEnvId(), tenantId))){
                mdmCleanTenantClient.disableMaintenanceModeForTenant(taskDetail.getEnvId(), tenantId);
            }
            PlatformManagementErrorCode errorCode = isDelete ? PlatformManagementErrorCode.DELETE_MDM_TASK_FAILED : PlatformManagementErrorCode.CLEAN_UP_TASK_FAILED;
            throw new PlatformManagementException(errorCode, HttpStatus.EXPECTATION_FAILED.value(), ex, taskDetail.getTaskId());
        }
        if (!(TaskStatus.FAILED.equals(taskDetail.getStatus()))) {
            taskDetail.setStatus(TaskStatus.COMPLETED);
        }

        if (taskDetail.isQaAutomation()) {
            String jobId = taskDetail.getJobId();
            jobService.updateJobStatusForQA(taskDetail.getEnvId(), jobId, JobStatus.COMPLETED);
        }
    }

    private void performDeleteSteps(String tenantId) {
        runOneStep(() -> mdmClient.deleteILMPolicy(environmentService.getEnvironment(taskDetail.getEnvId()).getUrl(), tenantId), isFailOnError, DELETE_ILM_COMPLETED);
        runOneStep(() -> deleteUIConfig(taskDetail.getEnvId(), tenantId), isFailOnError, DELETE_UI_COMPLETED);
        runOneStep(this::deleteTenantFromPlatform,isFailOnError,DELETE_FROM_SYSTEM_KEYSPACE_COMPLETED);
        List<String> skipUsers = ((DeleteMDMTenantExecutionTaskInstance)taskDetail).getSkipUsers();
        List<String> skipCustomers = ((DeleteMDMTenantExecutionTaskInstance)taskDetail).getSkipCustomers();
        authDeProvisionHelper.removeTenantFromAuthResources(tenantId, taskDetail.getEnvId(), skipUsers, skipCustomers);
        updateTenantStatus(tenantId);
    }

    private void cleanElasticSearchIndices() {
        if (taskDetail.getDataCleanUpOptions().isSecondary()) {
            runOneStep(() -> mdmCleanTenantClient.cleanTenantIndex(taskDetail.getEnvId(), taskDetail.getTenantName()), isFailOnError, ELASTIC_SEARCH_CLEAN_UP_COMPLETED);
        }
    }

    private void cleanGraph() {
        if (taskDetail.getDataCleanUpOptions().isGraphClean()) {
            runOneStep(() -> mdmCleanTenantClient.cleanGraphSafe(taskDetail.getEnvId(), taskDetail.getTenantName()), isFailOnError, GRAPH_CLEAN_UP_COMPLETED);
        }
    }

    private void cleanExternalQueue() {
        if (taskDetail.isExternalQueue() && !taskDetail.getEvents().contains(STREAMING_DESTINATION_CLEAN_UP_COMPLETED)) {
            mdmCleanTenantClient.clearStreamingDestinations(taskDetail.getEnvId(), taskDetail.getTenantName(), isDelete);
            addEventToTask(STREAMING_DESTINATION_CLEAN_UP_COMPLETED);
        }
    }

    private void cleanExternalService() {
        if (taskDetail.getExternalServicesCleanUp().isWorkflow()) {
            runOneStep(() -> workflowServiceClient.terminateProcessInstances(taskDetail.getTenantName(), taskDetail.getEnvId()), isFailOnError, WORKFLOW_CLEAN_UP_COMPLETED);
        }
    }

    private void cleanAnalyticsCleanup() {
        if (taskDetail.getAnalyticsCleanUp().isRiq()) {
            runOneStep(this::doCleanupRiq, isFailOnError, RIQ_CLEAN_UP_COMPLETED);
        }
        if (taskDetail.getAnalyticsCleanUp().isMatchIQ()) {
            runOneStep(this::doCleanMatchIq, isFailOnError, MATCH_IQ_CLEAN_UP_COMPLETED);
        }
    }

    private void doCleanupRiq() {
        if (Objects.isNull(getServiceUrlByType(ServiceType.RI_API))) {
            LOG.warn(String.format("RIQ service is not supported in %s", taskDetail.getEnvId()));
            return;
        }
        riqClient.cleanUpRIQ(getServiceUrlByType(ServiceType.RI_API), taskDetail.getTenantName());
    }

    private void doCleanMatchIq() {
        if (Objects.isNull(getServiceUrlByType(ServiceType.ML))) {
            LOG.warn(String.format("MatchIQ service is not supported in %s", taskDetail.getEnvId()));
            return;
        }
        matchIQClient.cleanMatchIq(getServiceUrlByType(ServiceType.ML), taskDetail.getTenantName());
    }

    private void cleanActivitiesAndHistory(TenantConfiguration tenantConfiguration) {
        if (taskDetail.getDataCleanUpOptions().isActivities()) {
            runOneStep(() -> mdmCleanTenantClient.cleanActivityLog(taskDetail.getTenantName(), environmentService.getEnvironment(taskDetail.getEnvId()).getUrl()), isFailOnError, ACTIVITY_LOG_CLEAN_UP_COMPLETED);
        }
        if (taskDetail.getDataCleanUpOptions().isHistory()) {
            if (tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig() != null &&
                    tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig().getCassandraVersion() != null) {
                runOneStep(() -> cassandraCleaner.cleanCassandraTenantHistory(taskDetail.getEnvId(), tenantConfiguration, true, taskDetail.getTenantName()), isFailOnError, CASSANDRA_CLEAN_UP_COMPLETED);
            } else if (tenantConfiguration.getDataStorageConfig().getHistoryBigTableConfig() != null) {
                String bigTableProjectId = tenantConfiguration.getDataStorageConfig().getHistoryBigTableConfig().getProject();
                String bigTableInstanceId = tenantConfiguration.getDataStorageConfig().getHistoryBigTableConfig().getInstanceId();
                String bigTableZoneName = tenantConfiguration.getDataStorageConfig().getHistoryBigTableConfig().getTableName();

                GcpBigTable gcpBigTable = new GcpBigTable(bigTableProjectId, bigTableInstanceId, bigTableZoneName);
                runOneStep(() -> gbtClient.deleteGBT(gcpBigTable), isFailOnError, GBT_CLEAN_UP_COMPLETED);
            }
        }
    }

    private void cleanQueues() {
        if (taskDetail.getDataCleanUpOptions().isInternalQueue()) {
            runOneStep(() -> mdmCleanTenantClient.clearCrudQueue(taskDetail.getEnvId(), taskDetail.getTenantName(), isDelete), isFailOnError, CRUD_QUEUE_CLEAN_UP_COMPLETED);

            runOneStep(() -> mdmCleanTenantClient.clearMatchQueue(taskDetail.getEnvId(), taskDetail.getTenantName(), isDelete), isFailOnError, MATCH_QUEUE_CLEAN_UP_COMPLETED);

            runOneStep(() -> irsClient.resetIRSTenant(taskDetail.getEnvId(), taskDetail.getTenantName()), isFailOnError, IRSQUEUE_CLEAN_UP_COMPLETED);
        }
        cleanExternalQueue();
    }

    private void cleanPrimary(TenantConfiguration tenantConfiguration, Set<DataStorageType> tenantDataStorages) {
        if (taskDetail.getDataCleanUpOptions().isPrimary()) {
            if (tenantDataStorages.contains(DataStorageType.DYNAMO_DB)) {
                runOneStep(() -> mdmCleanTenantClient.cleanTenantDynamoDBStorage(taskDetail.getEnvId(), taskDetail.getTenantName(), isDelete,
                        false), isFailOnError, DYNAMO_DB_CLEAN_UP_COMPLETED);
            }
            if (tenantDataStorages.contains(DataStorageType.CASSANDRA_EXECUTOR)) {
                runOneStep(() -> cassandraCleaner.cleanup(taskDetail.getEnvId(), tenantConfiguration, isDelete), isFailOnError, CASSANDRA_CLEAN_UP_COMPLETED);
            }
            if (tenantDataStorages.contains(DataStorageType.SPANNER_DB)) {
                runOneStep(() -> {
                    mdmCleanTenantClient.cleanTenantSpannerDBStorage(taskDetail.getEnvId(), taskDetail.getTenantName(),
                            isDelete, false);
                    if (isDelete) {
                        handleSpannerDeletion(tenantConfiguration);
                    }
                }, isFailOnError, SPANNER_DB_CLEAN_UP_COMPLETED);
            }

            if (tenantDataStorages.contains(DataStorageType.COSMOS_DB)) {
                mdmCleanTenantClient.cleanTenantCosmosDBStorage(taskDetail.getEnvId(), taskDetail.getTenantName(),
                        false, false);
                addEventToTask(COSMOS_DB_CLEAN_UP_COMPLETED);
            }
        }
        cleanGraph();
    }

    private void handleSpannerDeletion(TenantConfiguration tenantConfiguration) {
        String spannerDatabase = null;
        if (tenantConfiguration.getDataStorageConfig() != null &&
                tenantConfiguration.getDataStorageConfig().getDataKeyspaceConfig() != null) {
            String dataStorageId = tenantConfiguration.getDataStorageConfig()
                    .getDataKeyspaceConfig().getDataStorageId();
            DataStorageMapping mapping = tenantConfiguration.getDataStorageConfig()
                    .getDataStoragesMap().get(dataStorageId);
            if (mapping != null && mapping.getType() == DataStorageType.SPANNER_DB) {
                spannerDatabase = mapping.getSpannerDBConfig().getDatabaseId();
            }
        }
        if (spannerDatabase != null &&
                spannerDatabase.endsWith(taskDetail.getTenantName().toLowerCase(Locale.ROOT))) {
            mdmCleanTenantClient.deleteSpannerResources(
                    taskDetail.getEnvId(), spannerDatabase
            );
        }
    }

    private Set<DataStorageType> getAllStorages(TenantConfiguration tenantConfiguration) {
        EnumSet<DataStorageType> storageTypes = EnumSet.noneOf(DataStorageType.class);
        TenantDatabaseConfiguration tenantDatabaseConfiguration = tenantConfiguration.getDataStorageConfig();
        CassandraConfigWithDSMapping dataConfig = tenantDatabaseConfiguration.getDataKeyspaceConfig();
        CassandraConfigWithDSMapping matchConfig = tenantDatabaseConfiguration.getMatchKeyspaceConfig();
        if (isDataStorageDynamoDB(tenantDatabaseConfiguration, dataConfig) || isDataStorageDynamoDB(tenantDatabaseConfiguration, matchConfig)) {
            storageTypes.add(DataStorageType.DYNAMO_DB);
        }
        if (isDataStorageSpannerDB(tenantDatabaseConfiguration, dataConfig) || isDataStorageSpannerDB(tenantDatabaseConfiguration,
                matchConfig)) {
            storageTypes.add(DataStorageType.SPANNER_DB);
        }
        if (isDataStorageCassandra(tenantDatabaseConfiguration, dataConfig) || isDataStorageCassandra(tenantDatabaseConfiguration, matchConfig)) {
            storageTypes.add(DataStorageType.CASSANDRA_EXECUTOR);
        }
        if (isDataStorageCosmosDB(tenantDatabaseConfiguration, dataConfig) || isDataStorageCosmosDB(tenantDatabaseConfiguration, matchConfig)) {
            storageTypes.add(DataStorageType.COSMOS_DB);
        }
        return storageTypes;
    }

    protected void addEventToTask(String event) {
        taskDetail.addEvent(event);
        updateTask();
    }

    @Override
    protected boolean isProcessed(String event) {
        return getTaskDetail().getEvents().contains(event);
    }

    /**
     * Delete tenant id and tenant configuration from Platform System keyspace
     */
    private void deleteTenantFromPlatform() {
        String tenantId = taskDetail.getTenantName();
        String environmentName = taskDetail.getEnvId();
        try{
            if(pmsLockService.acquireLock(SYSTEM_KEYSPACE_LOCK,tenantId)){
                JsonNode sysConfig = mdmClient.getEnvironmentSystemConfig(environmentName);
                JsonNode json = sysConfig.get("api_config");
                Map<String, Object> properties = new HashMap<>();
                Iterator<Map.Entry<String, JsonNode>> fields = json.fields();
                while (fields.hasNext()) {
                    Map.Entry<String, JsonNode> field = fields.next();
                    String name = field.getKey();
                    String value = field.getValue().asText();
                    if (CASSANDRA_HOSTS_PROPS.contains(name) && value.startsWith("[")) { // NOSONAR
                        properties.put(name, value.substring(1, value.length() - 1));
                    } else {
                        properties.put(name, value);
                    }
                }
                String systemDb = (String) properties.get("SYSTEM.DEFAULT_DB");
                if(systemDb != null){
                    setDbCredentials(environmentName,properties,systemDb);
                }
                cassandraCleaner.deleteTenantFromSysKeyspace(environmentName,tenantId,properties);
            }
        }finally {
            System.clearProperty(AWS_ACCESS_KEY_ID_SYSTEM_PROPERTY);
            System.clearProperty(AWS_SECRET_KEY_SYSTEM_PROPERTY);
            System.clearProperty(GCP_PRIVATE_KEY_SYSTEM_PROPERTY);
            pmsLockService.releaseLock(SYSTEM_KEYSPACE_LOCK);
        }

    }

    @VisibleForTesting
    void setDbCredentials(String env, Map<String,Object> properties, String systemDb) {
        switch (systemDb.toLowerCase()) {
            case DYNAMO:
                if(AWSRoleFeatureSupportUtil.isFeatureEnabled()){
                    LOG.info("[IRSA TRACE] enabled");
                    properties.put(BASE_AWS_ROLE_ARN, configuration.getString(BASE_AWS_ROLE_ARN, null));
                    break;
                }
                LOG.info("[IRSA TRACE] disabled");
                SecretDto accessKey = secretService.get(env, DYNAMO_DB_ACCESS_KEY_NAME);
                SecretDto secretKey = secretService.get(env, DYNAMO_DB_SECRET_KEY_NAME);
                System.setProperty(AWS_ACCESS_KEY_ID_SYSTEM_PROPERTY, accessKey.getSecret());
                System.setProperty(AWS_SECRET_KEY_SYSTEM_PROPERTY,secretKey.getSecret());
                break;

            case SPANNER:
                SecretDto gcpPrivateKeyEncoded = secretService.get(env, SPANNER_DB_GCP_ENCODED_KEY);
                System.setProperty(GCP_PRIVATE_KEY_SYSTEM_PROPERTY, gcpPrivateKeyEncoded.getSecret());
                break;

            case COSMOS:
                SecretDto cosmosAccountKey = secretService.get(env, COSMOS_DB_ACCOUNT_KEY);
                properties.put(SYSTEM_COSMOS_ACCOUNT_KEY, cosmosAccountKey.getSecret());
                break;

            default:
                break;
        }
    }


    /**
     * Get service url by service type
     *
     * @param serviceType service type to get url for
     * @return service url
     */
    private String getServiceUrlByType(ServiceType serviceType) {
        return environmentService.getEnvironment(taskDetail.getEnvId()).getDefaultUrls().get(serviceType);
    }

    /**
     * Delete UI config for specified tenant. In case tenant does not have UI config print warn to log.
     *
     * @param envId    environment
     * @param tenantId tenant id to delete UI config
     */
    private void deleteUIConfig(String envId, String tenantId) {
        try {
            tenantUIConfigService.deleteUIConfig(envId,Set.of(tenantId), true);
        } catch (PlatformManagementException platformManagementException) {
            if (platformManagementException.getCode().equals(PlatformManagementErrorCode.GENERIC_ERROR) && platformManagementException.getHttpStatus() == HttpStatus.NOT_FOUND.value()) {
                LOG.warn(String.format("UI config for %s@%s was not found", tenantId, envId));
            } else {
                throw platformManagementException;
            }
        }
    }

    private void updateTenantStatus(String tenantId){
        ReltioTenant tenant = tenantsService.getTenant(tenantId);
        RdmProductConfig rdmProduct = (RdmProductConfig) tenant.getAdditionalProducts().get(PMSProductName.RDM);
        if (rdmProduct != null) {
            String rdmTenantId = rdmProduct.getRdmTenantId();
            rdmTenantsService.removeAssociatedMdmTenant(tenantId, rdmTenantId);
        }
        tenant.setTenantStatus(TenantStatus.DELETED);
        tenantsService.updateTenant(tenant);
    }
}
