package com.reltio.services.pms.common.model.compliance.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The type Cp usage count response.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public class CpUsageCountResponse extends CpUsageResponse {
    /**
     * The Tenant match rule entities count total.
     */
    @JsonProperty("cpUsage")
    public String cpUsage;

    /**
     * Instantiates a new Cp usage count response.
     *
     * @param reportDate                        the report date
     * @param environment                       the environment
     * @param tenantId                          the tenant id
     * @param cpUsage the tenant match rule entities count total
     */
    public CpUsageCountResponse(String reportDate, String environment, String tenantId, String cpUsage) {
        super(reportDate, environment, tenantId);
        this.cpUsage = cpUsage;
    }
}
