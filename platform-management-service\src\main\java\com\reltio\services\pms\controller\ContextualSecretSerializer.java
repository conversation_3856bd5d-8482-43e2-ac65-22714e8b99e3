package com.reltio.services.pms.controller;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.reltio.auth.provider.service.security.PermissionManager;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.annotation.Secret;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.jackson.JsonComponent;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;

@JsonComponent
public class ContextualSecretSerializer extends StdSerializer<String> implements ContextualSerializer {
    private static final Logger LOG = Logger.getLogger(ContextualSecretSerializer.class);
    private static final String SHOW_UNMASKED = "showUnmasked";
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final PermissionManager permissionManager;
    private final HttpServletRequest httpServletRequest;
    private final ReltioUserHolder reltioUserHolder;
    private final boolean masked;
    private final Secret annotation;

    @Autowired
    public ContextualSecretSerializer(PermissionManager permissionManager,
                                      HttpServletRequest httpServletRequest,
                                      ReltioUserHolder reltioUserHolder) {

        this(permissionManager, httpServletRequest, reltioUserHolder, false, null);
    }

    public ContextualSecretSerializer(PermissionManager permissionManager,
                                      HttpServletRequest httpServletRequest,
                                      ReltioUserHolder reltioUserHolder,
                                      boolean masked,
                                      Secret annotation) {
        super(String.class);
        this.permissionManager = permissionManager;
        this.httpServletRequest = httpServletRequest;
        this.reltioUserHolder = reltioUserHolder;
        this.masked = masked;
        this.annotation = annotation;
    }


    @Override
    public void serialize(String value, JsonGenerator jgen, SerializerProvider provider) {
        try {
            if (!masked || shouldShowSecrets()) {
                objectMapper.writeValue(jgen, value);
            } else {
                jgen.writeString(annotation.mask());
            }
        }catch (IOException iex) {
            LOG.warn(String.format("Problem writing value -> %s", iex.getMessage()));
        }
    }

    private boolean shouldShowSecrets() {
        return (httpServletRequest.getParameter(SHOW_UNMASKED) != null || httpServletRequest.getHeader(SHOW_UNMASKED) != null) && isAdminUser();
    }

    private boolean isAdminUser() {
        return reltioUserHolder.getUserDetails() != null && permissionManager.hasAdminRole(reltioUserHolder.getUserDetails());
    }

    @Override
    public JsonSerializer<String> createContextual(SerializerProvider config, BeanProperty property) {
        if (null == property) {
            return this;
        }
        Secret propertyAnnotation = property.getAnnotation(Secret.class);
        if (propertyAnnotation == null) {
            propertyAnnotation = property.getContextAnnotation(Secret.class);
        }
        if (propertyAnnotation == null) {
            return this;
        } else {
            return new ContextualSecretSerializer(permissionManager, httpServletRequest, reltioUserHolder, true, propertyAnnotation);
        }
    }

}