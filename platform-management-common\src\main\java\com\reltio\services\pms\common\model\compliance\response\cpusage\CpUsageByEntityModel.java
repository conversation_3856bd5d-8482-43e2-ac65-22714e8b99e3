package com.reltio.services.pms.common.model.compliance.response.cpusage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The type Cp usage by date model.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class CpUsageByEntityModel {

    /**
     * The Date.
     */
    @JsonProperty("date")
    String date;
    /**
     * The Tenant id.
     */
    @JsonProperty("tenantId")
    String tenantId;
    /**
     * The Entity type.
     */
    @JsonProperty("entityType")
    String entityType;
    /**
     * The Entity count.
     */
    @JsonProperty("entityCount")
    String entityCount;
    /**
     * The Customer.
     */
    @JsonProperty("customer")
    String customer;

    /**
     * The Purpose.
     */
    @JsonProperty("purpose")
    String purpose;

}
