package com.reltio.services.pms.common.model.supportability.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.reltio.services.pms.common.model.supportability.Facet;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class EventParams extends Params {
    private final String queueType;

    private final String consumerType;

    public EventParams(Long startTime, Long endTime, String tenantId, String queueType, String consumerType, Facet facet) {
        super(startTime, endTime, tenantId, facet);
        this.queueType = queueType;
        this.consumerType = consumerType;
    }
}
