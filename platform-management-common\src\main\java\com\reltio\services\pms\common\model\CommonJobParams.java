package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import lombok.Getter;

import java.util.Collections;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;

@Getter
@JsonDeserialize(using = CommonJobParamsDeserializer.class)
public class CommonJobParams {
    @JsonProperty("pipelineId")
    private final String pipelineId;
    @JsonProperty("skipTasks")
    private final EnumSet<PMSProductName> skipTasks;
    @JsonProperty("endDate")
    private String endDate;
    @JsonProperty("checkDuplicates")
    private Boolean checkDuplicates;
    @JsonProperty("rih")
    private final Map<String, String> rihKey;
    @JsonProperty("ownedByReltioDept")
    private final String ownedByReltioDept;
    @JsonProperty("shortDescription")
    private final String shortDescription;
    @JsonProperty("customerConfigParams")
    private CustomerConfigParams customerConfigParams;
    @JsonProperty("tenantConfigParams")
    private TenantConfigParams tenantConfigParams;
    @JsonProperty("databaseOptions")
    private DatabaseOptions databaseOptions;
    @JsonProperty("rdmConfigParams")
    private RdmConfigParams rdmConfigParams;
    @JsonProperty("streamingConfigParams")
    private StreamingConfigParams streamingConfigParams;
    @JsonProperty("cleanseConfigParams")
    private CleanseConfigParams cleanseConfigParams;

    @JsonCreator
    public CommonJobParams(@JsonProperty(value="pipelineId",required = true)String pipelineId,
                           @JsonProperty(value = "skipTasks")EnumSet<PMSProductName> skipTasks,
                           @JsonProperty(value = "endDate")String endDate,
                           @JsonProperty(value = "checkDuplicates")Boolean checkDuplicates,
                           @JsonProperty(value = "rih")Map<String,String> rihKey,
                           @JsonProperty(value = "ownedByReltioDept")String ownedByReltioDept,
                           @JsonProperty(value = "shortDescription")String shortDescription,
                           @JsonProperty(value = "customerConfigParams")CustomerConfigParams customerConfigParams,
                           @JsonProperty(value = "tenantConfigParams")TenantConfigParams tenantConfigParams,
                           @JsonProperty(value = "databaseOptions")DatabaseOptions databaseOptions,
                           @JsonProperty(value = "rdmConfigParams")RdmConfigParams rdmConfigParams,
                           @JsonProperty(value = "streamingConfigParams")StreamingConfigParams streamingConfigParams,
                           @JsonProperty(value = "cleanseConfigParams")CleanseConfigParams cleanseConfigParams){
        this.pipelineId=pipelineId;
        this.skipTasks=skipTasks;
        this.endDate=endDate;
        this.checkDuplicates = checkDuplicates != null && checkDuplicates;
        this.rihKey = rihKey == null ? Collections.emptyMap() : rihKey;
        this.ownedByReltioDept=ownedByReltioDept;
        this.shortDescription=shortDescription;
        this.customerConfigParams=customerConfigParams;
        this.tenantConfigParams=tenantConfigParams;
        this.databaseOptions=databaseOptions;
        this.rdmConfigParams=rdmConfigParams;
        this.streamingConfigParams=streamingConfigParams;
        this.cleanseConfigParams=cleanseConfigParams;
    }

    @Getter
    public static class CustomerConfigParams{
        @JsonProperty("customerName")
        private final String customerName;
        @JsonProperty("customerId")
        private final String customerId;
        @JsonProperty("customerType")
        private final CustomerType customerType;
        @JsonProperty("division")
        private final String division;
        @JsonProperty("department")
        private final String department;
        @JsonProperty("costCenter")
        private final String costCenter;

        @JsonCreator
        public CustomerConfigParams(@JsonProperty(value="customerName",required = true)String customerName,
                                    @JsonProperty(value="customerId")String customerId,
                                    @JsonProperty(value="customerType")CustomerType customerType,
                                    @JsonProperty(value="division")String division,
                                    @JsonProperty(value="department")String department,
                                    @JsonProperty(value="costCenter")String costCenter){
            this.customerName=customerName;
            this.customerId=customerId;
            this.customerType=customerType;
            this.division=division;
            this.department=department;
            this.costCenter=costCenter;
        }

    }

    @Getter
    public static class TenantConfigParams{
        @JsonProperty("tenantPurpose")
        private final TenantPurpose tenantPurpose;
        @JsonProperty("tenantSize")
        private final TenantSize tenantSize;
        @JsonProperty("isQaAutomation")
        private final Boolean isQaAutomation;
        @JsonProperty("industry")
        private final String industry;
        @JsonProperty("productEdition")
        private final String productEdition;
        @JsonProperty(value = "reltioPackageType")
        private final ReltioPackageType reltioPackageType;
        @JsonProperty("enableRiq")
        private final boolean enableRiq;
        @JsonProperty(value = "tenantRecordType")
        private final String tenantRecordType;

        @JsonCreator
        public TenantConfigParams(@JsonProperty(value="tenantPurpose",required = true)TenantPurpose tenantPurpose,
                                  @JsonProperty(value="tenantSize",required = true)TenantSize tenantSize,
                                  @JsonProperty(value = "isQaAutomation") Boolean isQaAutomation,
                                  @JsonProperty(value = "industry") String industry,
                                  @JsonProperty(value = "productEdition", required = true) String productEdition,
                                  @JsonProperty(value = "reltioPackageType") ReltioPackageType reltioPackageType,
                                  @JsonProperty(value = "enableRiq") Boolean enableRiq,
                                  @JsonProperty(value = "tenantRecordType", defaultValue = "Customer Tenant") String tenantRecordType){
            this.tenantPurpose = tenantPurpose;
            this.tenantSize = tenantSize;
            this.isQaAutomation =  isQaAutomation != null && isQaAutomation;
            this.industry = industry;
            this.productEdition = productEdition;
            this.reltioPackageType = reltioPackageType == null ? ReltioPackageType.MDM : reltioPackageType;
            this.enableRiq = enableRiq == null ? Boolean.FALSE : enableRiq;
            this.tenantRecordType = tenantRecordType;
        }
    }

    @Getter
    public static class DatabaseOptions{
        @JsonProperty("dataStorageArn")
        private  String dataStorageArn;
        @JsonProperty("matchStorageArn")
        private  String matchStorageArn;
        @JsonProperty("newInstanceId")
        private String newInstanceId;
        @JsonProperty("useSpannerCloudFunction")
        private Boolean useSpannerCloudFunction;

        @JsonCreator
        public DatabaseOptions(@JsonProperty(value = "dataStorageArn") String dataStorageArn,
                               @JsonProperty(value = "matchStorageArn") String matchStorageArn,
                               @JsonProperty(value = "newInstanceId") String newInstanceId,
                               @JsonProperty(value = "useSpannerCloudFunction") Boolean useSpannerCloudFunction){
            this.dataStorageArn = dataStorageArn;
            this.matchStorageArn = matchStorageArn;
            this.useSpannerCloudFunction = useSpannerCloudFunction == null ? Boolean.TRUE : useSpannerCloudFunction;
            this.newInstanceId = newInstanceId;
        }

        public DatabaseOptions(){

        }
    }

    @Getter
    public static class RdmConfigParams{
        @JsonProperty("rdmTenantToReuse")
        private  String rdmTenantToReuse;
        @JsonProperty(value = "rdmHostNameUrl")
        private  String rdmHostNameUrl;

        @JsonCreator
        public RdmConfigParams(@JsonProperty(value = "rdmTenantToReuse") String rdmTenantToReuse,
                               @JsonProperty(value = "rdmHostNameUrl") String rdmHostNameUrl){
            this.rdmTenantToReuse=rdmTenantToReuse;
            this.rdmHostNameUrl=rdmHostNameUrl;
        }

        public RdmConfigParams(){

        }
    }

   @Getter
   public static class StreamingConfigParams{
        @JsonProperty("streamingCloud")
        private  String streamingCloud;
        @JsonProperty("configureDefaultQueue")
        private  boolean configureDefaultQueue;

        @JsonCreator
        public StreamingConfigParams(@JsonProperty(value = "streamingCloud") String streamingCloud,
                                     @JsonProperty(value = "configureDefaultQueue") Boolean configureDefaultQueue){
            this.streamingCloud=streamingCloud;
            this.configureDefaultQueue=configureDefaultQueue == null ? Boolean.TRUE : configureDefaultQueue;
        }

        public StreamingConfigParams(){

        }
    }

    @Getter
    public static class CleanseConfigParams{
        @JsonProperty("cleanseRegions")
        private  List<String> cleanseRegions;
        @JsonProperty("loqateProcesses")
        private  String loqateProcesses;

        @JsonCreator
        public CleanseConfigParams(@JsonProperty(value = "cleanseRegions") List<String> cleanseRegions,
                                   @JsonProperty(value = "loqateProcesses") String loqateProcesses){
            this.cleanseRegions=cleanseRegions == null ? Collections.singletonList("LOQ-NA-NT") : cleanseRegions;
            this.loqateProcesses=loqateProcesses;
        }

        public CleanseConfigParams(){

        }
    }

}
