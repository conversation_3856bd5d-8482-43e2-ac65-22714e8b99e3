package com.reltio.services.pms.common.model.enterprise.contracts;

import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.util.StdConverter;

import java.util.Arrays;

public enum SubscribedProductName {
    CLEANSE("Address Validation and Geocoding"),
    DTSS("Data Connector"),
    RIH("Reltio Integration Hub"),
    SNOWFLAKE("Reltio Connected Data for Snowflake"),
    MULESOFT("Mulesoft Connector"),
    DNB("Reltio D&B Direct 2.0/Direct+ Connector"),
    SFDC("Connected Customer Data for Salesforce"),
    REPORTING("Reltio Reporting"),
    SHIELD("Reltio Shield"),
    MATCHIQ("Match IQ"),
    QUEUES("Queues"),
    ADD_QUEUES("Additional Queues"),
    WORKFLOW("Workflow Processes"),
    ADDITIONAL_WORKFLOW_PROCESSES("Additional Workflow Processes"),
    RDM_DEV("RDM Development Tenant"),
    RDM_PROD("RDM Production Tenant"),
    RDM_TEST("RDM Test Tenant"),
    MDM_DEV("Development Tenant"),
    MDM_TEST("Test Tenant"),
    MDM_PROD("Production Tenant"),
    BASE_PCKG("Base Package"),
    PROFILES_COUNT("Consolidated Profiles"),
    EMBEDDED_UI_WIDGET("Embedded UI Widget"),
    RDM_ENTERPRISE_ADDON("RDM - Enterprise Add-on"),
    ADDITIONAL_DOMAIN("Additional Domain"),
    SNAPSHOT_RESTORE("Set of Tenant Snapshot and Restore"),
    MONTHLY_SNAPSHOT_RESTORE("Set of Tenant Snapshot and Restore (1 Per Month)"),
    FILE_STORAGE("File Storage"),
    ADDITIONAL_FILE_STORAGE("Additional File Storage"),
    TENANT_CLONING("Tenant Cloning"),
    MONTHLY_TENANT_CLONING("Tenant Cloning (1 Per Month)"),
    DATA_APIS("Data APIs (#M/Day)"),
    ADDITIONAL_DATA_APIS("Additional Data APIs"),
    RDM_APIS("RDM For MDM API calls"),
    RSU("Reltio Storage Units (RSU)"),
    ADDITIONAL_RSU("Additional Reltio Storage Units (RSU)"),
    DATA_SCIENCE("Reltio Data Science"),
    ANALYTICS("Reltio Analytics"),
    DATA_SOURCE("Data Source"),
    CASUAL_USERS("Casual User (CU)"),
    MONTHLY_ACTIVE_USERS("Monthly Active Users (MAU)"),
    DATA_GOVERANCE_USERS("Data Governance User (DGU)"),
    TEST_TENANT_PCKG("Test Tenant Package"),
    DEV_TENANT_PCKG("Development Tenant Package"),
    AMA_LICENSE("AMA License Fee"),
    PREMIER_SUPPORT("Premier Support"),
    CONCIERGE_SUPPORT("Concierge Support"),
    STANDARD_SUPPORT("Standard Support"),
    RIH_TASKS("RIH Tasks")
    ;

    private final String value;

    @JsonValue
    public String getValue() {
        return value;
    }

    SubscribedProductName(String value) {
        this.value = value;
    }

    public static SubscribedProductName convertFromString(String value) {
        return Arrays.stream(SubscribedProductName.values())
                .filter(e -> e.value.equals(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid value '" + value + "'"));

    }

    public static final class ServiceTypeConverter extends StdConverter<String, SubscribedProductName> {

        @Override
        public SubscribedProductName convert(String value) {
            return SubscribedProductName.convertFromString(value);
        }
    }
}
