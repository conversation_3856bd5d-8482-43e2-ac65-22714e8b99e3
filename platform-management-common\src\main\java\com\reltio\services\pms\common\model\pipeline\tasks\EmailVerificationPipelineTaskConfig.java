package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class EmailVerificationPipelineTaskConfig extends AbstractPipelineTaskConfig{

    @JsonCreator
    public EmailVerificationPipelineTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.EMAIL_VERIFICATION_TASK);
    }

    @Override
    public String toString() {
        return "FreeTierEmailNotificationPipelineTaskConfig{}";
    }
}
