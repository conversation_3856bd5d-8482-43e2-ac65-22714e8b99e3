package com.reltio.services.pms.common.model.jobs.tasks.streaming.deprovision;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Getter
public class StreamingDeProvisionTaskInstance extends TaskInstance {

    @JsonProperty(value = "tenantId")
    private final String tenantId;

    @JsonProperty(value = "gcpProjectName")
    private final String gcpProjectName;

    @JsonProperty(value = "deleteGcpServiceAccount")
    private final boolean deleteGcpServiceAccount;

    @JsonProperty(value = "streamingCloud")
    private final String streamingCloud;

    @JsonProperty(value = "streamingProviders")
    private final List<String> streamingProviders;

    @JsonProperty(value = "events")
    private final List<String> events;


    @JsonCreator
    public StreamingDeProvisionTaskInstance(@JsonProperty(value = "taskId") String taskId,
                                            @JsonProperty(value = "name") String name,
                                            @JsonProperty(value = "jobId") String jobId,
                                            @JsonProperty(value = "startTime") Long startTime,
                                            @JsonProperty(value = "finishTime") Long finishTime,
                                            @JsonProperty(value = "status") TaskStatus status,
                                            @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                            @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                            @JsonProperty(value = "executingNodeName") String executingNodeName,
                                            @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                            @JsonProperty(value = "envId") String envId,
                                            @JsonProperty(value = "tenantId") String tenantId,
                                            @JsonProperty(value = "gcpProjectName") String gcpProjectName,
                                            @JsonProperty(value = "deleteGcpServiceAccount") Boolean deleteGcpServiceAccount,
                                            @JsonProperty(value = "streamingCloud") String streamingCloud,
                                            @JsonProperty(value = "streamingProviders") List<String> streamingProviders,
                                            @JsonProperty(value = "events") List<String> events) {
        super(taskId, name, jobId, startTime, finishTime, TaskType.STREAMING_DE_PROVISION_TASK, status, lastUpdatedTime,
                taskFailureContext,executingNodeName, serviceNodeStatus, envId);
        this.tenantId = tenantId;
        this.gcpProjectName = gcpProjectName;
        this.deleteGcpServiceAccount = Optional.ofNullable(deleteGcpServiceAccount).orElse(Boolean.FALSE);
        this.streamingCloud = streamingCloud;
        this.streamingProviders = streamingProviders == null ? new ArrayList<>() : streamingProviders;
        this.events = new ArrayList<>(Optional.ofNullable(events).orElse(new ArrayList<>()));
    }

    public void addEvent(String event) {
        events.add(event);
    }
}

