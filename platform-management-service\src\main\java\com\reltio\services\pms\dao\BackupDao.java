package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.sales.model.Backup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collection;

@Service
public class BackupDao extends AbstractRootCollectionDao<Backup> {

    private static final String BACKUP_COLLECTION_NAME = "PMS_BACKUP_DYNAMO";

    private static final String FILTER_FIELD = "sourceTenantId";

    @Autowired
    public BackupDao(CredentialsProvider provider, @Value("${firestore.env.name}") String deployedEnv,ReltioUserHolder reltioUserHolder) {
        super(provider, BACKUP_COLLECTION_NAME, deployedEnv, reltioUserHolder);
    }

    @Override
    protected TypeReference<Backup> getTypeReference() {
        return new TypeReference<>() {
        };
    }

    public Collection<Backup> getBackupDetails(String sourceTenantId, String ticketId) {
        Collection<Backup> backups = getAllFiltered(getBaseCollection(), FILTER_FIELD, sourceTenantId);
        if (ticketId != null) {
            return backups.stream()
                    .filter(backup -> ticketId.equals(backup.getTicketId()))
                    .toList();
        }
        return backups;
    }

}
