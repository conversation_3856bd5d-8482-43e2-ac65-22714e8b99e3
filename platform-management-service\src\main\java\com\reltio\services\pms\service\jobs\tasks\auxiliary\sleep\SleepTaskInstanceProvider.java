package com.reltio.services.pms.service.jobs.tasks.auxiliary.sleep;

import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.jobs.tasks.SleepTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstanceParams;
import com.reltio.services.pms.common.model.pipeline.tasks.SleepPipelineTaskConfig;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.ValidationErrorCollector;
import com.reltio.services.pms.service.jobs.tasks.TaskInstanceProvider;
import org.apache.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class SleepTaskInstanceProvider implements TaskInstanceProvider<SleepTaskInstance, SleepPipelineTaskConfig, TaskInstanceParams> {

    @Override
    public TaskType getTaskType() {
        return TaskType.SLEEP_TASK;
    }

    @Override
    public void validateTaskConfig(String envId, SleepPipelineTaskConfig pipelineTaskConfig, ValidationErrorCollector validationErrorCollector) {
        if (pipelineTaskConfig.getSleepTime() < 0) {
            validationErrorCollector.addValidationError(new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.SC_INTERNAL_SERVER_ERROR));
        }
    }

    @Override
    public SleepTaskInstance getTaskDetail(String envId, String jobId, SleepPipelineTaskConfig pipelineTaskConfig, TaskInstanceParams onInput) {
        return new SleepTaskInstance(
                UUID.randomUUID().toString(),
                TaskType.SLEEP_TASK.toString(),
                jobId,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                pipelineTaskConfig.getSleepTime(),
                envId
        );
    }
}
