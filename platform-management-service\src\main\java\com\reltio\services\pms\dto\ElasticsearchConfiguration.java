package com.reltio.services.pms.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ElasticsearchConfiguration {
    private String hosts;
    private String clusterName;
    private Integer numberOfShards;
    private Integer numberOfRelationsShards;
    private Integer numberOfActivitiesShards;
    private Integer numberOfInteractionsShards;
    private Integer numberOrReplicas;
    private Boolean indexActivityDelta;

    @JsonCreator
    public ElasticsearchConfiguration(
            @JsonProperty(value = "hosts", required = true) String hosts,
            @JsonProperty(value = "clusterName", required = true) String clusterName,
            @JsonProperty(value = "numberOfShards", required = true) Integer numberOfShards,
            @JsonProperty(value = "numberOfRelationsShards") Integer numberOfRelationsShards,
            @JsonProperty(value = "numberOfActivitiesShards") Integer numberOfActivitiesShards,
            @JsonProperty(value = "numberOfInteractionsShards") Integer numberOfInteractionsShards,
            @JsonProperty(value = "numberOrReplicas", required = true) Integer numberOrReplicas,
            @JsonProperty(value = "indexActivityDelta", required = true) Boolean indexActivityDelta) {
        this.hosts = hosts;
        this.clusterName = clusterName;
        this.numberOfShards = numberOfShards;
        this.numberOfRelationsShards = numberOfRelationsShards;
        this.numberOfActivitiesShards = numberOfActivitiesShards;
        this.numberOfInteractionsShards = numberOfInteractionsShards;
        this.numberOrReplicas = numberOrReplicas;
        this.indexActivityDelta = indexActivityDelta;
    }
}
