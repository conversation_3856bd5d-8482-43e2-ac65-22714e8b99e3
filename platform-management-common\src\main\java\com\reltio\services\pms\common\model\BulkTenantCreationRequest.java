package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import lombok.Getter;

import java.util.Collections;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Getter
public class BulkTenantCreationRequest extends TenantCreationRequest {

    private CommonJobParams commonJobParams;

    private Set<String> owners;

    @JsonCreator
    public BulkTenantCreationRequest(@JsonProperty(value = "commonJobParams") CommonJobParams commonJobParams,
                                     @JsonProperty(value = "owners") Set<String> owners){
        super(commonJobParams.getPipelineId(), commonJobParams.getTenantConfigParams().getTenantSize(),null,commonJobParams.getTenantConfigParams().getIsQaAutomation(),commonJobParams.getDatabaseOptions().getNewInstanceId(),
                commonJobParams.getDatabaseOptions().getUseSpannerCloudFunction(),commonJobParams.getTenantConfigParams().getReltioPackageType(),commonJobParams.getSkipTasks());
        this.commonJobParams=commonJobParams;
        this.owners = owners == null ? Collections.emptySet() : owners;
    }

}
