package com.reltio.services.pms.common.model.jobs.tenantssync;

import com.reltio.services.pms.common.model.jobs.tasks.tenants.sync.TenantsSyncTaskInstanceParams;
import com.reltio.services.pms.common.sales.TenantPurpose;

import java.util.List;

public class TenantsSyncJobInstanceParams implements TenantsSyncTaskInstanceParams {

    private final TenantPurpose tenantPurpose;
    private final List<String> tenantsList;
    private final boolean processAllTenants;

    public TenantsSyncJobInstanceParams(TenantPurpose tenantPurpose, List<String> tenantsList, boolean processAllTenants) {
        this.tenantPurpose = tenantPurpose;
        this.tenantsList = tenantsList;
        this.processAllTenants = processAllTenants;
    }

    @Override
    public TenantPurpose getTenantPurpose() {
        return tenantPurpose;
    }

    @Override
    public List<String> getTenantsList() {
        return tenantsList;
    }

    @Override
    public boolean isProcessAllTenants() {
        return processAllTenants;
    }
}
