package com.reltio.services.pms.common.model.compliance.response.billingreport;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;


/**
 * The type Billing report by customer response.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class BillingReportByCustomerResponse {
    /**
     * The Billing report by customer model list.
     */
    @JsonProperty("billingReportByCustomerModel")
    List<BillingReportByCustomerModel> billingReportByCustomerModelList;


    /**
     * The Total count.
     */
    @JsonProperty("totalCount")
    Long totalCount;
}
