package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class UpdateR360TenantPipelineTaskConfig extends AbstractPipelineTaskConfig{


    @JsonCreator
    public UpdateR360TenantPipelineTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.UPDATE_R360_TENANT_TASK);
    }

    @Override
    public String toString() {
        return "UPDATE_R360_TENANT_TASK{}";
    }
}
