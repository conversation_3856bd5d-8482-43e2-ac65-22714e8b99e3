package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;

public class RdmTaskConfigBuilder {

    private String productEdition;
    private RdmCache rdmCache;
    private Boolean publishRDMErrors ;
    private Boolean trackTranscodeErrors;
    private String name;

    @JsonCreator
    public RdmTaskConfigBuilder(
            String name) {
        this.name = name;
    }

    public RdmTaskConfigBuilder setProductEdition(String productEdition) {
        this.productEdition = productEdition;
        return this;
    }

    public RdmTaskConfigBuilder setRdmCache(RdmCache rdmCache) {
        this.rdmCache = rdmCache;
        return this;
    }

    public RdmTaskConfigBuilder setPublishRDMErrors(Boolean publishRDMErrors) {
        this.publishRDMErrors = publishRDMErrors;
        return this;
    }

    public RdmTaskConfigBuilder setTrackTranscodeErrors(Boolean trackTranscodeErrors) {
        this.trackTranscodeErrors = trackTranscodeErrors;
        return this;
    }

    public RdmTaskConfig build() {
        return new RdmTaskConfig(this.name, this.productEdition, this.rdmCache,
                this.publishRDMErrors, this.trackTranscodeErrors);
    }
}
