package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.api.core.ApiFuture;
import com.google.cloud.firestore.CollectionReference;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.SetOptions;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.Query;
import com.google.cloud.firestore.QuerySnapshot;
import com.google.cloud.firestore.WriteBatch;
import com.google.common.collect.Iterators;
import com.reltio.auth.ReltioUser;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import com.reltio.services.pms.common.sales.model.SalesAccount;
import org.springframework.http.HttpStatus;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.model.ProductEdition;
import com.reltio.services.pms.common.sales.model.ReltioContract;
import com.reltio.services.pms.common.model.Requester;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.Map;
import java.util.List;
import java.util.Arrays;

/**
 * This Abstract class provides common functions needed by all the level of document collections in the Firestore DAOs.
 */
public abstract class AbstractDao<T extends BaseFirestoreEntity> {

    private final ObjectMapper mapper = new ObjectMapper();

    private final ReltioUserHolder reltioUserHolder;

    protected AbstractDao(ReltioUserHolder reltioUserHolder) {
        this.reltioUserHolder = reltioUserHolder;
        mapper.registerModule(new JavaTimeModule());
    }


    protected T create(CollectionReference collectionReference, T entity) {
        String username = getUsername();
        Long time = System.currentTimeMillis();
        entity.setCreatedBy(username);
        entity.setCreatedTime(time);
        entity.setUpdatedBy(username);
        entity.setUpdatedTime(time);
        return createOrUpdate(collectionReference, entity);
    }

    protected T update(CollectionReference collectionReference, T entity) {
        String username = getUsername();
        Long time = System.currentTimeMillis();
        entity.setUpdatedBy(username);
        entity.setUpdatedTime(time);
        return createOrUpdate(collectionReference, entity);
    }


    private T createOrUpdate(CollectionReference collectionReference, T entity) {
        try {
            String id = entity.getID();
            Map<String, Object> objectMap = convertToMap(entity);
            collectionReference.document(id).set(objectMap, SetOptions.merge()).get();
            return entity;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    protected T override(CollectionReference collectionReference, T entity) {
        try {
            String id = entity.getID();
            Map<String, Object> objectMap = convertToMap(entity);
            collectionReference.document(id).set(objectMap).get();
            return entity;
        } catch (Exception e) {
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }


    protected T get(CollectionReference collectionReference, String docId) throws InvalidDocumentIdException {
        DocumentSnapshot documentSnapshot = getDocumentSnapshot(collectionReference, docId);
        if (!documentSnapshot.exists()) {
            throw new InvalidDocumentIdException("Document ID not exists in the Database: " + docId);
        }
        Map<String, Object> data = documentSnapshot.getData();
        return convertToObject(data);
    }

    protected boolean isExists(CollectionReference collectionReference, String docId) {
        if (docId.isEmpty()) {
            throw new PlatformManagementException(PlatformManagementErrorCode.DOC_ID_IS_EMPTY, HttpStatus.OK.value(), docId);
        }
        return getDocumentSnapshot(collectionReference, docId).exists();
    }

    private DocumentSnapshot getDocumentSnapshot(CollectionReference collectionReference, String docId) {
        try {
            return collectionReference.document(docId).get().get();
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
        }
    }

    protected Collection<T> getAll(CollectionReference collectionReference) {
        try {
            Iterator<DocumentReference> iterator = collectionReference.listDocuments().iterator();
            Collection<T> result = new ArrayList<>();

            while (iterator.hasNext()) {
                Map<String, Object> data = iterator.next().get().get().getData();
                if (data != null) {
                    result.add(convertToObject(data));
                }
            }
            return result;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    protected Iterator<T> getIterator(CollectionReference collectionReference) {
        return Iterators.transform(collectionReference.listDocuments().iterator(), e -> {
            try {
                return this.convertToObject(e != null ? e.get().get().getData() : null);
            } catch (Exception ex) {
                throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
            }
        });
    }

    protected void delete(CollectionReference collectionReference, String id) {
        try {
            collectionReference.document(id).delete().get();
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    protected final T convertToObject(Map<String, Object> data) {
        return mapper.convertValue(data, getTypeReference());
    }

    protected abstract TypeReference<T> getTypeReference();

    protected final Map<String, Object> convertToMap(Object bean) {
        Map<String, Object> entityMap=mapper.convertValue(bean, new TypeReference<Map<String, Object>>() {});
        List<Class<?>> reltioPackageTypeClassList = Arrays.asList(ReltioTenant.class, ProductEdition.class,
                ReltioContract.class, Requester.class, SalesAccount.class);
        if(reltioPackageTypeClassList.contains(bean.getClass())){
            entityMap.putIfAbsent("reltioPackageType", ReltioPackageType.MDM);
        }
        return entityMap;
    }

    protected Collection<T> getResultFromQuery(Query query) {
        Collection<T> entities = new ArrayList<>();
        try {
            ApiFuture<QuerySnapshot> querySnapshot = query.get();
            for (DocumentSnapshot document : querySnapshot.get().getDocuments()) {
                Map<String, Object> map = document.getData();
                entities.add(convertToObject(map));
            }
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
        return entities;
    }

    protected Long getCountFromQuery(Query query) {
        try {
            return query.count().get().get().getCount();
        } catch (InterruptedException e) {
            // Restore interrupted status
            Thread.currentThread().interrupt();
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    protected String getUsername() {
        ReltioUser reltioUser = reltioUserHolder.getUserDetails();
        return reltioUser != null ? reltioUser.getUsername() : null;
    }

    protected Collection<T> getAllWithSizeAndOffset(CollectionReference collectionReference, Integer size, Integer offset) {
        try {
            Query query = collectionReference.limit(size).offset(offset);
            Collection<T> result = new ArrayList<>();
            ApiFuture<QuerySnapshot> querySnapshot = query.get();
            for (DocumentSnapshot document : querySnapshot.get().getDocuments()) {
                Map<String, Object> map = document.getData();
                result.add(convertToObject(map));
            }
            return result;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    protected Collection<T> getFilteredWithSizeAndOffset(CollectionReference collectionReference, String filterField, String value, Integer size, Integer offset) {
        try {
            Query query = collectionReference.whereGreaterThanOrEqualTo(filterField, value).whereLessThan(filterField, value.concat("\uf8ff")).limit(size).offset(offset);
            Collection<T> result = new ArrayList<>();
            ApiFuture<QuerySnapshot> querySnapshot = query.get();
            for (DocumentSnapshot document : querySnapshot.get().getDocuments()) {
                Map<String, Object> map = document.getData();
                result.add(convertToObject(map));
            }
            return result;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    protected Collection<T> getAllFiltered(CollectionReference collectionReference, String filterField, String value) {
        try {
            Query query = collectionReference.whereGreaterThanOrEqualTo(filterField, value).whereLessThan(filterField, value.concat("\uf8ff"));
            Collection<T> result = new ArrayList<>();
            ApiFuture<QuerySnapshot> querySnapshot = query.get();
            for (DocumentSnapshot document : querySnapshot.get().getDocuments()) {
                Map<String, Object> data = document.getData();
                result.add(convertToObject(data));
            }
            return result;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    protected Collection<T> getAllFilteredByFeaturesWithSizeAndOffset(CollectionReference collectionReference, Map<String, String> features, Integer size, Integer offset) {
        try {
            Query query = collectionReference;
            for (Map.Entry<String, String> feature : features.entrySet()) {
                query = query.whereEqualTo(feature.getKey(), feature.getValue());
            }
            query = query.offset(offset).limit(size);
            Collection<T> result = new ArrayList<>();
            ApiFuture<QuerySnapshot> querySnapshot = query.get();
            for (DocumentSnapshot document : querySnapshot.get().getDocuments()) {
                Map<String, Object> data = document.getData();
                result.add(convertToObject(data));
            }
            return result;
        } catch (Exception e) {
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
        }
    }


    protected Collection<T> getFilteredByBooleanField(CollectionReference collectionReference, String filterField, boolean value) {
        try {
            Query query = collectionReference.whereEqualTo(filterField, value);
            Collection<T> result = new ArrayList<>();
            ApiFuture<QuerySnapshot> querySnapshot = query.get();
            for (DocumentSnapshot document : querySnapshot.get().getDocuments()) {
                Map<String, Object> data = document.getData();
                result.add(convertToObject(data));
            }
            return result;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    protected List<T> batch(CollectionReference collectionReference, WriteBatch batch, List<T> entityList) {
        List<T> updatedRecordsList = new ArrayList<>();
        try {
            for (T entity : entityList) {
                DocumentReference documentReference = collectionReference.document(entity.getID());
                if (getDocumentSnapshot(collectionReference, entity.getID()).exists()) {
                    batch.update(documentReference, convertToMap(entity));
                }
                updatedRecordsList.add(entity);
            }
            batch.commit();
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
        return updatedRecordsList;
    }

}
