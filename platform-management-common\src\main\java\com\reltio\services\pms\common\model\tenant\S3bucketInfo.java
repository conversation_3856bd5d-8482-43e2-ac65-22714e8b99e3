package com.reltio.services.pms.common.model.tenant;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

public class S3bucketInfo {


    @JsonProperty(value = "accessKey")
    private String accessKey;

    @JsonProperty(value = "secretKey")
    private String secretKey;

    public String getAccessKey() {
        return accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    @JsonProperty(value = "region")
    private String region;

    @JsonProperty(value = "bucketName")
    private String bucketName;

    public String getSecret() {
        return accessKey;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof S3bucketInfo)) return false;
        S3bucketInfo that = (S3bucketInfo) o;
        return getSecret().equals(that.getSecret()) && getRegion().equals(that.getRegion()) && getBucketName().equals(that.getBucketName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getSecret(), getRegion(), getBucketName());
    }

    public S3bucketInfo(@JsonProperty(value = "secret") String secretKey,@JsonProperty(value = "region") String region,@JsonProperty(value = "bucketName") String bucketName, @JsonProperty(value = "secretKey") String accessKey) {
        this.secretKey = secretKey;
        this.region = region;
        this.bucketName = bucketName;
        this.accessKey = accessKey;
    }

    public String getRegion() {
        return region;
    }

    public String getBucketName() {
        return bucketName;
    }


}
