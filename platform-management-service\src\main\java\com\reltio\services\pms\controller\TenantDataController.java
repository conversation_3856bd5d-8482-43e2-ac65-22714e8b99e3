package com.reltio.services.pms.controller;

import com.fasterxml.reltio.jackson.databind.ObjectMapper;
import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.auth.domain.ReltioPrivileges;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.CleanTenantJobCreationRequest;
import com.reltio.services.pms.common.model.jobs.CleanTenantJob;
import com.reltio.services.pms.common.model.jobs.Job;
import com.reltio.services.pms.common.model.jobs.JobStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.combined.CleanTenantTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.separated.execution.CleanTenantExecutionTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.SalesAccount;
import com.reltio.services.pms.service.SalesAccountService;
import com.reltio.services.pms.service.jobs.CleanTenantJobService;
import com.reltio.services.pms.service.jobs.TaskService;
import com.reltio.services.pms.service.jobs.tasks.GenericTaskExecutionService;
import com.reltio.services.pms.service.reltiotenant.TenantsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.log4j.Logger;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping(value = "/api/v1/tdm/environments/{envId}", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name= "Tenant Data Management")
public class TenantDataController {
    private static final String STATUS = "status";
    private static final String DURATION = "duration";
    private static final Logger LOG = Logger.getLogger(TenantDataController.class);
    private static final long MAX_DURATION_BETWEEN_CLEAUP_JOB = 30L;
    private final CleanTenantJobService cleanTenantJobCreationService;
    private final GenericTaskExecutionService genericTaskExecutionService;
    private final TenantsService tenantsService;
    private final TaskService cleanTenantTaskService;
    private final SalesAccountService salesAccountService;
    ObjectMapper mapper = new ObjectMapper();

    @Autowired
    public TenantDataController(CleanTenantJobService cleanTenantJobCreationService,
                                GenericTaskExecutionService genericTaskExecutionService,
                                TenantsService tenantsService,
                                TaskService cleanTenantTaskService,
                                SalesAccountService salesAccountService) {
        this.cleanTenantJobCreationService = cleanTenantJobCreationService;
        this.genericTaskExecutionService = genericTaskExecutionService;
        this.tenantsService = tenantsService;
        this.cleanTenantTaskService = cleanTenantTaskService;
        this.salesAccountService = salesAccountService;
    }

    @PostMapping(value = "/deleteTenantData", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.Jobs.TenantCleanup.Customer.class)
    public CleanTenantJob createCleanTenantJob(@RequestBody @Valid CleanTenantJobCreationRequest createJobRequest, @PathVariable String envId) {
        checkEligiblityForCreatingJob(createJobRequest.getTenantName(), envId);
        if (!tenantsService.isTenantExist(createJobRequest.getTenantName())) {
            throw new PlatformManagementException(PlatformManagementErrorCode.TENANT_SYNC_REQUIRED, HttpStatus.NOT_FOUND.value());
        }
        ReltioTenant reltioTenant = tenantsService.getTenant(createJobRequest.getTenantName());
        if (reltioTenant.getTenantPurpose() == TenantPurpose.PROD) {
            throw new PlatformManagementException(PlatformManagementErrorCode.PROD_TENANT_DATA_DELETION_NOT_SUPPORTED, HttpStatus.BAD_REQUEST.value());
        }
        //TODO Fix in next Phase
        String contractId = reltioTenant.getContractId();
        SalesAccount salesAccount = null;
        try {
            salesAccount = salesAccountService.getCustomerByContractId(contractId);
        } catch (PlatformManagementException e) {
            LOG.warn(e.getMessage());
        }
        if (salesAccount != null && !salesAccount.isCleanableFlag()) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CLEANABILITY_NOT_ENABLED, HttpStatus.METHOD_NOT_ALLOWED.value(), salesAccount.getAccountId());
        }

        return cleanTenantJobCreationService.createJob(envId, createJobRequest, EnumSet.noneOf(PMSProductName.class),
                JobStatus.SCHEDULED);
    }


    @PostMapping(value = "/deleteTenantQAData", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.Jobs.TenantCleanup.Customer.class)
    public CleanTenantJob deleteTenantQAData(@RequestBody @Valid CleanTenantJobCreationRequest createJobRequest, @PathVariable String envId, @RequestParam boolean executeJob) {
        CleanTenantJob cleanTenantJob = cleanTenantJobCreateRequest(createJobRequest, envId, true);
        LOG.error("createJobRequest"+createJobRequest.isQaAutomation());
        if (executeJob) {
            String jobId = cleanTenantJob.getJobId();
            String taskId = cleanTenantJob.getTasks().get(0);
            genericTaskExecutionService.executeTask(envId, jobId, taskId);
        }
        return cleanTenantJob;
    }

    @PostMapping(value = "/deleteTenantData2", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.Jobs.TenantCleanup.Admin.class, privileges = ReltioPrivileges.CREATE)
    public CleanTenantJob createCleanTenantJob2(@RequestBody @Valid CleanTenantJobCreationRequest createJobRequest, @PathVariable String envId) {
        return cleanTenantJobCreateRequest(createJobRequest, envId, false);
    }

    private CleanTenantJob cleanTenantJobCreateRequest(CleanTenantJobCreationRequest createJobRequest, String envId, boolean skipEligibility) {
        if (!skipEligibility) {
            checkEligiblityForCreatingJob(createJobRequest.getTenantName(), envId);
        }
        if (!tenantsService.isTenantExist(createJobRequest.getTenantName())) {
            throw new PlatformManagementException(PlatformManagementErrorCode.TENANT_SYNC_REQUIRED, HttpStatus.NOT_FOUND.value());
        }

        if (tenantsService.getTenant(createJobRequest.getTenantName()).getTenantPurpose() == TenantPurpose.PROD) {
            throw new PlatformManagementException(PlatformManagementErrorCode.PROD_TENANT_DATA_DELETION_NOT_SUPPORTED, HttpStatus.BAD_REQUEST.value());
        }
        return cleanTenantJobCreationService.createJob(envId, createJobRequest, EnumSet.noneOf(PMSProductName.class), JobStatus.SCHEDULED);
    }

    @PutMapping(value = "/jobs/{jobId}/tasks/{taskId}/_execute")
    @ReltioSecured(resourceClass = Pms.Environment.Jobs.TenantCleanup.Customer.class, privileges = ReltioPrivileges.EXECUTE)
    public TaskInstance executeTenantCleanTask(@PathVariable String jobId, @PathVariable String taskId, @PathVariable String envId) {
        return genericTaskExecutionService.executeTask(envId, jobId, taskId);
    }

    @PutMapping(value = "/jobs/{jobId}/_reExecute")
    @ReltioSecured(resourceClass = Pms.Environment.Jobs.TenantCleanup.Admin.class, privileges = ReltioPrivileges.EXECUTE)
    public TaskInstance reExecuteTenantCleanTask(@PathVariable String jobId,
                                                 @PathVariable String envId) {
        Set<String> clenTaskIds = cleanTenantJobCreationService.getTasksByType(envId, jobId, TaskType.DELETE_TENANT_DATA_EXECUTION_TASK);
        Set<String> validationTaskIds = cleanTenantJobCreationService.getTasksByType(envId, jobId,
                TaskType.DELETE_TENANT_DATA_VALIDATION_TASK);
        if (validationTaskIds != null && !validationTaskIds.isEmpty()) {
            if (!clenTaskIds.isEmpty()) {
                genericTaskExecutionService.rescheduleDependentTasks(envId, jobId, clenTaskIds);
            }
            return genericTaskExecutionService.reExecuteTask(envId, jobId, validationTaskIds.iterator().next(),
                    cleanTenantJobCreationService);
        } else {
            return genericTaskExecutionService.reExecuteTask(envId, jobId, clenTaskIds.iterator().next(),
                    cleanTenantJobCreationService);
        }
    }

    @GetMapping(value = "/jobs/{jobId}/tasks/{taskId}")
    @ReltioSecured(resourceClass = Pms.Environment.Jobs.TenantCleanup.class, privileges = ReltioPrivileges.READ)
    public JSONObject getTask(@PathVariable String jobId, @PathVariable String taskId, @PathVariable String envId) {
        CleanTenantTaskInstance task = (CleanTenantTaskInstance) cleanTenantTaskService.getTask(envId, jobId, taskId);
        JSONObject taskJson = mapper.convertValue(task, JSONObject.class);
        taskJson.put(DURATION, task.taskDuration());
        return getFormattedTask(taskJson);
    }

    @GetMapping(value = "/lastCleanUpJob/tenant/{tenantId}")
    public JSONObject getLastCleanupJob(@PathVariable(value = "envId") String envId, @PathVariable(value = "tenantId") String tenantId) {
        Job job = cleanTenantJobCreationService.getLastCleanupJob(envId, tenantId);
        if (job == null) {
            return new JSONObject();
        }
        TaskInstance task = cleanTenantTaskService.getAllTasks(envId, job.getJobId()).stream()
                .filter(taskInstance -> Arrays.asList(TaskType.DELETE_TENANT_DATA_TASK,
                        TaskType.DELETE_TENANT_DATA_EXECUTION_TASK).contains(taskInstance.getType())).findFirst()
                .orElseThrow(() -> new PlatformManagementException(PlatformManagementErrorCode.TASK_NOT_FOUND, HttpStatus.NOT_FOUND.value()));
        JSONObject taskJson;
        long duration;
        if (task.getType() == TaskType.DELETE_TENANT_DATA_TASK) {
            CleanTenantTaskInstance cleanTask = (CleanTenantTaskInstance) cleanTenantTaskService.getTask(envId,
                    task.getJobId(), task.getTaskId());
            duration = cleanTask.taskDuration();
            taskJson = mapper.convertValue(cleanTask, JSONObject.class);
        } else {
            CleanTenantExecutionTaskInstance cleanTask = (CleanTenantExecutionTaskInstance) cleanTenantTaskService.getTask(envId,
                    task.getJobId(), task.getTaskId());
            duration = cleanTask.taskDuration();
            TaskStatus taskStatusToSet = TaskStatus.fromJob(job.getStatus());
            cleanTask.setStatus(taskStatusToSet);
            taskJson = mapper.convertValue(cleanTask, JSONObject.class);
        }
        taskJson.put(DURATION, duration);
        return getFormattedTask(taskJson);
    }

    @DeleteMapping(value = "/{serviceName}")
    public Map<String, String> deleteServiceNode(@PathVariable String envId, @PathVariable String serviceName) {
        Map<String, String> statusMap = new HashMap<>();
        cleanTenantTaskService.deleteServiceNode(serviceName);
        statusMap.put("serviceName", serviceName);
        statusMap.put(STATUS, "deleted");
        return statusMap;
    }

    private JSONObject getFormattedTask(JSONObject task) {

        JSONObject taskJson = mapper.convertValue(task, JSONObject.class);
        taskJson.remove("type");
        taskJson.put("type", "com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.CleanTenantTaskInstance");
        if ("INPROGRESS".equals(taskJson.get(STATUS))) {
            taskJson.remove(STATUS);
            taskJson.put(STATUS, "PROCESSING");
        }
        long duration = Long.parseLong(task.get(DURATION).toString());
        String formattedDuration = String.format("%dm %ds",
                TimeUnit.MILLISECONDS.toSeconds(duration) / 60,
                TimeUnit.MILLISECONDS.toSeconds(duration) % 60);
        taskJson.remove(DURATION);
        taskJson.put(DURATION, formattedDuration);
        taskJson.put("endTime", task.get("finishTime"));
        taskJson.remove("finishTime");
        return taskJson;
    }

    private void checkEligiblityForCreatingJob(String tenantId, String envId) {
        JSONObject lastCleanupJob = getLastCleanupJob(envId, tenantId);
        if (!lastCleanupJob.isEmpty()) {
            long createdTime = (long) lastCleanupJob.get("createdTime");
            long minuteDifference = TimeUnit.MILLISECONDS.toMinutes(System.currentTimeMillis() - createdTime);
            if (minuteDifference <= MAX_DURATION_BETWEEN_CLEAUP_JOB && !lastCleanupJob.get(STATUS).equals("FAILED")) {
                throw new PlatformManagementException(PlatformManagementErrorCode.CANNOT_CREATE_JOB_EARLY, HttpStatus.NOT_ACCEPTABLE.value());
            }
        }
    }
}
