package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Rih {

    @JsonProperty("manifests")
    private final List<String> manifests;

    @JsonProperty("connections")
    private final List<RihConnection> connections;

    public Rih(@JsonProperty("manifests") List<String> manifests, @JsonProperty("connections") List<RihConnection> connections) {
        this.manifests = manifests;
        this.connections = connections;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }
        Rih that = (Rih) o;
        return Objects.equals(getManifests(), that.getManifests()) && Objects.equals(getConnections(), that.getConnections());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getManifests(), getConnections());
    }

    public List<String> getManifests() {
        return manifests;
    }

    public List<RihConnection> getConnections() {
        return connections;
    }
}
