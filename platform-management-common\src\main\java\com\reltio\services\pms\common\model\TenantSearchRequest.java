package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.TenantStatus;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class TenantSearchRequest {
    @JsonProperty("enabledProducts")
    List<PMSProductName> enabledProducts;
    @JsonProperty("tenantId")
    private String tenantId;
    @JsonProperty("tenantName")
    private String tenantName;
    @JsonProperty("accountId")
    private String accountId;
    @JsonProperty("subscriptionId")
    private String subscriptionId;
    @JsonProperty("salesConfig")
    private Map<String, Object> salesConfig;
    @JsonProperty("tenantPurpose")
    private TenantPurpose tenantPurpose;
    @JsonProperty("reltioEnv")
    private String reltioEnv;
    @JsonProperty("deploymentCloud")
    private String deploymentCloud;
    @JsonProperty("deploymentRegion")
    private String deploymentRegion;
    @JsonProperty("isHipaa")
    private String isHipaa;
    @JsonProperty("industry")
    private String industry;
    @JsonProperty("productEdition")
    private String productEdition;
    @JsonProperty("companyName")
    private String companyName;
    @JsonProperty("authCustomerId")
    private String authCustomerId;
    @JsonProperty("contractId")
    private String contractId;
    @JsonProperty("packageId")
    private String packageId;
    @JsonProperty("tenantInternalInfo")
    private Map<String, Object> tenantInternalInfo;
    @JsonProperty("tenantConfigurationValues")
    private Map<String, Object> tenantConfigurationValues;
    @JsonProperty("tenantStatus")
    private TenantStatus tenantStatus;

    public Map<String, String> createFeatureMap() {
        Map<String, String> features = new HashMap<>();
        if (enabledProducts != null && !enabledProducts.isEmpty()){
            for (PMSProductName product : enabledProducts) {
                features.put(String.format("additionalProducts.%s.provisioningStatus", product.toString()), "ENABLED");
            }
        }
        features = addIfNotNull(features, "tenantId", tenantId);
        features = addIfNotNull(features, "tenantName", tenantName);
        features = addIfNotNull(features, "accountId", accountId);
        features = addIfNotNull(features, "subscriptionId", subscriptionId);
        if (salesConfig != null && !salesConfig.isEmpty()) {
            for (Map.Entry<String, Object> salesConfigValue : salesConfig.entrySet()) {
                features.put(String.format("salesConfig.%s", salesConfigValue.getKey()), salesConfigValue.getValue().toString());
            }
        }
        features = addIfNotNull(features, "tenantPurpose", tenantPurpose);
        features = addIfNotNull(features, "reltioEnv", reltioEnv);
        features = addIfNotNull(features, "deploymentCloud", deploymentCloud);
        features = addIfNotNull(features, "deploymentRegion", deploymentRegion);
        features = addIfNotNull(features, "industry", industry);
        features = addIfNotNull(features, "productEdition", productEdition);
        features = addIfNotNull(features, "companyName", companyName);
        features = addIfNotNull(features, "authCustomerId", authCustomerId);
        features = addIfNotNull(features, "contractId", contractId);
        features = addIfNotNull(features, "packageId", packageId);
        if (tenantInternalInfo != null && !tenantInternalInfo.isEmpty()) {
            for (Map.Entry<String, Object> tenantInternalInfoValue : tenantInternalInfo.entrySet()) {
                features.put(String.format("tenantInternalInfo.%s", tenantInternalInfoValue.getKey()), tenantInternalInfoValue.getValue().toString());
            }
        }
        if (tenantConfigurationValues != null && !tenantConfigurationValues.isEmpty()) {
            getFeaturesFromTenantConfigurationValues(features);
        }
        features = addIfNotNull(features, "tenantStatus", tenantStatus);
        return features;
    }

    private void getFeaturesFromTenantConfigurationValues(Map<String, String> features) {
        for (Map.Entry<String, Object> tenantConfigurationValue : tenantConfigurationValues.entrySet()) {
            StringBuilder keySearch = new StringBuilder("tenantConfigurationValues");
            if (tenantConfigurationValue.getValue().getClass().equals(String.class)) {
                keySearch.append(String.format(".%s", tenantConfigurationValue.getKey()));
                features.put(keySearch.toString(), tenantConfigurationValue.getValue().toString());
            } else if (tenantConfigurationValue.getValue().getClass().equals(LinkedHashMap.class)) {
                keySearch.append(String.format(".%s", tenantConfigurationValue.getKey()));
                Map<String, Object> innerMap = (Map<String, Object>) tenantConfigurationValue.getValue();
                for (Map.Entry<String, Object> innerElement : innerMap.entrySet()) {
                    if (innerElement.getValue().getClass().equals(String.class)) {
                        keySearch.append(String.format(".%s", innerElement.getKey()));
                        features.put(keySearch.toString(), innerElement.getValue().toString());
                    }
                }
            }
        }
    }

    public Map<String, String> addIfNotNull(Map<String, String> map, String key, Object value) {
        if (value != null) {
            map.put(key, value.toString());
        }
        return map;
    }
}