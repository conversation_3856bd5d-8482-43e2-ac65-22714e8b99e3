package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Set;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class ContractRequest {
    @JsonProperty("accountIds")
    Set<String> accountIds;
    @JsonProperty("subscriptionType")
    String subscriptionType;
}
