package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.TenantPurpose;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TenantsSyncResponse {


    @JsonProperty("inputTenantsList")
    private final List<String> inputTenantsList;

    /*
    List of tenants available in the env
     */
    @JsonProperty("validTenants")
    private final List<String> validTenants;

    @JsonProperty("environment")
    private final String environment;

    /*
   List of tenants which is not available in the env from the inputTenant List
    */
    @JsonProperty("invalidTenants")
    private final List<String> invalidTenants;


    @JsonProperty("tenantPurpose")
    private final TenantPurpose tenantPurpose;

    @JsonProperty("message")
    private String message ;

    @JsonProperty("failedWhileSync")
    private final List<String> failedWhileSync;

    @JsonProperty("failedCustomersWhileSync")
    private final List<String> failedCustomersWhileSync;

    @JsonProperty("failedClientsWhileSync")
    private final List<String> failedClientsWhileSync;




    @JsonCreator
    public TenantsSyncResponse(@JsonProperty(value = "inputTenantsList") List<String> inputTenantsList,
                               @JsonProperty(value = "validTenants") List<String> validTenants,
                               @JsonProperty(value = "tenantPurpose") TenantPurpose tenantPurpose,
                               @JsonProperty(value = "invalidTenants") List<String> invalidTenants,
                               @JsonProperty(value = "environment") String environment,
                               @JsonProperty(value = "failedWhileSync") List<String> failedWhileSync,
                               @JsonProperty(value = "failedCustomersWhileSync") List<String> failedCustomersWhileSync,
                               @JsonProperty(value = "failedClientsWhileSync") List<String> failedClientsWhileSync) {
        this.inputTenantsList = inputTenantsList != null && inputTenantsList.size() > 0 ? inputTenantsList : null;
        this.validTenants = validTenants != null && validTenants.size() > 0 ? validTenants : null;
        this.tenantPurpose = tenantPurpose;
        this.invalidTenants = invalidTenants != null && invalidTenants.size() > 0 ? invalidTenants : null;
        this.environment = environment;
        this.failedWhileSync = failedWhileSync != null && failedWhileSync.size() > 0 ? failedWhileSync : null;
        this.failedCustomersWhileSync = failedCustomersWhileSync != null && failedCustomersWhileSync.size() > 0 ? failedCustomersWhileSync : null;
        this.failedClientsWhileSync = failedClientsWhileSync != null && failedClientsWhileSync.size() > 0 ? failedClientsWhileSync : null;
    }


    public TenantPurpose getTenantPurpose() {
        return tenantPurpose;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<String> getInvalidTenants() {
        return invalidTenants;
    }

    public String getMessage() {
        return message;
    }
}
