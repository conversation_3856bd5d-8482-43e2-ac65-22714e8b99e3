package com.reltio.services.pms.service.compliance;

import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuBreakdownResponse;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.SalesAccount;

import java.util.Collection;

/**
 * The interface Rsu usage service.
 */
public interface RsuUsageService {
    /**
     * Gets rsu usage by breakdown.
     *
     * @param accountCollection the account collection
     * @param tenantCollection  the tenant collection
     * @param rsuBreakdownResponse   the rsu usage breakdown
     * @return the cp usage by tenant
     */
    RsuBreakdownResponse getRsuBreakdownResponse(Collection<SalesAccount> accountCollection, Collection<ReltioTenant> tenantCollection, RsuBreakdownResponse rsuBreakdownResponse);

}
