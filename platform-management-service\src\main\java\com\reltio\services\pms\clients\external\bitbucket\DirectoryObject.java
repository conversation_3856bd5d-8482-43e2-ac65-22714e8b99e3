package com.reltio.services.pms.clients.external.bitbucket;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;


@JsonIgnoreProperties(ignoreUnknown = true)
public class DirectoryObject {
    private final String path;
    private final DirectoryObjectType type;

    @JsonCreator
    public DirectoryObject(
            @JsonProperty("path") String path,
            @JsonProperty("type") DirectoryObjectType type) {
        this.path = path;
        this.type = type;
    }

    public String getPath() {
        return path;
    }

    public DirectoryObjectType getType() {
        return type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DirectoryObject)) {
            return false;
        }
        DirectoryObject that = (DirectoryObject) o;
        return Objects.equals(getPath(), that.getPath()) &&
                getType() == that.getType();
    }

    @Override
    public int hashCode() {
        return Objects.hash(getPath(), getType());
    }

    @Override
    public String toString() {
        return "DirectoryObject{" +
                "path='" + path + '\'' +
                ", type=" + type +
                '}';
    }
}