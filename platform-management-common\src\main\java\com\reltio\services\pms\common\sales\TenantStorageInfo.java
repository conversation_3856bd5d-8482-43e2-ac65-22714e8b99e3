package com.reltio.services.pms.common.sales;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.reltio.common.config.DataStorageType;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

/**
 * TenantStorageInfo
 * Created by apy<PERSON><PERSON>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Value
@Jacksonized
public class TenantStorageInfo {
    DataStorageType dataStorageType;
    String clusterName;
    String databaseId;
    String awsIAMExternalRole;
    String gbt;
}
