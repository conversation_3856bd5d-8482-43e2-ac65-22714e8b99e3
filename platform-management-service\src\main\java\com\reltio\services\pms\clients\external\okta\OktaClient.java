package com.reltio.services.pms.clients.external.okta;

import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import org.apache.commons.collections.CollectionUtils;
import com.okta.sdk.resource.api.UserApi;
import com.okta.sdk.resource.model.User;
import com.okta.sdk.resource.model.UserProfile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OktaClient {
    private final UserApi userApi;

    @Autowired
    public OktaClient(UserApi userApi) {
        this.userApi = userApi;
    }

    public String getCountryCodeForUser(String userEmail) {
        String search = String.format("profile.email eq \"%s\"", userEmail);
        List<User> users = userApi.listUsers(null, null, null, 1, null, search, null, null);
        if (CollectionUtils.isEmpty(users)) {
            throw new PlatformManagementException(PlatformManagementErrorCode.NO_OKTA_USER_PROFILE, HttpStatus.BAD_REQUEST.value(), userEmail);
        }

        UserProfile userProfile = users.get(0).getProfile();
        if (userProfile == null) {
            throw new PlatformManagementException(PlatformManagementErrorCode.NO_OKTA_USER_PROFILE, HttpStatus.BAD_REQUEST.value(), userEmail);
        }

        return userProfile.getCountryCode();
    }
}
