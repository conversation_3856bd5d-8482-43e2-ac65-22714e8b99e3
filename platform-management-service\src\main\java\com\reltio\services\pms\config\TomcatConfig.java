package com.reltio.services.pms.config;

import com.google.common.base.Strings;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.catalina.connector.Connector;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.apache.log4j.Logger;
import java.util.HashSet;
import java.util.Set;

@Configuration
public class TomcatConfig {
    private static final Logger LOGGER = Logger.getLogger(TomcatConfig.class);


    @Bean
    public ServletWebServerFactory servletContainer(@Value("${server.port.additional:#{null}}") String additionalPort) {
        Set<String> properties=new HashSet<>();
        properties.add(additionalPort);
        PropertiesValidator.validateProperties(properties,LOGGER);
        TomcatServletWebServerFactory tomcat = new TomcatServletWebServerFactory();
        Connector additionalConnector = tryCreateAdditionalConnector(additionalPort);
        if (additionalConnector != null) {
            tomcat.addAdditionalTomcatConnectors(additionalConnector);
        }
        return tomcat;
    }

    private Connector tryCreateAdditionalConnector(String additionalPort) {
        if (Strings.isNullOrEmpty(additionalPort)) {
            return null;
        }
        Connector connector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
        connector.setScheme("http");
        connector.setPort(Integer.parseInt(additionalPort));
        return connector;
    }
}
