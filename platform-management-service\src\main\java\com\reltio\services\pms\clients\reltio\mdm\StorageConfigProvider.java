package com.reltio.services.pms.clients.reltio.mdm;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.reltio.services.pms.common.model.TenantStorageConfig;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class StorageConfigProvider {

    private static final Logger logger = Logger.getLogger(StorageConfigProvider.class);
    private static final String TENANT_PHYSICAL_CONFIG_URL_FORMAT = "https://%s.reltio.com/reltio/tenants/%s";
    private final ObjectMapper mapper;
    private final MDMClient mdmClient;

    @Autowired
    public StorageConfigProvider(MDMClient mdmClient) {
        this.mdmClient = mdmClient;
        this.mapper = new ObjectMapper();
    }

    public TenantStorageConfig getTenantModel(String env, String tenantId) {
        try {
            String tenantUrl = String.format(TENANT_PHYSICAL_CONFIG_URL_FORMAT, env, tenantId);
            String response = mdmClient.getTenantConfigurationAsString(env, tenantId);
            JsonNode body = mapper.readTree(response);
            Objects.requireNonNull(body, "Invalid response from server");
            String tenantName = body.get("tenantName").asText();
            String customerName = body.get("customerName").asText();
            String matchHosts = body.get("dataStorageConfig").get("matchKeyspaceConfig").get("host").asText();
            String matchClusters = body.get("dataStorageConfig").get("matchKeyspaceConfig").get("clusterName").asText();
            String dataHost = body.get("dataStorageConfig").get("dataKeyspaceConfig").get("host").asText();
            String dataCluster = body.get("dataStorageConfig").get("dataKeyspaceConfig").get("clusterName").asText();
            String historyProject = body.get("dataStorageConfig").get("historyBigTableConfig").get("project").asText();
            String historyInstance = body.get("dataStorageConfig").get("historyBigTableConfig").get("instanceId").asText();
            String historyTableName = body.get("dataStorageConfig").get("historyBigTableConfig").get("tableName").asText();
            String esHost = body.get("searchStorageConfiguration").get("esHosts").asText();
            String esCluster = body.get("searchStorageConfiguration").get("esClusterName").asText();
            String indexOvStrategy = body.get("searchStorageConfiguration").get("indexOvStrategy").asText();
            String matchingStrategy = body.get("matchingConfiguration").get("strategy").asText();
            return new TenantStorageConfig(env, null, tenantName, customerName, tenantId, matchHosts, matchClusters, dataHost, dataCluster, historyProject, historyInstance,
                    historyTableName, esHost, esCluster, indexOvStrategy, matchingStrategy, tenantUrl);
        } catch (Exception e) {
            logger.error("Unable to request tenant physical configuration: " + tenantId, e);
            return null;
        }
    }

    public void updateTenantModel(String env, String config) {
        mdmClient.updateTenantPhysicalConfig(env, config);
    }
}
