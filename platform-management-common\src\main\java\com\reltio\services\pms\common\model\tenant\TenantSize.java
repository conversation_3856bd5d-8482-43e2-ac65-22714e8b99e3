package com.reltio.services.pms.common.model.tenant;

// Tenant Size sequence is important to maintain
public enum TenantSize {
    XX_SMALL,
    X_SMALL,
    SMALL,
    PRE_MEDIUM,
    MEDIUM,
    LARGE,
    X_LARGE,
    XX_LARGE,
    XXX_LARGE;

    public String getDataPostfix() {
        switch (this) {
            case XXX_LARGE:
            case XX_LARGE:
            case X_LARGE:
                return "DH";
            case LARGE:
                return "D";
            case MEDIUM:
            case PRE_MEDIUM:
            case SMALL:
            case X_SMALL:
            default:
                return "DHM";
        }

    }

    public String getHistoryPostfix() {
        switch (this) {
            case XXX_LARGE:
            case XX_LARGE:
            case X_LARGE:
            case LARGE:
                return "DH";
            case MEDIUM:
            case PRE_MEDIUM:
                return "H";
            case X_SMALL:
            case SMALL:
            default:
                return "DHM";
        }
    }

    public String getMatchPostfix() {
        switch (this) {
            case XXX_LARGE:
            case XX_LARGE:
            case X_LARGE:
            case LARGE:
            case MEDIUM:
            case PRE_MEDIUM:
                return "M";
            case X_SMALL:
            case SMALL:
            default:
                return "DHM";
        }
    }

    public Integer getESShardsCount() {
        switch (this) {
            case SMALL:
                return 2;
            case PRE_MEDIUM:
            case MEDIUM:
                return 4;
            case LARGE:
            case X_LARGE:
            case XX_LARGE:
            case XXX_LARGE:
                return 8;
            case X_SMALL:
            default:
                return 1;
        }
    }

}