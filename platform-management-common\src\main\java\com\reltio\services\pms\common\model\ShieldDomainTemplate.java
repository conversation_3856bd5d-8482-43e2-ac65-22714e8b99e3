package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ShieldDomainTemplate extends BaseFirestoreEntity {

    @JsonProperty("domain")
    private String domain;

    @JsonProperty("domainId")
    private String domainId;

    @JsonProperty("userName")
    private String userName;

    @JsonProperty("password")
    private String password;


    @JsonCreator
    public ShieldDomainTemplate(@JsonProperty("domain") String domain,
                                @JsonProperty("domainId") String domainId,
                                @JsonProperty("userName") String userName,
                                @JsonProperty("password") String password) {
        this.domain = domain;
        this.domainId = domainId;
        this.userName = userName;
        this.password = password;


    }

    @Override
    public String getID() {
        return domainId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getDomainId() {
        return domainId;
    }

    public void setDomainId(String domainId) {
        this.domainId = domainId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
