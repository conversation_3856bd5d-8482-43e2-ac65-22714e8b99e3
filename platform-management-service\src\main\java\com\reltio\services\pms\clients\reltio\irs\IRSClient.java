package com.reltio.services.pms.clients.reltio.irs;

import com.fasterxml.jackson.databind.JsonNode;
import com.reltio.services.pms.common.PMSRestTemplate;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.service.environment.EnvironmentService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Objects;

@Service
public class IRSClient {
    private static final Logger LOG = Logger.getLogger(IRSClient.class);
    private final EnvironmentService environmentService;
    private final PMSRestTemplate restTemplate;

    private static final String TENANTS_URL = "%s/tenants/";
    private static final String RESET_URL = "%s/tenants/%s/reset";
    private static final String CONFIG_URL = "%s/tenants/%s/config";
    private static final String IRS_NOT_SUPPORTED = "IRS is not supported in env: %s";

    @Autowired
    public IRSClient(PMSRestTemplate restTemplate, EnvironmentService environmentService) {
        this.restTemplate = restTemplate;
        this.environmentService = environmentService;
    }

    public JsonNode getTenants(String envId) {
        if (Objects.isNull(environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.IRS))) {
            LOG.warn(String.format(IRS_NOT_SUPPORTED, envId));
            return null;
        }
        String url = String.format(TENANTS_URL, environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.IRS));
        return restTemplate.getForObject(url, JsonNode.class);
    }

    public JsonNode getTenant(String envId, String id) {
        if (Objects.isNull(environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.IRS))) {
            LOG.warn(String.format(IRS_NOT_SUPPORTED, envId));
            return null;
        }
        String url = String.format(TENANTS_URL, environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.IRS)) + id;
        return restTemplate.getForObject(url, JsonNode.class);
    }

    public JsonNode getTenantConfig(String envId, String id) {
        if (Objects.isNull(environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.IRS))) {
            LOG.warn(String.format(IRS_NOT_SUPPORTED, envId));
            return null;
        }
        String url = String.format(CONFIG_URL, environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.IRS), id);
        return restTemplate.getForObject(url, JsonNode.class);
    }

    public void resetIRSTenant(String envId, String id) {
        if (Objects.isNull(environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.IRS))) {
            LOG.warn(String.format(IRS_NOT_SUPPORTED, envId));
            return;
        }
        String url = String.format(RESET_URL, environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.IRS), id);
        try {
            restTemplate.postForObject(url, Collections.emptyMap(), JsonNode.class);
        } catch (Exception ex) {
            LOG.error(String.format("Error while cleaning irs tenant for %s@%s, Error: %s", id, envId, ex.getMessage()), ex);
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
        }
    }

    public void deleteIRSTenant(String envId, String id) {
        if (Objects.isNull(environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.IRS))) {
            LOG.warn(String.format(IRS_NOT_SUPPORTED, envId));
            return;
        }
        String url = String.format(CONFIG_URL, environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.IRS), id);
        restTemplate.delete(url);
    }

    public JsonNode createIRSTenantConfig(String envId, String id, JsonNode request) {
        if (Objects.isNull(environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.IRS))) {
            LOG.warn(String.format(IRS_NOT_SUPPORTED, envId));
            return null;
        }
        String url = String.format(CONFIG_URL, environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.IRS), id);
        return restTemplate.postForObject(url, request, JsonNode.class);
    }

}
