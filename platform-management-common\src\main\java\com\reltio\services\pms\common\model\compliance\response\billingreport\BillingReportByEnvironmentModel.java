package com.reltio.services.pms.common.model.compliance.response.billingreport;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class BillingReportByEnvironmentModel {

    /**
     * The Tenant id.
     */
    @JsonProperty("tenantId")
    String tenantId;

    /**
     * The Environment.
     */
    @JsonProperty("environment")
    String environment;

    /**
     * The Billing Start Date.
     */
    @JsonProperty("billingStartDate")
    String billingStartDate;
    /**
     * The Billing End Date.
     */
    @JsonProperty("billingEndDate")
    String billingEndDate;

    /**
     * The Task usage.
     */
    @JsonProperty("taskUsage")
    Long taskUsage;



}
