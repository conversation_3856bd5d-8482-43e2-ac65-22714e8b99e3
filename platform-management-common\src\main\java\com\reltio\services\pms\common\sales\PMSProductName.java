package com.reltio.services.pms.common.sales;

public enum PMSProductName {
    BASE,
    ADDITIONAL,
    TENANT_SIZE,
    CLEANSE,
    QUEUES,
    WOR<PERSON>FLOW,
    SHIELD,
    MATCHIQ,
    RDM,
    RIH,
    ANALYTICS,
    //Data Integration Connectors
    MULESOFT_CONNECTOR,
    DNB_CONNECTOR,
    SALESFORCE_CONNECTOR,
    //Analytics Connectors
    BIGQUERY_CONNECTOR,
    SN<PERSON><PERSON><PERSON><PERSON>_CONNECTOR,
    GENERATOR,
    //Data as Service
    DT_NPI,
    DT_DEA,
    DT_340B,
    DT_AMA,
    BVD_CONNECTOR,
    AUTH_ACCESS,
    RIQ,
    IMAGE_HOSTING,
    EMAIL_NOTIFICATION,
    FERN,
    ZOOM_INFO,
    RI<PERSON>_PACKAGE,
    PURVIEW,
    COLLIBRA,
    DN<PERSON>_DATABLOCKS,
    DATABRICKS,
    PRIVATELINK,
    IMMUTABLE_VAULT,
    SFDC_RIH,
    REDSHIFT,
    MEDPRO,
    AZURE_BLOB_STORAGE,
    MDM,
    DTSS,
    <PERSON>ORE_CONFIG,
    CLONE_CONFIG,
    DATA_BACKUP,
    DATA_RESTORE,
    ALATION,
    HISTORY_BACKUP,
    HISTORY_RESTORE;

    public static final String CLEANSE_PRODUCT = "CLEANSE";
    public static final String TENANT_SIZE_PRODUCT = "TENANT_SIZE";
    public static final String QUEUES_PRODUCT = "QUEUES";
    public static final String ANALYTICS_PRODUCT = "ANALYTICS";
    public static final String MATCHIQ_PRODUCT = "MATCHIQ";
    public static final String RDM_PRODUCT = "RDM";
    public static final String RIH_PRODUCT = "RIH";
    public static final String GENERATOR_PRODUCT = "GENERATOR";
    public static final String DNB_CONNECTOR_PRODUCT = "DNB_CONNECTOR";
    public static final String DT_NPI_PRODUCT = "DT_NPI";
    public static final String DT_DEA_PRODUCT = "DT_DEA";
    public static final String DT_340B_PRODUCT = "DT_340B";
    public static final String DT_AMA_PRODUCT = "DT_AMA";
    public static final String RIQ_PRODUCT = "RIQ";
    public static final String SALESFORCE_CONNECTOR_PRODUCT = "SALESFORCE_CONNECTOR";
    public static final String IMAGE_HOSTING_PRODUCT = "IMAGE_HOSTING";
    public static final String AZURE_BLOB_STORAGE_PRODUCT = "AZURE_BLOB_STORAGE";
    public static final String MDM_PRODUCT = "MDM";
    public static final String DTSS_PRODUCT = "DTSS";
    public static final String STORE_CONFIG_PRODUCT = "STORE_CONFIG";
    public static final String CLONE_CONFIG_PRODUCT = "CLONE_CONFIG";
    public static final String DATA_BACKUP_PRODUCT = "DATA_BACKUP";
    public static final String DATA_RESTORE_PRODUCT = "DATA_RESTORE";
    public static final String HISTORY_BACKUP_PRODUCT = "HISTORY_BACKUP";
    public static final String HISTORY_RESTORE_PRODUCT = "HISTORY_RESTORE";

}
