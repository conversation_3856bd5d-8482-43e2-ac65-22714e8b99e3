package com.reltio.services.pms.clients.reltio.shield;

public class Shield<PERSON>lientException extends RuntimeException {

    public ShieldClientException() {
    }

    public ShieldClientException(String message) {
        super(message);
    }

    public ShieldClientException(String message, Throwable cause) {
        super(message, cause);
    }

    public ShieldClientException(Throwable cause) {
        super(cause);
    }

    public ShieldClientException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
