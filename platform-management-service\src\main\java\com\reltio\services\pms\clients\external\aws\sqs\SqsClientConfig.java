package com.reltio.services.pms.clients.external.aws.sqs;

import com.reltio.services.pms.clients.external.aws.AwsCredentialsFactory;
import com.reltio.services.pms.clients.external.aws.credentials.PlatformManagementAwsCredentials;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;

@Slf4j
@Service
public class SqsClientConfig {

    private final PlatformManagementAwsCredentials credentials;

    @Autowired
    public SqsClientConfig(PlatformManagementAwsCredentials credentials) {
        this.credentials = credentials;
    }

    public AwsCredentialsProvider getCredentialsProvider() {
        return AwsCredentialsFactory.createAwsCredentialsProvider(credentials);
    }
}
