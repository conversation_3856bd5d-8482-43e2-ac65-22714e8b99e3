package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.Query;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.jobs.Job;
import com.reltio.services.pms.common.model.jobs.JobStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class JobDao extends AbstractLevel1CollectionDao<Job> {
    private static final String JOBS_COLLECTION_NAME = "JOBS";
    private static final String OWNERS = "owners";
    private static final String CREATED_TIME = "createdTime";
    private static final String CREATED_BY = "createdBy";
    private static final String DE_PROVISIONING = "deProvisioning";
    private static final String STATUS = "status";
    private static final String TENANT_NAME = "tenantName";

    @Autowired
    public JobDao(CredentialsProvider provider,
                  EnvironmentDao environmentDao, ReltioUserHolder reltioUserHolder) {
        super(provider, environmentDao, JOBS_COLLECTION_NAME, reltioUserHolder);
    }

    @Override
    protected TypeReference<Job> getTypeReference() {
        return new TypeReference<>() {
        };
    }

    public Collection<Job> getJobsByStatus(String envId, Collection<JobStatus> jobStatuses, Integer size, Integer offset, String sortOrder, String sortField, Boolean deProvisioning) {
        List<String> jobStatusesInString = jobStatuses.stream().map(Objects::toString).collect(Collectors.toList());
        Query query;
        if(Boolean.TRUE.equals(deProvisioning)) {
            query = getBaseCollection(envId).whereEqualTo(DE_PROVISIONING, true).whereIn(STATUS, jobStatusesInString).limit(size).offset(offset).orderBy(sortField, Query.Direction.valueOf(sortOrder));
        }else if(Boolean.FALSE.equals(deProvisioning)) {
            query = getBaseCollection(envId).whereNotEqualTo(DE_PROVISIONING, true).whereIn(STATUS, jobStatusesInString).limit(size).offset(offset).orderBy(sortField, Query.Direction.valueOf(sortOrder));
        }else{
            query = getBaseCollection(envId).whereIn(STATUS, jobStatusesInString).limit(size).offset(offset).orderBy(sortField, Query.Direction.valueOf(sortOrder));
        }
        return getResultFromQuery(query);
    }

    public Collection<Job> getJobsWithTimeFilter(String envId, Long start, Long end, Integer size, Integer offset) {
        Query query = getBaseCollection(envId).whereGreaterThanOrEqualTo(CREATED_TIME, start).whereLessThanOrEqualTo(CREATED_TIME, end).limit(size).offset(offset);
        return getResultFromQuery(query);
    }

    public Collection<Job> getJobWithTaskByTenantName(String envId, String tenantName) {
        Query query = getBaseCollection(envId).whereEqualTo(TENANT_NAME, tenantName);
        try {
            return getResultFromQuery(query);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.NO_JOB_FOUND_FOR_THIS_TENANTID, HttpStatus.BAD_REQUEST.value(), tenantName);
        }
    }

    public Collection<Job> getAllJobs(String envId, Integer size, Integer offset, String sortField, String sortOrder, Boolean deProvisioning) {
        Query query;
        if(Boolean.TRUE.equals(deProvisioning)) {
            query = getBaseCollection(envId).whereEqualTo(DE_PROVISIONING, true).limit(size).offset(offset).orderBy(sortField, Query.Direction.valueOf(sortOrder));
        }else if (Boolean.FALSE.equals(deProvisioning)) {
            query = getBaseCollection(envId).whereNotEqualTo(DE_PROVISIONING, true).limit(size).offset(offset).orderBy(sortField, Query.Direction.valueOf(sortOrder));
        }else {
            query = getBaseCollection(envId).limit(size).offset(offset).orderBy(sortField, Query.Direction.valueOf(sortOrder));
        }
        return getResultFromQuery(query);
    }

    public Collection<Job> getJobsWithParentJobId(String envId,String fieldName, String fieldValue) {
        Query query = getBaseCollection(envId).whereEqualTo(fieldName, fieldValue);
        return getResultFromQuery(query);
    }

    public Collection<Job> getJobsWithOwnerAndCreatedByFilters(String envId,Integer size,Integer offset, String owner, String createdBy, String sortOrder){
        Query query = getBaseCollection(envId).whereArrayContainsAny(OWNERS, Collections.singletonList(owner)).limit(size).offset(offset).orderBy(CREATED_TIME, Query.Direction.valueOf(sortOrder));
        if(createdBy == null) {
            return getResultFromQuery(query);
        }
        query = query.whereEqualTo(CREATED_BY,createdBy);
        return getResultFromQuery(query);

    }

}
