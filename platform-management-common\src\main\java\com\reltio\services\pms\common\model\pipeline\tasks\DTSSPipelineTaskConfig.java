package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.enterprise.contracts.DTSSType;
import com.reltio.services.pms.common.sales.PMSProductName;

import static com.reltio.services.pms.common.model.pipeline.tasks.TaskType.DTSS_TASK;
import static com.reltio.services.pms.common.model.pipeline.tasks.TaskType.DTSS_TASK_NAME;

public class DTSSPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonProperty
    private final String dataTenantId;

    @JsonProperty
    private final DTSSType dtssSubscriptionType;

    @JsonCreator
    public DTSSPipelineTaskConfig(@JsonProperty(value = "name") String name,
                                  @JsonProperty(value = "dataTenantId", required = true) String dataTenantId,
                                  @JsonProperty(value = "dtssSubscriptionType", required = true) String dtssSubscriptionType) {
        super(name, DTSS_TASK);
        this.dataTenantId = dataTenantId;
        this.dtssSubscriptionType = DTSSType.convertFromString(dtssSubscriptionType);

    }

    @Override
    public PMSProductName getProductName() {
        switch (dtssSubscriptionType) {
            case NPI:
                return PMSProductName.DT_NPI;
            case DEA:
                return PMSProductName.DT_DEA;
            case DT340B:
                return PMSProductName.DT_340B;
        }
        return null;
    }
    @Override
    public boolean visibleInContract() {
        return true;
    }

    @Override
    public String toString() {
        return DTSS_TASK_NAME + "{}";
    }

    public String getDataTenantId() {
        return dataTenantId;
    }

    public DTSSType getDtssSubscriptionType() {
        return dtssSubscriptionType;
    }


}
