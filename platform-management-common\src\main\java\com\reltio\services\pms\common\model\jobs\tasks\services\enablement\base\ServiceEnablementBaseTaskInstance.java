package com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Getter
public class ServiceEnablementBaseTaskInstance extends TaskInstance {

    @JsonProperty(value = "failedTenants")
    private final Set<String> failedTenants;
    @JsonProperty(value = "tenantsForServiceEnablement")
    private final Set<String> tenantsForServiceEnablement;
    @JsonProperty(value = "events")
    private final Map<String, Set<String>> events;
    @JsonProperty(value = "totalProcessedTenants")
    private int totalProcessedTenants;


    @JsonCreator
    public ServiceEnablementBaseTaskInstance(@JsonProperty(value = "id") String id,
                                             @JsonProperty(value = "name") String name,
                                             @JsonProperty(value = "jobId") String jobId,
                                             @JsonProperty(value = "startTime") Long startTime,
                                             @JsonProperty(value = "finishTime") Long finishTime,
                                             @JsonProperty(value = "type") TaskType type,
                                             @JsonProperty(value = "status") TaskStatus status,
                                             @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                             @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                             @JsonProperty(value = "executingNodeName") String executingNodeName,
                                             @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                             @JsonProperty(value = "envId") String envId,
                                             @JsonProperty(value = "failedTenants") Set<String> failedTenants,
                                             @JsonProperty(value = "totalProcessedTenants", defaultValue = "0") int totalProcessedTenants,
                                             @JsonProperty(value = "tenantsForServiceEnablement") Set<String> tenantsForServiceEnablement,
                                             @JsonProperty(value = "events") Map<String, Set<String>> events) {
        super(id, name, jobId, startTime, finishTime, type, status, lastUpdatedTime, taskFailureContext, executingNodeName,
                serviceNodeStatus, envId);
        this.failedTenants = failedTenants == null ? new HashSet<>() : failedTenants;
        this.totalProcessedTenants = totalProcessedTenants;
        this.tenantsForServiceEnablement = tenantsForServiceEnablement;
        this.events = events == null ? new HashMap<>() : events;
    }

    public void addFailedTenants(String tenantId) {
        failedTenants.add(tenantId);
    }

    public void addEvent(String tenantId, String event) {
        Set<String> tenantEvents = events.getOrDefault(tenantId, new HashSet<>());
        tenantEvents.add(event);
        events.put(tenantId, tenantEvents);
    }

    public boolean isEventPresentForTenantId(String tenantId, String event) {
        Set<String> tenantEvents = events.getOrDefault(tenantId, new HashSet<>());
        return tenantEvents.contains(event);
    }

    public void incrementTotalProcessedTenants() {
        totalProcessedTenants++;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }
        ServiceEnablementBaseTaskInstance that = (ServiceEnablementBaseTaskInstance) o;
        return super.equals(that) &&
                Objects.equals(getFailedTenants(), that.getFailedTenants()) &&
                Objects.equals(getTotalProcessedTenants(), that.getTotalProcessedTenants()) &&
                Objects.equals(getTenantsForServiceEnablement(), that.getTenantsForServiceEnablement());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getFailedTenants(), getTotalProcessedTenants(), getTenantsForServiceEnablement());
    }
}
