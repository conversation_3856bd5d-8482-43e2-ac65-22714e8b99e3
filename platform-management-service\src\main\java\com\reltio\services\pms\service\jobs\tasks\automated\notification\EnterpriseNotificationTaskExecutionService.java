package com.reltio.services.pms.service.jobs.tasks.automated.notification;

import com.reltio.services.pms.common.model.EmailContentType;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.notification.EnterpriseNotificationTaskInstance;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.AbstractTaskExecutionService;
import com.reltio.services.pms.service.notification.EmailNotificationService;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

public class EnterpriseNotificationTaskExecutionService extends AbstractTaskExecutionService<EnterpriseNotificationTaskInstance> {

    private static final Logger logger = Logger.getLogger(EnterpriseNotificationTaskExecutionService.class);
    private final EmailNotificationService emailNotificationService;
    private final String FIRST_NAME = "firstName";
    private final String ALL = "all";

    protected EnterpriseNotificationTaskExecutionService(String envId,
                                                         EnterpriseNotificationTaskInstance taskDetail,
                                                         GrafanaDashboardGBQService grafanaDashboardGBQService,
                                                         EmailNotificationService emailNotificationService) {
        super(envId, taskDetail, grafanaDashboardGBQService);
        this.emailNotificationService = emailNotificationService;
    }

    @Override
    public void executeTask() throws Exception {
        Map<String, Object> params = new HashMap<>();
        List<String> owners = taskDetail.getOwners();
        if (owners.isEmpty()) {
            logger.debug("Owners list is empty");
        } else {
            if (owners.size() > 1) {
                params.put(FIRST_NAME, ALL);
            } else {
                List<String> names = Arrays.asList(owners.stream().findFirst().get().split("@")[0].split("\\."));
                String firstName = names.size() > 0 ? StringUtils.capitalize(names.get(0).toLowerCase()) : null;
                params.put(FIRST_NAME, firstName);
            }
            emailNotificationService.notify(EmailContentType.WELCOME_EMAIL, null, new HashSet<>(owners), Collections.unmodifiableMap(params));
            updateTask();
            taskDetail.setStatus(TaskStatus.COMPLETED);
        }
    }
}
