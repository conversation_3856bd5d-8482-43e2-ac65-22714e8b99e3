package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.Getter;

import java.util.Objects;

@Getter
public class RDMServiceDeploymentTaskConfig extends AbstractPipelineTaskConfig{
    @JsonProperty(value = "productEdition")
    private final String productEdition;

    @JsonProperty(value = "cache")
    private final RdmCache rdmCache;

    @JsonProperty(value = "publishRDMErrors")
    private final Boolean publishRDMErrors ;

    @JsonProperty(value = "trackTranscodeErrors")
    private final Boolean trackTranscodeErrors;

    @JsonCreator
    public RDMServiceDeploymentTaskConfig(
            @JsonProperty(value = "name") String name,
            @JsonProperty(value = "productEdition") String productEdition,
            @JsonProperty(value = "cache") RdmCache rdmCache ,
            @JsonProperty(value = "publishRDMErrors" , defaultValue = "false") Boolean publishRDMErrors,
            @JsonProperty(value = "trackTranscodeErrors" , defaultValue = "true") Boolean trackTranscodeErrors) {
        super(name, TaskType.RDM_SERVICE_DEPLOYMENT_TASK);
        this.productEdition = productEdition;
        this.publishRDMErrors = publishRDMErrors != null ? publishRDMErrors : Boolean.FALSE;
        this.rdmCache = rdmCache != null ? rdmCache : new RdmCache(true , "TranscodeInMemory");
        this.trackTranscodeErrors = Objects.nonNull(trackTranscodeErrors) ? trackTranscodeErrors : Boolean.TRUE;
    }


    @Override
    public PMSProductName getProductName() {
        return PMSProductName.RDM;
    }

    @Override
    public boolean visibleInContract() {
        return true;
    }
}
