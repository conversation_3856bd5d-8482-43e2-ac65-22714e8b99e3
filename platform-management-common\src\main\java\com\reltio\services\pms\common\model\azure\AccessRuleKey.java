package com.reltio.services.pms.common.model.azure;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class AccessRuleKey {

    @JsonProperty("rulePrimaryKey")
    private String rulePrimaryKey;

    @JsonProperty("ruleName")
    private String ruleName;


    @JsonCreator
    public AccessRuleKey( @JsonProperty("rulePrimaryKey") String rulePrimaryKey,
                          @JsonProperty("ruleName")String ruleName) {
        this.rulePrimaryKey = rulePrimaryKey;
        this.ruleName = ruleName;
    }


    public String getRulePrimaryKey() {
        return rulePrimaryKey;
    }

    public String getRuleName() {
        return ruleName;
    }
}
