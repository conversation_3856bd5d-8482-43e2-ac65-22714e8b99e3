package com.reltio.services.pms.dao;

import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.CollectionReference;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.FirestoreOptions;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import org.apache.log4j.Logger;

import javax.annotation.PreDestroy;
import java.util.Collection;

/**
 * Provides the generic functions to support the sub collections at level 2.
 *
 * @param <T>
 */
public abstract class AbstractLevel2CollectionDao<T extends BaseFirestoreEntity> extends AbstractDao<T> {


    private static final Logger logger = Logger.getLogger(AbstractLevel2CollectionDao.class);

    protected final Firestore db;
    protected final AbstractLevel1CollectionDao parentCollectionDao;
    protected final String collectionName;

    public AbstractLevel2CollectionDao(CredentialsProvider provider, AbstractLevel1CollectionDao parentDao, String collectionName, ReltioUserHolder reltioUserHolder) {
        super(reltioUserHolder);
        this.db = FirestoreOptions.newBuilder().setCredentialsProvider(provider).build().getService();
        this.parentCollectionDao = parentDao;
        this.collectionName = collectionName;
    }

    @PreDestroy
    public void destroy() throws Exception {
        db.close();
    }

    public T create(String rootDocId, String parentDocId, T entity) {
        return create(getBaseCollection(rootDocId, parentDocId), entity);
    }

    public T update(String rootDocId, String parentDocId, T entity) {
        return update(getBaseCollection(rootDocId, parentDocId), entity);
    }


    public T get(String rootDocId, String parentDocId, String docId) throws InvalidDocumentIdException {
        return get(getBaseCollection(rootDocId, parentDocId), docId);
    }

    public boolean isExists(String rootDocId, String parentDocId, String docId) {
        return isExists(getBaseCollection(rootDocId, parentDocId), docId);
    }

    public Collection<T> getAll(String rootDocId, String parentDocId) {
        return getAll(getBaseCollection(rootDocId, parentDocId));
    }

    public void delete(String rootDocId, String parentDocId, String docId) {
        delete(getBaseCollection(rootDocId, parentDocId), docId);
    }

    public final DocumentReference getDocumentReference(String rootDocId, String parentDocId, String docId) {
        return getBaseCollection(rootDocId, parentDocId).document(docId);
    }

    protected final CollectionReference getBaseCollection(String rootDocId, String parentDocId) {
        return parentCollectionDao.getDocumentReference(rootDocId, parentDocId).collection(collectionName);
    }

}
