package com.reltio.services.pms.common.model.supportability.result.combiner;

import com.reltio.services.pms.common.model.supportability.CassandraClusterStatsDto;
import com.reltio.services.pms.common.model.supportability.CpuUsage;
import com.reltio.services.pms.common.model.supportability.MemoryUsage;
import com.reltio.services.pms.common.model.supportability.result.StatsCombiner;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;

/**
 * The type Event stats combiner.
 */
public class CassandraStatsCombiner implements StatsCombiner<CassandraClusterStatsDto> {
    /**
     * mergeAllRestRows method is used to merge all the provided rows from GBQ into single record by sum/avg based on the column in GBQ.
     *
     * @param dtoList the dto list
     * @return the list
     */
    @Override
    public CassandraClusterStatsDto mergeAllRows(List<CassandraClusterStatsDto> dtoList) {
        CassandraClusterStatsDto response = dtoList.get(NumberUtils.INTEGER_ZERO);
        CassandraClusterStatsDto lastResponse = dtoList.get(dtoList.size() - NumberUtils.INTEGER_ONE);
        long startTime = response.getStartTime();
        long endTime = lastResponse.getEndTime();
        int totalRows = dtoList.size();
        Float cpuSum = NumberUtils.FLOAT_ZERO;
        Float cpuMax = NumberUtils.FLOAT_ZERO;
        Float cpuP90Sum = NumberUtils.FLOAT_ZERO;
        Float cpuP95Sum = NumberUtils.FLOAT_ZERO;
        Float memorySum = NumberUtils.FLOAT_ZERO;
        Float memoryMax = NumberUtils.FLOAT_ZERO;
        Float memoryP90Sum = NumberUtils.FLOAT_ZERO;
        Float memoryP95Sum = NumberUtils.FLOAT_ZERO;
        for (CassandraClusterStatsDto dto : dtoList) {
            cpuSum += dto.getCpu().getAverage();
            cpuMax = Math.max(dto.getCpu().getMax(), cpuMax);
            cpuP90Sum += dto.getCpu().getP90();
            cpuP95Sum += dto.getCpu().getP95();

            memorySum += dto.getMemoryUsage().getAverage();
            memoryMax = Math.max(dto.getMemoryUsage().getMax(), memoryMax);
            memoryP90Sum += dto.getMemoryUsage().getP90();
            memoryP95Sum += dto.getMemoryUsage().getP95();

        }
        Float cpuAvg = roundOff2Digits(cpuSum / totalRows);
        Float cpuP90Avg = roundOff2Digits(cpuP90Sum / totalRows);
        Float cpuP95Avg = roundOff2Digits(cpuP95Sum / totalRows);
        CpuUsage cpuUsage = new CpuUsage(cpuAvg, roundOff2Digits(cpuMax), cpuP90Avg, cpuP95Avg, null);

        Float memoryAvg = roundOff2Digits(memorySum / totalRows);
        Float memoryP90Avg = roundOff2Digits(memoryP90Sum / totalRows);
        Float memoryP95Avg = roundOff2Digits(memoryP95Sum / totalRows);

        MemoryUsage memoryUsage = new MemoryUsage(memoryAvg, roundOff2Digits(memoryMax), memoryP90Avg, memoryP95Avg, null);

        CassandraClusterStatsDto dto = new CassandraClusterStatsDto();
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setCpu(cpuUsage);
        dto.setMemoryUsage(memoryUsage);
        return dto;
    }
}
