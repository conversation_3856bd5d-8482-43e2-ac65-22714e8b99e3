package com.reltio.services.pms.common.model.pipeline.tasks;

import com.amazonaws.HttpMethod;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.NullNode;
import org.springframework.http.HttpHeaders;

import java.util.Objects;

public class RESTRequest {

    private HttpMethod httpMethod;

    private String url;

    private JsonNode requestBody;

    private HttpHeaders headers;

    public HttpMethod getHttpMethod() {
        return httpMethod;
    }

    public void setHttpMethod(HttpMethod httpMethod) {
        this.httpMethod = httpMethod;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public JsonNode getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(JsonNode requestBody) {
        this.requestBody = requestBody;
    }

    public HttpHeaders getHeaders() {
        return headers;
    }

    public void setHeaders(HttpHeaders headers) {
        this.headers = headers;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }


        RESTRequest that = (RESTRequest) o;
        return getHttpMethod() == that.getHttpMethod() &&
                Objects.equals(getUrl(), that.getUrl()) &&
                ((getRequestBody() == null && that.getRequestBody() == NullNode.getInstance()) ||
                        (getRequestBody() == NullNode.getInstance() && that.getRequestBody() == null) ||
                        Objects.equals(getRequestBody(), that.getRequestBody()));
    }

    @Override
    public int hashCode() {
        return Objects.hash(getHttpMethod(), getUrl(), getRequestBody(), getHeaders());
    }


    @Override
    public String toString() {
        return "RESTRequest{" +
                "httpMethod=" + httpMethod +
                ", url='" + url + '\'' +
                ", requestBody=" + requestBody +
                ", headers=" + headers +
                '}';
    }
}
