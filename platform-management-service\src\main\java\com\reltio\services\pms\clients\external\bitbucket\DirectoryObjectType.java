package com.reltio.services.pms.clients.external.bitbucket;


import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;

public enum DirectoryObjectType {
    FILE("commit_file"),
    DIRECTORY("commit_directory");

    private final String name;

    @JsonCreator
    DirectoryObjectType(@JsonProperty("name") String name) {
        this.name = name;
    }

    @JsonValue
    public String getName() {
        return name;
    }
}