package com.reltio.services.pms.common.model.supportability;

import lombok.Getter;

/**
 * The enum Facet contains the values of time based rows in GBQ for customer supportability.
 */
@Getter
public enum Facet {

    /**
     * Every 10 Minutes has 1 row in GBQ.
     */
    min(1,600),

    /**
     * Every Hour has 6 rows in GBQ.
     */
    hour(6,3600),

    /**
     * Every Day has 144 rows in GBQ.
     */
    day(144,86400);

    /**
     * The No of rows.
     */
    int noOfRows;

    /**
     * The Seconds.
     */
    int seconds;


    /**
     * Instantiates a new Facet.
     *
     * @param rows    the rows
     * @param inSeconds the seconds
     */
    Facet(int rows,int inSeconds) {
        noOfRows= rows;
        seconds =inSeconds;
    }
}
