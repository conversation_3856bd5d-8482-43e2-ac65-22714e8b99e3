package com.reltio.services.pms.common.model.supportability;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

/**
 * DynamoDBStatsDto
 * Created by a<PERSON><PERSON><PERSON>
 */
@Value
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DynamoDBStatsDto {
    @JsonProperty("average")
    Double average;
    @JsonProperty("max")
    Double max;
    @JsonProperty("p90")
    Double p90;
    @JsonProperty("p95")
    Double p95;
}
