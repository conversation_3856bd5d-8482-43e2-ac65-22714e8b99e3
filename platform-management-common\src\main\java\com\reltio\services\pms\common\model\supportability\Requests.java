package com.reltio.services.pms.common.model.supportability;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * The type Requests.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class Requests {


    /**
     * The Request.
     */
    @JsonProperty("request")
    private String request;

    /**
     * The Avg.
     */
    @JsonProperty("avg")
    private Float avg;
    /**
     * The Count.
     */
    @JsonProperty("totalCount")
    private Integer count;


    /**
     * The Failed request.
     */
    @JsonProperty("failedRequests")
    private Integer failedRequests;
    /**
     * The P 90.
     */
    @JsonProperty("p90")
    private Integer p90;
}
