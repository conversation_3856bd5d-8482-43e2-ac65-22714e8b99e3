package com.reltio.services.pms.service.compliance;

import com.reltio.services.pms.common.model.compliance.response.CpUsageResponse;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByEntityResponse;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByTenantResponse;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.SalesAccount;

import java.util.Collection;
import java.util.List;

/**
 * The interface Cp usage service.
 */
public interface CpUsageService {
    /**
     * Gets cp usage by tenant.
     *
     * @param accountCollection the account collection
     * @param tenantCollection  the tenant collection
     * @param cpUsageByTenant   the cp usage by tenant
     * @return the cp usage by tenant
     */
    CpUsageByTenantResponse getCpUsageByTenant(Collection<SalesAccount> accountCollection, Collection<ReltioTenant> tenantCollection, CpUsageByTenantResponse cpUsageByTenant);

    /**
     * Gets cp usage by entity.
     *
     * @param accountCollection the account collection
     * @param tenantCollection  the tenant collection
     * @param cpUsageByEntity   the cp usage by entity
     * @return the cp usage by entity
     */
    CpUsageByEntityResponse getCpUsageByEntity(Collection<SalesAccount> accountCollection, Collection<ReltioTenant> tenantCollection, List<CpUsageResponse> cpUsageByEntity);
}
