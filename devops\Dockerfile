FROM gcr.io/customer-facing/eclipse-temurin:21.0.7_6-jdk-noble

COPY platform-management.jar /usr/src/application/application.jar
COPY entrypoint.sh /usr/src/application/entrypoint.sh

WORKDIR /usr/src/application

RUN mkdir config
RUN mkdir apm
RUN mkdir logs

COPY opentelemetry-javaagent.jar apm/opentelemetry-javaagent.jar

RUN groupadd -g 10001 -r reltio-group \
  && useradd -u 10000 -r -g reltio-group reltio-user \
  && chown -R reltio-user:reltio-group /usr/src/application \
  && chmod -R 755 /usr/src/application

USER reltio-user

EXPOSE 8080
EXPOSE 9090

CMD  exec java $JAVA_OPTS \
 -XX:OnOutOfMemoryError="kill -9 %p" \
 -Dfile.encoding=UTF-8 \
 -DPLATFORM-MANAGEMENT_CONFIG_NAME="$PLATFORM_MANAGEMENT_CONFIG_NAME" \
 -jar application.jar \
 --spring.config.location=file:/usr/src/application/config/"$PLATFORM_MANAGEMENT_CONFIG_NAME" \
 --spring.main.allow-bean-definition-overriding=true
