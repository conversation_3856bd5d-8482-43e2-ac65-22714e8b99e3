package com.reltio.services.pms.controller;
import com.fasterxml.jackson.databind.JsonNode;
import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.common.config.LcaConfig;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.model.ConfigOverrideElement;
import com.reltio.services.pms.common.model.TenantStorageConfig;
import com.reltio.services.pms.service.reltiotenant.TenantStorageService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/api/v1/environments/{environmentName}/tenants", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name= "Tenants")
@ReltioSecured(resourceClass = Pms.Environment.Config.Templates.class)
public class TenantStorageController {

    private final TenantStorageService tenantStorageService;

    @Autowired
    public TenantStorageController(TenantStorageService tenantStorageService) {
        this.tenantStorageService = tenantStorageService;
    }

    @GetMapping("/{tenantId}")
    public TenantStorageConfig getTenantStorageConfig(@PathVariable String environmentName,
                                                      @PathVariable String tenantId) {
        return tenantStorageService.getConfigFromFirestore(tenantId, environmentName);
    }

    @PostMapping("/{tenantId}/sync")
    public TenantStorageConfig syncTenantStorageConfig(@PathVariable String environmentName,
                                                       @PathVariable String tenantId) {
        return tenantStorageService.syncFSTenantStorageConfigFromReal(environmentName, tenantId);
    }

    @PostMapping("/generate")
    public JsonNode syncTenantStorageConfig(@PathVariable(value = "environmentName") String environment,
                                            @RequestParam(value = "tenantName") String tenantName,
                                            @RequestParam(value = "customerName") String customerName,
                                            @RequestParam(value = "tenantId") String tenantId,
                                            @RequestParam(value = "autoCreate", required = false, defaultValue = "false") Boolean autoCreate,
                                            @RequestParam(value = "templateName", required = false) String templateName,
                                            @RequestParam(value = "lcaConfig", required = false) LcaConfig lcaConfig,
                                            @RequestParam(value = "externalElements", required = false) List<ConfigOverrideElement> externalElements) {
        if (templateName != null) {
            return tenantStorageService.makePhysConfig(environment, tenantName, customerName, tenantId, autoCreate, templateName, externalElements, lcaConfig);
        } else {
            return tenantStorageService.makePhysConfig(environment, tenantName, customerName, tenantId, autoCreate, externalElements, lcaConfig);
        }
    }
}
