package com.reltio.services.pms.service.jobs.tasks.clean.tenant.separated.execution;

import com.reltio.services.pms.clients.external.gcp.pubsub.GBTClient;
import com.reltio.services.pms.clients.reltio.cassandra.CassandraCleaner;
import com.reltio.services.pms.clients.reltio.irs.IRSClient;
import com.reltio.services.pms.clients.reltio.mdm.MDMCleanTenantClient;
import com.reltio.services.pms.clients.reltio.mdm.MDMClient;
import com.reltio.services.pms.clients.reltio.workflow.WorkflowServiceClient;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.separated.execution.CleanTenantExecutionTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.PmsLockService;
import com.reltio.services.pms.service.SecretService;
import com.reltio.services.pms.service.environment.EnvironmentService;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.JobService;
import com.reltio.services.pms.service.jobs.tasks.TaskFactory;
import com.reltio.services.pms.service.jobs.tasks.deprovision.auth.AuthDeProvisionHelper;
import com.reltio.services.pms.service.jobs.tasks.provisioning.matchiq.MatchIQClient;
import com.reltio.services.pms.service.jobs.tasks.provisioning.matchiq.RIQClient;
import com.reltio.services.pms.service.reltiotenant.RdmTenantsService;
import com.reltio.services.pms.service.reltiotenant.TenantUIConfigService;
import com.reltio.services.pms.service.reltiotenant.TenantsService;
import org.apache.commons.configuration.Configuration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CleanTenantExecutionTaskFactory implements TaskFactory<CleanTenantExecutionTaskInstance,
        CleanTenantExecutionTaskService> {
    private final MDMClient mdmClient;
    private final MDMCleanTenantClient mdmCleanTenantClient;
    private final WorkflowServiceClient workflowServiceClient;
    private final GBTClient gbtClient;
    private final CassandraCleaner cassandraCleaner;
    private final MatchIQClient matchIQClient;
    private final EnvironmentService environmentService;
    private final RIQClient riqClient;
    private final IRSClient irsClient;
    private final JobService jobService;
    private final TenantUIConfigService tenantUIConfigService;
    private final TenantsService tenantsService;
    private final AuthDeProvisionHelper authDeProvisionHelper;
    private final RdmTenantsService rdmTenantsService;
    private final SecretService secretService;
    private final PmsLockService pmsLockService;
    private final Configuration configuration;

    @Autowired
    public CleanTenantExecutionTaskFactory(MDMClient mdmClient,
                                           WorkflowServiceClient workflowServiceClient,
                                           GBTClient gbtClient,
                                           CassandraCleaner cassandraCleaner,
                                           MatchIQClient matchIQClient,
                                           EnvironmentService environmentService,
                                           RIQClient riqClient,
                                           IRSClient irsClient,
                                           MDMCleanTenantClient mdmCleanTenantClient,
                                           TenantUIConfigService tenantUIConfigService,
                                           JobService jobService,
                                           TenantsService tenantsService,
                                           AuthDeProvisionHelper authDeProvisionHelper,
                                           RdmTenantsService rdmTenantsService,
                                           SecretService secretService,
                                           PmsLockService pmsLockService,
                                           Configuration configuration) {
        this.mdmClient = mdmClient;
        this.workflowServiceClient = workflowServiceClient;
        this.gbtClient = gbtClient;
        this.cassandraCleaner = cassandraCleaner;
        this.matchIQClient = matchIQClient;
        this.environmentService = environmentService;
        this.riqClient = riqClient;
        this.irsClient = irsClient;
        this.mdmCleanTenantClient = mdmCleanTenantClient;
        this.jobService = jobService;
        this.tenantUIConfigService = tenantUIConfigService;
        this.tenantsService = tenantsService;
        this.authDeProvisionHelper = authDeProvisionHelper;
        this.rdmTenantsService = rdmTenantsService;
        this.secretService = secretService;
        this.pmsLockService = pmsLockService;
        this.configuration = configuration;
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.DELETE_TENANT_DATA_EXECUTION_TASK;
    }

    @Override
    public CleanTenantExecutionTaskService createTask(String envId, CleanTenantExecutionTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService) {
        return new CleanTenantExecutionTaskService(envId, taskDetail, grafanaDashboardGBQService, mdmClient, workflowServiceClient,
                gbtClient, cassandraCleaner, matchIQClient, environmentService, riqClient, irsClient, mdmCleanTenantClient,
                jobService, tenantUIConfigService,tenantsService, authDeProvisionHelper, rdmTenantsService, secretService,
                pmsLockService, configuration);
    }
}