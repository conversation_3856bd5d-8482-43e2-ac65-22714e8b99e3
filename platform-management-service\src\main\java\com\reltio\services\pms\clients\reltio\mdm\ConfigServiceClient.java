package com.reltio.services.pms.clients.reltio.mdm;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.reltio.io.IOUtils;
import com.reltio.services.pms.clients.reltio.notification.EmailNotificationClient;
import com.reltio.services.pms.common.IPMSRestTemplate;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.io.IOException;

@Service
@Retryable(exceptionExpression = "${retry.shouldRetry:true}", maxAttempts = 5, backoff = @Backoff(delay = 1000, multiplier = 5))
public class ConfigServiceClient {
    private static final String CONSOLE_APP_URL = "%s/service/consoleApps?environment=%s&tenant=%s";
    private static final String CHOCOLATE_BAR_URL = "%s/service/common?environment=%s&tenant=%s";
    private static final String CONFIG_URL = "%s/service/common?environment=%s&tenant=%s&default=true";
    private static final String NOTIFICATION_URL = "%s/service/notifications?environment=default&tenant=%s";
    private static final Logger LOG = Logger.getLogger(ConfigServiceClient.class);
    private final ObjectMapper mapper;
    private final IPMSRestTemplate restTemplate;

    @Autowired
    public ConfigServiceClient(@Qualifier("console") IPMSRestTemplate rest) {
        this.restTemplate = rest;
        mapper = new ObjectMapper();
    }

    public void configureConsoleApps(String serviceUrl, String env, String tenantId, String body) {
        String url = String.format(CONSOLE_APP_URL, serviceUrl, env, tenantId);
        try {
            JsonNode request = mapper.readTree(body);
            restTemplate.postForObject(url, request, JsonNode.class);
        } catch (Exception ex) {
            LOG.warn(String.format("Error: %s, while configuring console apps", ex.getMessage()), ex);
        }
    }

    public void updateConsoleApps(String serviceUrl, String env, String tenantId, String body) {
        String url = String.format(CONSOLE_APP_URL, serviceUrl, env, tenantId);
        try {
            JsonNode request = mapper.readTree(body);
            restTemplate.put(url, request, JsonNode.class);
        } catch (Exception ex) {
            LOG.warn(String.format("Error: %s, while configuring console apps", ex.getMessage()), ex);
        }
    }

    public void enableChocolateBar(String serviceUrl, String env, String tenantId, String body) {
        try {
            JsonNode request = mapper.readTree(body);
            postConfigForTenant(serviceUrl, env, tenantId, request);
        } catch (Exception ex) {
            LOG.warn(String.format("Error: %s, while enabling chocolate bar", ex.getMessage()), ex);
        }
    }

    public void updateChocolateBar(String serviceUrl, String env, String tenantId, String body) {
        try {
            JsonNode request = mapper.readTree(body);
            putConfigForTenant(serviceUrl, env, tenantId, request);
        } catch (Exception ex) {
            LOG.warn(String.format("Error: %s, while enabling chocolate bar", ex.getMessage()), ex);
        }
    }

    public String defaultChocolateBarOfTenant(String serviceUrl, String env, String tenantId, boolean failOnError) {
        return defaultConfigOfTenant(serviceUrl, env, tenantId, CHOCOLATE_BAR_URL, failOnError);
    }

    public String defaultConsoleAppsOfTenant(String serviceUrl, String env, String tenantId, boolean failOnError) {
        return defaultConfigOfTenant(serviceUrl, env, tenantId, CONSOLE_APP_URL, failOnError);
    }

  private String defaultConfigOfTenant(String serviceUrl, String env, String tenantId, String baseUrl, boolean failOnError) {
    String url = String.format(baseUrl, serviceUrl, env, tenantId);
    try {
      return restTemplate.getForObject(url, String.class);
    } catch (Exception ex) {
      if (!failOnError) {
        return "";
      }
      throw new PlatformManagementException(
          PlatformManagementErrorCode.CONFIG_IS_NOT_AVAILABLE_IN_CONFIG_SERVICE,
          HttpStatus.NOT_FOUND.value(),
          tenantId);
    }
  }

    public String defaultConfig(String serviceUrl, String env, String tenantId) {
        String url = String.format(CONFIG_URL, serviceUrl, env, tenantId);
        try {
            return restTemplate.getForObject(url, String.class);
        } catch (Exception ex) {
            LOG.warn(String.format("Error: %s, while getting config from config service", ex.getMessage()), ex);
        }
        return null;
    }


    public void postConfigForTenant(String serviceUrl, String env, String tenantId, JsonNode body) {
        String url = String.format(CHOCOLATE_BAR_URL, serviceUrl, env, tenantId);
        try {
            restTemplate.postForObject(url, body, JsonNode.class);
        } catch (Exception ex) {
            LOG.error(String.format("Error while creating a configuration  for %s@%s, Error: %s", tenantId, env, ex.getMessage()), ex);
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
        }
    }

    public void putConfigForTenant(String serviceUrl, String env, String tenantId, JsonNode body) {
        String url = String.format(CHOCOLATE_BAR_URL, serviceUrl, env, tenantId);
        try {
            restTemplate.put(url, body, JsonNode.class);
        } catch (Exception ex) {
            LOG.error(String.format("Error while updating the configuration  for %s@%s, Error: %s", tenantId, env, ex.getMessage()), ex);
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
        }
    }

    public boolean isNewConfigServiceTenant(String serviceUrl, String env, String tenantId) {
        String url = String.format(CHOCOLATE_BAR_URL, serviceUrl, env, tenantId);
        try {
            restTemplate.getForObject(url, JsonNode.class);
            return false;
        } catch (HttpClientErrorException.NotFound ex) {
            return true;
        }
    }

    public void configureNotificationTemplate(String serviceUrl, String tenantId) {
        try {
            String url = String.format(NOTIFICATION_URL, serviceUrl, tenantId);
            String file = IOUtils.readResource(EmailNotificationClient.class, "notification_requests/configure_notification_template.json");
            JsonNode request = mapper.readTree(file);
            restTemplate.postForObject(url, request, JsonNode.class);
        } catch (IOException ex) {
            LOG.warn(String.format("Error: %s, while configuring notification template", ex.getMessage()), ex);
        }
    }
}
