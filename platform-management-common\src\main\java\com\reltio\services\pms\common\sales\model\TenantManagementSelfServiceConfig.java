package com.reltio.services.pms.common.sales.model;

import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import lombok.Getter;
import lombok.Setter;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

@Getter
@Setter
public class TenantManagementSelfServiceConfig extends BaseFirestoreEntity {

    @JsonProperty(value = "configName")
    private String configName;

    @JsonProperty(value = "jsonSchemaPhysicalConfig")
    private String jsonSchemaPhysicalConfig;

    @JsonProperty(value = "selfServiceConfig")
    private Map<String,Map<String,Object>> selfServiceConfig;

    @JsonCreator
    public TenantManagementSelfServiceConfig(@JsonProperty(value = "configName")String configName,
                                             @JsonProperty(value = "jsonSchemaPhysicalConfig")String jsonSchemaPhysicalConfig,
                                             @JsonProperty(value = "selfServiceConfig")Map<String,Map<String,Object>> selfServiceConfig
                                          ){
        this.configName = configName;
        this.jsonSchemaPhysicalConfig = jsonSchemaPhysicalConfig;
        this.selfServiceConfig = selfServiceConfig;

    }


    @Override
    public String getID() {
        return configName;
    }
}
