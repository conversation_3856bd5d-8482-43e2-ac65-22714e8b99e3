package com.reltio.services.pms.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.reltio.services.pms.clients.external.gcp.secret.SecretsManagerService;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.ShieldDomainTemplate;
import com.reltio.services.pms.common.model.ShieldStorageTemplate;
import com.reltio.services.pms.common.model.StorageTemplate;
import com.reltio.services.pms.common.model.jobs.tasks.approve.ApprovalStatus;
import com.reltio.services.pms.common.model.jobs.tasks.shield.ShieldNotificationStatus;
import com.reltio.services.pms.common.model.proxy.ActionStatus;
import com.reltio.services.pms.common.model.proxy.approve.ApprovalSecureAction;
import com.reltio.services.pms.common.model.shield.ShieldSecrets;
import com.reltio.services.pms.service.proxy.ApprovalActionProxyServiceImpl;
import com.reltio.services.pms.service.proxy.GenericSecureProxyService;
import com.reltio.services.pms.service.proxy.SecureActionExecutionService;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.log4j.Logger;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import jakarta.validation.Valid;
import java.nio.charset.StandardCharsets;

@Controller
@RequestMapping(value = SecureProxyController.BASE_SECURE_URL, produces = MediaType.APPLICATION_XHTML_XML_VALUE)
@Tag(name = "Secure Proxy Controller")
public class SecureProxyController {
    private static final Logger logger = Logger.getLogger(SecureProxyController.class);


    private final ApprovalActionProxyServiceImpl approvalActionProxyService;
    private final SecureActionExecutionService<ApprovalSecureAction> approvalSecureActionExecutionService;
    private final GenericSecureProxyService secureActionProxyService;
    private final SecretsManagerService secretsManagerService;

    public static final String BASE_SECURE_URL = "api/v1/secure/";
    public static final String TASK_APPROVAL_URL = "task/approve/";
    public static final String TASK_REJECT_URL = "task/reject/";
    public static final String STORAGE_APPROVAL_URL = "storage/approve/";
    public static final String STORAGE_MODIFY_URL = "storage/modify/";
    public static final String SHIELD_MODIFY_URL = "shield/saveShieldDomainInfo/";
    public static final String SHIELD_ENABLEMENT_CONFIRMTION_URL = "shield/shieldEnablementConfirmation/";

    public SecureProxyController(ApprovalActionProxyServiceImpl approvalActionProxyService,
                                 SecureActionExecutionService<ApprovalSecureAction> approvalSecureActionExecutionService,
                                 GenericSecureProxyService secureActionProxyService,
                                 SecretsManagerService secretsManagerService) {
        this.approvalActionProxyService = approvalActionProxyService;
        this.approvalSecureActionExecutionService = approvalSecureActionExecutionService;
        this.secureActionProxyService = secureActionProxyService;
        this.secretsManagerService = secretsManagerService;
    }


    @GetMapping(value = TASK_APPROVAL_URL + "{secureKey}")
    public String approve(@PathVariable String secureKey) {
        try {
            ApprovalSecureAction approvalSecureAction = getSecureAction(secureKey);
            approvalSecureAction.setApprovalStatus(ApprovalStatus.APPROVED);
            approvalSecureActionExecutionService.executeInternalAction(approvalSecureAction);
        } catch (InvalidDocumentIdException e) {
            return "invalid";
        }
        return "success";
    }

    @GetMapping(value = STORAGE_APPROVAL_URL + "{secureKey}")
    public String approveStorage(@PathVariable String secureKey) {
        try {
            ApprovalSecureAction approvalSecureAction = getSecureAction(secureKey);
            approvalSecureAction.setApprovalStatus(ApprovalStatus.APPROVED);
            approvalSecureActionExecutionService.executeInternalAction(approvalSecureAction);
        } catch (InvalidDocumentIdException e) {
            return "invalid";
        }
        return "success";
    }
    @Operation(requestBody = @RequestBody(content = @Content(mediaType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)))
    @PostMapping(value = STORAGE_MODIFY_URL + "{secureKey}", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String modifyStorage(@PathVariable String secureKey, @ModelAttribute("storageTemplate") @Valid StorageTemplate template) {
        try {
            ApprovalSecureAction approvalSecureAction = getSecureAction(secureKey);
            approvalSecureActionExecutionService.modifyStorageTemplateAction(approvalSecureAction, template);
            approvalSecureAction.setApprovalStatus(ApprovalStatus.MODIFIED);
        } catch (InvalidDocumentIdException | PlatformManagementException e) {
            return "invalid";
        }
        return "modified";
    }
    @Operation(requestBody = @RequestBody(content = @Content(mediaType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)))
    @GetMapping(value = STORAGE_MODIFY_URL + "{secureKey}", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String getAttemptModifyStorage(@PathVariable String secureKey, @ModelAttribute("storageTemplate") @Valid StorageTemplate template) {
        return "invalid";
    }

    @Operation(requestBody = @RequestBody(content = @Content(mediaType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)))
    @PostMapping(value = SHIELD_MODIFY_URL + "{secureKey}", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String saveShieldDomainInfo(@PathVariable String secureKey, @ModelAttribute("shieldDomainTemplate") @Valid ShieldDomainTemplate template) {
        try {
            ApprovalSecureAction approvalSecureAction = getSecureAction(secureKey);
            ShieldStorageTemplate shieldStorageTemplate = new ShieldStorageTemplate(template.getDomain(), template.getDomainId(), approvalSecureAction.getTaskId(),
                    ShieldNotificationStatus.DomainDetailsReceived, null, null);
            approvalSecureActionExecutionService.saveShieldTemplateAction(shieldStorageTemplate);
            ShieldSecrets shieldSecrets = new ShieldSecrets(template.getUserName(), template.getPassword());
            saveSecrets(shieldSecrets, approvalSecureAction.getTaskId());
            approvalSecureAction.setApprovalStatus(ApprovalStatus.MODIFIED);
        } catch (InvalidDocumentIdException | PlatformManagementException | JsonProcessingException e) {
            return "invalid";
        }
        return "modified";
    }

    private void saveSecrets(ShieldSecrets shieldSecrets, String taskId) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        if (!secretsManagerService.doesSecretExist(taskId)) {
            String idKeyPair = objectMapper.writeValueAsString(shieldSecrets);
            secretsManagerService.saveSecret(taskId, idKeyPair.getBytes(StandardCharsets.UTF_8));
        }
    }
    @Operation(requestBody = @RequestBody(content = @Content(mediaType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)))
    @GetMapping(value = SHIELD_MODIFY_URL + "{secureKey}", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String getAttemptShieldDomainInfo(@PathVariable String secureKey, @ModelAttribute("shieldDomainTemplate") @Valid ShieldDomainTemplate template) {
        return "invalid";
    }


    @GetMapping(value = SHIELD_ENABLEMENT_CONFIRMTION_URL + "{secureKey}")
    public String getShieldEnablementConfirmation(@PathVariable String secureKey) {
        try {
            ApprovalSecureAction approvalSecureAction = getSecureAction(secureKey);
            String taskId = approvalSecureAction.getTaskId();
            ShieldStorageTemplate template = approvalSecureActionExecutionService.getShieldTemplateAction(taskId);
            template.setShieldNotificationStatus(ShieldNotificationStatus.EncryptionEnabled);
            approvalSecureActionExecutionService.saveShieldTemplateAction(template);

        } catch (InvalidDocumentIdException e) {
            return "invalid";
        }
        return "success";
    }


    @GetMapping(value = TASK_REJECT_URL + "{secureKey}")
    public String reject(@PathVariable String secureKey) {
        try {
            ApprovalSecureAction approvalSecureAction = getSecureAction(secureKey);
            approvalSecureAction.setApprovalStatus(ApprovalStatus.REJECTED);
            approvalSecureActionExecutionService.executeInternalAction(approvalSecureAction);
        } catch (InvalidDocumentIdException e) {
            return "invalid";
        }
        return "success";
    }

    @GetMapping(value = "{secureKey}")
    public ModelAndView handleGenericAction(@PathVariable String secureKey,
                                            @RequestParam MultiValueMap<String, String> param) {
        ModelAndView modelAndView = new ModelAndView("error.page");
        try {
            return secureActionProxyService.handleAction(secureKey, param);
        } catch (InvalidDocumentIdException e) {
            logger.warn(String.format("There was not found error while handling action request with id %s and param %s", secureKey, param.toString()), e);
            modelAndView.addObject("message", "This link is not valid. Please click link provided in mail.");
            return modelAndView;
        } catch (Exception e) {
            logger.error(String.format("There was unexpected error while handling action request with id %s and param %s", secureKey, param.toString()), e);
            modelAndView.addObject("message", "There is some expected error while handling your request. Please retry after sometime or Contact support.");
            return modelAndView;
        }
    }

    private ApprovalSecureAction getSecureAction(String secureKey) throws InvalidDocumentIdException {
        ApprovalSecureAction approvalSecureAction = approvalActionProxyService.getAction(secureKey);
        if (approvalSecureAction.getStatus().equals(ActionStatus.EXPIRED) || approvalSecureAction.getStatus().equals(ActionStatus.COMPLETED)) {
            throw new InvalidDocumentIdException("");
        }
        approvalSecureAction.setStatus(ActionStatus.COMPLETED);
        return approvalSecureAction;
    }
}
