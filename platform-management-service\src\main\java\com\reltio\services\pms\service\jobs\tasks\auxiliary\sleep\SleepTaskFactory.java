package com.reltio.services.pms.service.jobs.tasks.auxiliary.sleep;

import com.reltio.services.pms.common.model.jobs.tasks.SleepTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.TaskFactory;
import org.springframework.stereotype.Service;

@Service
public class SleepTaskFactory implements TaskFactory<SleepTaskInstance, SleepTaskExecutionService> {
    @Override
    public TaskType getTaskType() {
        return TaskType.SLEEP_TASK;
    }

    @Override
    public SleepTaskExecutionService createTask(String jobId, SleepTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService) {
        return new SleepTaskExecutionService(jobId, taskDetail, grafanaDashboardGBQService);
    }
}
