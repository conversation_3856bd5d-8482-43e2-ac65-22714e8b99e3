package com.reltio.services.pms.common;

import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.NoSuchElementException;

public class PaginatedIterator<T> implements Iterator<T> {

    private int offSet;
    private PageLoader<T> pageLoader;
    private final int pageSize;

    private Iterator<T> currentPageIterator;

    public PaginatedIterator(PageLoader<T> pageLoader, int pageSize) {
        this.pageLoader = pageLoader;
        this.pageSize = pageSize;
        this.offSet = 0;
        currentPageIterator = Collections.emptyIterator();
    }

    @Override
    public boolean hasNext() {
        if (currentPageIterator.hasNext()) {
            return true;
        }
        loadPage();
        return currentPageIterator.hasNext();
    }

    private void loadPage() {
        Collection<T> collection = pageLoader.loadPage(offSet, pageSize);
        offSet += collection.size();
        currentPageIterator = collection.iterator();
    }

    @Override
    public T next() {
        if (!hasNext()) {
            throw new NoSuchElementException();
        }
        return currentPageIterator.next();
    }
}
