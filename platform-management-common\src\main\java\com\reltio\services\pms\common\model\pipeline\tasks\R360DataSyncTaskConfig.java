package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

public class R360DataSyncTaskConfig extends AbstractPipelineTaskConfig {
    @JsonProperty(value = "r360TenantEnvironment")
    private final String r360TenantEnvironment;


    @JsonProperty(value = "r360TenantId")
    private final String r360TenantId;

    @JsonCreator
    public R360DataSyncTaskConfig(
            @JsonProperty(value = "name") String name,
            @JsonProperty(value = "r360TenantEnvironment", required = true) String r360TenantEnvironment,
            @JsonProperty(value = "r360TenantId") String r360TenantId) {
        super(name, TaskType.R360_DATA_SYNC_TASK);
        this.r360TenantEnvironment = r360TenantEnvironment;
        this.r360TenantId = r360TenantId;
    }


    public String getR360TenantEnvironment() {
        return r360TenantEnvironment;
    }

    public String getR360TenantId() {
        return r360TenantId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof R360DataSyncTaskConfig)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        R360DataSyncTaskConfig that = (R360DataSyncTaskConfig) o;
        return Objects.equals(getR360TenantEnvironment(), that.getR360TenantEnvironment()) &&
                Objects.equals(getR360TenantId(), that.getR360TenantId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getR360TenantEnvironment(), getR360TenantId());
    }
}
