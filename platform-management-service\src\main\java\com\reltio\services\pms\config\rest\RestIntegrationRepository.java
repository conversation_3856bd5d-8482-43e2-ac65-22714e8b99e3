package com.reltio.services.pms.config.rest;

import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;

/**
 * <AUTHOR>
 * @since 18.11.2022
 */
public interface RestIntegrationRepository extends ClientRegistrationRepository {
    ClientRegistration findByRegistrationId(String registrationId);

    UserRegistration findUserByRegistrationId(String registrationId);
}
