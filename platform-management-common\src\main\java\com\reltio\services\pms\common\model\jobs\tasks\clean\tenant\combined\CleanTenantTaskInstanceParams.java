package com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.combined;

import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.Parameters;
import com.reltio.services.pms.common.model.jobs.tasks.maintenance.MaintenanceModeTaskInstanceParams;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Builder
@Getter
@NoArgsConstructor
public class CleanTenantTaskInstanceParams implements MaintenanceModeTaskInstanceParams {
    private String tenantName;
    private Parameters parameters;
    private boolean qaAutomation;
}
