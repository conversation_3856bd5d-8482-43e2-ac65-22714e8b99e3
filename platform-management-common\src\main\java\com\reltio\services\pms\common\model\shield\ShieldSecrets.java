package com.reltio.services.pms.common.model.shield;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class ShieldSecrets {

    @JsonProperty(value = "userName")
    private String userName;
    @JsonProperty(value = "password")
    private String password;

    @JsonCreator
    public ShieldSecrets(@JsonProperty(value = "userName") String userName,
                         @JsonProperty(value = "password") String password){
        this.userName = userName;
        this.password = password;
    }

    public String getUserName() {
        return userName;
    }

    public String getPassword() {
        return password;
    }
}
