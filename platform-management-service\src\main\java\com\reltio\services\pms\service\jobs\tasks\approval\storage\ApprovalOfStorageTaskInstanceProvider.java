package com.reltio.services.pms.service.jobs.tasks.approval.storage;

import com.reltio.collection.CollectionUtils;
import com.reltio.devops.common.environment.ServicePurpose;
import com.reltio.devops.common.environment.ServiceType;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.Environment;
import com.reltio.services.pms.common.model.jobs.tasks.approve.storage.ApprovalOfStorageTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.approve.storage.ApprovalOfStorageTaskInstanceParams;
import com.reltio.services.pms.common.model.pipeline.tasks.ApprovalOfStoragePipelineTaskConfig;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.ValidationErrorCollector;
import com.reltio.services.pms.service.environment.EnvironmentService;
import com.reltio.services.pms.service.jobs.tasks.TaskInstanceProvider;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class ApprovalOfStorageTaskInstanceProvider implements TaskInstanceProvider<ApprovalOfStorageTaskInstance, ApprovalOfStoragePipelineTaskConfig, ApprovalOfStorageTaskInstanceParams> {

    private final EnvironmentService environmentService;

    public ApprovalOfStorageTaskInstanceProvider(EnvironmentService environmentService) {
        this.environmentService = environmentService;
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.APPROVAL_OF_STORAGE;
    }


    @Override
    public void validateTaskConfig(String envId, ApprovalOfStoragePipelineTaskConfig pipelineTaskConfig, ValidationErrorCollector validationErrorCollector) {
        Environment environment = environmentService.getEnvironment(envId);
        validateStorage(pipelineTaskConfig, environment);
    }

    private void validateStorage(ApprovalOfStoragePipelineTaskConfig pipelineTaskConfig, Environment environment) {
        String envId = environment.getID();
        Map<ServicePurpose, List<ServiceType>> storagePriorityList;
        if (CollectionUtils.isNotEmpty(pipelineTaskConfig.getStoragePriorityList())) {
            storagePriorityList = pipelineTaskConfig.getStoragePriorityList();
        } else {

            storagePriorityList = environment.getStoragePriorityList();
        }
        environmentService.validateStoragePriorityList(envId,storagePriorityList);
    }

    private Map<ServicePurpose, List<ServiceType>> setStoragePriorityList(Map<ServicePurpose, List<ServiceType>> storagePriorityList, String envId)
    {
        storagePriorityList = CollectionUtils.isNotEmpty(storagePriorityList) ? storagePriorityList : environmentService.getEnvironment(envId).getStoragePriorityList();
        if(storagePriorityList==null)
        {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), "Please use Api to configure storagePriorityList for the environment");
        }
        else {
            return storagePriorityList;
        }
    }


    @Override
    public ApprovalOfStorageTaskInstance getTaskDetail(String envId, String jobId, ApprovalOfStoragePipelineTaskConfig pipelineTaskConfig, ApprovalOfStorageTaskInstanceParams taskInstanceParams) {
        Map<ServicePurpose, List<ServiceType>> storagePriorityList = pipelineTaskConfig.getStoragePriorityList();
        storagePriorityList = setStoragePriorityList(storagePriorityList,envId);

        return new ApprovalOfStorageTaskInstance(UUID.randomUUID().toString(),
                TaskType.APPROVAL_OF_STORAGE.toString(),
                jobId,
                null,
                null,
                null,
                null,
                null,
                pipelineTaskConfig.getApproverEmails(),
                null, envId,
                taskInstanceParams.getDefaultStorageTemplate(),
                taskInstanceParams.getCustomerName(),
                taskInstanceParams.getTenantId(),
                taskInstanceParams.getTenantSize(),
                null,
                taskInstanceParams.getDataStorageArn(),
                taskInstanceParams.getMatchStorageArn(),
                taskInstanceParams.getTenantPurpose(),
                storagePriorityList,
                pipelineTaskConfig.getLcaConfig());
    }
}
