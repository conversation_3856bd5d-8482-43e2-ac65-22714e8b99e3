package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.Query;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.RepositoryDirectory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;


/**
 * The type Repository directory dao.
 */
@Service
public class RepositoryDirectoryDao extends AbstractLevel1CollectionDao<RepositoryDirectory> {
    private static final String REPOSITORY_CONTENT_NAME = "REPOSITORY_DIRECTORY";


    /**
     * Instantiates a new Repository directory dao.
     *
     * @param provider         the provider
     * @param repositoryDao    the repository dao
     * @param reltioUserHolder the reltio user holder
     */
    @Autowired
    public RepositoryDirectoryDao(CredentialsProvider provider,
                                  RepositoryDao repositoryDao, ReltioUserHolder reltioUserHolder) {
        super(provider, repositoryDao, REPOSITORY_CONTENT_NAME, reltioUserHolder);
    }

    @Override
    protected TypeReference<RepositoryDirectory> getTypeReference() {
        return new TypeReference<RepositoryDirectory>() {
        };
    }

    /**
     * Gets repo directory.
     *
     * @param repoName   the repo name
     * @param branchName the branch name
     * @param fieldName  the field name
     * @param fieldValue the field value
     * @return the repo contents
     */
    public Collection<RepositoryDirectory> getRepoDirectory(String repoName, String branchName , String fieldName , String fieldValue) {
        Query query = getBaseCollection(String.format("%s@%s", repoName, branchName)).whereEqualTo(fieldName,fieldValue);
        return getResultFromQuery(query);
    }

}
