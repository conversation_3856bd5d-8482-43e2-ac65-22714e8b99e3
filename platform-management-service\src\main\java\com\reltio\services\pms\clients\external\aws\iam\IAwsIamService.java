package com.reltio.services.pms.clients.external.aws.iam;

import com.reltio.services.pms.common.model.aws.IamUserAccessKey;
import org.json.simple.JSONObject;
import software.amazon.awssdk.services.iam.model.AttachedPolicy;
import software.amazon.awssdk.services.iam.model.DeleteAccessKeyResponse;
import software.amazon.awssdk.services.iam.model.DeleteGroupResponse;
import software.amazon.awssdk.services.iam.model.DeletePolicyResponse;
import software.amazon.awssdk.services.iam.model.DeletePolicyVersionResponse;
import software.amazon.awssdk.services.iam.model.DeleteUserResponse;
import software.amazon.awssdk.services.iam.model.DetachGroupPolicyResponse;
import software.amazon.awssdk.services.iam.model.Group;
import software.amazon.awssdk.services.iam.model.ListAccessKeysResponse;
import software.amazon.awssdk.services.iam.model.ListPolicyVersionsResponse;
import software.amazon.awssdk.services.iam.model.Policy;

import java.util.List;

public interface IAwsIamService {

    void createRole(String roleName, JSONObject assumeRolePolicyDocument, String policyName, String region);

    void createPolicy(String policyName, String policyDocument);

    void createGroupInlinePolicy(String groupName, String policyName, String policyDocument);

    void createUser(String userName, String boundaryArn);

    DeleteUserResponse deleteUser(String userName);

    boolean doesUserExist(String userName);

    IamUserAccessKey createAwsKeysForUser(String userName);

    void createUserGroup(String groupName);

    void attachGroupPolicy(String groupName, String policyName, String region);

    void addUserToGroup(String userName, String groupName);

    void deleteUserKey(String userName, String accessKey);

    List<String> getUserGroups(String userName);

    void attachUserPolicy(String userName, String policyName, String region);

    void putUserPermissionsBoundary(String username, String boundaryArn);

    List<AttachedPolicy> listUserPolicies(String userName);

    void detachUserPolicy(String userName, String policyArn);

    DetachGroupPolicyResponse detachGroupPolicyByPolicyName(String groupName, String policyName, String region);

    DetachGroupPolicyResponse detachGroupPolicy(String groupName, String policyArn);

    void createGroupForPolicy(String groupName);

    Policy checkPolicyExists(String policyArn);

    String getPolicyDocument(String policyArn, String defaultVersion);

    void updatePolicy(String policyArn, String policyDocument);

    List<Group> listGroupsForUser(String userName);

    List<String> listGroupPolicies(String groupName);

    DeleteGroupResponse deleteGroup(String groupName);

    DeletePolicyResponse deletePolicyByPolicyName(String policyName, String region);

    DeletePolicyResponse deletePolicy(String policyArn);

    ListPolicyVersionsResponse listPolicyVersions(String policyArn);

    DeletePolicyVersionResponse deletePolicyVersion(String policyArn, String versionId);

    ListPolicyVersionsResponse listPolicyVersionsByPolicyName(String policyName, String region);

    DeletePolicyVersionResponse deletePolicyVersionByPolicyName(String policyName, String versionId, String region);

    ListAccessKeysResponse listAccessKeys(String userName);

    DeleteAccessKeyResponse deleteAccessKey(String userName, String accessKeyId);

    String getUserByPolicyArn(String policyArn);
}
