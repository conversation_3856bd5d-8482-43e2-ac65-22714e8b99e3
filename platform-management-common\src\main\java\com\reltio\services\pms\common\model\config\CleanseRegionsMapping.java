package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class CleanseRegionsMapping extends PMSConfig{

    @JsonProperty("cleanseRegions")
    private Map<String, List<String>> cleanseRegions;


    @JsonCreator
    public CleanseRegionsMapping(@JsonProperty("configName") String configName,
                                 @JsonProperty("cleanseRegions") Map<String, List<String>> cleanseRegions) {
        super(configName);
        this.cleanseRegions = cleanseRegions;
    }

    @Override
    public String getID() {
        return super.getID();
    }

    public Map<String, List<String>> getcleanseRegions() {
        return cleanseRegions;
    }

    @Override
    public void setConfigName(String configName) {
        super.setConfigName(configName);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CleanseRegionsMapping)) return false;
        CleanseRegionsMapping that = (CleanseRegionsMapping) o;
        return cleanseRegions.equals(that.cleanseRegions);
    }

    @Override
    public int hashCode() {
        return Objects.hash(cleanseRegions);
    }
}
