package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.databind.JsonNode;
import com.reltio.services.pms.common.model.jobs.tasks.auth.AuthTaskConstants;
import com.reltio.services.pms.common.model.jobs.tasks.auth.AuthTaskInstance;

public class AuthTaskUtil {


    public static AuthTaskInstance.CustomerConfig setCaseSensitiveLoginEnabled(JsonNode node, AuthTaskInstance.CustomerConfig customerConfig) {
        Boolean caseSensitiveLoginEnabled;
        if (node.get(AuthTaskConstants.CUSTOMER_CONFIG).hasNonNull(AuthTaskConstants.CASE_SENSITIVE_LOGIN_ENABLED)) {
            caseSensitiveLoginEnabled = node.get(AuthTaskConstants.CUSTOMER_CONFIG).get(AuthTaskConstants.CASE_SENSITIVE_LOGIN_ENABLED).asBoolean();
            customerConfig.setCaseSensitiveLoginEnabled(caseSensitiveLoginEnabled);
        } else {
            customerConfig.setCaseSensitiveLoginEnabled(false);
        }
        return customerConfig;
    }
}
