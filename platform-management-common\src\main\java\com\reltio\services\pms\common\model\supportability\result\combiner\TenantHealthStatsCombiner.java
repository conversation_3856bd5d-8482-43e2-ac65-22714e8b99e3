package com.reltio.services.pms.common.model.supportability.result.combiner;

import com.reltio.collection.CollectionUtils;
import com.reltio.services.pms.common.model.supportability.TenantHealthStatsDto;
import com.reltio.services.pms.common.model.supportability.result.StatsCombiner;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * The type Tenant Health stats combiner.
 */
public class TenantHealthStatsCombiner implements StatsCombiner<TenantHealthStatsDto> {
    /**
     * Combine list.
     *
     * @param dtoList the dto list
     * @return the list
     */
    @Override
    public List<TenantHealthStatsDto> combine(List<TenantHealthStatsDto> dtoList) {
        List<TenantHealthStatsDto> valueList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dtoList)) {
            valueList.add(mergeAllRows(dtoList));
        }
        return valueList;
    }

    /**
     * mergeAllTenantHealthRows method is used to merge all the provided rows from GBQ into single record by sum based on the column in GBQ.
     *
     * @param dtoList the dto list
     * @return the list
     */
    @Override
    public TenantHealthStatsDto mergeAllRows(List<TenantHealthStatsDto> dtoList) {
        TenantHealthStatsDto response = dtoList.get(NumberUtils.INTEGER_ZERO);
        TenantHealthStatsDto lastResponse = dtoList.get(dtoList.size() - NumberUtils.INTEGER_ONE);
        long startTime = response.getStartTime();
        long endTime = lastResponse.getEndTime();
        long totalCount = NumberUtils.INTEGER_ZERO;
        long errorCount = NumberUtils.INTEGER_ZERO;
        long warningCount = NumberUtils.INTEGER_ZERO;
        Map<String, Long> top5ErrorsByCount = new LinkedHashMap<>();
        Map<String, Long> top5WarningsByCount = new LinkedHashMap<>();
        for (TenantHealthStatsDto dto : dtoList) {
            totalCount += dto.getTotalCount();
            errorCount += dto.getErrorCount();
            warningCount += dto.getWarningCount();
            top5ErrorsByCount = merge(top5ErrorsByCount, dto.getTop5ErrorsByCount());
            top5WarningsByCount = merge(top5WarningsByCount, dto.getTop5WarningsByCount());
        }

        TenantHealthStatsDto dto = new TenantHealthStatsDto();
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setTotalCount(totalCount);
        dto.setErrorCount(errorCount);
        dto.setWarningCount(warningCount);
        dto.setTop5ErrorsByCount(top5ErrorsByCount);
        dto.setTop5WarningsByCount(top5WarningsByCount);
        return dto;
    }

    private static Map<String, Long> merge(Map<String, Long> map1, Map<String, Long> map2) {
        return Stream.concat(map1.entrySet().stream(), map2.entrySet().stream())
                .sorted((e1, e2) -> Long.compare(e2.getValue(), e1.getValue()))
                .limit(5)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (v1, v2) -> v1,
                        LinkedHashMap::new // Maintain insertion order
                ));
    }
}
