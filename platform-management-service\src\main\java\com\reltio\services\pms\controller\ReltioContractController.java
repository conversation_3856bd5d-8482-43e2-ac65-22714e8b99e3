package com.reltio.services.pms.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.auth.domain.ReltioPrivileges;
import com.reltio.collection.CollectionUtils;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.ContractRequest;
import com.reltio.services.pms.common.model.JobWithTask;
import com.reltio.services.pms.common.model.request.ProcessContract;
import com.reltio.services.pms.common.sales.ContractStatus;
import com.reltio.services.pms.common.sales.model.ReltioContract;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.TenantStatus;
import com.reltio.services.pms.service.sales.ContractDeltaService;
import com.reltio.services.pms.service.sales.ContractService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/api/v1/contracts", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name= "Contracts")
public class ReltioContractController {
    private final ContractService contractService;
    private final ContractDeltaService contractDeltaService;

    @Autowired
    public ReltioContractController(ContractService contractService, ContractDeltaService contractDeltaService) {
        this.contractService = contractService;
        this.contractDeltaService = contractDeltaService;
    }

    @GetMapping("/{contractId}")
    @ReltioSecured(resourceClass = Pms.Contracts.class, privileges = ReltioPrivileges.READ)
    public ReltioContract getReltioContract(@PathVariable String contractId) {
        return contractService.getContract(contractId);
    }

    @GetMapping("")
    @ReltioSecured(resourceClass = Pms.Contracts.class, privileges = ReltioPrivileges.READ)
    public Collection<ReltioContract> getReltioContractByAccountId(@RequestParam(value = "accountId", required = false) String accountId,
                                                                   @RequestParam(defaultValue = "true", value = "active", required = false) boolean active) {
        Collection<ReltioContract> result;
        if (accountId != null) {
            result = contractService.getContractsByAccountId(accountId);
        } else {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), "Please enter accountId parameter");
        }
        Date currentDate = new Date(System.currentTimeMillis());
        if (active) {
            result = result.stream().filter(
                    e -> Objects.nonNull(e.getStartDate()) &&
                            Objects.nonNull(e.getEndDate()) &&
                            e.getStartDate().before(currentDate) &&
                            e.getEndDate().after(currentDate) &&
                            !e.getPackages().isEmpty() &&
                            e.getStatus().equalsIgnoreCase(ContractStatus.Activated.name())
            ).collect(Collectors.toList());
        } else {
            result = result.stream().filter(
                    e -> Objects.isNull(e.getStartDate()) ||
                            Objects.isNull(e.getEndDate()) ||
                            e.getEndDate().before(currentDate) ||
                            e.getPackages().isEmpty() ||
                            e.getStatus().equalsIgnoreCase(ContractStatus.Expired.name())
            ).collect(Collectors.toList());
        }
        return result;
    }

    @PostMapping(value = "/bulkFetch", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Contracts.class, privileges = ReltioPrivileges.READ)
    public Collection<ReltioContract> getReltioContractByAccountIds(@RequestBody ContractRequest contractRequest) {
        if (CollectionUtils.isNotEmpty(contractRequest.getAccountIds())) {
            return contractService.getContractsByAccountIdAndSubscriptionType(contractRequest.getAccountIds(), contractRequest.getSubscriptionType());
        }
        return Collections.emptyList();
    }

    @PostMapping("/{contractId}")
    @ReltioSecured(resourceClass = Pms.Contracts.class, privileges = ReltioPrivileges.CREATE)
    public ReltioContract syncReltioContractFromSF(@PathVariable String contractId) {
        try {
            return contractService.syncContractById(contractId);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "Not able to sync contract " + e.getMessage());
        }
    }

    @GetMapping(value="/expired/getTenants/{contractId}")
    @ReltioSecured(resourceClass = Pms.Contracts.class)
    public Collection<ReltioTenant> getTenantsFromExpiredContract(@PathVariable String contractId) {
        return contractService.getDeleteableMdmTenants(contractId);
    }

    @GetMapping(value="/_total")
    @ReltioSecured(resourceClass = Pms.Contracts.class)
    public Long getActiveContractsCount() {
        return contractService.getActiveContractsCount();
    }

    @PutMapping(value="/expired/deleteTenants", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Contracts.class)
    public Map<String, TenantStatus> deleteTenantsFromExpiredContract(@RequestBody List<String> tenantIds) {
        return contractService.markTenantDeletedInDB(tenantIds);
    }

    @PostMapping("/sync")
    @ReltioSecured(resourceClass = Pms.Contracts.class, privileges = ReltioPrivileges.CREATE)
    public String syncReltioContractForAll(@Valid @RequestBody ProcessContract processContract) throws IOException {
        ReltioContract contract;
        String returnData = null;
        ObjectMapper objectMapper = new ObjectMapper();
        switch (processContract.getProcessType()) {
            case SYNC_CONTRACT:
                contract = contractService.syncContract(processContract.getContractId(), processContract.getContractStage());
                returnData = objectMapper.writeValueAsString(contract);
                break;

            case PROVISION_JOBS:
                contract = contractDeltaService.getDelta(processContract.getContractId(), processContract.getContractStage());
                Collection<JobWithTask> jobWithTaskCollection= contractDeltaService
                        .createProvisioningJob(processContract.getContractStage(),
                                processContract.getMatchDataArnList(), contract, processContract.getSkipTasks());
                returnData = objectMapper.writeValueAsString(jobWithTaskCollection);
                break;
            case DELTA_ONLY:
                contract = contractDeltaService.getDelta(processContract.getContractId(), processContract.getContractStage());
                returnData = objectMapper.writeValueAsString(contract);
                break;
        }
        return returnData;
    }
}
/*
    @GetMapping("/searchByStartDate")
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public Collection<ReltioContract> getContractByStartDate(@RequestParam(value = "startDateAfter", required = false) String after,
             @RequestParam(value = "startDateBefore", required = false) String before) {
        Long startBefore = convertToLong(before);
        Long startAfter = convertToLong(after);
        return contractService.getContractsByStartDate(startBefore, startAfter);
    }

    @GetMapping("/searchByEndDate")
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public Collection<ReltioContract> getContractByEndDate(@RequestParam(value = "endDateAfter", required = false) String after,
                                                     @RequestParam(value = "endDateBefore", required = false) String before) {
        Long endBefore = convertToLong(before);
        Long endAfter = convertToLong(after);
        return contractService.getContractsByEndDate(endBefore, endAfter);
    }

    @GetMapping("/{contractId}/jobs")
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public Collection<Job> getContractJobs(@PathVariable String contractId) {
        return jobService.getJobsForContract(contractId);
    }

    @GetMapping("/jobs")
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public Collection<Job> getJobs(@RequestParam(value = "customerId", required = false) String customerId,
                                   @RequestParam(value = "accountId", required = false) String accountId) {
        List<String> contractIds = new ArrayList<>();
        if (customerId != null && accountId == null) {
            for (ReltioContract c : contractService.getContractByCustomerId(customerId)) {
                contractIds.add(c.getContractId());
            }
        } else if (customerId == null && accountId != null) {
            for (ReltioContract c : contractService.getContractsByAccountId(accountId)) {
                contractIds.add(c.getContractId());
            }
        } else {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), "Please use either customerId or accountId parameter");
        }
        List<Job> result = new ArrayList<>();
        for (String contractId : contractIds) {
            result.addAll(jobService.getJobsForContract(contractId));
        }
        return result;
    }

    @GetMapping("/customer")
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public SalesAccount getCustomerForTenant(@RequestParam(value = "tenantId", required = false) String tenantId,
                                                 @RequestParam(value = "rdmTenantId", required = false) String rdmTenantId) {
        if (tenantId != null && rdmTenantId == null) {
            return contractService.getContractByTenant(tenantId).getCustomer();
        } else if (tenantId == null && rdmTenantId != null) {
            return contractService.getContractByRDMTenant(rdmTenantId).getCustomer();
        } else {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), "Please use either tenantId or rdmTenantId parameter");
        }
    }

    @GetMapping("/{contractNumber}/subscriptions")
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public Collection<SubscriptionRaw> getSubscriptionsByContractNumber(@PathVariable String contractNumber) {
        return contractService.getSubscriptionsByContractNumber(contractNumber);
    }



    @GetMapping("/{contractId}/delta")
    @ReltioSecured(resourceClass = Pms.Environment.class)
    public ContractDelta getDelta(@PathVariable String contractId) {
        return contractDeltaService.getDelta(contractId);
    }

    public long convertToLong(String time) {
        try {
            return Long.parseLong(time);
        } catch (NumberFormatException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INCORRECT_FORMAT_FOUND, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }



}*/
