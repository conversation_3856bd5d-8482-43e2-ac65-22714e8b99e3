package com.reltio.services.pms.common.model.jobs.tasks.updateR360Tenant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.common.sales.TenantPurpose;

import java.util.Objects;

public class UpdateR360TenantTaskInstance extends TaskInstance {


    @JsonProperty(value = "shortDescription")
    private final String shortDescription;

    @JsonProperty(value = "mdmtenantID")
    private final String mdmtenantID;

    @JsonProperty(value = "tenantRecordType")
    private final String tenantRecordType;

    @JsonProperty(value = "ownedByReltioDept")
    private final String ownedByReltioDept;

    @JsonProperty(value = "contractId")
    private final String contractId;

    @JsonProperty(value = "rdmTenantId")
    private final String rdmTenantId;

    @JsonProperty(value = "tenantPurpose")
    private final TenantPurpose tenantPurpose;



    @JsonCreator
    public UpdateR360TenantTaskInstance(@JsonProperty(value = "id") String id,
                                        @JsonProperty(value = "name") String name,
                                        @JsonProperty(value = "jobId") String jobId,
                                        @JsonProperty(value = "startTime") Long startTime,
                                        @JsonProperty(value = "finishTime") Long finishTime,
                                        @JsonProperty(value = "status") TaskStatus status,
                                        @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                        @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                        @JsonProperty(value = "executingNodeName") String executingNodeName,
                                        @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                        @JsonProperty(value = "envId") String envId,
                                        @JsonProperty(value = "shortDescription") String shortDescription,
                                        @JsonProperty(value = "mdmtenantID") String mdmtenantID,
                                        @JsonProperty(value = "tenantRecordType", defaultValue = "Customer Tenant") String tenantRecordType,
                                        @JsonProperty(value = "ownedByReltioDept") String ownedByReltioDept,
                                        @JsonProperty(value = "contractId") String contractId,
                                        @JsonProperty(value = "rdmTenantId") String rdmTenantId,
                                        @JsonProperty(value = "tenantPurpose") TenantPurpose tenantPurpose)
    {
        super(id, name, jobId, startTime, finishTime, TaskType.UPDATE_R360_TENANT_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.shortDescription = shortDescription;
        this.mdmtenantID = mdmtenantID;
        this.tenantRecordType = tenantRecordType;
        this.ownedByReltioDept = ownedByReltioDept;
        this.contractId = contractId;
        this.rdmTenantId = rdmTenantId;
        this.tenantPurpose = tenantPurpose;
    }


    public String getContractId() {
        return contractId;
    }

    public String getShortDescription() {
        return shortDescription;
    }


    public String getTenantRecordType() {
        return tenantRecordType;
    }


    public String getMdmtenantID() {
        return mdmtenantID;
    }

    public String getOwnedByReltioDept() {
        return ownedByReltioDept;
    }


    public String getRdmTenantId() {
        return rdmTenantId;
    }

    public TenantPurpose getTenantPurpose() {
        return tenantPurpose;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof UpdateR360TenantTaskInstance)) return false;
        if (!super.equals(o)) return false;
        UpdateR360TenantTaskInstance that = (UpdateR360TenantTaskInstance) o;
        return getShortDescription().equals(that.getShortDescription()) && getMdmtenantID().equals(that.getMdmtenantID()) && getTenantRecordType().equals(that.getTenantRecordType()) && getOwnedByReltioDept().equals(that.getOwnedByReltioDept()) && getContractId().equals(that.getContractId()) && getRdmTenantId().equals(that.getRdmTenantId()) && getTenantPurpose() == that.getTenantPurpose();
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getShortDescription(), getMdmtenantID(), getTenantRecordType(), getOwnedByReltioDept(), getContractId(), getRdmTenantId(), getTenantPurpose());
    }

    @Override
    public String toString() {
        return "UpdateR360TenantTaskInstance{" +
                "shortDescription='" + shortDescription + '\'' +
                ", mdmtenantID='" + mdmtenantID + '\'' +
                ", tenantRecordType='" + tenantRecordType + '\'' +
                ", ownedByReltioDept='" + ownedByReltioDept + '\'' +
                ", contractId='" + contractId + '\'' +
                ", rdmTenantId='" + rdmTenantId + '\'' +
                ", tenantPurpose=" + tenantPurpose +
                '}';
    }
}
