package com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.separated.execution;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.AnalyticsCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.DataCleanUpOptions;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.ExternalServicesCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.MetaDataCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.Parameters;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class CleanTenantExecutionTaskInstance extends TaskInstance {

    private final DataCleanUpOptions dataCleanUpOptions;

    private final AnalyticsCleanUp analyticsCleanUp;

    private final MetaDataCleanUp metaDataCleanUp;

    private final boolean externalQueue;

    private final ExternalServicesCleanUp externalServicesCleanUp;

    private final String tenantName;

    private final List<String> events;

    private final List<String> currentState;

    private final Parameters parameters;

    private final boolean qaAutomation;
    private final Long duration;

    @JsonCreator
    public CleanTenantExecutionTaskInstance(@JsonProperty(value = "taskId") String taskId,
                                            @JsonProperty(value = "name") String name,
                                            @JsonProperty(value = "jobId") String jobId,
                                            @JsonProperty(value = "startTime") Long startTime,
                                            @JsonProperty(value = "finishTime") Long finishTime,
                                            @JsonProperty(value = "status") TaskStatus status,
                                            @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                            @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                            @JsonProperty(value = "executingNodeName") String executingNodeName,
                                            @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                            @JsonProperty(value = "envId") String envId,
                                            @JsonProperty(value = "dataCleanUpOptions") DataCleanUpOptions dataCleanUpOptions,
                                            @JsonProperty(value = "externalServicesCleanUp") ExternalServicesCleanUp externalServicesCleanUp,
                                            @JsonProperty(value = "externalQueue") boolean externalQueue,
                                            @JsonProperty(value = "metaDataCleanUp") MetaDataCleanUp metaDataCleanUp,
                                            @JsonProperty(value = "analyticsCleanUp") AnalyticsCleanUp analyticsCleanUp,
                                            @JsonProperty(value = "tenantName") String tenantName,
                                            @JsonProperty(value = "events") List<String> events,
                                            @JsonProperty(value = "currentState") List<String> currentState,
                                            @JsonProperty(value = "parameters") Parameters parameters,
                                            @JsonProperty(value = "qaAutomation") boolean qaAutomation) {
        super(taskId, name, jobId, startTime, finishTime, TaskType.DELETE_TENANT_DATA_EXECUTION_TASK, status,
                lastUpdatedTime, taskFailureContext, executingNodeName, serviceNodeStatus, envId);
        this.dataCleanUpOptions = dataCleanUpOptions;
        this.externalServicesCleanUp = externalServicesCleanUp;
        this.externalQueue = externalQueue;
        this.metaDataCleanUp = metaDataCleanUp;
        this.analyticsCleanUp = analyticsCleanUp;
        this.tenantName = tenantName;
        this.events = events == null ? new ArrayList<>() : new ArrayList<>(events);
        this.currentState = currentState == null ? new ArrayList<>() : new ArrayList<>(currentState);
        this.parameters = parameters;
        this.qaAutomation = qaAutomation;
        if (finishTime == null) {
            this.duration = startTime == 0 ? 0 : System.currentTimeMillis() - startTime;
        } else {
            this.duration = finishTime - startTime;
        }
    }

    public DataCleanUpOptions getDataCleanUpOptions() {
        return dataCleanUpOptions;
    }

    public AnalyticsCleanUp getAnalyticsCleanUp() {
        return analyticsCleanUp;
    }

    public MetaDataCleanUp getMetaDataCleanUp() {
        return metaDataCleanUp;
    }

    public boolean isExternalQueue() {
        return externalQueue;
    }

    public ExternalServicesCleanUp getExternalServicesCleanUp() {
        return externalServicesCleanUp;
    }

    public String getTenantName() {
        return tenantName;
    }

    public List<String> getEvents() {
        return events;
    }

    public void addEvent(String event) {
        events.add(event);
    }

    public Parameters getParameters() {
        return parameters;
    }

    public boolean isQaAutomation() {
        return qaAutomation;
    }

    public Long getDuration() {
        return duration;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        CleanTenantExecutionTaskInstance that = (CleanTenantExecutionTaskInstance) o;
        return externalQueue == that.externalQueue && dataCleanUpOptions.equals(that.dataCleanUpOptions) && analyticsCleanUp.equals(that.analyticsCleanUp) && metaDataCleanUp.equals(that.metaDataCleanUp) && externalServicesCleanUp.equals(that.externalServicesCleanUp) && tenantName.equals(that.tenantName) && Objects.equals(events, that.events) && Objects.equals(currentState, that.currentState) && parameters.equals(that.parameters);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), dataCleanUpOptions, analyticsCleanUp, metaDataCleanUp, externalQueue, externalServicesCleanUp, tenantName, events, currentState);
    }
}
