package com.reltio.services.pms.common.model.jobs.tasks.auth;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.config.CustomSettings;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServiceEnablementBaseTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;


@JsonDeserialize(using = AuthTaskInstanceDeserializer.class)
public class AuthTaskInstance extends ServiceEnablementBaseTaskInstance {

    private CustomerConfig customerConfig;
    private ClientConfig clientConfig;
    private UserConfig userConfig;
    private List<GroupConfig> groupConfig;
    private String rdmTenantId;
    private Boolean reuseRdmTenant;

    @JsonCreator
    public AuthTaskInstance(@JsonProperty(value = "taskId") String taskId,
                            @JsonProperty(value = "name") String name,
                            @JsonProperty(value = "jobId") String jobId,
                            @JsonProperty(value = "startTime") Long startTime,
                            @JsonProperty(value = "finishTime") Long finishTime,
                            @JsonProperty(value = "status") TaskStatus status,
                            @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                            @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                            @JsonProperty(value = "executingNodeName") String executingNodeName,
                            @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                            @JsonProperty(value = "failedTenants") Set<String> failedTenants,
                            @JsonProperty(value = "totalProcessedTenants", defaultValue = "0") int totalProcessedTenants,
                            @JsonProperty(value = "tenantsForServiceEnablement") Set<String> tenantsForServiceEnablement,
                            @JsonProperty(value = "events") Map<String, Set<String>> events,
                            @JsonProperty(value = "envId") String envId,
                            @JsonProperty(value = AuthTaskConstants.CUSTOMER_CONFIG) CustomerConfig customerConfig,
                            @JsonProperty(value = AuthTaskConstants.CLIENT_CONFIG) ClientConfig clientConfig,
                            @JsonProperty(value = AuthTaskConstants.USER_CONFIG) UserConfig userConfig,
                            @JsonProperty(value = AuthTaskConstants.GROUP_CONFIG) List<GroupConfig> groupConfig,
                            @JsonProperty(value = "rdmTenantId") String rdmTenantId,
                            @JsonProperty(value = "reuseRdmTenant") Boolean reuseRdmTenant) {
        super(taskId, name, jobId, startTime, finishTime, TaskType.AUTH_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId, failedTenants, totalProcessedTenants,
                tenantsForServiceEnablement, events);
        this.customerConfig = customerConfig;
        this.clientConfig = clientConfig;
        this.userConfig = userConfig;
        this.groupConfig = groupConfig;
        this.rdmTenantId = rdmTenantId;
        this.reuseRdmTenant = reuseRdmTenant;
    }

    public CustomerConfig getCustomerConfig() {
        return customerConfig;
    }

    public ClientConfig getClientConfig() {
        return clientConfig;
    }

    public UserConfig getUserConfig() {
        return userConfig;
    }

    public List<GroupConfig> getGroupConfig(){return groupConfig;}

    public void setCustomerConfig(CustomerConfig customerConfig) {
        this.customerConfig = customerConfig;
    }

    public void setClientConfig(ClientConfig clientConfig) {
        this.clientConfig = clientConfig;
    }

    public void setUserConfig(UserConfig userConfig) {
        this.userConfig = userConfig;
    }

    public String getRdmTenantId() {
        return rdmTenantId;
    }

    public Boolean getReuseRdmTenant() {
        return reuseRdmTenant;
    }

    public void setReuseRdmTenant(Boolean reuseRdmTenant) {
        this.reuseRdmTenant = reuseRdmTenant;
    }

    public void setRdmTenantId(String rdmTenantId) {
        this.rdmTenantId = rdmTenantId;
    }


    public static class UserConfig {

        private String requesterSpecificRoleName;
        private String accessLevel;
        private String roleMappingName;
        private Set<String> owners;
        private AdditionalUsers additionalUsers;
        private String userNamePrefix;
        private Boolean defaultPassword;

        @JsonCreator
        public UserConfig(@JsonProperty(AuthTaskConstants.PREASSIGNED_ROLE_NAME) String requesterSpecificRoleName,
                          @JsonProperty(AuthTaskConstants.ACCESS_LEVEL) String accessLevel,
                          @JsonProperty(AuthTaskConstants.ROLE_MAPPING_NAME) String roleMappingName,
                          @JsonProperty(AuthTaskConstants.TENANT_OWNERS) Set<String> owners,
                          @JsonProperty(AuthTaskConstants.ADDITIONAL_USERS) AdditionalUsers additionalUsers,
                          @JsonProperty(AuthTaskConstants.USER_NAME_PREFIX) String userNamePrefix,
                          @JsonProperty(AuthTaskConstants.DEFAULT_PASSWORD) Boolean defaultPassword) {
            this.requesterSpecificRoleName = requesterSpecificRoleName;
            this.accessLevel = accessLevel;
            this.roleMappingName = roleMappingName;
            this.owners = owners;
            this.additionalUsers = additionalUsers;
            this.userNamePrefix = userNamePrefix;
            this.defaultPassword = defaultPassword == null ? Boolean.FALSE : defaultPassword;
        }

        public UserConfig() {
        }

        public void setRequesterSpecificRoleName(String requesterSpecificRoleName) {
            this.requesterSpecificRoleName = requesterSpecificRoleName;
        }

        public void setAccessLevel(String accessLevel) {
            this.accessLevel = accessLevel;
        }

        public void setRoleMappingName(String roleMappingName) {
            this.roleMappingName = roleMappingName;
        }

        public void setOwners(Set<String> owners) {
            this.owners = owners;
        }

        public String getRequesterSpecificRoleName() {
            return requesterSpecificRoleName;
        }

        public String getAccessLevel() {
            return accessLevel;
        }

        public String getRoleMappingName() {
            return roleMappingName;
        }

        public Set<String> getOwners() {
            return owners;
        }

        public void setAdditionalUsers(AdditionalUsers additionalUsers){this.additionalUsers=additionalUsers;}

        public AdditionalUsers getAdditionalUsers(){return additionalUsers;}

        public String getUserNamePrefix(){return userNamePrefix; }

        public Boolean getDefaultPassword(){return defaultPassword; }

        public void setUserNamePrefix(String userNamePrefix){this.userNamePrefix=userNamePrefix ;}

        public void setDefaultPassword(Boolean defaultPassword){this.defaultPassword = defaultPassword ;}


    }

    public static class CustomerConfig {

        private JsonNode roleBody;
        private String customerId;
        private CustomSettings customSettings;
        private boolean caseSensitiveLoginEnabled;
        private String customerIdPrefix;

        @JsonCreator
        public CustomerConfig(@JsonProperty(AuthTaskConstants.CUSTOMER_SPECIFIC_ROLE_BODY) JsonNode roleBody,
                              @JsonProperty(AuthTaskConstants.CUSTOMER_ID) String customerId,
                              @JsonProperty(AuthTaskConstants.CUSTOMER_SETTINGS) CustomSettings customSettings,
                              @JsonProperty(AuthTaskConstants.CASE_SENSITIVE_LOGIN_ENABLED) boolean caseSensitiveLoginEnabled,
                              @JsonProperty(AuthTaskConstants.CUSTOMER_ID_PREFIX) String customerIdPrefix) {
            this.roleBody = roleBody;
            this.customerId = customerId;
            this.customSettings = customSettings;
            this.caseSensitiveLoginEnabled = caseSensitiveLoginEnabled;
            this.customerIdPrefix = customerIdPrefix;
        }

        public CustomerConfig() {
        }

        public void setRoleBody(JsonNode roleBody) {
            this.roleBody = roleBody;
        }

        public void setCustomerId(String customerId) {
            this.customerId = customerId;
        }

        public JsonNode getRoleBody() {
            return roleBody;
        }

        public String getCustomerId() {
            return customerId;
        }

        public CustomSettings getCustomSettings() {
            return customSettings;
        }

        public void setCustomSettings(CustomSettings customSettings) {
            this.customSettings = customSettings;
        }

        public void setCaseSensitiveLoginEnabled(boolean caseSensitiveLoginEnabled) {
            this.caseSensitiveLoginEnabled = caseSensitiveLoginEnabled;
        }
        public boolean isCaseSensitiveLoginEnabled() {
            return caseSensitiveLoginEnabled;
        }

        public String getCustomerIdPrefix() {
            return customerIdPrefix;
        }

        public void setCustomerIdPrefix(String customerIdPrefix) {
            this.customerIdPrefix = customerIdPrefix;
        }

    }

    public static class ClientConfig {

        private Long clientsCount;
        private Set<String> applicationClients;

        @JsonCreator
        public ClientConfig(@JsonProperty(AuthTaskConstants.CLIENTS_COUNT) Long clientsCount,
                            @JsonProperty(AuthTaskConstants.CLIENTS) Set<String> applicationClients) {
            this.clientsCount = clientsCount;
            this.applicationClients = applicationClients;
        }

        public ClientConfig() {
        }

        public void setClientsCount(Long clientsCount) {
            this.clientsCount = clientsCount;
        }

        public void setApplicationClients(Set<String> applicationClients) {
            this.applicationClients = applicationClients;
        }

        public Long getClientsCount() {
            return clientsCount;
        }

        public Set<String> getApplicationClients() {
            return applicationClients == null ? new HashSet<>(): applicationClients;
        }
    }

    public static class GroupConfig {
        private String groupId;
        private String customer;
        private Set<String> roles;

        @JsonCreator
        public GroupConfig(@JsonProperty(AuthTaskConstants.GROUP_ID) String groupId,
                           @JsonProperty(AuthTaskConstants.Customer) String customer,
                           @JsonProperty(AuthTaskConstants.ROLES) Set<String> roles) {
            this.groupId = groupId;
            this.customer = customer;
            this.roles = roles;
        }

        public GroupConfig() {
        }

        public void setGroupId(String groupId){this.groupId=groupId;}

        public void setCustomer(String customer){this.customer=customer;}

        public void setRoles(Set<String> roles){this.roles=roles;}

        public String getGroupId(){return this.groupId;}

        public String getCustomer(){return this.customer;}

        public Set<String> getRoles(){return this.roles;}

    }

    public static class AdditionalUsers {
        private String accessLevel;
        private Set<String> userMails;

        @JsonCreator
        public AdditionalUsers(@JsonProperty(value = "accessLevel") String accessLevel,
                               @JsonProperty(value = "userMails") Set<String> userMails) {
            this.accessLevel = accessLevel;
            this.userMails = userMails;
        }

        public AdditionalUsers() {

        }

        public void setAccessLevel(String accessLevel){this.accessLevel=accessLevel;}
        public void setUserMails(Set<String> userMails){this.userMails=userMails;}
        public String getAccessLevel(){return accessLevel;}
        public Set<String> getUserMails(){return userMails;}
    }
}
