package com.reltio.services.pms.common.model.enterprise.tenants;

import com.reltio.services.pms.common.model.jobs.tasks.approve.storage.ApprovalOfStorageTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.auth.AuthTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.azureBlobStorage.AzureBlobStorageTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.dnb.connector.DNBConnectorTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.dtss.DTSSTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.email.notification.EmailNotificationTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.fern.FernTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.imagehosting.ImageHostingTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.mdm.MdmTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.notification.EnterpriseNotificationTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.rdm.RDMServiceDeploymentTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.rdm.RdmTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.rih.RihGenericTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.sfdcconnector.SFDCConnectorEnablementTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.streaming.provision.StreamingTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.update.tenant.UpdateTenantTaskInstanceParms;
import com.reltio.services.pms.common.model.jobs.tasks.updateR360Tenant.UpdateR360TenantTaskInstanceParms;
import com.reltio.services.pms.common.model.jobs.tasks.workato.WorkatoTaskInstanceParms;
import com.reltio.services.pms.common.model.jobs.tasks.workflow.WorkflowTaskInstanceParams;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import lombok.Setter;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class EnterpriseTenantCreationJobInstanceParams implements WorkflowTaskInstanceParams, MdmTaskInstanceParams, StreamingTaskInstanceParams, AuthTaskInstanceParams, RdmTaskInstanceParams, WorkatoTaskInstanceParms,
        ApprovalOfStorageTaskInstanceParams, ImageHostingTaskInstanceParams, EnterpriseNotificationTaskInstanceParams,
        SFDCConnectorEnablementTaskInstanceParams, DNBConnectorTaskInstanceParams, DTSSTaskInstanceParams,
        UpdateTenantTaskInstanceParms, UpdateR360TenantTaskInstanceParms, EmailNotificationTaskInstanceParams, FernTaskInstanceParams,
        AzureBlobStorageTaskInstanceParams, RihGenericTaskInstanceParams, RDMServiceDeploymentTaskInstanceParams {

    private final String customerName;
    private final String customerId;
    private final String tenantId;
    private final TenantSize tenantSize;
    private final String industry;
    private final List<String> cleanseRegions;
    private final List<String> owners;
    private final String rdmTenantId;
    private final boolean configureDefaultQueue;
    private final TenantPurpose tenantPurpose;
    private final Set<String> clientNames;
    private final String productEdition;
    private final Boolean reuseRdmTenant;
    private final boolean enableRiq;
    private final String defaultStorageTemplate;
    private final String streamingCloud;
    private final String loqateProcesses;
    private final String dataStorageArn;
    private final String matchStorageArn;
    private final String shortDescription;
    private final String tenantRecordType;
    private final String ownedByReltioDept;
    private final String contractId;
    private final String dnbApiSecret;
    private final String dnbApiKey;
    private final String rdmHostNameUrl;
    private final Boolean isQaAutomation;
    private final String newInstanceId;
    private final Set<String> queueNames;
    private final ReltioPackageType reltioPackageType;
    private final Boolean useSpannerCloudFunction;
    @Setter
    private boolean warmupRequired;
    @Setter
    private String vaultKey;
    @Setter
    private boolean keyRequired;
    @Setter
    private boolean useServiceBusFunction;
    @Setter
    private boolean bceTenant;
    @Setter
    private String awsAccountId;
    @Setter
    private String gitBranchName;
    @Setter
    private Set<String> predicateRegions;

    public EnterpriseTenantCreationJobInstanceParams(String customerName,
                                                     String customerId,
                                                     String tenantId,
                                                     TenantSize tenantSize,
                                                     String industry,
                                                     List<String> cleanseRegions,
                                                     List<String> owners,
                                                     boolean configureDefaultQueue,
                                                     String rdmTenantId,
                                                     boolean reuseRdmTenant,
                                                     TenantPurpose tenantPurpose,
                                                     Set<String> clientNames,
                                                     String productEdition,
                                                     boolean enableRiq,
                                                     String defaultStorageTemplate,
                                                     String streamingCloud,
                                                     String loqateProcesses,
                                                     String dataStorageArn,
                                                     String matchStorageArn,
                                                     String shortDescription,
                                                     String tenantRecordType,
                                                     String ownedByReltioDept,
                                                     String contractId,
                                                     String dnbApiSecret,
                                                     String dnbApiKey, String rdmHostNameUrl,
                                                     Boolean isQaAutomation, String newInstanceId, Set<String> queueNames,
                                                     ReltioPackageType reltioPackageType,
                                                     Boolean useSpannerCloudFunction) {
        this.customerName = customerName;
        this.customerId = customerId;
        this.tenantId = tenantId;
        this.tenantSize = tenantSize;
        this.industry = industry;
        this.cleanseRegions = cleanseRegions;
        this.owners = owners;
        this.configureDefaultQueue = configureDefaultQueue;
        this.rdmTenantId = rdmTenantId;
        this.reuseRdmTenant = reuseRdmTenant;
        this.tenantPurpose = tenantPurpose;
        this.clientNames = clientNames;
        this.productEdition = productEdition;
        this.enableRiq = enableRiq;
        this.defaultStorageTemplate = defaultStorageTemplate;
        this.streamingCloud = streamingCloud;
        this.loqateProcesses = loqateProcesses;
        this.dataStorageArn = dataStorageArn;
        this.matchStorageArn = matchStorageArn;
        this.shortDescription = shortDescription;
        this.tenantRecordType = tenantRecordType;
        this.ownedByReltioDept = ownedByReltioDept;
        this.contractId = contractId;
        this.dnbApiSecret = dnbApiSecret;
        this.dnbApiKey = dnbApiKey;
        this.rdmHostNameUrl = rdmHostNameUrl;
        this.isQaAutomation = isQaAutomation != null && isQaAutomation;
        this.newInstanceId = newInstanceId;
        this.queueNames = queueNames;
        this.reltioPackageType = reltioPackageType;
        this.useSpannerCloudFunction = useSpannerCloudFunction == null ? Boolean.TRUE : useSpannerCloudFunction;
    }

    @Override
    public String getContractId() {
        return contractId;
    }

    @Override
    public String getCustomerId() {
        return customerId;
    }

    @Override
    public boolean isEnableRiq() {
        return enableRiq;
    }

    @Override
    public String getRdmTenantId() {
        return rdmTenantId;
    }

    @Override
    public List<String> getMdmTenantId() {
        if (tenantId != null) {
            return Collections.singletonList(tenantId);
        }
        return Collections.emptyList();
    }

    @Override
    public Boolean reuseRdmTenant() {
        return reuseRdmTenant;
    }

    @Override
    public String getLoqateProcesses() {
        return loqateProcesses;
    }

    @Override
    public String getDataStorageArn() {
        return dataStorageArn;
    }

    @Override
    public String getMatchStorageArn() {
        return matchStorageArn;
    }

    @Override
    public String getCustomerName() {
        return customerName;
    }

    @Override
    public Set<String> getEmailsToAuthorizeAccess() {
        return new HashSet<>(owners);
    }

    @Override
    public String getTenantId() {
        return tenantId;
    }

    @Override
    public boolean isConfigureDefaultQueue() {
        return configureDefaultQueue;
    }

    @Override
    public String streamingCloud() {
        return streamingCloud;
    }

    @Override
    public Set<String> getQueueNames() {
        if (queueNames == null) {
            return Collections.emptySet();
        }
        return new HashSet<>(queueNames);
    }

    @Override
    public TenantPurpose getTenantPurpose() {
        return tenantPurpose;
    }


    @Override
    public Set<String> getTenantsForServiceEnablement() {
        return Collections.singleton(tenantId);
    }

    @Override
    public Set<String> getClients() {
        return clientNames;
    }

    @Override
    public String getShortDescription() {
        return shortDescription;
    }

    @Override
    public String getmdmTenantID() {
        return tenantId;
    }

    @Override
    public String getTenantRecordType() {
        return tenantRecordType;
    }

    @Override
    public String getOwnedByReltioDept() {
        return ownedByReltioDept;
    }

    @Override
    public String getProductEdition() {
        return productEdition;
    }

    @Override
    public TenantSize getTenantSize() {
        return tenantSize;
    }

    @Override
    public String getIndustry() {
        return industry;
    }

    @Override
    public List<String> getCleanseRegions() {
        return cleanseRegions;
    }

    @Override
    public String getDefaultStorageTemplate() {
        return defaultStorageTemplate;
    }

    @Override
    public Boolean validateSameCustomerId() {
        return Boolean.TRUE;
    }

    @Override
    public String getRdmHostNameUrl() {
        return rdmHostNameUrl;
    }

    @Override
    public List<String> getOwners() {
        return owners;
    }

    @Override
    public String getOfferType() {
        return productEdition;
    }

    @Override
    public String getDnbApiSecret() {
        return dnbApiSecret;
    }

    @Override
    public String getDnbApiKey() {
        return dnbApiKey;
    }

    @Override
    public Boolean getIsQaAutomation() {
        return isQaAutomation;
    }

    @Override
    public String getNewInstanceId() {
        return newInstanceId;
    }

    @Override
    public ReltioPackageType getReltioPackageType() {
        return reltioPackageType;
    }

    @Override
    public String getAwsAccountId() {
        return this.awsAccountId;
    }

    @Override
    public String getGitBranchName() {
        return this.gitBranchName;
    }

    @Override
    public Boolean getUseSpannerCloudFunction() {
        return useSpannerCloudFunction;
    }

    @Override
    public boolean isWarmupRequired() {
        return this.warmupRequired;
    }

    @Override
    public String getvaultKey() {
        return this.vaultKey;
    }

    @Override
    public boolean isKeyRequired() {
        return this.keyRequired;
    }

    @Override
    public boolean getUseServiceBusFunction() {
        return useServiceBusFunction;
    }

    @Override
    public boolean isBceTenant(){
        return this.bceTenant;
    }

    @Override
    public Set<String> getPredicateRegions(){
        return this.predicateRegions;
    }

}
