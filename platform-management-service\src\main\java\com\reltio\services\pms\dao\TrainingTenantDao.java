package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.sales.model.TrainingTenant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service public class TrainingTenantDao extends AbstractRootCollectionDao<TrainingTenant> {
    private static final String TRAINING_TENANTS_COLLECTION_NAME = "PMS_TRAINING_TENANTS";

    @Autowired
    public TrainingTenantDao(CredentialsProvider provider,
                             @Value("${firestore.env.name}") String deployedEnv,
                             ReltioUserHolder reltioUserHolder){
        super(provider,TRAINING_TENANTS_COLLECTION_NAME,deployedEnv,reltioUserHolder);
    }

    @Override
    protected TypeReference<TrainingTenant> getTypeReference() {
        return new TypeReference<TrainingTenant>() {
        };

    }
}
