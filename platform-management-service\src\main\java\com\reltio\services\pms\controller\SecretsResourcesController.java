package com.reltio.services.pms.controller;

import com.reltio.services.pms.common.model.secrets.KeyDto;
import com.reltio.services.pms.common.model.secrets.SecretDto;
import com.reltio.services.pms.controller.api.SecretsResources;
import com.reltio.services.pms.service.SecretService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * SecretsResourcesController
 * Created by apy<PERSON><PERSON>
 */
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SecretsResourcesController implements SecretsResources {

    public static final String STATUS_OK = "{\"status\":\"ok\"}";
    private final SecretService secretService;

    @Override
    public ResponseEntity<KeyDto> createKey(String environment, String service, SecretDto secretDto) {
        return ResponseEntity.ok(KeyDto.builder()
                .key(secretService.add(environment, service, secretDto))
                .build());
    }

    @Override
    public ResponseEntity<KeyDto> updateKey(String environment, String service, SecretDto secretDto) {
        return ResponseEntity.ok(KeyDto.builder().key(secretService.update(environment, service, secretDto)).build());
    }

    @Override
    public ResponseEntity<String> deleteKey(String environment, String service) {
        secretService.delete(environment, service);
        return ResponseEntity.ok(STATUS_OK);
    }

    @Override
    public ResponseEntity<List<String>> listKeys(String environment, String service) {
        return ResponseEntity.ok(secretService.list(environment, service));
    }
}
