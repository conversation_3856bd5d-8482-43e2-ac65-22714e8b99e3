package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.Getter;

@Getter
public class CloneTenantConfigsPipeline extends AbstractPipelineTaskConfig {

    @JsonCreator
    public CloneTenantConfigsPipeline(@JsonProperty(value = "name") String name) {
        super(name, TaskType.CLONE_TENANTS_CONFIGS_TASK);
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.CLONE_CONFIG;
    }
}
