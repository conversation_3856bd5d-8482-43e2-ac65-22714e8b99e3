package com.reltio.services.pms.service.compliance.impl;

import com.google.cloud.bigquery.FieldValue;
import com.google.cloud.bigquery.FieldValueList;
import com.google.cloud.bigquery.QueryJobConfiguration;
import com.google.cloud.bigquery.TableResult;
import com.reltio.services.pms.common.model.compliance.SortField;
import com.reltio.services.pms.common.model.compliance.SortOrder;
import com.reltio.services.pms.common.model.compliance.request.ApiUsageReportRequest;
import com.reltio.services.pms.common.model.compliance.request.ApiUsageRequest;
import com.reltio.services.pms.common.model.compliance.request.ComplianceReportRequest;
import com.reltio.services.pms.common.model.compliance.request.ComplianceRequest;
import com.reltio.services.pms.common.model.compliance.request.CpUsageReportRequest;
import com.reltio.services.pms.common.model.compliance.request.CpUsageRequest;
import com.reltio.services.pms.common.model.compliance.response.ApiUsageResponse;
import com.reltio.services.pms.common.model.compliance.response.CpUsageCountResponse;
import com.reltio.services.pms.common.model.compliance.response.CpUsageEntityResponse;
import com.reltio.services.pms.common.model.compliance.response.CpUsageResponse;
import com.reltio.services.pms.common.model.compliance.response.RsuUsageResponse;
import com.reltio.services.pms.common.model.compliance.response.TaskUsageResponse;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByDateModel;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByDateResponse;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByEndpointModel;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByEndpointResponse;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByTenantModel;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByTenantResponse;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByEnvironmentModel;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByTenantModel;
import com.reltio.services.pms.common.model.compliance.response.cpusage.ApiByMonthModel;
import com.reltio.services.pms.common.model.compliance.response.cpusage.ApiByMonthResponse;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByDateModel;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByDateResponse;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByMonthModel;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByMonthResponse;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByTenantModel;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByTenantResponse;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuBreakdownModel;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuBreakdownResponse;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuByTenantResponse;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuUsageByDateResponse;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuUsageByMonthResponse;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuUsageByTenantModel;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuUsageDateModel;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuUsageMonthModel;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.service.compliance.ComplianceService;
import com.reltio.services.pms.service.compliance.impl.gbq.QueryBuilder;
import com.reltio.services.pms.service.compliance.util.ComplianceServiceImplUtil;
import org.apache.commons.configuration.Configuration;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import jakarta.validation.Valid;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import java.util.Optional;


/**
 * The type Compliance service.
 */
@Service
public class ComplianceServiceImpl extends ComplianceServiceImplUtil implements ComplianceService  {


    /**
     * The constant decfor.
     */
    private static final DecimalFormat decfor = new DecimalFormat("0.00");

    /**
     * Instantiates a new Gbq service.
     *
     * @param configuration the configuration
     */
    protected ComplianceServiceImpl(Configuration configuration) {
        super(configuration);
    }


    /**
     * Get String value but in case field value is null return default
     *
     * @param fieldValue field value to get value from
     * @return String value
     */
    public String getStringOrDefault(FieldValue fieldValue) {
        if (fieldValue.isNull() || "null".equals(fieldValue.getStringValue())) {
            return StringUtils.EMPTY;
        }

        return fieldValue.getStringValue();
    }



    /**
     * Gets task usage.
     *
     * @param complianceRequest the task usage request
     * @return the task usage
     */
    @Override
    public List<TaskUsageResponse> getTaskUsage(ComplianceRequest complianceRequest) {
        List<TaskUsageResponse> taskUsageList = new ArrayList<>();
        TableResult results = execute(buildTaskUsageQueryJob(complianceRequest));
        for (FieldValueList row : results.iterateAll()) {
            taskUsageList.add(new TaskUsageResponse(
                    row.get(0).getStringValue(),
                    row.get(1).getStringValue(),
                    row.get(2).getStringValue(),
                    row.get(3).getStringValue(),
                    row.get(4).getStringValue(),
                    row.get(5).getStringValue()));
        }
        return taskUsageList;
    }


    /**
     * Gets rsu usage.
     *
     * @param complianceRequest the rsu usage request
     * @return the rsu usage
     */
    @Override
    public List<RsuUsageResponse> getRsuUsage(ComplianceRequest complianceRequest) {
        List<RsuUsageResponse> rsuUsageList = new ArrayList<>();
        TableResult results = execute(buildRsuUsageQueryJob(complianceRequest));
        for (FieldValueList row : results.iterateAll()) {
            long indexValue = row.get(10).getLongValue();
            long fullCount = row.get(11).getLongValue();
            rsuUsageList.add(new RsuUsageResponse(
                    row.get(0).getStringValue(),
                    row.get(1).getStringValue(),
                    row.get(2).getStringValue(),
                    row.get(3).getLongValue(),
                    row.get(4).getLongValue(),
                    row.get(5).getLongValue(),
                    row.get(6).getLongValue(),
                    row.get(7).getLongValue(),
                    row.get(8).getLongValue(),
                    row.get(9).getLongValue(),
                    indexValue,
                    fullCount));
        }
        return rsuUsageList;
    }




    /**
     * Gets cp usage.
     *
     * @param cpUsageRequest the cp usage request
     * @param export         the export
     * @return the cp usage
     */
    @Override
    public List<CpUsageResponse> getCpUsage(CpUsageRequest cpUsageRequest, Boolean export) {
        List<CpUsageResponse> cpUsageResponseList = new ArrayList<>();
        List<QueryJobConfiguration> queryJobConfigurations = buildCpUsageQueryJob(cpUsageRequest, export);

        for (QueryJobConfiguration queryJob : queryJobConfigurations) {
            TableResult results = execute(queryJob);

            if (cpUsageRequest.isEntityTypeBreakdown()) {
                for (FieldValueList row : results.iterateAll()) {
                    cpUsageResponseList.add(new CpUsageEntityResponse(
                            getStringOrDefault(row.get(0)),
                            getStringOrDefault(row.get(1)),
                            getStringOrDefault(row.get(2)),
                            getStringOrDefault(row.get(3)),
                            getStringOrDefault(row.get(4)),
                            getStringOrDefault(row.get(5))
                    ));
                }
            } else {
                for (FieldValueList row : results.iterateAll()) {
                    cpUsageResponseList.add(new CpUsageCountResponse(
                            getStringOrDefault(row.get(0)),
                            getStringOrDefault(row.get(1)),
                            getStringOrDefault(row.get(2)),
                            getStringOrDefault(row.get(3))
                    ));
                }
            }
        }

        return cpUsageResponseList;
    }



    /**
     * Gets api usage.
     *
     * @param apiUsageRequest the api usage request
     * @return the api usage
     */
    @Override
    public Collection<ApiUsageResponse> getApiUsage(ApiUsageRequest apiUsageRequest) {
        List<ApiUsageResponse> apiUsageStats = new ArrayList<>();
        TableResult results = execute(buildApiUsageQueryJob(apiUsageRequest));
        for (FieldValueList row : results.iterateAll()) {
            apiUsageStats.add(new ApiUsageResponse(
                    getStringOrDefault(row.get(0)),
                    getStringOrDefault(row.get(1)),
                    getStringOrDefault(row.get(2)),
                    getStringOrDefault(row.get(3)),
                    getStringOrDefault(row.get(4)),
                    row.get(5).getLongValue()));
        }
        return apiUsageStats;
    }




    /**
     * Gets rsu usage by tenant.
     *
     * @param rsuUsageRequest  the rsu usage request
     * @param tenantPurposeMap the tenant id and purpose map
     * @param export           the export
     * @return the rsu usage by tenant
     */
    @Override
    public RsuByTenantResponse getRsuUsageByTenant(@Valid ComplianceReportRequest rsuUsageRequest, Map<String, TenantPurpose> tenantPurposeMap, Boolean export) {
        List<RsuUsageByTenantModel> rsuUsageByTenantModelList = new ArrayList<>();
        if (!tenantPurposeMap.isEmpty()) {
            TableResult results = execute(buildRsuUsageByTenantQueryJob(rsuUsageRequest, new ArrayList<>(tenantPurposeMap.keySet()), export));
            Long totalCount = NumberUtils.LONG_ZERO;
            for (FieldValueList row : results.iterateAll()) {
                String value0 = getStringOrDefault(row.get(0));
                String value1 = getStringOrDefault(row.get(1));
                TenantPurpose tenantPurpose = tenantPurposeMap.get(value1);
                String value2 = getStringOrDefault(row.get(3));
                String tenantPurposeStr = (tenantPurpose != null) ? tenantPurpose.toString() : null;
                String rsuValue = convertRsuToTb(row.get(2).getLongValue());
                rsuUsageByTenantModelList.add(new RsuUsageByTenantModel(value0, value1, tenantPurposeStr, rsuValue, value2));

                String totalCountStr = getStringOrDefault(row.get(4));
                totalCount = (totalCountStr != null) ? Long.valueOf(totalCountStr) : NumberUtils.LONG_ZERO;
            }
            return new RsuByTenantResponse(rsuUsageByTenantModelList, totalCount);
        }
        return new RsuByTenantResponse(Collections.emptyList(), NumberUtils.LONG_ZERO);

    }



    /**
     * Gets cp usage by date.
     *
     * @param cpUsageRequest the cp usage request
     * @param tenantIdList   the tenant id list
     * @param export         the export
     * @return the cp usage by date
     */
    @Override
    public CpUsageByDateResponse getCpUsageByDate(CpUsageReportRequest cpUsageRequest, List<String> tenantIdList, Boolean export) {
        List<CpUsageByDateModel> cpUsageByDateModelList = new ArrayList<>();
        long totalCount = 0L;

        List<QueryJobConfiguration> queryJobs = buildCpUsageByDateQueryJob(cpUsageRequest, tenantIdList, export);

        for (QueryJobConfiguration queryJob : queryJobs) {
            TableResult results = execute(queryJob);
            // Aggregate usages by date using utility method
            totalCount = aggregateUsagesByDate(results, cpUsageByDateModelList);
        }

        return new CpUsageByDateResponse(cpUsageByDateModelList, totalCount);
    }

    private long aggregateUsagesByDate(TableResult results, List<CpUsageByDateModel> cpUsageByDateModelList) {
        Map<String, Long> usageMap = new LinkedHashMap<>();
        long totalCount = 0L;
        for (FieldValueList row : results.iterateAll()) {
            String date = getStringOrDefault(row.get(0));
            Long usageValue = (getStringOrDefault(row.get(1)) != null && !getStringOrDefault(row.get(1)).isEmpty()) ? Long.parseLong(getStringOrDefault(row.get(1))) : 0L;
            totalCount = Long.parseLong(getStringOrDefault(row.get(2)));
            // Accumulate usages for the same date
            usageMap.merge(date, usageValue, Long::sum);
        }

        // Add aggregated results to the model list
        for (Map.Entry<String, Long> entry : usageMap.entrySet()) {
            String date = entry.getKey();
            Long usageValue = entry.getValue();

            // Check if the date already exists in the list
            Optional<CpUsageByDateModel> existingModel = cpUsageByDateModelList.stream()
                    .filter(model -> model.getDate().equals(date))
                    .findFirst();

            if (existingModel.isPresent()) {
                // Update the existing model
                existingModel.get().setCpUsage(String.valueOf(Long.parseLong(existingModel.get().getCpUsage()) + usageValue));
            } else {
                // Add a new model to the list
                cpUsageByDateModelList.add(new CpUsageByDateModel(date, String.valueOf(usageValue)));
            }
        }
        return totalCount;
    }





    /**
     * Gets cp usage by tenant.
     *
     * @param cpUsageRequest the cp usage request
     * @param tenantIdList   the tenant id list
     * @param export         the export
     * @return the cp usage by tenant
     */
    @Override
    public CpUsageByTenantResponse getCpUsageByTenant(CpUsageReportRequest cpUsageRequest, List<String> tenantIdList, Boolean export) {
        List<CpUsageByTenantModel> cpUsageByTenantModelList = new ArrayList<>();
        long totalCount = 0L;

        List<String> queryTemplates = getChildQueryTemplatesOnEntityRule(cpUsageRequest);

        for (String queryTemplate : queryTemplates) {
            TableResult results = execute(buildCpUsageByTenantQueryJob(cpUsageRequest, tenantIdList, export, queryTemplate));

            // Aggregate usages by tenant using utility method and accumulate totalCount
            totalCount = aggregateUsagesByTenant(results, cpUsageByTenantModelList);
        }

        return new CpUsageByTenantResponse(cpUsageByTenantModelList, totalCount);
    }


    private long aggregateUsagesByTenant(TableResult results, List<CpUsageByTenantModel> cpUsageByTenantModelList) {
        Map<String, Long> usageMap = new LinkedHashMap<>();
        long totalCount = 0L;

        for (FieldValueList row : results.iterateAll()) {
            String date = getStringOrDefault(row.get(0));
            String tenantId = getStringOrDefault(row.get(1));
            String usageValue = getStringOrDefault(row.get(2));

            // Convert empty string or null to 0
            long currentUsage = (usageValue == null || usageValue.isEmpty()) ? 0L : Long.parseLong(usageValue);

            // Build a unique key for each tenantId and entity combination
            String key = tenantId + "_" + date;

            // Accumulate total count for each row
            totalCount = Long.parseLong(getStringOrDefault(row.get(3)));

            // Check if the key already exists in the map
            usageMap.compute(key, (k, existingValue) -> (existingValue == null) ? currentUsage : existingValue + currentUsage);


        }

        // Add aggregated results to the model list after processing all rows
        for (Map.Entry<String, Long> entry : usageMap.entrySet()) {
            String[] parts = entry.getKey().split("_");
            String tenantId = parts[0];
            String date = parts[1];
            String aggregatedUsage = entry.getValue().toString();

            // Find the corresponding model in the list and update the usage
            Optional<CpUsageByTenantModel> existingModel = cpUsageByTenantModelList.stream()
                    .filter(model -> model.getTenantId().equals(tenantId) && model.getDate().equals(date))
                    .findFirst();
            if (existingModel.isPresent()) {
                // Update the existing model
                existingModel.get().setCpUsage(String.valueOf(Long.parseLong(existingModel.get().getCpUsage()) + Long.parseLong(aggregatedUsage)));
            } else {
                // Add a new model to the list
                cpUsageByTenantModelList.add(new CpUsageByTenantModel(date, tenantId, aggregatedUsage, StringUtils.EMPTY, StringUtils.EMPTY));
            }

        }

        return totalCount;
    }


    /**
     * Gets cp usage by month.
     *
     * @param cpUsageRequest the compliance request
     * @param tenantIdList   the tenant id list
     * @return the cp usage by month
     */
    @Override
    public CpUsageByMonthResponse getCpUsageByMonth(CpUsageReportRequest cpUsageRequest, List<String> tenantIdList) {
        TableResult results = execute(buildCpUsageByMonthQueryJob(cpUsageRequest, tenantIdList));
        List<CpUsageByMonthModel> finalResultList = new ArrayList<>();

        // Iterate through the results
        for (FieldValueList row : results.iterateAll()) {
            String year = getStringOrDefault(row.get(0));
            String month = getStringOrDefault(row.get(1));
            if (StringUtils.isNotBlank(getStringOrDefault(row.get(2)))) {
                Long maxCpUsage = Long.valueOf(getStringOrDefault(row.get(2)));
                finalResultList.add(new CpUsageByMonthModel(year, month, maxCpUsage));
            }
        }
        return new CpUsageByMonthResponse(finalResultList, finalResultList.size());
    }


    /**
     * Build cp usage by month query job query job configuration.
     *
     * @param cpUsageRequest the cp usage request
     * @param tenantIdList   the tenant id list
     * @return the query job configuration
     */
    public QueryJobConfiguration buildCpUsageByMonthQueryJob(CpUsageReportRequest cpUsageRequest, List<String> tenantIdList) {
        Map<String, Object> params = getDefaultParams(rsuDataset, rsuTableName, tenantIdList, String.valueOf(cpUsageRequest.getStartDate()), String.valueOf(cpUsageRequest.getEndDate()));
        QueryBuilder queryBuilder = new QueryBuilder(QueryBuilder.getCpUsageByMonthTemplate(QueryBuilder.getCpUsageByDateTemplate(getChildQueryTemplatesOnCpUsage(cpUsageRequest))), params);
        return QueryJobConfiguration.newBuilder(queryBuilder.build()).build();
    }

    /**
     * Gets rsu usage breakdown.
     *
     * @param rsuUsageRequest the rsu usage request
     * @param tenantIds       the tenant id, purpose list
     * @param export          the export
     * @return the rsu usage by tenant
     */
    @Override
    public RsuBreakdownResponse getRsuUsageBreakdown(ComplianceReportRequest rsuUsageRequest, List<String> tenantIds, Boolean export) {
        List<RsuBreakdownModel> rsuBreakdownModelList = new ArrayList<>();
        TableResult results = execute(buildRsuUsageBreakdownQueryJob(rsuUsageRequest, new ArrayList<>(tenantIds), export));
        Long totalCount = NumberUtils.LONG_ZERO;
        for (FieldValueList row : results.iterateAll()) {
            rsuBreakdownModelList.add(new RsuBreakdownModel(getStringOrDefault(row.get(0)), getStringOrDefault(row.get(1)), StringUtils.EMPTY, convertRsuToTb(row.get(2).getLongValue()), convertRsuToTb(row.get(3).getLongValue()), convertRsuToTb(row.get(4).getLongValue()), convertRsuToTb(row.get(5).getLongValue()), convertRsuToTb(row.get(6).getLongValue()), convertRsuToTb(row.get(7).getLongValue()), convertRsuToTb(row.get(8).getLongValue())));
            totalCount = Long.valueOf(getStringOrDefault(row.get(9)));
        }
        return new RsuBreakdownResponse(rsuBreakdownModelList, totalCount);

    }

    /**
     * Gets rsu usage by date.
     *
     * @param rsuUsageRequest  the cp usage request
     * @param tenantPurposeMap the tenant id and purpose map
     * @param export           the export
     * @return the rsu usage by date
     */
    @Override
    public RsuUsageByDateResponse getRsuUsageByDate(ComplianceReportRequest rsuUsageRequest, Map<String, TenantPurpose> tenantPurposeMap, Boolean export) {
        if (!tenantPurposeMap.isEmpty()) {
            List<RsuUsageDateModel> rsuUsageDateModelList = new ArrayList<>();
            TableResult results = execute(buildRsuUsageByDateQueryJob(rsuUsageRequest, new ArrayList<>(tenantPurposeMap.keySet()), export));
            Long totalCount = NumberUtils.LONG_ZERO;
            for (FieldValueList row : results.iterateAll()) {
                rsuUsageDateModelList.add(new RsuUsageDateModel(getStringOrDefault(row.get(0)), convertRsuToTb(row.get(1).getLongValue())));
                totalCount = Long.valueOf(getStringOrDefault(row.get(2)));
            }
            return new RsuUsageByDateResponse(rsuUsageDateModelList, totalCount);
        }
        return new RsuUsageByDateResponse(Collections.emptyList(), NumberUtils.LONG_ZERO);
    }

    /**
     * Build rsu usage by date query job query job configuration.
     *
     * @param rsuUsageRequest the cp usage request
     * @param tenantIdList    the tenant id list
     * @param export          the export
     * @return the query job configuration
     */
    private QueryJobConfiguration buildRsuUsageByDateQueryJob(ComplianceReportRequest rsuUsageRequest, List<String> tenantIdList, Boolean export) {
        Map<String, Object> params = getDefaultParams(rsuDataset, rsuTableName, tenantIdList, String.valueOf(rsuUsageRequest.getStartDate()), String.valueOf(rsuUsageRequest.getEndDate()));
        QueryBuilder queryBuilder;
        queryBuilder = new QueryBuilder(QueryBuilder.RSU_USAGE_BY_DATE_TEMPLATE, params);
        if (export) {
            queryBuilder.orderBy(SortField.ReportDate.name(), SortOrder.DESC.name());
        } else {
            makeQueryBuilder(rsuUsageRequest, queryBuilder);
        }
        return QueryJobConfiguration.newBuilder(queryBuilder.build()).build();
    }

    public QueryJobConfiguration buildRsuUsageByMonthQueryJob(ComplianceReportRequest rsuUsageRequest, List<String> tenantIdList) {
        Map<String, Object> params = getDefaultParams(rsuDataset, rsuTableName, tenantIdList, String.valueOf(rsuUsageRequest.getStartDate()), String.valueOf(rsuUsageRequest.getEndDate()));
        QueryBuilder queryBuilder = new QueryBuilder(QueryBuilder.getRsuUsageByMonthTemplate(QueryBuilder.RSU_USAGE_BY_DATE_TEMPLATE), params);
        return QueryJobConfiguration.newBuilder(queryBuilder.build()).build();
    }




    /**
     * Gets api usage by date.
     *
     * @param apiUsageRequest the api usage request
     * @param tenantIdList    the tenant id list
     * @param export          the export
     * @return the api usage by date
     */
    @Override
    public ApiUsageByDateResponse getApiUsageByDate(ApiUsageReportRequest apiUsageRequest, List<String> tenantIdList, Boolean export) {
        List<ApiUsageByDateModel> apiUsageByDateModelList = new ArrayList<>();
        TableResult results = execute(buildApiUsageByDateQueryJob(apiUsageRequest, tenantIdList, export));
        Long totalCount = NumberUtils.LONG_ZERO;
        for (FieldValueList row : results.iterateAll()) {
            apiUsageByDateModelList.add(new ApiUsageByDateModel(getStringOrDefault(row.get(0)), getStringOrDefault(row.get(1))));
            totalCount = Long.valueOf(getStringOrDefault(row.get(2)));
        }
        return new ApiUsageByDateResponse(apiUsageByDateModelList, totalCount);
    }

    /**
     * Gets billing report by environment.
     *
     * @param billingReportRequest the cp usage request
     * @return the cp usage
     */
    @Override
    public List<BillingReportByEnvironmentModel> getBillingByEnvironment(ComplianceReportRequest billingReportRequest, Map<String, TenantPurpose> tenantPurposeMap) {
        if (!tenantPurposeMap.isEmpty()) {
            List<BillingReportByEnvironmentModel> billingRepportModelList = new ArrayList<>();
            TableResult results = execute(buildBillingReportByEnvironmentQueryJob(billingReportRequest, new ArrayList<>(tenantPurposeMap.keySet())));
            for (FieldValueList row : results.iterateAll()) {
                billingRepportModelList.add(new BillingReportByEnvironmentModel(getStringOrDefault(row.get(3)), getStringOrDefault(row.get(2)), getStringOrDefault(row.get(0)), getStringOrDefault(row.get(1)), row.get(4).getLongValue()));
            }
            return billingRepportModelList;
        }
        return Collections.emptyList();
    }

    /**
     * Gets billing report by tenant.
     *
     * @param billingReportRequest the task usage request
     * @return the task usage
     */
    @Override
    public List<BillingReportByTenantModel> getBillingByTenant(ComplianceReportRequest billingReportRequest, Map<String, TenantPurpose> tenantPurposeMap) {
        if (!tenantPurposeMap.isEmpty()) {
            List<BillingReportByTenantModel> billingReportModelList = new ArrayList<>();
            TableResult results = execute(buildBillingReportByCustomerQueryJob(billingReportRequest, new ArrayList<>(tenantPurposeMap.keySet())));
            for (FieldValueList row : results.iterateAll()) {
                billingReportModelList.add(new BillingReportByTenantModel("", getStringOrDefault(row.get(2)), getStringOrDefault(row.get(0)), getStringOrDefault(row.get(1)), row.get(3).getLongValue()));
            }
            return billingReportModelList;
        }
        return Collections.emptyList();
    }


    /**
     * Gets RSU Usage report for the month by accountId.
     *
     * @param rsuUsageReportRequest the task usage request
     * @return the RSU usage
     */
    @Override
    public RsuUsageByMonthResponse getRsuUsageByMonth(ComplianceReportRequest rsuUsageReportRequest, Map<String, TenantPurpose> tenantIdWithPurpose) {
        if (!tenantIdWithPurpose.isEmpty()) {
            TableResult results = execute(buildRsuUsageByMonthQueryJob(rsuUsageReportRequest, new ArrayList<>(tenantIdWithPurpose.keySet())));

            List<RsuUsageMonthModel> finalResultList = new ArrayList<>();
            // Iterate through the results
            for (FieldValueList row : results.iterateAll()) {
                String year = getStringOrDefault(row.get(0));
                String month = getStringOrDefault(row.get(1));
                if (row.get(2)!=null) {
                    String rsuUsage = convertRsuToTb(row.get(2).getLongValue());
                    String totalUsage = convertRsuToTb(row.get(3).getLongValue());
                    Double averageRsu = calculateAverageUsage(totalUsage, month, year);
                    finalResultList.add(new RsuUsageMonthModel(year, month, rsuUsage,averageRsu.toString()));
                }

            }
            return new RsuUsageByMonthResponse(finalResultList, (long) finalResultList.size());
        }
        return new RsuUsageByMonthResponse(Collections.emptyList(), NumberUtils.LONG_ZERO);
    }


    /**
     * Gets api usage by tenant.
     *
     * @param apiUsageRequest the api usage request
     * @param tenantIdList    the tenant id list
     * @param export          the export
     * @return the api usage by tenant
     */
    @Override
    public ApiUsageByTenantResponse getApiUsageByTenant(ApiUsageReportRequest apiUsageRequest, List<String> tenantIdList, Boolean export) {
        List<ApiUsageByTenantModel> apiUsageByTenantModelList = new ArrayList<>();
        TableResult results = execute(buildApiUsageByTenantQueryJob(apiUsageRequest, tenantIdList, export));

        // Populate the list with the results
        for (FieldValueList row : results.iterateAll()) {
            apiUsageByTenantModelList.add(new ApiUsageByTenantModel(
                    getStringOrDefault(row.get(0)),
                    getStringOrDefault(row.get(1)),
                    StringUtils.EMPTY,
                    getStringOrDefault(row.get(2))
            ));
        }

        // Filter out models where tenantId is empty
        apiUsageByTenantModelList.removeIf(tenantModel -> tenantModel.getTenantId().isEmpty());

        // Update totalCount based on the filtered list size
        Long totalCount = (long) apiUsageByTenantModelList.size();

        // Return the response with the filtered list and updated count
        return new ApiUsageByTenantResponse(apiUsageByTenantModelList, totalCount);
    }



    /**
     * Gets api usage by endpoint.
     *
     * @param apiUsageRequest the api usage request
     * @param tenantIdList    the tenant id list
     * @param export          the export
     * @return the api usage by endpoint
     */
    @Override
    public ApiUsageByEndpointResponse getApiUsageByEndpoint(ApiUsageReportRequest apiUsageRequest, List<String> tenantIdList, Boolean export) {
        List<ApiUsageByEndpointModel> apiUsageByEndpointModelList = new ArrayList<>();
        TableResult results = execute(buildApiUsageByEndpointQueryJob(apiUsageRequest, tenantIdList, export));

        Long totalCount = Long.valueOf(getStringOrDefault(results.iterateAll().iterator().next().get(6)));
        int skippedRowsCount = 0;

        for (FieldValueList row : results.iterateAll()) {
            String tenantId = getStringOrDefault(row.get(1));

            if (StringUtils.isEmpty(tenantId)) {
                skippedRowsCount++;
                continue; // Skip the row if tenantId is empty
            }

            apiUsageByEndpointModelList.add(new ApiUsageByEndpointModel(
                    getStringOrDefault(row.get(0)),
                    tenantId,
                    getStringOrDefault(row.get(2)),
                    getStringOrDefault(row.get(3)),
                    getStringOrDefault(row.get(4)),
                    StringUtils.EMPTY,
                    getStringOrDefault(row.get(5))
            ));
        }

        totalCount -= skippedRowsCount;

        return new ApiUsageByEndpointResponse(apiUsageByEndpointModelList, totalCount);
    }




    @Override
    public ApiByMonthResponse getApiUsageByMonth(ComplianceReportRequest apiUsageRequest, List<String> tenantIdList) {
        List<ApiByMonthModel> apiByMonthModelList = new ArrayList<>();
        TableResult results = execute(buildApiUsageByMonthQueryJob(apiUsageRequest, tenantIdList));
        Long totalCount = NumberUtils.LONG_ZERO;
        for (FieldValueList row : results.iterateAll()) {
            Double avgUsage = calculateAverageUsage(Long.valueOf(getStringOrDefault(row.get(2))), getStringOrDefault(row.get(1)), getStringOrDefault(row.get(0)));
            apiByMonthModelList.add(new ApiByMonthModel(getStringOrDefault(row.get(0)), getStringOrDefault(row.get(1)), Long.valueOf(getStringOrDefault(row.get(2))), avgUsage));
            totalCount = Long.valueOf(getStringOrDefault(row.get(3)));
        }
        return new ApiByMonthResponse(apiByMonthModelList, totalCount);
    }

    /**
     * Convert rsu to tb string.
     *
     * @param usage the usage
     * @return the string
     */
    private static String convertRsuToTb(Long usage) {
        double converted = usage / (1024.0 * 1024.0 * 1024.0 * 1024.0);
        return decfor.format(converted);
    }










}
