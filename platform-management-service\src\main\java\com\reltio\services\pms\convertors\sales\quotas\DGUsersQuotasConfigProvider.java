package com.reltio.services.pms.convertors.sales.quotas;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.QuantityType;
import com.reltio.services.pms.common.sales.QuotaPeriod;
import com.reltio.services.pms.common.sales.QuotaType;
import com.reltio.services.pms.common.sales.model.PMSQuotaName;
import com.reltio.services.pms.common.sales.model.SalesForceProductName;
import com.reltio.services.pms.common.sales.model.quotas.BaseQuotasConfig;
import com.reltio.services.pms.service.SalesPackageService;
import org.springframework.stereotype.Service;

import java.util.Set;
@Service
public class DGUsersQuotasConfigProvider extends AbstractQuotasConfigProvider<BaseQuotasConfig> {

    public DGUsersQuotasConfigProvider(SalesPackageService salesPackageService) {
        super(salesPackageService);
    }

    @Override
    public PMSQuotaName getQuotaName() {
        return PMSQuotaName.DATA_GOVERNANCE_USERS;
    }

    @Override
    public QuotaType getQuotaType() {
        return QuotaType.TENANT_LEVEL;
    }

    @Override
    public QuotaPeriod getQuotaPeriod() {
        return QuotaPeriod.FIXED;
    }

    @Override
    public QuantityType getQuantityType() {
        return QuantityType.COUNT;
    }

    @Override
    public PMSProductName getPMSProductName() {
        return null;
    }

    @Override
    public Set<String> getQuotaProductCodes() {
        return salesPackageService.getSalesForceQuotas(SalesForceProductName.DGUSERS);
    }
}
