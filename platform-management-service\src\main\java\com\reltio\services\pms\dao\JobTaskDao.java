package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import org.springframework.stereotype.Service;

/**
 * The Tasks DAO implementation for Tasks collections stored under the Job document.
 * This Tasks collection is the level 2 sub-collection which under the Job sub collection and Environment Root Collection.
 */
@Service
public class JobTaskDao extends AbstractLevel2CollectionDao<TaskInstance> {

    private static final String TASKS_COLLECTIONS_NAME = "TASKS";

    public JobTaskDao(CredentialsProvider provider, JobDao jobDao, ReltioUserHolder reltioUserHolder) {
        super(provider, jobDao, TASKS_COLLECTIONS_NAME, reltioUserHolder);
    }

    @Override
    protected TypeReference<TaskInstance> getTypeReference() {
        return new TypeReference<TaskInstance>() {
        };
    }
}
