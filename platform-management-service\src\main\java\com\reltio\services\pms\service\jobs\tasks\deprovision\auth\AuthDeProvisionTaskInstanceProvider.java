package com.reltio.services.pms.service.jobs.tasks.deprovision.auth;

import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.jobs.tasks.authDeprovision.AuthDeProvisionTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.authDeprovision.AuthDeProvisionTaskInstanceParams;
import com.reltio.services.pms.common.model.pipeline.tasks.AuthDeProvisionPipelineTaskConfig;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.jobs.tasks.TaskInstanceProvider;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.UUID;

@Service
public class AuthDeProvisionTaskInstanceProvider implements TaskInstanceProvider< AuthDeProvisionTaskInstance,AuthDeProvisionPipelineTaskConfig, AuthDeProvisionTaskInstanceParams> {

    private static final String ERROR_AUTH_CUSTOMER_ID_MANDATORY = "Auth Customer ID is mandatory for full customer deprovision.";
    private static final int BAD_REQUEST_STATUS = HttpStatus.BAD_REQUEST.value();

    @Override
    public TaskType getTaskType() {
        return TaskType.AUTH_DE_PROVISION_TASK;
    }

    @Override
    public AuthDeProvisionTaskInstance getTaskDetail(String envId,String jobId,AuthDeProvisionPipelineTaskConfig pipelineTaskConfig,
                                                     AuthDeProvisionTaskInstanceParams taskInstanceParams){
        validateTaskInstanceParams(taskInstanceParams);
        return new AuthDeProvisionTaskInstance(
                UUID.randomUUID().toString(),
                TaskType.AUTH_DE_PROVISION_TASK.toString(),
                jobId,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                envId,
                taskInstanceParams.getTenantId(),
                Collections.emptyList(),
                taskInstanceParams.isDeleteAssociatedUsers(),
                taskInstanceParams.isRemoveAssociatedRdms(),
                taskInstanceParams.isRemoveGroupAssignment(),
                taskInstanceParams.getAuthCustomerId(),
                taskInstanceParams.isFullCustomerDeprovision(),
                taskInstanceParams.getUsersToDelete(),
                taskInstanceParams.isFailOnError(),
                pipelineTaskConfig.getProtectedUsers(),
                pipelineTaskConfig.getProtectedCustomers()
        );
    }

    private void validateTaskInstanceParams(AuthDeProvisionTaskInstanceParams taskInstanceParams) {

        if (taskInstanceParams.isFullCustomerDeprovision() && (taskInstanceParams.getAuthCustomerId() == null
                || taskInstanceParams.getAuthCustomerId().trim().isEmpty())) {
            throw new PlatformManagementException(
                    PlatformManagementErrorCode.BAD_REQUEST,
                    BAD_REQUEST_STATUS,
                    ERROR_AUTH_CUSTOMER_ID_MANDATORY
            );
        }
    }
}
