package com.reltio.services.pms.common.model.supportability;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The type ProcessorTypeGroupAggregateResult.
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ProcessorTypeGroupAggregateResult {
    /**
     * The total count.
     */
    private Long totalCount;

    /**
     * The Failed count.
     */
    private Long failedCount;

    /**
     * The successfulCount count.
     */
    private Long successfulCount;

    /**
     * The Avg.
     */
    private Long avg;
}
