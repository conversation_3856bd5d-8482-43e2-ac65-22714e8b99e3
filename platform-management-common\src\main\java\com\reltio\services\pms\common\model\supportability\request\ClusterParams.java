package com.reltio.services.pms.common.model.supportability.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.reltio.services.pms.common.model.supportability.Facet;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class ClusterParams extends Params {
    private final String clusterType;

    public ClusterParams(Long startTime, Long endTime, String tenantId, String clusterType, Facet facet) {
        super(startTime, endTime, tenantId, facet);
        this.clusterType = clusterType;
    }
}
