package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.sales.model.TenantManagementSelfServiceConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class TenantManagementSelfServiceConfigDao extends AbstractRootCollectionDao<TenantManagementSelfServiceConfig> {
    private static final String CONFIG_COLLECTION_NAME = "PMS_CONFIG";

    @Autowired
    public TenantManagementSelfServiceConfigDao(CredentialsProvider provider,
                                                @Value("${firestore.env.name}") String deployedEnv,
                                                ReltioUserHolder reltioUserHolder) {
        super(provider, CONFIG_COLLECTION_NAME, deployedEnv, reltioUserHolder);
    }
    @Override
    protected TypeReference<TenantManagementSelfServiceConfig> getTypeReference() {
        return new TypeReference<TenantManagementSelfServiceConfig>() {
        };
    }
}
