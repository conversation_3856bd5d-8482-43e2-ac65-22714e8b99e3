package com.reltio.services.pms.common.model.supportability.request;

import com.reltio.services.pms.common.model.supportability.Facet;

import java.util.Objects;

/**
 * TasksParams
 * Created by apylkov
 */
public class Params {
    private final Long startTime;
    private final Long endTime;
    private final String tenantId;

    private final Facet facet;


    public Params(Long startTime, Long endTime, String tenantId, Facet facet) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.tenantId = tenantId;
        this.facet = facet;
    }

    public String getTenantId() {
        return tenantId;
    }

    public Long getStartTime() {
        return startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public Facet getFacet() {
        return facet;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Params params = (Params) o;
        return Objects.equals(getTenantId(), params.getTenantId()) &&
                Objects.equals(getStartTime(), params.getStartTime()) &&
                Objects.equals(getEndTime(), params.getEndTime()) && Objects.equals(getFacet(), params.getFacet());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getTenantId(), getStartTime(), getEndTime(), getFacet());
    }
}
