package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.Query;
import com.google.cloud.firestore.QuerySnapshot;
import com.google.common.collect.Lists;
import com.reltio.collection.CollectionUtils;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.CustomerType;
import com.reltio.services.pms.common.model.TenantFilter;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.TenantStatus;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * The type Reltio tenant dao.
 */
@Service
public class ReltioTenantDao extends AbstractRootCollectionDao<ReltioTenant> {

    /**
     * The constant TENANT_COLLECTION_NAME.
     */
    private static final String TENANT_COLLECTION_NAME = "PMS_MDM_TENANTS";
    /**
     * The constant LOGGER.
     */
    private static final Logger LOGGER = Logger.getLogger(ReltioTenantDao.class);
    /**
     * The constant MAX_LIMIT.
     */
    private static final int MAX_LIMIT = 30;

    /**
     * Instantiates a new Reltio tenant dao.
     *
     * @param provider         the provider
     * @param deployedEnv      the deployed env
     * @param reltioUserHolder the reltio user holder
     */
    @Autowired
    public ReltioTenantDao(CredentialsProvider provider, @Value("${firestore.env.name}") String deployedEnv,
            ReltioUserHolder reltioUserHolder) {
        super(provider, TENANT_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties = Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties, LOGGER);
    }

    /**
     * Gets type reference.
     *
     * @return the type reference
     */
    @Override
    protected TypeReference<ReltioTenant> getTypeReference() {
        return new TypeReference<>() {
        };
    }

    /**
     * Gets tenant by array field.
     *
     * @param field the field
     * @param value the value
     * @return the tenant by array field
     */
    public Collection<ReltioTenant> getTenantByArrayField(String field, List<String> value) {
        Query query = getBaseCollection().whereArrayContainsAny(field, value);
        return getResultFromQuery(query);
    }

    /**
     * Gets tenants filtered.
     *
     * @param filterField the filter field
     * @param value       the value
     * @return the tenants filtered
     */
    public Collection<ReltioTenant> getTenantsFiltered(String filterField, String value) {
        return getAllFiltered(getBaseCollection(), filterField, value);
    }

    /**
     * Gets tenants filtered count.
     *
     * @param filterField the filter field
     * @param value       the value
     * @return the tenants filtered count
     */
    public Long getTenantsFilteredCount(String filterField, String value) {
        Query query = getBaseCollection().whereEqualTo(filterField, value);
        return getCountFromQuery(query);
    }

    /**
     * Gets tenants with active contract count.
     *
     * @return the tenants with active contract count
     */
    public Long getTenantsWithActiveContractCount() {
        Query query = getBaseCollection().whereEqualTo("tenantStatus", "ACTIVE");
        query = query.whereNotEqualTo("contractId", null);
        return getCountFromQuery(query);
    }

    /**
     * Gets tenants filtered count by env.
     *
     * @param filterField the filter field
     * @param value       the value
     * @return the tenants filtered count by env
     */
    public Map<String, Long> getTenantsFilteredCountByEnv(String filterField, String value) {
        try {
            Query query = getBaseCollection().whereEqualTo(filterField, value);
            Collection<ReltioTenant> reltioTenants = getResultFromQuery(query);
            if (Objects.nonNull(reltioTenants)) {
                return reltioTenants.stream()
                        .map(ReltioTenant::getReltioEnv)
                        .filter(Objects::nonNull)
                        .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
            }
        } catch (PlatformManagementException exception) {
            LOGGER.error("Error has occurred while getting mdm tenants collection " + exception);
        }

        return Collections.emptyMap();
    }

    /**
     * Gets non customer tenant count by env.
     *
     * @return the non customer tenant count by env
     */
    public Map<String, Long> getNonCustomerTenantCountByEnv() {
        Query query = getBaseCollection().whereEqualTo("tenantStatus", "ACTIVE");
        Collection<ReltioTenant> reltioTenants = getResultFromQuery(query);
        if (Objects.nonNull(reltioTenants)) {
            return reltioTenants.stream()
                    .filter(e -> Objects.isNull(e.getContractId()) || StringUtils.isBlank(e.getContractId()))
                    .map(ReltioTenant::getReltioEnv)
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        }

        return Collections.emptyMap();
    }

    /**
     * Gets tenants by tenant ids and account id.
     *
     * @param accountId the account id
     * @param tenantIds the tenant ids
     * @return the tenants by tenant ids and account id
     */
    public Collection<ReltioTenant> getTenantsByTenantIdsAndAccountId(String accountId, List<String> tenantIds) {
        int maxSize = 30;
        Collection<ReltioTenant> reltioTenants = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tenantIds) && StringUtils.isNotBlank(accountId)) {
            if (tenantIds.size() > maxSize) {
                for (int i = 0; i < tenantIds.size(); i += maxSize) {
                    Collection<ReltioTenant> limitTenants = getResultFromQuery(
                            getBaseCollection().whereEqualTo("accountId", accountId)
                                    .whereIn("tenantId", tenantIds.stream().sorted().skip(i + 1L).limit(maxSize)
                                            .collect(Collectors.toList())));
                    reltioTenants.addAll(limitTenants);
                }
            } else {
                reltioTenants = getResultFromQuery(
                        getBaseCollection().whereEqualTo("accountId", accountId).whereIn("tenantId", tenantIds));
            }
        }
        return reltioTenants;
    }

    /**
     * Gets tenants filtered with size and offset.
     *
     * @param filterField the filter field
     * @param value       the value
     * @param size        the size
     * @param offset      the offset
     * @return the tenants filtered with size and offset
     */
    public Collection<ReltioTenant> getTenantsFilteredWithSizeAndOffset(String filterField, String value, Integer size,
            Integer offset) {
        return super.getFilteredWithSizeAndOffset(getBaseCollection(), filterField, value, size, offset);
    }

    /**
     * Gets tenants by tenant ids.
     *
     * @param tenantIds the tenant ids
     * @return the tenants by tenant ids
     */
    public Collection<ReltioTenant> getTenantsByTenantIds(List<String> tenantIds) {
        Collection<ReltioTenant> reltioTenants = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tenantIds)) {
            reltioTenants = getResultFromQuery(getBaseCollection().whereIn("tenantId", tenantIds));
        }
        return reltioTenants;
    }

    /**
     * Gets by status with size and offset.
     *
     * @param status the status
     * @param size   the size
     * @param offset the offset
     * @return the by status with size and offset
     */
    public Collection<ReltioTenant> getByStatusWithSizeAndOffset(String status, Integer size, Integer offset) {
        Query query = getBaseCollection().whereEqualTo("tenantStatus", status)
                .offset(offset * size)
                .limit(size);
        return getResultFromQuery(query);
    }

    /**
     * Gets tenants by tenant filter.
     *
     * @param tenantFilter the tenant filter
     * @return the tenants by tenant filter
     */
    public Collection<ReltioTenant> getTenantsByTenantFilter(TenantFilter tenantFilter) {
        Query query = getTenantQueryByTenantFilter(tenantFilter);
        return getResultFromQuery(query);
    }

    /**
     * Gets tenant count by tenant filter.
     *
     * @param tenantFilter the tenant filter
     * @return the tenant count by tenant filter
     */
    public Long getTenantCountByTenantFilter(TenantFilter tenantFilter) {
        Query query = getTenantQueryByTenantFilter(tenantFilter);
        return getCountFromQuery(query);
    }

    /**
     * Gets enterprise tenant count.
     *
     * @param tenantFilter the tenant filter
     * @return the enterprise tenant count
     */
    public Long getEnterpriseTenantCount(TenantFilter tenantFilter) {
        Query query = getBaseCollection();
        query = applyFilter(tenantFilter, query);
        return getCountFromQuery(query);
    }

    /**
     * Gets non enterprise tenant count.
     *
     * @param tenantFilter the tenant filter
     * @return the non enterprise tenant count
     */
    public Long getNonEnterpriseTenantCount(TenantFilter tenantFilter) {
        Query query = getBaseCollection().whereNotEqualTo("customerType", CustomerType.ENTERPRISE.name());
        query = applyFilter(tenantFilter, query);
        return getCountFromQuery(query);
    }

    /**
     * Gets no owner tenant count.
     *
     * @param tenantFilter the tenant filter
     * @return the no owner tenant count
     */
    public Long getNoOwnerTenantCount(TenantFilter tenantFilter) {
        List<String> emptyList = new ArrayList<>();
        Query emptyOwnersQuery = getBaseCollection().whereEqualTo("owners", emptyList);
        emptyOwnersQuery = applyFilter(tenantFilter, emptyOwnersQuery);
        Long emptyOwnersTenantCount = getCountFromQuery(emptyOwnersQuery);

        Query nonNullOwnersQuery = getBaseCollection().whereNotEqualTo("owners", null);
        nonNullOwnersQuery = applyFilter(tenantFilter, nonNullOwnersQuery);
        Long nonNullOwnersTenantCount = getCountFromQuery(nonNullOwnersQuery);

        Query activeTenantsQuery = getBaseCollection();
        activeTenantsQuery = applyFilter(tenantFilter, activeTenantsQuery);
        Long activeTenantCount = getCountFromQuery(activeTenantsQuery);

        return emptyOwnersTenantCount + (activeTenantCount - nonNullOwnersTenantCount);
    }

    /**
     * Gets tenant query by tenant filter.
     *
     * @param tenantFilter the tenant filter
     * @return the tenant query by tenant filter
     */
    @NotNull
    private Query getTenantQueryByTenantFilter(TenantFilter tenantFilter) {
        Query query = getBaseCollection();
        if (Objects.nonNull(tenantFilter)) {
            query = applyFilter(tenantFilter, query);
            if (Objects.nonNull(tenantFilter.getOffset()) && Objects.nonNull(tenantFilter.getSize())) {
                query = query.offset(tenantFilter.getOffset()).limit(tenantFilter.getSize());
            }
        }
        return query;
    }

    /**
     * Apply filter.
     *
     * @param tenantFilter the tenant filter
     * @param query        the query
     */
    private Query applyFilter(TenantFilter tenantFilter, Query query) {
        if (Objects.nonNull(tenantFilter)) {
            if (Objects.nonNull(tenantFilter.getStatus())) {
                query = query.whereEqualTo("tenantStatus", tenantFilter.getStatus().name());
            }
            query = applyFilterIfNotBlank(query, "parentJobId", tenantFilter.getParentJobId());
            query = applyFilterIfNotBlank(query, "department", tenantFilter.getDepartment());
            query = applyFilterIfNotBlank(query, "division", tenantFilter.getDivision());
            query = applyFilterIfNotBlank(query, "costCenter", tenantFilter.getCostCenter());
            query = applyFilterIfNotBlank(query, "customerType", tenantFilter.getCustomerType());
            query = applyFilterIfNotBlank(query, "deploymentCloud", tenantFilter.getDeploymentCloud());
            query = applyFilterIfNotBlank(query, "owners", tenantFilter.getOwners());
            query = applyFilterIfNotBlank(query, "reltioEnv", tenantFilter.getEnvironment());

            // Handling salesConfig.endDate filter
            if (Objects.nonNull(tenantFilter.getEndDate())) {
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

                    LocalDate endDate = LocalDate.parse(tenantFilter.getEndDate(), formatter);

                    long endDateMillis = endDate.atStartOfDay(ZoneOffset.UTC).toInstant().toEpochMilli();

                    query = query.whereLessThanOrEqualTo("salesConfig.endDate", endDateMillis);
                } catch (DateTimeParseException e) {
                    throw new PlatformManagementException(
                            PlatformManagementErrorCode.INVALID_DATE_FORMAT,
                            HttpStatus.BAD_REQUEST.value(),
                            "Invalid date format for endDate. Use 'yyyyMMdd'."
                    );
                }
            }
        }
        return query;
    }

    /**
     * Regular expression pattern for email validation
     */
    private static final String EMAIL_REGEX = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";

    /**
     * Check if the email is valid
     */
    public static boolean isValidEmail(String emails) {
        // Split the input string by commas
        String[] emailArray = emails.split(",");
        // Iterate through each email and validate
        for (String email : emailArray) {
            // Trim the email to remove any leading or trailing whitespace
            email = email.trim();
            // Compile the regular expression pattern
            Pattern pattern = Pattern.compile(EMAIL_REGEX);
            // Check if the email matches the pattern
            if (!pattern.matcher(email).matches()) {
                return false; // Return false if any email is invalid
            }
        }
        return true; // Return true if all emails are valid
    }

    /**
     * Apply filter if not blank.
     *
     * @param query     the query
     * @param fieldName the field name
     * @param value     the value
     */
    protected Query applyFilterIfNotBlank(Query query, String fieldName, String value) {
        if (value != null && !value.trim().isEmpty()) {
            // Check if the value contains commas
            if (isValidEmail(value)) {
                // If it contains commas, split it and apply array-contains-any filter
                String[] ownersArray = value.split(",");
                return query.whereArrayContainsAny(fieldName, Arrays.asList(ownersArray));
            } else {
                // If it's a single value, apply the equality filter
                return query.whereEqualTo(fieldName, value.trim());
            }
        }
        return query;
    }

    public Collection<ReltioTenant> getReltioTenantByFieldIdList(String fieldId, Set<String> idList) {
        try {
            if (CollectionUtils.isNotEmpty(idList)) {
                List<String> values = new ArrayList<>(idList);
                if (values.size() >= MAX_LIMIT) {
                    return getReltioTenantByPartition(fieldId, values);
                } else {
                    return getResultFromQuery(getCurrentCollectionGroup().whereIn(fieldId, values));
                }
            }
        } catch (Exception exception) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR,
                    HttpStatus.EXPECTATION_FAILED.value(), exception);
        }
        return Collections.emptyList();
    }

    private Collection<ReltioTenant> getReltioTenantByPartition(String fieldId, List<String> values) {
        List<List<String>> valueBatches = Lists.partition(values, MAX_LIMIT);
        List<CompletableFuture<List<QuerySnapshot>>> completableFutureList = valueBatches.parallelStream()
                .map(batch -> CompletableFuture.supplyAsync(() -> processBatch(fieldId, batch)))
                .toList();
        return CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]))
                .thenApply(apply -> completableFutureList.stream()
                        .map(CompletableFuture::join)
                        .map(getResult())
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList()))
                .join();

    }

    @NotNull
    private Function<List<QuerySnapshot>, List<ReltioTenant>> getResult() {
        return query -> query.stream().map(instance -> getResultFromQuery(instance.getQuery()))
                .flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<QuerySnapshot> processBatch(String fieldId, List<String> valueBatch) {
        Query query = getCurrentCollectionGroup().whereIn(fieldId, valueBatch).limit(MAX_LIMIT);
        List<QuerySnapshot> querySnapshots = new ArrayList<>();
        while (true) {
            QuerySnapshot snapshot = null;
            try {
                snapshot = query.get().get();
            } catch (ExecutionException executionException) {
                throw new RuntimeException(executionException.getCause());
            } catch (InterruptedException interruptedException) {
                LOGGER.error("Interrupted", interruptedException);
                Thread.currentThread().interrupt();
            }
            if (Objects.isNull(snapshot) || snapshot.isEmpty()) {
                break;
            }
            querySnapshots.add(snapshot);
            DocumentSnapshot lastVisible = snapshot.getDocuments().get(snapshot.size() - 1);
            query = getCurrentCollectionGroup().whereIn(fieldId, valueBatch).startAfter(lastVisible).limit(MAX_LIMIT);
        }
        return querySnapshots;
    }

    /**
     * Gets tenants by customerType and owners.
     */
    public Collection<ReltioTenant> getTenantsByCustomerTypeAndOwners(String customerType, List<String> owners) {
        return getResultFromQuery(getBaseCollection().whereEqualTo("customerType", customerType)
                .whereIn("initialOwner", owners).whereEqualTo("tenantStatus", TenantStatus.ACTIVE.name()));
    }
}
