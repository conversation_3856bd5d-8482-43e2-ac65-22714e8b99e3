package com.reltio.services.pms.common.model.jobs.tasks.tenants.sync;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.common.sales.TenantPurpose;
import lombok.Getter;

import java.util.List;
import java.util.Set;

@Getter
public class TenantsSyncTaskInstance extends TaskInstance {
    @JsonProperty(value = "tenantPurpose")
    private final TenantPurpose tenantPurpose;

    @JsonProperty(value = "tenantsList")
    private final List<String> tenantsList;

    @JsonProperty(value = "processAllTenants")
    private final boolean processAllTenants;

    @JsonProperty(value = "failedCustomers")
    private Set<String> failedCustomers;
    @JsonProperty(value = "failedClients")
    private Set<String> failedClients;
    @JsonProperty(value = "failedTenants")
    private Set<String> failedTenants;

    @JsonCreator
    public TenantsSyncTaskInstance(@JsonProperty(value = "taskId") String taskId,
                                   @JsonProperty(value = "name") String name,
                                   @JsonProperty(value = "jobId") String jobId,
                                   @JsonProperty(value = "startTime") Long startTime,
                                   @JsonProperty(value = "finishTime") Long finishTime,
                                   @JsonProperty(value = "status") TaskStatus status,
                                   @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                   @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                   @JsonProperty(value = "executingNodeName") String executingNodeName,
                                   @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                   @JsonProperty(value = "envId") String envId,
                                   @JsonProperty(value = "tenantPurpose") TenantPurpose tenantPurpose,
                                   @JsonProperty(value = "tenantsList") List<String> tenantsList,
                                   @JsonProperty(value = "processAllTenants") boolean processAllTenants,
                                   @JsonProperty(value = "failedCustomers") Set<String> failedCustomers,
                                   @JsonProperty(value = "failedClients") Set<String> failedClients,
                                   @JsonProperty(value = "failedTenants") Set<String> failedTenants

    ) {
        super(taskId, name, jobId, startTime, finishTime, TaskType.TENANTS_SYNC_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.tenantPurpose = tenantPurpose;
        this.tenantsList = tenantsList;
        this.processAllTenants = processAllTenants;
        this.failedCustomers = failedCustomers;
        this.failedClients = failedClients;
        this.failedTenants = failedTenants;
    }

    public void setFailedCustomers(Set<String> failedCustomers) {
        this.failedCustomers = failedCustomers;
    }

    public void setFailedClients(Set<String> failedClients) {
        this.failedClients = failedClients;
    }

    public void setFailedTenants(Set<String> failedTenants) {
        this.failedTenants = failedTenants;
    }
}
