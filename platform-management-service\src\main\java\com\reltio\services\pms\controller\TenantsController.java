package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.auth.domain.ReltioPrivileges;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.CustomerType;
import com.reltio.services.pms.common.model.RdmTenant;
import com.reltio.services.pms.common.model.TenantFilter;
import com.reltio.services.pms.common.model.TenantSearchRequest;
import com.reltio.services.pms.common.model.TenantSummaryCount;
import com.reltio.services.pms.common.model.tenant.Invitee;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.TenantStatus;
import com.reltio.services.pms.service.UserService;
import com.reltio.services.pms.service.reltiotenant.MDMTenantService;
import com.reltio.services.pms.service.reltiotenant.RdmTenantsService;
import com.reltio.services.pms.service.reltiotenant.TenantUIConfigService;
import com.reltio.services.pms.service.reltiotenant.TenantsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping(value = "/api/v1/tenants", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Tenant Specific info")
public class TenantsController {

    private static final String ONLY_ONE = "Only one parameter can be used for filtering";
    private static final String STATUS = "status";
    private static final String DELETED = "deleted";
    private final TenantsService service;
    private final RdmTenantsService rdmTenantsService;
    private final UserService userService;
    private final TenantUIConfigService tenantUIConfigService;

    private final MDMTenantService mdmTenantService;

    @Autowired
    public TenantsController(TenantsService service, RdmTenantsService rdmTenantsService, UserService userService,
                             TenantUIConfigService tenantUIConfigService, MDMTenantService mdmTenantService) {
        this.service = service;
        this.rdmTenantsService = rdmTenantsService;
        this.userService = userService;
        this.tenantUIConfigService = tenantUIConfigService;
        this.mdmTenantService = mdmTenantService;
    }

    private static boolean onlyAccountId(String contractId, String customerName, String customerId, String accountId) {
        return contractId == null && customerId == null && customerName == null && accountId != null;
    }

    private static boolean onlyCustomerName(String contractId, String customerName, String customerId, String accountId) {
        return contractId == null && customerName != null && customerId == null && accountId == null;
    }

    private static boolean onlyCustomerId(String contractId, String customerName, String customerId, String accountId) {
        return contractId == null && customerId != null && customerName == null && accountId == null;
    }

    private static boolean onlyContractId(String contractId, String customerName, String customerId, String accountId) {
        return contractId != null && customerId == null && customerName == null && accountId == null;
    }

    private static boolean areAllParametersNull(String contractId, String customerName, String customerId, String accountId) {
        return contractId == null && customerId == null && customerName == null && accountId == null;
    }

    @ReltioSecured(resourceClass = Pms.Config.Freetier.Client.class)
    @GetMapping(value = "/{tenantId}/clients")
    public List<Map<String, String>> getClients(@PathVariable String tenantId) {
        return service.getClients(tenantId);
    }

    @ReltioSecured(resourceClass = Pms.Tenant.class)
    @GetMapping(value = "/rdm/{rdmId}")
    public RdmTenant getRdmTenant(@PathVariable String rdmId) {
        return rdmTenantsService.getRdmTenant(rdmId);
    }

    @GetMapping(value = "/{tenantId}")
    public ReltioTenant getTenant(@PathVariable String tenantId) {
        return service.getTenantDetails(tenantId);
    }

    @GetMapping(value = "/_total")
    public Long getMDMTenantCount(@RequestParam(required = false) String options) {
        if ("noncustomer".equalsIgnoreCase(options)) {
            return service.getNonCustomerMDMTenantCount();
        }
        return service.getMDMTenantCount();
    }

    @GetMapping(value = "/_totalByEnv")
    public Map<String, Long> getMDMTenantCountByEnv(@RequestParam(required = false) String options) {
        if ("noncustomer".equalsIgnoreCase(options)) {
            return service.getNonCustomerMDMTenantCountByEnv();
        }
        return service.getMDMTenantCountByEnv();
    }

    @GetMapping(value = "")
    @ReltioSecured(resourceClass = Pms.Tenant.class, privileges = ReltioPrivileges.READ)
    public Collection<ReltioTenant> getTenants(@RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                               @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
                                               @RequestParam(value = "contractId", required = false) String contractId,
                                               @RequestParam(value = "customerId", required = false) String customerId,
                                               @RequestParam(value = "accountId", required = false) String accountId,
                                               @RequestParam(value = "tenantStatus", required = false) TenantStatus tenantStatus,
                                               @RequestParam(value = "department", required = false) String department,
                                               @RequestParam(value = "division", required = false) String division,
                                               @RequestParam(value = "costCenter", required = false) String costCenter,
                                               @RequestParam(value = "customerType", required = false) String customerType,
                                               @RequestParam(value = "deploymentCloud", required = false) String deploymentCloud,
                                               @RequestParam(value = "environment", required = false) String environment,
                                               @RequestParam(value = "owners", required = false) String owners,
                                               @RequestParam(value = "parentJobId" , required = false)String parentJobId,
                                               @RequestParam(value = "endDate" , required = false)String endDate){

        if (areAllParametersNull(contractId, null, customerId, accountId)) {
            TenantFilter tenantFilter = TenantFilter.builder().offset(offset).size(size).status(tenantStatus).department(department).division(division).costCenter(costCenter).customerType(customerType).deploymentCloud(deploymentCloud).owners(owners).environment(environment).parentJobId(parentJobId).endDate(endDate).build();
            return service.getTenants(tenantFilter);
        } else if (onlyContractId(contractId, null, customerId, accountId)) {
            return service.getTenantsForContractWithSizeAndOffset(contractId, size, offset);
        } else if (onlyCustomerId(contractId, null, customerId, accountId)) {
            return service.getTenantsForCustomerIdWithSizeAndOffset(customerId, size, offset);
        } else if (onlyAccountId(contractId, null, customerId, accountId)) {
            return service.getTenantsByAccountId(accountId);
        } else {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), ONLY_ONE);
        }
    }

    @GetMapping(value = "/_count")
    @ReltioSecured(resourceClass = Pms.Tenant.class, privileges = ReltioPrivileges.READ)
    public TenantSummaryCount getTenantSummaryCount(@RequestParam(value = "contractId", required = false) String contractId,
                                                    @RequestParam(value = "customerId", required = false) String customerId,
                                                    @RequestParam(value = "accountId", required = false) String accountId,
                                                    @RequestParam(value = "tenantStatus", required = false) TenantStatus tenantStatus,
                                                    @RequestParam(value = "department", required = false) String department,
                                                    @RequestParam(value = "division", required = false) String division,
                                                    @RequestParam(value = "costCenter", required = false) String costCenter,
                                                    @RequestParam(value = "customerType", required = false) String customerType,
                                                    @RequestParam(value = "deploymentCloud", required = false) String deploymentCloud) {
        int offset = 0;
        int size = 50000;
        TenantSummaryCount.TenantSummaryCountBuilder builder = TenantSummaryCount.builder();
        if (areAllParametersNull(contractId, null, customerId, accountId)) {
            TenantFilter tenantFilter = TenantFilter.builder().offset(offset).size(size).status(tenantStatus).department(department).division(division).costCenter(costCenter).customerType(customerType).deploymentCloud(deploymentCloud).build();
            TenantFilter enterpriseTenantFilter = TenantFilter.builder().status(tenantStatus).department(department).division(division).costCenter(costCenter).customerType(CustomerType.ENTERPRISE.name()).deploymentCloud(deploymentCloud).build();
            TenantFilter nonEnterpriseTenantFilter = TenantFilter.builder().status(tenantStatus).department(department).division(division).costCenter(costCenter).deploymentCloud(deploymentCloud).build();
            builder.withoutOwnersCount(service.getNoOwnerTenantCount(tenantFilter));
            builder.enterpriseCount(service.getEnterpriseTenantCount(enterpriseTenantFilter));
            builder.nonEnterpriseCount(service.getNonEnterpriseTenantCount(nonEnterpriseTenantFilter));
            builder.totalCount(service.getTenantCountByTenantFilter(tenantFilter));
        } else if (onlyContractId(contractId, null, customerId, accountId)) {
            builder.totalCount(Long.valueOf(service.getTenantsForContractWithSizeAndOffset(contractId, size, offset).size()));
        } else if (onlyCustomerId(contractId, null, customerId, accountId)) {
            builder.totalCount(Long.valueOf(service.getTenantsForCustomerIdWithSizeAndOffset(customerId, size, offset).size()));
        } else if (onlyAccountId(contractId, null, customerId, accountId)) {
            builder.totalCount(Long.valueOf(service.getTenantsByAccountId(accountId).size()));
        } else {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), ONLY_ONE);
        }
        return builder.build();
    }

    @GetMapping(value = "/rdm")
    @ReltioSecured(resourceClass = Pms.Tenant.class, privileges = ReltioPrivileges.READ)
    public Collection<RdmTenant> getRDMTenants(@RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                               @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
                                               @RequestParam(value = "contractId", required = false) String contractId,
                                               @RequestParam(value = "customerName", required = false) String customerName,
                                               @RequestParam(value = "customerId", required = false) String customerId,
                                               @RequestParam(value = "accountId", required = false) String accountId) {

        if (areAllParametersNull(contractId, customerName, customerId, accountId)) {
            return rdmTenantsService.getRDMTenants(offset, size);
        } else if (onlyContractId(contractId, customerName, customerId, accountId)) {
            return rdmTenantsService.getRDMTenantsForContract(contractId);
        } else if (onlyCustomerId(contractId, customerName, customerId, accountId)) {
            return rdmTenantsService.getRDMTenantsForCustomerId(customerId);
        } else if (onlyCustomerName(contractId, customerName, customerId, accountId)) {
            return rdmTenantsService.getRDMTenantsForCustomerName(customerName);
        } else if (onlyAccountId(contractId, customerName, customerId, accountId)) {
            return rdmTenantsService.getRdmTenantsByAccountId(accountId);
        } else {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), ONLY_ONE);
        }
    }

    @GetMapping(value = "/getTenantsWithExpiredContracts")
    @ReltioSecured(resourceClass = Pms.Tenant.class, privileges = ReltioPrivileges.READ)
    public Collection<ReltioTenant> getTenantsWithExpiredContracts(@RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset, @RequestParam(value = "size", required = false, defaultValue = "20") Integer size) {
        return service.getTenantsWithExpiredContracts(size, offset);
    }

    @GetMapping(value = "/nextTenantID")
    public Map<String, String> getNextTenantID() {
        return Collections.singletonMap("tenantID", service.getUniqueId());
    }

    @GetMapping(value = "/{tenantId}/users")
    @ReltioSecured(resourceClass = Pms.Config.Freetier.User.class)
    public Collection<Invitee> getUsers(@PathVariable String tenantId,
                                        @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                        @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
                                        @RequestParam(value = "property", required = false) String property,
                                        @RequestParam(value = "value", required = false) String value) {
        if (property == null && value == null) {
            return userService.getUserLimited(tenantId, size, offset);
        } else if (property != null && value != null) {
            return userService.getFilteredUserLimited(tenantId, property, value, size, offset);
        } else {
            throw new PlatformManagementException(PlatformManagementErrorCode.NONE_OR_BOTH_PARAMETERS_MUST_BE_SPECIFIED, HttpStatus.BAD_REQUEST.value());
        }
    }

    @GetMapping(value = "/{tenantId}/users/{username}")
    @ReltioSecured(resourceClass = Pms.Config.Freetier.User.class)
    public Invitee getUser(@PathVariable String tenantId, @PathVariable String username) {
        return userService.getUser(tenantId, username);
    }

    @GetMapping(value = "/{tenantId}/users/_total")
    @ReltioSecured(resourceClass = Pms.Config.Freetier.User.class)
    public Map<String, Integer> getUsersCount(@PathVariable String tenantId,
                                              @RequestParam(value = "property", required = false) String property,
                                              @RequestParam(value = "value", required = false) String value) {
        if (property == null && value == null) {
            return userService.getAllUsersSize(tenantId);
        } else if (property != null && value != null) {
            return userService.getFilteredWithActivenessUsersCount(tenantId, property, value);
        } else {
            throw new PlatformManagementException(PlatformManagementErrorCode.NONE_OR_BOTH_PARAMETERS_MUST_BE_SPECIFIED, HttpStatus.BAD_REQUEST.value());
        }
    }

    @PostMapping(value = "/{tenantId}/users", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Config.Freetier.User.class)
    public Invitee inviteUser(@PathVariable String tenantId, @RequestBody Invitee user, @RequestParam(required = false, defaultValue = "false") Boolean isDnbUser) {
        return service.inviteUser(tenantId, user, isDnbUser);
    }

    //TODO remove this userName
    @PutMapping(value = "/{tenantId}/users/{userName}", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Config.Freetier.User.class)
    public Invitee updateUser(@PathVariable String tenantId, @PathVariable String userName, @RequestBody Invitee user, @RequestParam(required = false, defaultValue = "false") Boolean isDnbUser) {
        return service.updateUser(tenantId, user, isDnbUser);
    }

    @PutMapping(value = "/{tenantId}/users/{userName}/_activate")
    @ReltioSecured(resourceClass = Pms.Config.Freetier.User.class)
    public Map<String, String> activateUser(@PathVariable String tenantId, @PathVariable String userName) {
        Invitee user = service.manageUserActiveness(tenantId, userName, true);
        return user.onlyEmailAndActiveness();
    }

    @PutMapping(value = "/{tenantId}/users/{userName}/_deactivate")
    @ReltioSecured(resourceClass = Pms.Config.Freetier.User.class)
    public Map<String, String> deactivateUser(@PathVariable String tenantId, @PathVariable String userName) {
        Invitee user = service.manageUserActiveness(tenantId, userName, false);
        return user.onlyEmailAndActiveness();
    }

    @DeleteMapping(value = "/{tenantId}/users/{userName}")
    @ReltioSecured(resourceClass = Pms.Config.Freetier.User.class)
    public Map<String, String> deleteUser(@PathVariable String tenantId, @PathVariable String userName) {
        Invitee user = service.deleteUser(tenantId, userName);
        Map<String, String> deletedUser = user.onlyEmailAndActiveness();
        deletedUser.put(STATUS, DELETED);
        return deletedUser;
    }

    @PutMapping(value = "/syncClusterDetails/{environment}")
    @ReltioSecured(resourceClass = Pms.Tenant.class, privileges = ReltioPrivileges.UPDATE)
    public String syncClusterDetails(@PathVariable String environment) {
        service.syncClusterDetails(environment);
        return "SUCCESS";
    }

    @GetMapping(value = "/getTenantsByFeature")
    @ReltioSecured(resourceClass = Pms.Tenant.class, privileges = ReltioPrivileges.READ)
    public Collection<ReltioTenant> getTenantsByFeature(@RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                                        @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
                                                        @RequestBody TenantSearchRequest tenantSearchRequest) {
        return service.getTenantsByFeature(tenantSearchRequest, size, offset);
    }

    @DeleteMapping(value = "/{tenantId}")
    public Map<String, String> deleteTenant(@PathVariable String tenantId) {
        Map<String, String> statusMap = new HashMap<>();
        service.deleteTenant(tenantId);

        statusMap.put("tenantId", tenantId);
        statusMap.put(STATUS, DELETED);
        return statusMap;
    }

    @PatchMapping(value = "/{tenantId}/ownership")
    @ReltioSecured(resourceClass = Pms.Tenant.class, privileges = ReltioPrivileges.UPDATE)
    public ReltioTenant updateOwnership(@PathVariable String tenantId,
                                        @RequestParam(value = "department", required = false) String department,
                                        @RequestParam(value = "division", required = false) String division,
                                        @RequestParam(value = "costCenter", required = false) String costCenter,
                                        @RequestParam(value = "customerType", required = false) CustomerType customerType,
                                        @RequestParam(value = "owners", required = false) Set<String> owners,
                                        @RequestParam(value = "deploymentCloud", required = false) String deploymentCloud,
                                        @RequestParam(value = "endDate", required = false) Date endDate) {
        ReltioTenant updatedTenant = ReltioTenant.builder().department(department)
                .division(division).costCenter(costCenter).customerType(customerType).owners(owners)
                .deploymentCloud(deploymentCloud).endDate(endDate).build();
        return service.updateTenantOwnership(tenantId, updatedTenant);
    }

    @DeleteMapping(value = "/uiConfig/{env}")
    public Map<String, Object> deleteUiConfig(@PathVariable String env, @RequestBody Set<String> tenantIds) {
        tenantUIConfigService.deleteUIConfig(env, tenantIds, true);
        Map<String, Object> deleteUiConfigTenant = new HashMap<>();
        deleteUiConfigTenant.put(STATUS, DELETED);
        deleteUiConfigTenant.put("uiConfig", tenantIds);
        return deleteUiConfigTenant;
    }

    @PostMapping(value = "/{tenantId}/{env}/enablePretrainedModels")
    public Map<String, Object> enablePreTrainedModels(@PathVariable String tenantId, @PathVariable String env) {
        Map<String, Object> statusMap = new HashMap<>();
        service.getTenant(tenantId);
        boolean status = mdmTenantService.enableMLForTenant(tenantId, env);
        statusMap.put("tenantId", tenantId);
        statusMap.put("status", status);
        return statusMap;
    }

}
