package com.reltio.services.pms.clients.external.gcp.secret;

import com.google.api.gax.core.CredentialsProvider;
import com.google.api.gax.rpc.AlreadyExistsException;
import com.google.api.gax.rpc.NotFoundException;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.secretmanager.v1.AccessSecretVersionResponse;
import com.google.cloud.secretmanager.v1.ListSecretsRequest;
import com.google.cloud.secretmanager.v1.ProjectName;
import com.google.cloud.secretmanager.v1.Replication;
import com.google.cloud.secretmanager.v1.Secret;
import com.google.cloud.secretmanager.v1.SecretManagerServiceClient;
import com.google.cloud.secretmanager.v1.SecretManagerServiceSettings;
import com.google.cloud.secretmanager.v1.SecretName;
import com.google.cloud.secretmanager.v1.SecretPayload;
import com.google.cloud.secretmanager.v1.SecretVersionName;
import com.google.protobuf.ByteString;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Service
public class GcpSecretsManagerService implements SecretsManagerService {
    public static final String SECRET_LOCATION = "us-east1";
    public static final String LATEST = "latest";
    public static final int TWENTY_SECONDS = 20000;
    private static final Logger logger = Logger.getLogger(GcpSecretsManagerService.class);
    private final String projectId;
    private final CredentialsProvider credentialsProvider;

    @Autowired
    public GcpSecretsManagerService(CredentialsProvider provider) {
        try {
            this.projectId = ((ServiceAccountCredentials) provider.getCredentials()).getProjectId();
            this.credentialsProvider = provider;
        } catch (IOException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    @Override
    public void saveSecret(String secretKeyId, byte[] secretKey) {
        try (SecretManagerServiceClient client = SecretManagerServiceClient.create(SecretManagerServiceSettings.newBuilder().setCredentialsProvider(credentialsProvider).build())) {
            ProjectName projectName = ProjectName.of(projectId);
            Secret secret = Secret.newBuilder()
                    .setReplication(Replication.newBuilder().setUserManaged(
                            Replication.UserManaged.newBuilder().build().newBuilderForType().addReplicas(
                                    Replication.UserManaged.Replica.newBuilder().setLocation(SECRET_LOCATION).build())
                    )).build();
            Secret createdSecret = createSecretWithRetry(secretKeyId, client, projectName, secret, true);
            SecretPayload payload = SecretPayload.newBuilder()
                    .setData(ByteString.copyFrom(secretKey))
                    .build();
            client.addSecretVersion(createdSecret.getName(), payload);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    @Override
    public void updateSecret(String secretKeyId, byte[] secretKey) {
        try (SecretManagerServiceClient client = SecretManagerServiceClient.create(SecretManagerServiceSettings.newBuilder().setCredentialsProvider(credentialsProvider).build())) {
            SecretName secretName = SecretName.of(projectId, secretKeyId);
            SecretPayload payload = SecretPayload.newBuilder()
                    .setData(ByteString.copyFrom(secretKey))
                    .build();
            client.addSecretVersion(secretName, payload);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    private Secret createSecretWithRetry(String secretKeyId, SecretManagerServiceClient client, ProjectName projectName, Secret secret, boolean retry) throws InterruptedException {
        try {
            return client.createSecret(projectName, secretKeyId, secret);
        } catch (AlreadyExistsException ex) {
            //sometimes we get ALREADY_EXISTS error for just deleted secret so just retry after 20 secs
            if (retry) {
                logger.warn("There was ALREADY_EXISTS exception while creating new secret and this would be reattempted");
                Thread.sleep(TWENTY_SECONDS);
                return createSecretWithRetry(secretKeyId, client, projectName, secret, false);
            } else {
                throw ex;
            }
        }
    }

    @Override
    public String getSecret(String secretKeyId) {
        try (SecretManagerServiceClient client = SecretManagerServiceClient.create(SecretManagerServiceSettings.newBuilder().setCredentialsProvider(credentialsProvider).build())) {
            SecretVersionName secretVersionName = SecretVersionName.of(projectId, secretKeyId, LATEST);
            AccessSecretVersionResponse response = client.accessSecretVersion(secretVersionName);
            return response.getPayload().getData().toStringUtf8();
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    @Override
    public void deleteSecret(String secretKeyId) {
        try (SecretManagerServiceClient client = SecretManagerServiceClient.create(SecretManagerServiceSettings.newBuilder().setCredentialsProvider(credentialsProvider).build())) {
            SecretName secretName = SecretName.of(projectId, secretKeyId);
            client.deleteSecret(secretName);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    @Override
    public boolean doesSecretExist(String secretKeyId) {
        try (SecretManagerServiceClient client = SecretManagerServiceClient.create(SecretManagerServiceSettings.newBuilder().setCredentialsProvider(credentialsProvider).build())) {
            SecretVersionName secretVersionName = SecretVersionName.of(projectId, secretKeyId, LATEST);
            client.accessSecretVersion(secretVersionName);
            return true;
        } catch (NotFoundException e) {
            return false;
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    @Override
    public List<String> listKeys(String keyStartWith) {
        try (SecretManagerServiceClient client = SecretManagerServiceClient.create(SecretManagerServiceSettings.newBuilder().setCredentialsProvider(credentialsProvider).build())) {
            SecretManagerServiceClient.ListSecretsPagedResponse pagedResponse = client.listSecrets(ListSecretsRequest.newBuilder()
                    .setParent(ProjectName.of(projectId).toString())
                    .setFilter("name:" + keyStartWith)
                    .build());
            return StreamSupport.stream(pagedResponse.iterateAll().spliterator(), false).map(Secret::getName).collect(Collectors.toList());
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

}
