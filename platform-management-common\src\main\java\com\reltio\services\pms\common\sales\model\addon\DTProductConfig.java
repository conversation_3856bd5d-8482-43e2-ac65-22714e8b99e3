package com.reltio.services.pms.common.sales.model.addon;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.enterprise.contracts.DTSSType;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@Getter
@Setter
public class DTProductConfig extends BaseProductConfig {

    @JsonProperty("dataTenantId")
    private String dataTenantId;

    public DTProductConfig(PMSProductName pmsProductName) {
        this.pmsProductName = pmsProductName;
    }

    public DTProductConfig(DTSSType type) {
        this.pmsProductName = DTSSType.getPMSProductNameByDTSSType(type);
    }

    public DTProductConfig(String dataTenantId) {
        this.dataTenantId = dataTenantId;
    }
}
