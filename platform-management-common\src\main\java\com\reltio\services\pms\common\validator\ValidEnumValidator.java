package com.reltio.services.pms.common.validator;

import com.reltio.services.pms.common.annotation.ValidEnum;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Arrays;

/**
 * The type Enum validator.
 */
public class ValidEnumValidator implements ConstraintValidator<ValidEnum, String> {
    /**
     * The Enum class.
     */
    private Class<? extends Enum<?>> enumClass;

    /**
     * The Message.
     */
    private String message;


    /**
     * Initialize.
     *
     * @param constraintAnnotation the constraint annotation
     */
    @Override
    public void initialize(ValidEnum constraintAnnotation) {
        this.enumClass = constraintAnnotation.enumClass();
        this.message = constraintAnnotation.message();
    }

    /**
     * Is valid boolean.
     *
     * @param value   the value
     * @param context the context
     * @return the boolean
     */
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        Enum<?>[] enumConstants = enumClass.getEnumConstants();
        for (Enum<?> enumValue : enumConstants) {
            if (enumValue.name().equals(value)) {
                return true;
            }
        }
        String allowedValues = Arrays.toString(enumConstants);
        String formattedMessage = message + ". Allowed values are " + allowedValues;
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(formattedMessage)
                .addConstraintViolation();
        return false;
    }
}
