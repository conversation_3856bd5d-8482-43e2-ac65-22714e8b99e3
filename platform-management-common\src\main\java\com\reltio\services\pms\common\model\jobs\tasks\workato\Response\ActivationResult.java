package com.reltio.services.pms.common.model.jobs.tasks.workato.Response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ActivationResult {
    @JsonProperty("errors")
    List<String> errors;

    @JsonProperty("recipes")
    List<Map<String, Object>> recipes;

    public ActivationResult(@JsonProperty("errors") List<String> errors,
                            @JsonProperty("recipes") List<Map<String, Object>> recipes) {
        this.errors = errors;
        this.recipes = recipes == null || recipes.isEmpty() ? new ArrayList<>() : recipes;
    }

    public List<String> getErrors() {
        return errors;
    }

    public List<Map<String, Object>> getRecipes() {
        return recipes;
    }
}
