package com.reltio.pms.trail.service;

import com.reltio.pms.trail.domain.HttpMethod;

import java.util.Map;

public interface RestAPIService {

    String get(String requestUrl, Map<String, String> requestHeaders)
            throws APICallFailureException, GenericException;

    String post(String requestUrl, Map<String, String> requestHeaders,
                String requestBody) throws APICallFailureException,
            GenericException;

    String put(String requestUrl, Map<String, String> requestHeaders,
               String requestBody) throws APICallFailureException,
            GenericException;

    String delete(String requestUrl, Map<String, String> requestHeaders,
                  String requestBody) throws APICallFailureException,
            GenericException;

    String doExecute(String requestUrl,
                     Map<String, String> requestHeaders, String requestBody,
                     HttpMethod requestMethod) throws APICallFailureException,
            GenericException;

}
