package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

public class TenantsManagementLogsRequest {

    @JsonProperty(value = "requesterEmail")
    private final String requesterEmail;

    @JsonProperty(value = "updatedParams")
    private final List<Map<String,Object>> updatedParams;

    @JsonProperty(value = "updatedParamsTimestamp")
    private final Timestamp updatedParamsTimestamp;

    @JsonCreator
    public TenantsManagementLogsRequest(@JsonProperty(value = "requesterEmail")String requesterEmail,
                                        @JsonProperty(value = "updatedParams")List<Map<String,Object>> updatedParams,
                                        @JsonProperty(value = "updatedParamsTimestamp")Timestamp updatedParamsTimestamp){
        this.requesterEmail = requesterEmail;
        this.updatedParams = updatedParams;
        this.updatedParamsTimestamp = updatedParamsTimestamp;
    }

}
