package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@EqualsAndHashCode(callSuper = false)
public class DeleteMDMTenantTaskConfig extends AbstractPipelineTaskConfig {

    @JsonProperty("skipUsers")
    private List<String> skipUsers;
    @JsonProperty("skipCustomers")
    private List<String> skipCustomers;

    @JsonCreator
    public DeleteMDMTenantTaskConfig(@JsonProperty(value = "name") String name,
                                     @JsonProperty("skipUsers") List<String> skipUsers) {
        super(name, TaskType.DELETE_MDM_TENANT_TASK);
        this.skipUsers = skipUsers == null ? new ArrayList<>() : new ArrayList<>(skipUsers);
        this.skipCustomers = skipCustomers == null? new ArrayList<>() : new ArrayList<>(skipCustomers);
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.MDM;
    }

    @Override
    public String toString() {
        return TaskType.DELETE_MDM_TENANT_TASK + "{}";
    }
}
