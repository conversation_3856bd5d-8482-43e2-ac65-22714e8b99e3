package com.reltio.services.pms.common.model.jobs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.Parameters;
import com.reltio.services.pms.common.model.pipeline.SkipReason;
import com.reltio.services.pms.common.sales.PMSProductName;

import java.util.EnumMap;
import java.util.EnumSet;
import java.util.List;
import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CleanTenantJob extends Job {

    @JsonProperty(value = "tenantName")
    private String tenantName;

    @JsonProperty(value = "tenantData")
    private boolean tenantData;

    @JsonProperty(value = "historyData")
    private boolean historyData;

    @JsonProperty(value = "events")
    private List<String> events;

    @JsonProperty(value = "parameters")
    private Parameters parameters;

    @JsonProperty(value = "qaAutomation")
    private boolean qaAutomation;

    public CleanTenantJob(String parentJobId ,String jobId, String pipelineId, Long startTime, Long finishTime, JobStatus status, List<String> tasks,
                          String envId, String tenantName, boolean tenantData, boolean historyData, List<String> events,
                          Parameters parameters, boolean qaAutomation, EnumMap<SkipReason, EnumSet<PMSProductName>> skippedTasks,List<String> owners) {
        super(parentJobId,jobId, pipelineId, startTime, finishTime, status, tasks, envId, skippedTasks,owners,false);
        this.tenantName = tenantName;
        this.tenantData = tenantData;
        this.historyData = historyData;
        this.events = events;
        this.parameters = parameters;
        this.qaAutomation = qaAutomation;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public boolean isTenantData() {
        return tenantData;
    }

    public boolean isHistoryData() {
        return historyData;
    }

    public List<String> getEvents() {
        return events;
    }

    public void setEvents(List<String> events) {
        this.events = events;
    }

    public void addEvent(String event) {
        events.add(event);
    }

    public Parameters getParameters() {
        return parameters;
    }

    public void setParameters(Parameters parameters) {
        this.parameters = parameters;
    }

    public boolean isQaAutomation() {
        return qaAutomation;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        CleanTenantJob cleanTenantJob = (CleanTenantJob) o;
        return tenantData == cleanTenantJob.tenantData &&
                historyData == cleanTenantJob.historyData &&
                Objects.equals(tenantName, cleanTenantJob.tenantName) &&
                Objects.equals(events, cleanTenantJob.events) &&
                Objects.equals(parameters, cleanTenantJob.parameters);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), tenantName, tenantData, historyData, events, parameters);
    }

    @Override
    public String toString() {
        return "CleanTenantJob{" +
                "tenantName='" + tenantName + '\'' +
                ", tenantData=" + tenantData +
                ", historyData=" + historyData +
                ", events=" + events +
                ", parameters=" + parameters +
                '}';
    }
}
