package com.reltio.services.pms.clients.reltio.activitylog;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.reltio.jackson.core.JsonProcessingException;
import com.reltio.common.config.*;
import com.reltio.devops.common.endpoints.ApiEndpoints;
import com.reltio.devops.common.errors.ErrorCodes;
import com.reltio.devops.common.exception.CommonException;
import com.reltio.devops.common.exception.EnvironmentException;
import com.reltio.rest.data.marshalling.impl.BasicObjectMapperFactory;
import com.reltio.sdk.credentials.AWSRoleFeatureSupportUtil;
import com.reltio.services.pms.clients.reltio.cassandra.CassandraCleaner;
import com.reltio.services.pms.clients.reltio.mdm.MDMClient;
import com.reltio.services.pms.common.PMSRestTemplate;
import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.common.model.secrets.SecretDto;
import com.reltio.services.pms.dto.AlTenantConfiguration;
import com.reltio.services.pms.dto.CassandraConfiguration;
import com.reltio.services.pms.dto.ElasticsearchConfiguration;
import com.reltio.services.pms.service.PmsLockService;
import com.reltio.services.pms.service.SecretService;
import com.reltio.services.pms.service.environment.EnvironmentService;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.configuration.Configuration;
import org.apache.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ActivityLogServiceClient {

  private static final Logger LOG = Logger.getLogger(ActivityLogServiceClient.class);
  private final EnvironmentService environmentService;
  private final PMSRestTemplate pmsRest;
  private final MDMClient mdmClient;
  private final CassandraCleaner cassandraCleaner;
  private final PmsLockService pmsLockService;
  private final Configuration configuration;
  private final SecretService secretService;
  private static final String CONFIG_URL = "%s/tenants/%s";
  public static final String GBQ_TABLES_DELETE_ERROR =
      "While deleting GBQ tables error occurred. Message: {} ";
  private static final String SYSTEM_KEYSPACE_LOCK = "SYSTEM_KEYSPACE_LOCK";
  public static final String GBQ_CONFIG_IS_NOT_DEFINED_FOR_TENANT =
      "GBQ configuration is absent; nothing to delete for tenant {}";
  private static final Set<String> CASSANDRA_HOSTS_PROPS =
      Set.of("CASSANDRA_HOSTS", "COMMON.CASSANDRA_HOSTS");
  private static final String DYNAMO = "dynamo";
  private static final String SPANNER = "spanner";
  private static final String COSMOS = "cosmos";
  private static final String BASE_AWS_ROLE_ARN = "BASE_AWS_ROLE_ARN";
  private static final String SYSTEM_COSMOS_ACCOUNT_KEY = "SYSTEM.COSMOS.KEY";
  private static final String AWS_ACCESS_KEY_ID_SYSTEM_PROPERTY = "aws.accessKeyId";
  private static final String AWS_SECRET_KEY_SYSTEM_PROPERTY = "aws.secretKey";
  private static final String GCP_PRIVATE_KEY_SYSTEM_PROPERTY = "GCP_PRIVATE_KEY_ENCODED";
  private static final String DYNAMO_DB_ACCESS_KEY_NAME = "system_keyspace_dynamodb_access_key";
  private static final String DYNAMO_DB_SECRET_KEY_NAME = "system_keyspace_dynamodb_secret_key";
  private static final String SPANNER_DB_GCP_ENCODED_KEY =
      "system_keyspace_spannerdb_gcp_encoded_key";
  private static final String COSMOS_DB_ACCOUNT_KEY = "system_keyspace_cosmosdb_account_key";

  @Autowired
  public ActivityLogServiceClient(
      EnvironmentService environmentService,
      PMSRestTemplate pmsRest,
      MDMClient mdmClient,
      CassandraCleaner cassandraCleaner,
      PmsLockService pmsLockService,
      Configuration configuration,
      SecretService secretService) {
    this.environmentService = environmentService;
    this.pmsRest = pmsRest;
    this.mdmClient = mdmClient;
    this.cassandraCleaner = cassandraCleaner;
    this.pmsLockService = pmsLockService;
    this.configuration = configuration;
    this.secretService = secretService;
  }

  public void deleteALTenant(String alTenantId, String envId) {

    String alServiceUrl =
        environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.ACTIVITYLOG);

    TenantConfiguration tenantConfiguration =
        getALTenantConfiguration(alTenantId, String.format(CONFIG_URL, alServiceUrl, alTenantId));

    disableILMPolicy(alServiceUrl, alTenantId);
    deleteILMPolicy(alServiceUrl, alTenantId);

    cassandraCleaner.cleanupActivityLog(envId, tenantConfiguration, false);

    cleanGBQ(alTenantId, alServiceUrl);
    deleteTenantFromSysKeyspace(alTenantId, envId);
  }

  private void deleteTenantFromSysKeyspace(String tenantId, String envId) {
    try {
      if (pmsLockService.acquireLock(SYSTEM_KEYSPACE_LOCK, tenantId)) {
        JsonNode sysConfig = mdmClient.getEnvironmentSystemConfig(envId);
        JsonNode json = sysConfig.get("api_config");
        Map<String, Object> properties = new HashMap<>();
        Iterator<Map.Entry<String, JsonNode>> fields = json.fields();
        while (fields.hasNext()) {
          Map.Entry<String, JsonNode> field = fields.next();
          String name = field.getKey();
          String value = field.getValue().asText();
          if (CASSANDRA_HOSTS_PROPS.contains(name) && value.startsWith("[")) { // NOSONAR
            properties.put(name, value.substring(1, value.length() - 1));
          } else {
            properties.put(name, value);
          }
        }
        String systemDb = (String) properties.get("SYSTEM.DEFAULT_DB");
        if (systemDb != null) {
          setDbCredentials(envId, properties, systemDb);
        }
        cassandraCleaner.deleteTenantFromSysKeyspace(envId, tenantId, properties);
      }
    } finally {
      System.clearProperty(AWS_ACCESS_KEY_ID_SYSTEM_PROPERTY);
      System.clearProperty(AWS_SECRET_KEY_SYSTEM_PROPERTY);
      System.clearProperty(GCP_PRIVATE_KEY_SYSTEM_PROPERTY);
      pmsLockService.releaseLock(SYSTEM_KEYSPACE_LOCK);
    }
  }

  private void setDbCredentials(String env, Map<String, Object> properties, String systemDb) {
    switch (systemDb.toLowerCase()) {
      case DYNAMO:
        if (AWSRoleFeatureSupportUtil.isFeatureEnabled()) {
          LOG.info("[IRSA TRACE] enabled");
          properties.put(BASE_AWS_ROLE_ARN, configuration.getString(BASE_AWS_ROLE_ARN, null));
          break;
        }
        LOG.info("[IRSA TRACE] disabled");
        SecretDto accessKey = secretService.get(env, DYNAMO_DB_ACCESS_KEY_NAME);
        SecretDto secretKey = secretService.get(env, DYNAMO_DB_SECRET_KEY_NAME);
        System.setProperty(AWS_ACCESS_KEY_ID_SYSTEM_PROPERTY, accessKey.getSecret());
        System.setProperty(AWS_SECRET_KEY_SYSTEM_PROPERTY, secretKey.getSecret());
        break;

      case SPANNER:
        SecretDto gcpPrivateKeyEncoded = secretService.get(env, SPANNER_DB_GCP_ENCODED_KEY);
        System.setProperty(GCP_PRIVATE_KEY_SYSTEM_PROPERTY, gcpPrivateKeyEncoded.getSecret());
        break;

      case COSMOS:
        SecretDto cosmosAccountKey = secretService.get(env, COSMOS_DB_ACCOUNT_KEY);
        properties.put(SYSTEM_COSMOS_ACCOUNT_KEY, cosmosAccountKey.getSecret());
        break;

      default:
        break;
    }
  }

  public void cleanGBQ(String tenantId, String serviceUrl) {
    try {
      deleteGBQTenantTables(tenantId, serviceUrl);
    } catch (CommonException | EnvironmentException e) {
      if (e instanceof CommonException ce) {
        if (ce.getCode() == ErrorCodes.NOT_FOUND) {
          log.info(GBQ_CONFIG_IS_NOT_DEFINED_FOR_TENANT, tenantId);
        }

        if (ce.getCode() == ErrorCodes.INTERNAL_PLATFORM_ERROR
            && ce.getMessage() != null
            && ce.getMessage().contains("GBQ configuration is not defined")) {
          log.info(GBQ_CONFIG_IS_NOT_DEFINED_FOR_TENANT, tenantId);
        }
      }
      log.warn(GBQ_TABLES_DELETE_ERROR, e.getMessage());
    }
  }

  private TenantConfiguration getALTenantConfiguration(String tenantId, String alUrl) {

    AlTenantConfiguration alTenantConfiguration =
        pmsRest.getForObject(alUrl, AlTenantConfiguration.class);

    CassandraConfiguration cassandraConfig = alTenantConfiguration.getCassandra();
    ElasticsearchConfiguration esConfig = alTenantConfiguration.getElasticsearch();
    TenantDatabaseConfiguration dbConfig = getTenantDatabaseConfiguration(cassandraConfig);
    TenantSearchBufferedConfiguration searchConfig = null;
    ActivityLogConfig activityLogConfig = ActivityLogConfig.defaultConfig();
    activityLogConfig
        .getLongTermStorage()
        .setEnabled(alTenantConfiguration.getBigQuery().isEnabled());
    activityLogConfig
        .getLongTermStorage()
        .setGbqLimitBytes(alTenantConfiguration.getBigQuery().getLimitBytes());
    if (esConfig != null) {
      searchConfig =
          TenantSearchBufferedConfigurationBuilder.builder()
              .setEsClusterName(esConfig.getClusterName())
              .setEsHosts(esConfig.getHosts())
              .setNumberOfShards(esConfig.getNumberOfShards())
              .setNumberOfRelationsShards(esConfig.getNumberOfRelationsShards())
              .setNumberOfActivitiesShards(esConfig.getNumberOfActivitiesShards())
              .setNumberOfInteractionsShards(esConfig.getNumberOfInteractionsShards())
              .setWaitForAllShards(Boolean.FALSE)
              .setNumberOrReplicas(esConfig.getNumberOrReplicas())
              .setIndexActivityDelta(esConfig.getIndexActivityDelta())
              .build();
    }

    return new TenantConfigurationBuilder(TenantConfigurationType.ACTIVITY_LOG, tenantId)
        .setTenantName(tenantId)
        .setCustomerName(tenantId)
        .setDataStorageConfig(dbConfig)
        .setSearchStorageConfiguration(searchConfig)
        .setDefaultTenant(tenantId)
        .setCreatedTime(0L)
        .setUpdatedTime(0L)
        .setActivityLogConfig(activityLogConfig)
        .build();
  }

  @NotNull
  private static TenantDatabaseConfiguration getTenantDatabaseConfiguration(
      CassandraConfiguration cassandraConfig) {
    TenantDatabaseConfiguration dbConfig = getEmptyTenantDatabaseConfiguration();

    dbConfig.setActivityLogV2KeyspaceConfig(
        new CassandraConfigWithDSMapping(
            null,
            null,
            null,
            cassandraConfig.getKeyspaceName(),
            null,
            null,
            null,
            cassandraConfig.getHosts(),
            cassandraConfig.getClusterName(),
            cassandraConfig.getReplicationFactor(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null));
    return dbConfig;
  }

  public void disableILMPolicy(String serviceUrl, String tenantId) throws EnvironmentException {
    String url = String.format(ApiEndpoints.API_DISABLE_ILM_POLICY, serviceUrl, tenantId);
    JSONObject body = new JSONObject();
    pmsRest.postForObject(url, body, ObjectNode.class);
  }

  public void deleteILMPolicy(String serviceUrl, String tenantId) throws EnvironmentException {
    String url = String.format(ApiEndpoints.API_DELETE_ILM_POLICY, serviceUrl, tenantId);
    JSONObject body = new JSONObject();
    pmsRest.postForObject(url, body, ObjectNode.class);
  }

  public static TenantDatabaseConfiguration getEmptyTenantDatabaseConfiguration() {
    com.fasterxml.reltio.jackson.databind.ObjectMapper marshaller =
        BasicObjectMapperFactory.createMapper();
    try {
      return marshaller.readValue("{}", TenantDatabaseConfiguration.class);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

  public void deleteGBQTenantTables(String tenantId, String serviceUrl)
      throws EnvironmentException {
    String url = String.format(ApiEndpoints.API_DELETE_GBQ_TABLE, serviceUrl, tenantId);
    pmsRest.delete(url);
  }
}
