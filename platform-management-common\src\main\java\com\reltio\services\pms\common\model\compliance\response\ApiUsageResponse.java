package com.reltio.services.pms.common.model.compliance.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class ApiUsageResponse {
    /**
     * The Report date.
     */
    @JsonProperty("reportDate")
    private String reportDate;
    /**
     * The Tenant id.
     */
    @JsonProperty("tenantId")
    private String tenantId;
    /**
     * The Environment.
     */
    @JsonProperty("environment")
    private String environment;
    /**
     * The Http method.
     */
    @JsonProperty("httpMethod")
    private String httpMethod;
    /**
     * The Handler mapping.
     */
    @JsonProperty("handlerMapping")
    private String handlerMapping;
    /**
     * The Request count.
     */
    @JsonProperty("apiUsage")
    private Long apiUsage;
}
