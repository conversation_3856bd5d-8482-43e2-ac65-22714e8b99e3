package com.reltio.services.pms.common.sales.model;

/**
 * The enum Sales package codes.
 */
public enum SalesForceProductName {
    /**
     * Sales force base package sales package codes.
     */
    BASE,
    /**
     * Sales force additional package sales package codes.
     */
    ADDITIONAL,
    /**
     * Sales force rih package sales package codes.
     */
    RIH,
    /**
     * Sales force add ons sales package codes.
     */
    ADD_ONS,


    /**
     * Purpose sales force codes.
     */
    PURPOSE,

    /**
     * Cloning quotas sales force product name.
     */
    CLONE,
    /**
     * Api quotas sales force product name.
     */
    DATA_API_CALLS,
    /**
     * Dgusers quotas sales force product name.
     */
    DGUSERS,
    /**
     * File storage quotas sales force product name.
     */
    FILE_STORAGE,
    /**
     * Ma users quotas sales force product name.
     */
    MA_USERS,
    /**
     * Rdm quotas sales force product name.
     */
    RDM_API_CALLS,
    /**
     * Rsu quotas sales force product name.
     */
    RSU,
    /**
     * Snap restore quotas sales force product name.
     */
    SNAP_RESTORE,
    /**
     * Workflow process quotas sales force product name.
     */
    WORKFLOW_PROCESS,
    /**
     * Sales force RIH AddOns package codes.
     */
    RIH_ADDONS,
    MDM_DEPLOYMENT_CLOUD,
    RDM_DEPLOYMENT_CLOUD,
    DEPLOYMENT_REGION,
    VELOCITY_PACK,
    DATA_DOMAIN,
    COMPLIANCE,
    /**
     * Sales force rih tasks sales package codes.
     */
    RIH_TASKS
}
