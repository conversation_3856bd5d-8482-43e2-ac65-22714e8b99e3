package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerSupport {

    @JsonProperty("level")
    private String level;

    @JsonProperty("zendeskId")
    private String zendeskId;

    @JsonProperty("orgName")
    private String orgName;

    public CustomerSupport() {
    }

    public CustomerSupport(String level, String zendeskId, String orgName) {
        this.level = level;
        this.zendeskId = zendeskId;
        this.orgName = orgName;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getZendeskId() {
        return zendeskId;
    }

    public void setZendeskId(String zendeskId) {
        this.zendeskId = zendeskId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}
