package com.reltio.services.pms.service.jobs.tasks.approval;

import com.reltio.services.pms.common.model.EmailContentType;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.approve.ApprovalStatus;
import com.reltio.services.pms.common.model.jobs.tasks.approve.ApprovalTaskInstance;
import com.reltio.services.pms.common.model.proxy.ActionStatus;
import com.reltio.services.pms.common.model.proxy.ExecutionStatus;
import com.reltio.services.pms.common.model.proxy.approve.ApprovalSecureAction;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.AbstractTaskExecutionService;
import com.reltio.services.pms.service.notification.EmailNotificationService;
import com.reltio.services.pms.service.proxy.SecureActionProxyService;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class ApprovalTaskExecutionService extends AbstractTaskExecutionService<ApprovalTaskInstance> {

    private final EmailNotificationService emailNotificationService;
    private final SecureActionProxyService<ApprovalSecureAction> secureActionProxyService;

    public ApprovalTaskExecutionService(String envId, ApprovalTaskInstance taskDetail,
                                        GrafanaDashboardGBQService grafanaDashboardGBQService,
                                        EmailNotificationService emailNotificationService,
                                        SecureActionProxyService<ApprovalSecureAction> secureActionProxyService) {
        super(envId, taskDetail, grafanaDashboardGBQService);
        this.emailNotificationService = emailNotificationService;
        this.secureActionProxyService = secureActionProxyService;
    }

    @Override
    public void executeTask() throws InterruptedException {

        if (taskDetail.getResolution() == null) {
            taskDetail.setResolution(ApprovalStatus.UNRESOLVED);

            ApprovalSecureAction approvalSecureAction = new ApprovalSecureAction(null, envId, taskDetail.getJobId(), taskDetail.getTaskId(), ActionStatus.OPEN, ExecutionStatus.OPEN, ApprovalStatus.UNRESOLVED);
            approvalSecureAction = secureActionProxyService.createAction(approvalSecureAction);

            Map<String, Object> requester = new HashMap<>();
            requester.put("requester", taskDetail.getRequester());
            emailNotificationService.notify(EmailContentType.APPROVAL_REQUEST, approvalSecureAction.getSecureKey(), taskDetail.getApproverEmails(), requester);
            updateTask();
            taskDetail.setStatus(TaskStatus.WAITING);
        } else if (taskDetail.getResolution().equals(ApprovalStatus.APPROVED)) {
            taskDetail.setStatus(TaskStatus.COMPLETED);
        } else if (taskDetail.getResolution().equals(ApprovalStatus.REJECTED)) {
            emailNotificationService.notify(EmailContentType.REJECT_NOTIFICATION, null, Collections.singleton(taskDetail.getRequester().getEmail()), null);
            taskDetail.setStatus(TaskStatus.CANCELED);
        } else if (taskDetail.getResolution().equals(ApprovalStatus.UNRESOLVED)) {
            taskDetail.setStatus(TaskStatus.WAITING);
        }
    }

}
