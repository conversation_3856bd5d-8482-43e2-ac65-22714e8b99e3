package com.reltio.services.pms.service.compliance.impl;

import com.reltio.collection.CollectionUtils;
import com.reltio.services.pms.common.model.compliance.request.ComplianceReportRequest;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingByEnvironmentResponse;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingByEnvironmentResponseModel;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByCustomerModel;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByCustomerResponse;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByEnvironmentModel;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByTenantModel;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.SalesAccount;
import com.reltio.services.pms.service.compliance.BillingReportService;
import com.reltio.services.pms.service.compliance.comparators.BillingReportByEnvironmentSorter;
import com.reltio.services.pms.service.compliance.impl.comparators.BillingReportByCustomerSorter;
import com.reltio.services.pms.service.compliance.util.ComplianceUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class BillingReportServiceImpl extends ComplianceUtil implements BillingReportService {

    /**
     * Gets billing report usage by environment.
     *
     * @param accountCollection                   the account collection
     * @param tenantCollection                    the tenant collection
     * @param billingReportByEnvironmentModelList the billing usage by environment
     * @return the billing report by environment
     */
    @Override
    public BillingByEnvironmentResponse getBillingUsageByEnvironment(Collection<SalesAccount> accountCollection, Collection<ReltioTenant> tenantCollection, List<BillingReportByEnvironmentModel> billingReportByEnvironmentModelList, ComplianceReportRequest billingReportRequest, Boolean export) {
        // forming a map of the accountId's with environment and the billing dates
        Map<String, List<BillingReportByEnvironmentModel>> groupedByAccountIdAndEnvironmentAndDates = billingReportByEnvironmentModelList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(tenantModel -> {
                    // Use tenantCollection to get tenantId based on accountId
                    Optional<String> accountIdOptional = tenantCollection.stream().filter(Objects::nonNull)
                            .filter(tenant -> StringUtils.isNotBlank(tenant.getTenantId()) && StringUtils.isNotBlank(tenantModel.getTenantId()) && Objects.equals(tenant.getTenantId(), tenantModel.getTenantId()))
                            .filter(tenant -> StringUtils.isNotBlank(tenant.getAccountId()))
                            .map(ReltioTenant::getAccountId)
                            .findFirst();

                    String accountId = String.valueOf(accountIdOptional);
                    String environment = tenantModel.getEnvironment() != null ? tenantModel.getEnvironment() : StringUtils.EMPTY;

                    // Null check for billing start and end dates
                    String billingStartDate = tenantModel.getBillingStartDate() != null ? tenantModel.getBillingStartDate() : StringUtils.EMPTY;
                    String billingEndDate = tenantModel.getBillingEndDate() != null ? tenantModel.getBillingEndDate() : StringUtils.EMPTY;
                    String billingDates = billingStartDate + "-" + billingEndDate;

                    return accountId + "-" + environment + "-" + billingDates;
                }));

        List<BillingByEnvironmentResponseModel> responseList = new ArrayList<>();
        for (List<BillingReportByEnvironmentModel> group : groupedByAccountIdAndEnvironmentAndDates.values()) {
            BillingByEnvironmentResponseModel billingByEnvironmentResponseModel = new BillingByEnvironmentResponseModel();
            Map<String, Long> environmentMap = new HashMap<>();

            // Process each model in the group
            for (BillingReportByEnvironmentModel tenantModel : group) {
                groupUsageByEnvironment(tenantModel, environmentMap);
                billingByEnvironmentResponseModel.setBillingStartDate(tenantModel.getBillingStartDate());
                billingByEnvironmentResponseModel.setBillingEndDate(tenantModel.getBillingEndDate());
                billingByEnvironmentResponseModel.setEnvironment(tenantModel.getEnvironment());
                billingByEnvironmentResponseModel.setCustomer(getCustomerName(tenantModel.getTenantId(), tenantCollection, accountCollection));
                billingByEnvironmentResponseModel.setPurpose(getPurpose(tenantModel.getTenantId(), tenantCollection));
                billingByEnvironmentResponseModel.setTaskUsage(environmentMap.getOrDefault(tenantModel.getEnvironment(), NumberUtils.LONG_ZERO));
            }

            // Set calculated values for the group
            String tenantId = group.stream().filter(tenant->StringUtils.isNotBlank(tenant.getTenantId())).map(BillingReportByEnvironmentModel::getTenantId).findFirst().orElse(StringUtils.EMPTY); // Assuming all elements in the group have the same tenantId
            Map<String, Long> cpQuotaMap = getCpQuotaByCustomerForEnvironment(accountCollection, tenantCollection, billingByEnvironmentResponseModel, tenantId);
            long cpQuota = cpQuotaMap.getOrDefault(billingByEnvironmentResponseModel.getCustomer(), NumberUtils.LONG_ZERO);
            billingByEnvironmentResponseModel.setCpQuota(cpQuota);
            long tasksQuota = (long) (cpQuota * 0.25);
            billingByEnvironmentResponseModel.setTaskQuota(tasksQuota);

            long tasksUsage = Objects.isNull(billingByEnvironmentResponseModel.getTaskUsage()) ? NumberUtils.LONG_ZERO : billingByEnvironmentResponseModel.getTaskUsage();

            // Handling division by zero scenario
            double taskPercentageValue = calculatePercentage(tasksUsage, tasksQuota);
            billingByEnvironmentResponseModel.setTaskPercentage(taskPercentageValue);
            responseList.add(billingByEnvironmentResponseModel);
        }
        List<BillingByEnvironmentResponseModel> paginatedBillingReportByEnvironmentList = paginateAndSortBillingReportByEnvironmentData(responseList,
                billingReportRequest.getOffset(),
                billingReportRequest.getSize(),
                billingReportRequest.getSortField(),
                billingReportRequest.getSortOrder(),
                export);
        BillingByEnvironmentResponse billingByEnvironmentResponse = new BillingByEnvironmentResponse();
        billingByEnvironmentResponse.setBillingByEnvironmentResponseModelList(paginatedBillingReportByEnvironmentList);
        billingByEnvironmentResponse.setTotalCount((long) responseList.size());
        return billingByEnvironmentResponse;
    }

    /**
     * Gets billing report usage by customer.
     *
     * @param accountCollection              the account collection
     * @param tenantCollection               the tenant collection
     * @param billingReportByTenantModelList the billing usage by tenant
     * @return the billing report by customer
     */
    @Override
    public BillingReportByCustomerResponse getBillingUsageByCustomer(Collection<SalesAccount> accountCollection, Collection<ReltioTenant> tenantCollection, List<BillingReportByTenantModel> billingReportByTenantModelList, ComplianceReportRequest billingReportRequest, Boolean export) {
        BillingReportByCustomerResponse billingReportByCustomerResponse = new BillingReportByCustomerResponse();
        Map<String, Long> cpQuotaMap = getCpQuotaByCustomer(accountCollection, tenantCollection, billingReportByTenantModelList);
        Map<BillingReportByCustomerModel, Long> taskUsageMapByCustomerAndBillingPeriod = groupAndSumTaskUsage(billingReportByTenantModelList);
        List<BillingReportByCustomerModel> billingReportByCustomerList = getBillingReportByCustomerList(taskUsageMapByCustomerAndBillingPeriod, cpQuotaMap);
        List<BillingReportByCustomerModel> paginatedBillingReportByCustomerList = paginateAndSortBillingReportByCustomerData(billingReportByCustomerList,
                billingReportRequest.getOffset(),
                billingReportRequest.getSize(),
                billingReportRequest.getSortField(),
                billingReportRequest.getSortOrder(),
                export);
        billingReportByCustomerResponse.setBillingReportByCustomerModelList(paginatedBillingReportByCustomerList);
        billingReportByCustomerResponse.setTotalCount((long) taskUsageMapByCustomerAndBillingPeriod.size());
        return billingReportByCustomerResponse;
    }

    @NotNull
    private Map<String, Long> getCpQuotaByCustomer(Collection<SalesAccount> accountCollection, Collection<ReltioTenant> tenantCollection, List<BillingReportByTenantModel> billingReportByTenantModelList) {
        Map<String, Long> cpQuotaMap = new HashMap<>();
        for (BillingReportByTenantModel tenantModel : billingReportByTenantModelList) {
            tenantModel.setCustomer(getCustomerName(tenantModel.getTenantId(), tenantCollection, accountCollection));
            String accountId = getAccountIdForTenant(tenantCollection, tenantModel.getTenantId());
            cpQuotaMap.computeIfAbsent(tenantModel.getCustomer(), key -> getCpQuotaFromContract(tenantCollection, accountId));
        }
        return cpQuotaMap;
    }

    /**
     * method to fetch the cpQuota from the contracts bnm≤bnm
     *
     * @param tenantCollection List of tenants
     * @param targetAccountId  account id
     * @return cpQuota
     */

    protected Long getCpQuotaFromContract(Collection<ReltioTenant> tenantCollection, String targetAccountId) {
        Integer cpQuota = NumberUtils.INTEGER_ZERO;
        Map<String, Set<String>> packageToTenantPurposeMap = new HashMap<>();

        // Map packageId to tenant purposes
        tenantCollection.stream()
                .filter(Objects::nonNull)
                .filter(tenant -> StringUtils.isNotBlank(tenant.getPackageId()))
                .forEach(tenant -> {
                    String packageId = tenant.getPackageId();
                    String tenantPurposeName = Optional.ofNullable(tenant.getTenantPurpose())
                            .map(TenantPurpose::name) // Assuming EnumType is the enum type of tenant purpose
                            .orElse(StringUtils.EMPTY);
                    packageToTenantPurposeMap
                            .computeIfAbsent(packageId, key -> new HashSet<>())
                            .add(tenantPurposeName);
                });

        // Filter tenants by target account ID
        List<ReltioTenant> filteredTenants = tenantCollection.stream()
                .filter(tenant -> Optional.ofNullable(tenant)
                        .filter(instance -> StringUtils.isNotBlank(instance.getAccountId()))
                        .map(ReltioTenant::getAccountId)
                        .orElse(StringUtils.EMPTY).equals(targetAccountId))
                .collect(Collectors.toList());

        boolean prodProcessed = false; // Flag to track if a PROD tenant has been processed

        for (ReltioTenant tenant : filteredTenants) {
            String tenantPurposeName = Optional.ofNullable(tenant.getTenantPurpose())
                    .map(TenantPurpose::name)
                    .orElse(StringUtils.EMPTY);

            // Check if PROD tenant exists in package
            Set<String> tenantPurposeSet = packageToTenantPurposeMap.getOrDefault(tenant.getPackageId(), Collections.emptySet());
            boolean prodExists = CollectionUtils.isNotEmpty(tenantPurposeSet) && tenantPurposeSet.contains("PROD");

            if (prodExists && "PROD".equals(tenantPurposeName) && !prodProcessed) {
                // Process only one PROD tenant
                cpQuota = getQuantity(cpQuota, tenant);
                break; // Exit loop since PROD tenant is found
            } else if (!prodProcessed && "TEST".equals(tenantPurposeName)) {
                // Process only one TEST tenant if no PROD tenant exists
                cpQuota = getQuantity(cpQuota, tenant);
            }
        }

        return cpQuota.longValue();
    }

    protected Integer getQuantity(Integer cpQuota, ReltioTenant tenant) {
        Map<PMSProductName, BaseProductConfig> additionalProducts = tenant.getAdditionalProducts();
        if (MapUtils.isNotEmpty(additionalProducts)) {
            cpQuota += Optional.of(additionalProducts)
                    .map(products -> products.getOrDefault(PMSProductName.TENANT_SIZE, new BaseProductConfig()))
                    .filter(baseProductConfig -> Objects.nonNull(baseProductConfig.getQuantity()))
                    .map(BaseProductConfig::getQuantity)
                    .orElse(NumberUtils.INTEGER_ZERO);
        }
        return cpQuota;
    }


    private static void groupUsageByEnvironment(BillingReportByEnvironmentModel model, Map<String, Long> envMap) {
        if (envMap.containsKey(model.getEnvironment())) {
            Long value = envMap.get(model.getEnvironment()) + model.getTaskUsage();
            envMap.put(model.getEnvironment(), value);
        } else {
            envMap.put(model.getEnvironment(), model.getTaskUsage());
        }
    }

    private static Map<BillingReportByCustomerModel, Long> groupAndSumTaskUsage(List<BillingReportByTenantModel> billingReportByTenantModelList) {
        return billingReportByTenantModelList.stream()
                .collect(Collectors.groupingBy(entry ->
                                new BillingReportByCustomerModel(entry.getCustomer(), NumberUtils.LONG_ZERO, NumberUtils.LONG_ZERO, entry.getBillingStartDate(), entry.getBillingEndDate(), NumberUtils.LONG_ZERO, NumberUtils.INTEGER_ZERO),
                        LinkedHashMap::new,
                        Collectors.summingLong(BillingReportByTenantModel::getTaskUsage)));
    }

    private static List<BillingReportByCustomerModel> getBillingReportByCustomerList(Map<BillingReportByCustomerModel, Long> taskUsageMapByCustomerAndBillingPeriod, Map<String, Long> cpQuotaMapByCustomer) {
        List<BillingReportByCustomerModel> billingReportByCustomerModelList = new ArrayList<>();
        for (Map.Entry<BillingReportByCustomerModel, Long> billingReportByCustomerModelEntry : taskUsageMapByCustomerAndBillingPeriod.entrySet()) {
            BillingReportByCustomerModel billingReportByCustomerModel = billingReportByCustomerModelEntry.getKey();
            long taskUsage = billingReportByCustomerModelEntry.getValue();
            billingReportByCustomerModel.setTaskUsage(taskUsage);
            long cpQuota = cpQuotaMapByCustomer.getOrDefault(billingReportByCustomerModel.getCustomer(), NumberUtils.LONG_ZERO);
            billingReportByCustomerModel.setCpQuota(cpQuota);
            long tasksQuota = (long) (cpQuota * 0.25);
            billingReportByCustomerModel.setTaskQuota(tasksQuota);
            double taskPercentage = calculatePercentage(taskUsage, tasksQuota);
            billingReportByCustomerModel.setTaskPercentage(taskPercentage);
            billingReportByCustomerModelList.add(billingReportByCustomerModel);
        }
        return billingReportByCustomerModelList;
    }

    private static List<BillingReportByCustomerModel> paginateAndSortBillingReportByCustomerData(List<BillingReportByCustomerModel> billingReportByCustomerModelList, Integer offset, Integer size, String sortField, String sortOrder, boolean export) {
        // Sorting
        List<BillingReportByCustomerModel> sortedReports = BillingReportByCustomerSorter.sort(billingReportByCustomerModelList, sortField, sortOrder, export);

        // Paginate
        if (Objects.isNull(offset) || Objects.isNull(size) || export) {
            return sortedReports;
        }

        int startIndex = offset * size;
        int endIndex = startIndex + size;
        if (startIndex >= sortedReports.size()) {
            return Collections.emptyList();
        } else if (endIndex > sortedReports.size()) {
            endIndex = sortedReports.size();
        }
        return sortedReports.subList(startIndex, endIndex);
    }


    private static List<BillingByEnvironmentResponseModel> paginateAndSortBillingReportByEnvironmentData(List<BillingByEnvironmentResponseModel> billingReportByCustomerModelList, Integer offset, Integer size, String sortField, String sortOrder, boolean export) {
        // Sorting
        List<BillingByEnvironmentResponseModel> sortedReports = BillingReportByEnvironmentSorter.sort(billingReportByCustomerModelList, sortField, sortOrder, export);
        // Paginate
        if (Objects.isNull(offset) || Objects.isNull(size) || export) {
            return sortedReports;
        }
        int startIndex = offset * size;
        int endIndex = startIndex + size;
        if (startIndex >= sortedReports.size()) {
            return Collections.emptyList();
        } else if (endIndex > sortedReports.size()) {
            endIndex = sortedReports.size();
        }
        return sortedReports.subList(startIndex, endIndex);
    }

    @NotNull
    private Map<String, Long> getCpQuotaByCustomerForEnvironment(Collection<SalesAccount> accountCollection, Collection<ReltioTenant> tenantCollection, BillingByEnvironmentResponseModel billingByEnvironmentResponseModel, String tenantId) {
        Map<String, Long> cpQuotaMap = new HashMap<>();
        billingByEnvironmentResponseModel.setCustomer(getCustomerName(tenantId, tenantCollection, accountCollection));
        String accountId = getAccountIdForTenant(tenantCollection, tenantId);
        cpQuotaMap.computeIfAbsent(billingByEnvironmentResponseModel.getCustomer(), key -> getCpQuotaFromContract(tenantCollection, accountId));
        return cpQuotaMap;
    }

    private static String getAccountIdForTenant(Collection<ReltioTenant> tenantCollection, String tenantId) {
        Optional<ReltioTenant> reltioTenant = tenantCollection.stream()
                .filter(Objects::nonNull)
                .filter(tenant -> StringUtils.isNotBlank(tenant.getTenantId()) && Objects.equals(tenant.getTenantId(), tenantId))
                .findFirst();
        return reltioTenant.map(ReltioTenant::getAccountId).orElse(StringUtils.EMPTY);
    }

    private static double calculatePercentage(long numerator, long denominator) {
        double percentage = (denominator == 0) ? 0.0 : (double) numerator / denominator * 100;
        return Math.round(percentage * 100.0) / 100.0;
    }
}
