package com.reltio.services.pms.dao.sales;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.Query;
import com.google.cloud.firestore.QuerySnapshot;
import com.google.common.collect.Lists;
import com.reltio.collection.CollectionUtils;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.sales.model.ReltioContract;
import com.reltio.services.pms.dao.AbstractLevel1CollectionDao;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ReltioContractDao extends AbstractLevel1CollectionDao<ReltioContract> {

    private static final String CONTRACTS_COLLECTION_NAME = "PMS_SALES_CONTRACTS";
    private static final String END_DATE_STR= "endDate";
    private static final int MAX_LIMIT = 30;
    private static final Logger LOGGER = Logger.getLogger(ReltioContractDao.class);



    @Autowired
    public ReltioContractDao(CredentialsProvider provider,
                             SalesAccountDao salesAccountDao, ReltioUserHolder reltioUserHolder, @Value("${firestore.env.name}") String deployedEnv) {
        super(provider, salesAccountDao, CONTRACTS_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    protected TypeReference<ReltioContract> getTypeReference() {
        return new TypeReference<ReltioContract>() {
        };
    }

    public Collection<ReltioContract> getContractByCustomerId(String custId) {
        try {
            Query query = getCurrentCollectionGroup().whereEqualTo("authCustomerId", custId);
            return getResultFromQuery(query);
        } catch (NoSuchElementException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CONTRACT_NOT_FOUND, HttpStatus.NOT_FOUND.value());
        }
    }


    public Collection<ReltioContract> getContractsByAccountId(String accountId) {
        Query query = getCurrentCollectionGroup().whereEqualTo("accountId", accountId);
        return getResultFromQuery(query);
    }

    public ReltioContract getContractByContractId(String contractId) {
        Collection<ReltioContract> reltioContracts = getContractsByFieldId("contractId", contractId);
        if (CollectionUtils.isEmpty(reltioContracts)) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CONTRACT_NOT_FOUND, HttpStatus.NOT_FOUND.value(), contractId);
        }
        return reltioContracts.iterator().next();
    }

    public Collection<ReltioContract> getContractsByFieldId(String fieldName, String fieldValue) {
        Query query = getCurrentCollectionGroup().whereEqualTo(fieldName, fieldValue);
        return getResultFromQuery(query);
    }

    public Collection<ReltioContract> getContractsByStartDate(Long startDateBefore, Long startDateAfter) {
        Query query = getCurrentCollectionGroup();

        if (startDateAfter != null) {
            query = query.whereGreaterThanOrEqualTo("startDate", startDateAfter);
        }
        if (startDateBefore != null) {
            query = query.whereLessThanOrEqualTo("startDate", startDateBefore);
        }

        return getResultFromQuery(query);
    }

    public Collection<ReltioContract> getContractsByEndDate(Long endDateBefore, Long endDateAfter) {
        Query query = getCurrentCollectionGroup();

        if (endDateAfter != null) {
            query = query.whereGreaterThanOrEqualTo(END_DATE_STR, endDateAfter);
        }
        if (endDateBefore != null) {
            query = query.whereLessThanOrEqualTo(END_DATE_STR, endDateBefore);
        }

        return getResultFromQuery(query);
    }

    public Collection<ReltioContract> getContractsByEndDateWithSizeAndOffset(Long endDateBefore, Long endDateAfter, Integer size, Integer offset) {
        Query query = getCurrentCollectionGroup();

        if (endDateAfter != null) {
            query = query.whereGreaterThanOrEqualTo(END_DATE_STR, endDateAfter).limit(size).offset(offset);
        }
        if (endDateBefore != null) {
            query = query.whereLessThanOrEqualTo(END_DATE_STR, endDateBefore).limit(size).offset(offset);
        }

        return getResultFromQuery(query);
    }

    public ReltioContract getContractByArrayField(String field, List<String> value) throws InvalidDocumentIdException {
        Query query = getCurrentCollectionGroup().whereArrayContainsAny(field, value);
        Collection<ReltioContract> reltioContracts = getResultFromQuery(query);
        if (reltioContracts.isEmpty()) {
            throw new InvalidDocumentIdException(field + "= " + value + " is Invalid");
        }
        return reltioContracts.iterator().next();
    }


    public Collection<ReltioContract> getContractsByFieldIdList(String fieldId, Set<String> idList) {
        try {
            if (CollectionUtils.isNotEmpty(idList)) {
                List<String> values = new ArrayList<>(idList);
                if (values.size() >= MAX_LIMIT) {
                    return getContractsByPartition(fieldId, values);
                } else {
                    return getResultFromQuery(getCurrentCollectionGroup().whereIn(fieldId, values));
                }
            }
        } catch (Exception exception) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.EXPECTATION_FAILED.value(), exception);
        }
        return Collections.emptyList();
    }

    private Collection<ReltioContract> getContractsByPartition(String fieldId, List<String> values) {
        List<List<String>> valueBatches = Lists.partition(values, MAX_LIMIT);
        List<CompletableFuture<List<QuerySnapshot>>> completableFutureList = valueBatches.parallelStream()
                .map(batch -> CompletableFuture.supplyAsync(() -> processBatch(fieldId, batch))).collect(Collectors.toList());
        return CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]))
                .thenApply(apply -> completableFutureList.stream()
                        .map(CompletableFuture::join)
                        .map(getResult())
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList()))
                .join();

    }

    @NotNull
    private Function<List<QuerySnapshot>, List<ReltioContract>> getResult() {
        return query -> query.stream().map(instance -> getResultFromQuery(instance.getQuery())).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<QuerySnapshot> processBatch(String fieldId, List<String> valueBatch) {
        Query query = getCurrentCollectionGroup().whereIn(fieldId, valueBatch).limit(MAX_LIMIT);
        List<QuerySnapshot> querySnapshots = new ArrayList<>();
        while (true) {
            QuerySnapshot snapshot = null;
            try {
                snapshot = query.get().get();
            } catch (ExecutionException executionException) {
                throw new RuntimeException(executionException.getCause());
            } catch (InterruptedException interruptedException) {
                LOGGER.error("Interrupted", interruptedException);
                Thread.currentThread().interrupt();
            }
            if (Objects.isNull(snapshot) || snapshot.isEmpty()) {
                break;
            }
            querySnapshots.add(snapshot);
            DocumentSnapshot lastVisible = snapshot.getDocuments().get(snapshot.size() - 1);
            query = getCurrentCollectionGroup().whereIn(fieldId, valueBatch).startAfter(lastVisible).limit(MAX_LIMIT);
        }
        return querySnapshots;
    }
}
