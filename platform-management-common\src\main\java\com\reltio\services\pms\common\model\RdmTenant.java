package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.TenantStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class RdmTenant extends BaseFirestoreEntity {


    @JsonProperty("customerId")
    private String customerId;

    @JsonProperty("customerName")
    private String customerName;

    @JsonProperty("rdmTenantId")
    private String rdmTenantId;

    @JsonProperty("associatedMdmTenants")
    private List<String> associatedMdmTenants;

    @JsonProperty("productEdition")
    private String productEdition;

    @JsonProperty("contractId")
    private String contractId;

    @JsonProperty("subscriptionId")
    private String subscriptionId;

    @JsonProperty("tenantStatus")
    private TenantStatus tenantStatus;

    @JsonProperty("serviceUrl")
    private String serviceUrl;

    @JsonProperty("customerType")
    private CustomerType customerType;

    @JsonProperty("division")
    private String division;

    @JsonProperty("department")
    private String department;

    @JsonProperty("costCenter")
    private String costCenter;

    @JsonProperty("endDate")
    private Date endDate;

    @JsonProperty("tenantPurpose")
    private TenantPurpose tenantPurpose;

    @Override
    public String getID() {
        return rdmTenantId;
    }
}
