package com.reltio.services.pms.common.model.supportability.request;

import com.reltio.services.pms.common.model.supportability.Facet;

import java.util.Objects;

/**
 * RestParams
 * Created by apylkov
 */
public class RestParams extends Params {
    private final String operationType;

    private final String clusterType;

    public RestParams(Long startTime, Long endTime, String tenantId, String operationType, String cluserType, Facet facet) {
        super(startTime, endTime, tenantId, facet);
        this.operationType = operationType;
        this.clusterType = cluserType;
    }

    public String getOperationType() {
        return operationType;
    }

    public String getClusterType() {
        return clusterType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RestParams restParams = (RestParams) o;
        return Objects.equals(getTenantId(), restParams.getTenantId()) &&
                Objects.equals(getStartTime(), restParams.getStartTime()) &&
                Objects.equals(getEndTime(), restParams.getEndTime()) &&
                Objects.equals(getOperationType(), restParams.getOperationType()) &&
                Objects.equals(getClusterType(), restParams.getClusterType()) && Objects.equals(getFacet(), restParams.getFacet());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getTenantId(), getStartTime(), getEndTime(), getOperationType(), getClusterType(), getFacet());
    }
}
