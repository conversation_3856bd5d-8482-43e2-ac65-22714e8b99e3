package com.reltio.services.pms.controller;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.model.KmsKeyDetails;
import com.reltio.services.pms.service.KmsKeyDetailsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
@RestController
@RequestMapping(value = "/api/v1/tenants/{tenantId}/encryption/kms", produces = MediaType.APPLICATION_JSON_VALUE)
public class KmsKeyController {
    private final KmsKeyDetailsService kmsKeyDetailsService;
    @Autowired
    public KmsKeyController(KmsKeyDetailsService kmsKeyDetailsService) {
        this.kmsKeyDetailsService = kmsKeyDetailsService;
    }
    @PostMapping(value = "/details", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    public KmsKeyDetails saveKmsKeyDetails(@PathVariable String tenantId,
                                           @RequestBody KmsKeyDetails kmsKeyDetails) {
        kmsKeyDetails.setTenantId(tenantId);
        return kmsKeyDetailsService.saveKmsKeyDetails(kmsKeyDetails);
    }
    @GetMapping(value = "/details")
    public KmsKeyDetails getKmsKeyDetails(@PathVariable String tenantId) throws InvalidDocumentIdException {
        return kmsKeyDetailsService.getKmsKeyDetailsByTenantId(tenantId);
    }
    @DeleteMapping(value = "/details")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteKmsKeyDetails(@PathVariable String tenantId) {
        kmsKeyDetailsService.deleteKmsKeyDetails(tenantId);
    }
}