package com.reltio.services.pms.convertors.sales.addon;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.common.sales.model.addon.FernProductConfig;
import com.reltio.services.pms.convertors.sales.AbstractTenantAddOnProductProvider;
import com.reltio.services.pms.service.SalesPackageService;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

@Service
public class FERNProductProvider extends AbstractTenantAddOnProductProvider<FernProductConfig> {

    public FERNProductProvider(SalesPackageService salesPackageService) {
        super(salesPackageService);
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.FERN;
    }

    @Override
    public Map<String, Set<String>> getProductCodesByTenant() {
        return salesPackageService.getSalesAddOnsByProductCodes(PMSProductName.FERN);
    }

    @Override
    public BaseProductConfig getProductConfig(Set<SalesConfig> salesConfigs, Set<String> tenantCodes, String currentTenantCode) {
        if (salesConfigs.isEmpty()) {
            return null;
        }

        FernProductConfig fernProductConfig = new FernProductConfig();
        fernProductConfig.setPmsProductName(getProductName());
        fernProductConfig.addAllSalesConfigs(salesConfigs);
        fernProductConfig.setQuantity(getQuantity(fernProductConfig));
        return fernProductConfig;
    }
}
