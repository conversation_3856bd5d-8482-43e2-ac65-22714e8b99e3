package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DepartmentDetails {
    private String costCenter;
    private String division;
    private String department;

    // Default constructor
    public DepartmentDetails() {
    }
    // Parameterized constructor
    public DepartmentDetails(String costCenter, String division,String department) {
        this.costCenter = costCenter;
        this.division = division;
        this.department = department;
    }
}
