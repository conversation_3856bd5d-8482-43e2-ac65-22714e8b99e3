package com.reltio.services.pms.clients.external.zendesk;

import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.zendesk.client.v2.Zendesk;
import org.zendesk.client.v2.model.Status;
import org.zendesk.client.v2.model.Ticket;
import org.apache.log4j.Logger;
import java.util.Collection;
import java.util.HashSet;
import java.util.Arrays;
import java.util.Set;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class ZendeskClient {

    private static final Logger LOGGER = Logger.getLogger(ZendeskClient.class);

    private static final String VALIDATE_ZENDESK_URL_PATTERN = "^https://([a-zA-Z0-9-]+)\\.zendesk\\.com$";
    public static final String ZENDESK_URL = "https://reltio.zendesk.com";

    private final Zendesk zd;
    private final String baseUrl;

    @Autowired
    public ZendeskClient(@Value("${zendesk.url:#{null}}") String zendeskUrl, @Value("${zendesk.username}") String username, @Value("${zendesk.token}") String token) {
        if (zendeskUrl == null || zendeskUrl.isEmpty()) {
            this.baseUrl = ZENDESK_URL;
        } else {
            this.baseUrl = zendeskUrl;
        }

        if (!isValidZendeskUrl(this.baseUrl)) {
            LOGGER.warn(String.format("Can't validate zendesk url %s", this.baseUrl));
            throw new PlatformManagementException(PlatformManagementErrorCode.INCORRECT_ZENDESK_URL,
                    HttpStatus.BAD_REQUEST.value());
        }

        this.zd = new Zendesk.Builder(this.baseUrl)
                .setUsername(username)
                .setToken(token)
                .build();
        Set<String> properties = new HashSet<>();
        properties.add(token);
        PropertiesValidator.validateProperties(properties, LOGGER);
    }

    public String getZendeskUrl() {
        return this.baseUrl;
    }

    public Set<String> getZendeskAccess(String zendeskId) {
        Ticket ticket = zd.getTicket(Long.parseLong(zendeskId));
        validateTicketStatus(ticket);
        try {
            List<String> roles = ticket.getTags();
            return new HashSet<>(roles);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.ZENDESK_TICKET_NOT_FOUND, HttpStatus.BAD_REQUEST.value(), zendeskId);
        }
    }

    private void validateTicketStatus(Ticket ticket) {
        Collection<Status> validStatuses = Arrays.asList(Status.CLOSED, Status.SOLVED, Status.DELETED);

        Status status = ticket.getStatus();
        if (validStatuses.stream().anyMatch(validStatus -> validStatus == status)) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INVALID_ZENDESK_TICKET_STATUS, HttpStatus.BAD_REQUEST.value(), status.name());
        }
    }

    public Ticket getTicketAndValidate(String zendeskId) {
        Ticket ticket = zd.getTicket(Long.parseLong(zendeskId));
        validateTicketStatus(ticket);
        return ticket;
    }

    public static boolean isValidZendeskUrl(String url) {
        if (url == null) {
            return false;
        }
        Pattern pattern = Pattern.compile(VALIDATE_ZENDESK_URL_PATTERN);
        Matcher matcher = pattern.matcher(url);
        return matcher.matches();
    }
}
