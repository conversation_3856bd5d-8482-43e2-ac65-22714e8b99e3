package com.reltio.services.pms.clients.external.gcp.pubsub;

import com.google.api.gax.core.CredentialsProvider;
import com.google.api.gax.rpc.NotFoundException;
import com.google.auth.oauth2.IdTokenProvider;
import com.google.cloud.bigtable.admin.v2.BigtableTableAdminClient;
import com.google.cloud.bigtable.admin.v2.BigtableTableAdminSettings;
import com.google.cloud.bigtable.admin.v2.models.Backup;
import com.google.cloud.bigtable.admin.v2.models.CreateBackupRequest;
import com.google.cloud.bigtable.admin.v2.models.RestoreTableRequest;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalNotification;
import com.reltio.devops.common.environment.service.gbt.IBigtableClient;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.gcp.GcpBigTable;
import com.reltio.services.pms.config.gcp.ProjectsServiceAccountKeyCredentialsProvider;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import org.threeten.bp.Instant;
import org.threeten.bp.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class GBTClient implements IBigtableClient {

    private static final Logger LOG = Logger.getLogger(GBTClient.class);

    private static final Integer WARN_TABLE_COUNT = 950;

    public static final Integer MAX_TABLE_COUNT = 1000;

    private static final String CLUSTER_ID = "%s-c1";

    private static final String BACKUP_ID = "%s_%s";

    private static final Cache<String, BigtableTableAdminClient> connections = CacheBuilder.newBuilder()
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .removalListener((RemovalNotification<String, BigtableTableAdminClient> notification) -> {
                BigtableTableAdminClient client = notification.getValue();
                if (client != null) {
                    client.close();
                }
            })
            .build();

    private final CredentialsProvider projectsCredentialsProvider;

    @Autowired
    public GBTClient(ProjectsServiceAccountKeyCredentialsProvider projectsCredentialsProvider) throws IOException {
        this.projectsCredentialsProvider = projectsCredentialsProvider;
        Objects.requireNonNull(projectsCredentialsProvider.getCredentialsGBT());
    }

    public Integer checkTableCount(String projectId, String instanceId) {
        try {
            BigtableTableAdminClient adminClient = getConnectionAdmin(projectId, instanceId);
            List<String> tableIds = adminClient.listTables();
            Integer currentSize = tableIds.size();
            if (currentSize > WARN_TABLE_COUNT) {
                LOG.warn(String.format("Bigtable instance %s in project %s contains %s tables. The limit can be " +
                        "reached soon", instanceId, projectId, currentSize));
            }
            return currentSize;
        } catch (ExecutionException | IOException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    private BigtableTableAdminSettings buildTableAdminSetting(String projectId, String instanceId) {
        BigtableTableAdminSettings adminSettings;
        try {
            adminSettings = BigtableTableAdminSettings.newBuilder()
                    .setProjectId(projectId)
                    .setInstanceId(instanceId)
                    .setCredentialsProvider(projectsCredentialsProvider)
                    .build();
        } catch (IOException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
        return adminSettings;
    }

    public BigtableTableAdminClient getConnectionAdmin(String projectId, String instanceId) throws IOException, ExecutionException {
        String key = projectId + instanceId;
        return connections.get(key, () -> {
            BigtableTableAdminSettings tableAdminSettings = buildTableAdminSetting(projectId, instanceId);
            return BigtableTableAdminClient.create(tableAdminSettings);
        });
    }

    public void deleteGBT(GcpBigTable gcpTable) {
        try {
            BigtableTableAdminClient adminClient = getConnectionAdmin(gcpTable.getProjectId(), gcpTable.getInstanceId());
            adminClient.deleteTable(gcpTable.getTableName());
        } catch (NotFoundException e ) {
            LOG.warn(String.format("Attempt was made to delete non existent table %s", gcpTable.getTableName()));
        } catch (ExecutionException | IOException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CANT_DELETE_GBT_TABLE, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
        }
    }

    public IdTokenProvider getIdTokenProvider() {
        try {
            com.google.auth.Credentials historyCredentials = projectsCredentialsProvider.getCredentials();
            if (historyCredentials instanceof IdTokenProvider) {
                return (IdTokenProvider) historyCredentials;
            }
            return null;
        } catch (IOException e) {
            throw new RuntimeException("Cant get history credentials IdTokenProvider", e);
        }
    }

    @Override
    public int getNumberOfTables(String projectId, String instanceId) throws IOException {
        try {
            BigtableTableAdminClient adminClient = getConnectionAdmin(projectId, instanceId);
            return adminClient.listTables().size();
        } catch (ExecutionException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CANT_DELETE_GBT_TABLE, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
        }
    }

    public void checkGbtDeleted(GcpBigTable gcpTable) {
        LOG.info("Checking table deletion for: " + gcpTable.getTableName());
        boolean deleted = waitForTableDeletion(gcpTable);
        if (deleted){
            return;
        }
        LOG.warn("Table still exists after 30 minutes. Retrying deletion");
        deleteGBT(gcpTable);
        deleted = waitForTableDeletion(gcpTable);
        if (!deleted) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CANT_DELETE_GBT_TABLE, HttpStatus.INTERNAL_SERVER_ERROR.value(), gcpTable.getTableName());
        }
    }

    private boolean waitForTableDeletion(GcpBigTable gcpTable) {
        int maxAttempts = 45;
        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                BigtableTableAdminClient adminClient = getConnectionAdmin(gcpTable.getProjectId(), gcpTable.getInstanceId());
                adminClient.getTable(gcpTable.getTableName());
                TimeUnit.SECONDS.sleep(40);
            } catch (NotFoundException e) {
                LOG.info(String.format("Table %s is deleted successfully",gcpTable.getTableName()),e);
                return true;
            } catch (IOException | ExecutionException e) {
                throw new PlatformManagementException(PlatformManagementErrorCode.CANT_DELETE_GBT_TABLE, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new PlatformManagementException(PlatformManagementErrorCode.CANT_DELETE_GBT_TABLE, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
            }
        }
        return false;
    }


    public String createGbtBackup(String projectId, String instanceId, String tableName, String ticketId, int retentionDays) {
        try {
            BigtableTableAdminClient adminClient = getConnectionAdmin(projectId, instanceId);
            Instant expireTime = Instant.now().plus(retentionDays, ChronoUnit.DAYS);
            String clusterId = String.format(CLUSTER_ID, instanceId);
            String backupId = String.format(BACKUP_ID, tableName, ticketId);
            CreateBackupRequest backupRequest = CreateBackupRequest.of(clusterId, backupId)
                    .setSourceTableId(tableName).setBackupType(Backup.BackupType.STANDARD).setExpireTime(expireTime);
            Backup backup = adminClient.createBackup(backupRequest);
            return backup.getId();
        } catch (ExecutionException | IOException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.ERROR_WHILE_CREATING_GBT_BACKUP, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
        }
    }

    public void restoreGbtBackup(String projectId, String instanceId, String backupId, String restoredTableId) {
        try {
            String clusterId = String.format(CLUSTER_ID, instanceId);
            BigtableTableAdminClient adminClient = getConnectionAdmin(projectId, instanceId);
            RestoreTableRequest restoreTableRequest = RestoreTableRequest.of(instanceId, clusterId, backupId, projectId).setTableId(restoredTableId);
            adminClient.restoreTable(restoreTableRequest);
        } catch (ExecutionException | IOException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.ERROR_WHILE_RESTORING_HISTORY_FROM_GBT_BACKUP, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
        } catch (InterruptedException e) {
            LOG.error("Interrupted while restoring GBT backup", e);
            Thread.currentThread().interrupt();
        }
    }
}
