package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
public class User extends BaseFirestoreEntity {

    @JsonIgnore
    private static final String ROLE_KEYWORD = "roles";

    private static final String CUSTOMER_ADMIN_PREFIX = "ROLE_ADMIN_CUSTOMER";

    @JsonProperty("username")
    private String username;

    @JsonProperty("customer")
    private String customer;

    @JsonProperty("email")
    private String email;

    @JsonProperty("userPermissions")
    private ReltioUserPermission userPermissions;

    @JsonProperty("password")
    private String password;

    @JsonCreator
    public User(@JsonProperty("username") String username, @JsonProperty("customer") String customer, @JsonProperty("email") String email,
                @JsonProperty("userPermissions") ReltioUserPermission userPermissions) {
        this.username = username;
        this.customer = customer;
        this.email = email;
        this.userPermissions = userPermissions;
    }

    public void addUserPermissions(Set<String> roleNames, Set<String> tenants) {
        for (String roleName : roleNames) {
            addUserPermissions(roleName, tenants);
        }

    }

    public void addUserPermissions(String roleName, Set<String> tenants) {
        Map<String, Set<String>> currentPermissions = new HashMap<>();
        if (getUserPermissions() != null && getUserPermissions().getRoles() != null) {
            currentPermissions = getUserPermissions().getRoles();
        }
        if (roleName.contains(CUSTOMER_ADMIN_PREFIX)) {
            currentPermissions.put(String.format("%s_%s", CUSTOMER_ADMIN_PREFIX, this.getCustomer()), Collections.emptySet());
        } else if (currentPermissions.containsKey(roleName)) {
            Set<String> tens = new HashSet<>();
            tens.addAll(currentPermissions.get(roleName));
            tens.addAll(tenants);
            currentPermissions.put(roleName, tens);
        } else {
            currentPermissions.put(roleName, tenants);
        }
        setUserPermissions(new ReltioUserPermission(currentPermissions, null, null));
    }

    public void removeUserPermissions(Set<String> roleNames, Set<String> tenants) {
        for (String roleName : roleNames) {
            removeUserPermissions(roleName, tenants);
        }
    }

    public void removeUserPermissions(String roleName, Set<String> tenants) {
        if (getUserPermissions() == null || getUserPermissions().getRoles() == null) {
            return;
        }
        Map<String, Set<String>> currentPermissions = getUserPermissions().getRoles();
        if (currentPermissions.containsKey(roleName)) {
            for (String tenant : tenants) {
                currentPermissions.get(roleName).remove(tenant);
                if (currentPermissions.get(roleName).isEmpty()) {
                    currentPermissions.remove(roleName);
                }
            }
            setUserPermissions(new ReltioUserPermission(currentPermissions, null, null));
        }
    }

    @Override
    public String getID() {
        return email;
    }

}

