package com.reltio.services.pms.common.model.proxy.approve;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.jobs.tasks.approve.ApprovalStatus;
import com.reltio.services.pms.common.model.proxy.ActionStatus;
import com.reltio.services.pms.common.model.proxy.ExecutionStatus;
import com.reltio.services.pms.common.model.proxy.SecureAction;

public class ApprovalSecureAction extends SecureAction {

    private ApprovalStatus approvalStatus;

    public ApprovalSecureAction(@JsonProperty("secureKey") String secureKey,
                                @JsonProperty("environmentId") String environmentId,
                                @JsonProperty("jobId") String jobId,
                                @JsonProperty("taskId") String taskId,
                                @JsonProperty("status") ActionStatus status,
                                @JsonProperty("executionStatus") ExecutionStatus executionStatus,
                                @JsonProperty("approvalStatus") ApprovalStatus approvalStatus) {
        super(secureKey, environmentId, jobId, taskId, status, executionStatus);
        this.approvalStatus = approvalStatus;
    }

    public ApprovalStatus getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(ApprovalStatus approvalStatus) {
        this.approvalStatus = approvalStatus;
    }
}
