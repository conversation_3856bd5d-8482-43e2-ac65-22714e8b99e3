package com.reltio.services.pms.common.model.jobs.tasks.streaming.provision;

import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServicesEnablementBaseTaskInstanceParams;

import java.util.Set;

public interface StreamingTaskInstanceParams extends ServicesEnablementBaseTaskInstanceParams {
    boolean isConfigureDefaultQueue();

    String streamingCloud();

    Set<String> getQueueNames();

}
