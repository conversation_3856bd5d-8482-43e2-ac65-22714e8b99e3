package com.reltio.services.pms.clients.reltio.mdm;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.reltio.devops.common.environment.spannerdb.ISpannerDBService;
import com.reltio.devops.common.exception.EnvironmentException;
import com.reltio.services.pms.common.PMSRestTemplate;
import com.reltio.services.pms.service.Util;
import com.reltio.services.pms.service.environment.EnvironmentService;
import com.reltio.services.pms.service.jobs.tasks.provisioning.mdm.configuration.TenantServiceProvider;
import org.apache.log4j.Logger;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.util.Objects;

@Service
public class MDMCleanTenantClient {
    public static final String API_MAINTENANCE = "%s/reltio/tenants/%s/maintenance?enable=%s";
    public static final String METADATA_TENANT_NOT_FOUND = "METADATA_TENANT_NOT_FOUND";
    public static final String TENANT_NOT_FOUND = "TENANT_NOT_FOUND";
    public static final String INTERNAL_APPLICATION_ERROR = "Internal application error";
    public static final String GBQ_CONFIG_IS_NOT_DEFINED_FOR_TENANT = "A GBQ configuration is not defined for the tenant";
    private static final Logger LOG = Logger.getLogger(MDMCleanTenantClient.class);
    private static final String RELTIO_ENVIRONMENT_URL = "%s/reltio";
    public static final String RELTIO_API_URL = RELTIO_ENVIRONMENT_URL + "/api";
    public static final String API_CLEAN_MATCH_URL = RELTIO_API_URL + "/%s/clearMatchInternalQueues?delete=%s";
    public static final String API_CLEAN_CRUD_URL = RELTIO_API_URL + "/%s/clearCrudInternalQueues?delete=%s";
    public static final String API_CLEAN_DYNAMODB_URL = RELTIO_API_URL + "/%s/cleanDynamoDBStorage?method=%s";
    public static final String API_CLEAN_SPANNERDB_URL = RELTIO_API_URL + "/%s/cleanSpannerDBStorage?method=%s";
    public static final String API_CLEAN_COSMOSDB_URL = RELTIO_API_URL + "/%s/cleanCosmosDBStorage?method=%s";
    public static final String API_CLEAN_GRAPH_URL = RELTIO_API_URL + "/%s/clearGraph?delete=%s";
    public static final String API_CLEAN_STREAMING_URL = RELTIO_API_URL + "/%s/clearStreamingDestinations?delete=%s";
    public static final String API_TENANT_INDEX = RELTIO_API_URL + "/%s/deleteTenantIndex";
    public static final String API_CLEAN_LOOKUP_URL = RELTIO_API_URL + "/%s/lookups";
    public static final String API_GRAPH_CONFIG = RELTIO_ENVIRONMENT_URL + "/tenants/%s/graph";
    public static final String API_DELETE_GBQ_TABLE = RELTIO_ENVIRONMENT_URL + "/tools/%s/gbq/activity-log";
    private final PMSRestTemplate rest;
    private final EnvironmentService environmentService;
    private final com.reltio.devops.common.environment.service.EnvironmentService environmentServiceDevops;
    private final TenantServiceProvider tenantServiceProvider;


    @Autowired
    public MDMCleanTenantClient(PMSRestTemplate rest, EnvironmentService environmentService,
                                com.reltio.devops.common.environment.service.EnvironmentService environmentServiceDevops,
                                TenantServiceProvider tenantServiceProvider) {
        this.rest = rest;
        this.environmentService = environmentService;
        this.environmentServiceDevops = environmentServiceDevops;
        this.tenantServiceProvider = tenantServiceProvider;
    }

    public void clearMatchQueue(String environment, String tenantId, boolean force) throws EnvironmentException {
        String apiUrl = environmentService.getEnvironment(environment).getUrl();
        String url = String.format(API_CLEAN_MATCH_URL, apiUrl, tenantId, force);
        try {
            rest.postForObject(url, null, ObjectNode.class);
        } catch (Exception e) {
            LOG.error(String.format("Error while cleaning match queue for %s@%s, Error: %s", tenantId, environment,
                    e.getMessage()), e);
            throw new MdmClientException(String.format("Can't clear match queue due to %s",
                    e.getMessage()), e);
        }
    }

    public void clearCrudQueue(String environment, String tenantId, boolean force) throws EnvironmentException {
        String apiUrl = environmentService.getEnvironment(environment).getUrl();
        String url = String.format(API_CLEAN_CRUD_URL, apiUrl, tenantId, force);
        try {
            rest.postForObject(url, null, ObjectNode.class);
        } catch (Exception e) {
            LOG.error(String.format("Error while cleaning crud queue for %s@%s, Error: %s", tenantId, environment,
                    e.getMessage()), e);
            throw new MdmClientException(String.format("Can't clear crud queue due to %s",
                    e.getMessage()), e);
        }
    }

    public void cleanActivityLog(String tenantId, String serviceUrl) throws EnvironmentException {
        String url = String.format(API_DELETE_GBQ_TABLE, serviceUrl, tenantId);
        try {
            rest.delete(url);
        } catch (Exception e) {
            if (e.getMessage().contains(GBQ_CONFIG_IS_NOT_DEFINED_FOR_TENANT)) {
                LOG.warn(String.format("%s raised while cleaning activity log for tenant %s", e, tenantId));
                return;
            }
            LOG.error(String.format("Error while cleaning activity log for %s, Error: %s", tenantId, e.getMessage()), e);
            throw new MdmClientException(String.format("Can't clear activity log due to %s",
                    e.getMessage()), e);
        }
    }

    public void cleanTenantDynamoDBStorage(String environment, String tenantId, boolean delete, boolean cleanKeys) {
        String apiUrl = environmentService.getEnvironment(environment).getUrl();
        String url = String.format(API_CLEAN_DYNAMODB_URL, apiUrl, tenantId, Util.getCleanStorageMethod(delete, cleanKeys));
        try {
            rest.postForObject(url, null, ObjectNode.class);
        } catch (Exception e) {
            LOG.error(String.format("Error while cleaning dynamodb storage for %s@%s, Error: %s", tenantId, environment, e.getMessage()), e);
            throw new MdmClientException(String.format("Can't clear dynamodb due to %s",
                    e.getMessage()), e);
        }
    }

    public void cleanTenantSpannerDBStorage(String environment, String tenantId, boolean delete, boolean cleanKeys) {
        String apiUrl = environmentService.getEnvironment(environment).getUrl();
        String url = String.format(API_CLEAN_SPANNERDB_URL, apiUrl, tenantId, Util.getCleanStorageMethod(delete,
                cleanKeys));
        try {
            rest.postForObject(url, null, ObjectNode.class);
        } catch (Exception e) {
            LOG.error(String.format("Error while cleaning spanner storage for %s@%s, Error: %s", tenantId, environment,
                    e.getMessage()), e);
            throw new MdmClientException(String.format("Can't clear spannerdb due to %s",
                    e.getMessage()), e);
        }
    }

    public void deleteSpannerResources(String envName, String databaseId) {
        com.reltio.devops.common.environment.Environment env = environmentServiceDevops.getEnvironmentByName(envName);
        try {
            ISpannerDBService spannerService = tenantServiceProvider.getSpannerDBService(envName,true);
            spannerService.clearResources(env, databaseId);
        } catch (RuntimeException e) {
            LOG.error("Spanner resources for tenant are not deleted", e);
            throw new MdmClientException("Spanner resources for tenant are not deleted: " + e.getMessage(), e);
        }
    }

    public void cleanTenantCosmosDBStorage(String environment, String tenantId,  boolean delete, boolean cleanKeys) {
        String apiUrl = environmentService.getEnvironment(environment).getUrl();
        String url = String.format(API_CLEAN_COSMOSDB_URL, apiUrl, tenantId, Util.getCleanStorageMethod(delete,
                cleanKeys));
        try {
            rest.postForObject(url, null, ObjectNode.class);
        } catch (Exception e) {
            LOG.error(String.format("Error while cleaning cosmos storage for %s@%s, Error: %s", tenantId, environment,
                    e.getMessage()), e);
            throw new MdmClientException(String.format("Can't clear cosmosdb due to %s",
                    e.getMessage()), e);
        }
    }

    public void putTenantToMaintenance(String environment, String tenantId, boolean isMaintenance) throws EnvironmentException {
        String apiUrl = environmentService.getEnvironment(environment).getUrl();
        String url = String.format(API_MAINTENANCE, apiUrl, tenantId, isMaintenance);
        try {
            rest.put(url, ObjectNode.class);
        } catch (Exception e) {
            throw new MdmClientException(String.format("Can't update maintenance mode due to %s",
                    e.getMessage()), e);
        }
    }

    public void clearStreamingDestinations(String environment, String tenantId, boolean force) throws EnvironmentException {
        String environmentUrl = environmentService.getEnvironment(environment).getUrl();
        String url = String.format(API_CLEAN_STREAMING_URL, environmentUrl, tenantId, force);
        try {
            rest.postForObject(url, null, ObjectNode.class);
        } catch (Exception e) {
            LOG.error(String.format("Error while clearing streaming destination for %s@%s, Error: %s", tenantId, environment, e.getMessage()), e);
            throw new MdmClientException(String.format("Can't clear streaming queues due to %s",
                    e.getMessage()), e);
        }
    }

    public void cleanTenantIndex(String environment, String tenantId) throws EnvironmentException {
        String apiEnvironment = environmentService.getEnvironment(environment).getUrl();
        String url = String.format(API_TENANT_INDEX, apiEnvironment, tenantId);
        try {
            rest.postForObject(url, null, ObjectNode.class);
        } catch (Exception e) {
            LOG.error(String.format("Error while cleaning tenant index for %s@%s, Error: %s", tenantId, environment, e.getMessage()), e);
            throw new MdmClientException(String.format("Can't clear tenant index due to %s",
                    e.getMessage()), e);
        }
    }


    public void enableMaintenanceModeForTenant(String environment, String tenantId) {
        putTenantToMaintenance(environment, tenantId, true);
    }

    public void disableMaintenanceModeForTenant(String environment, String tenantId) {
        putTenantToMaintenance(environment, tenantId, false);
    }

    public void cleanLookup(String environment, String tenantId) throws EnvironmentException {
        String apiEnvironment = environmentService.getEnvironment(environment).getUrl();
        String url = String.format(API_CLEAN_LOOKUP_URL, apiEnvironment, tenantId);
        try {
            JSONObject response = rest.getForObject(url, JSONObject.class);
            if (Objects.nonNull(response) && response.isEmpty()) {
                LOG.warn("No data to be cleaned in lookup stage");
                return;
            }
            rest.delete(url);
        } catch (Exception e) {
            if (isTenantNotFoundError(e)) {
                LOG.warn(String.format("Tenant %s is not defined in the system", tenantId));
            } else {
                throw new MdmClientException(String.format("Can't clean lookup due to %s",
                        e.getMessage()), e);
            }
        }
    }
    private boolean isTenantNotFoundError(Exception e) {
        if (!(e instanceof HttpClientErrorException.BadRequest ||
                e instanceof HttpClientErrorException.NotFound)) {
            return false;
        }

        String message = e.getMessage();
        if (message == null) {
            return false;
        }

        return message.contains(METADATA_TENANT_NOT_FOUND) ||
                message.contains(TENANT_NOT_FOUND) ||
                message.contains(INTERNAL_APPLICATION_ERROR);
    }


    public void cleanGraphSafe(String environment, String tenantId) {
        String apiEnvironment = environmentService.getEnvironment(environment).getUrl();
        String url = String.format(API_GRAPH_CONFIG, apiEnvironment, tenantId);
        JSONObject response = rest.getForObject(url, JSONObject.class);
        if (Objects.nonNull(response) && response.isEmpty()) {
            try {
                String cleanGraphUrl = String.format(API_CLEAN_GRAPH_URL, apiEnvironment, tenantId, false);
                rest.postForObject(cleanGraphUrl, null, JSONObject.class);
            } catch (Exception e) {
                LOG.warn(String.format("Cannot clear graph data for %s", tenantId));
            }
        }
    }
}
