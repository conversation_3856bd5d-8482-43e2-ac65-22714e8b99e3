package com.reltio.services.pms.common.sales;

import java.util.Locale;

public enum AccountType {

    CUSTOMER,
    PARTNER,
    PROSPECT,
    UNKNOWN;


    public static AccountType convertSalesforceString(String value) {
        if (value == null) {
            return null;
        }

        if (value.toLowerCase(Locale.US).contains("customer")) {
            return CUSTOMER;
        } else if (value.toLowerCase(Locale.US).contains("partner")) {
            return PARTNER;
        } else if (value.toLowerCase(Locale.US).contains("prospect")) {
            return PROSPECT;
        }

        return UNKNOWN;
    }
}
