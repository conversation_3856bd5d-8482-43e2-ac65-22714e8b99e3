package com.reltio.services.pms.clients.external.gcp.storage;

import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.storage.*;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Service
public class GcpStorageImpl implements FileStorage {

    private final Storage gStorage;

    public GcpStorageImpl(CredentialsProvider provider) throws IOException {
        gStorage = StorageOptions.newBuilder().setCredentials(provider.getCredentials()).build().getService();

    }

    @Override
    public void saveFile(FilePointer filePointer, Map<String, String> metaData, String content) {
        BlobId blobId = BlobId.of(filePointer.getBucket(), filePointer.getPath());
        BlobInfo blobInfo = BlobInfo.newBuilder(blobId)
                .setContentType("text/plain")
                .setMetadata(metaData)
                .build();

        gStorage.create(blobInfo, content.getBytes(StandardCharsets.UTF_8));


    }

    @Override
    public String getContent(FilePointer filePointer) {
        BlobId blobId = BlobId.of(filePointer.getBucket(), filePointer.getPath());
        Blob blob = getBlob(blobId);
        byte[] content = blob.getContent();
        return new String(content, StandardCharsets.UTF_8);

    }

    @Override
    public Map<String, String> getMetaData(FilePointer filePointer) {
        BlobId blobId = BlobId.of(filePointer.getBucket(), filePointer.getPath());
        Blob blob = getBlob(blobId);
        return blob.getMetadata();
    }

    private Blob getBlob(BlobId blobId) {
        Blob blob = gStorage.get(blobId);
        if (blob == null) {
            throw new FileNotFound("File not found with given pointer");
        }
        return blob;
    }

    @Override
    public void deleteFile(FilePointer filePointer) {
        BlobId blobId = BlobId.of(filePointer.getBucket(), filePointer.getPath());
        gStorage.delete(blobId);
    }
}
