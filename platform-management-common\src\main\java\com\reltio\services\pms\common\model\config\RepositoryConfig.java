package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;


/**
 * The type Repository config.
 */
@Getter
@Setter
public class RepositoryConfig extends PMSConfig {

    @JsonProperty("repoDetails")
    private Set<RepositoryDetails> repoDetails;


    /**
     * Instantiates a new Repository config.
     *
     * @param configName  the config name
     * @param repoDetails the repo details
     */
    @JsonCreator
    public RepositoryConfig(@JsonProperty("configName") String configName,
                            @JsonProperty("repoDetails") Set<RepositoryDetails> repoDetails) {
        super(configName);
        this.repoDetails = repoDetails;
    }


}
