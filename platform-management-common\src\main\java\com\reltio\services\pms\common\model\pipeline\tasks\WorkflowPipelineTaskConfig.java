package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;

public class WorkflowPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonCreator
    public WorkflowPipelineTaskConfig(@JsonProperty(value = "name") String name){
        super(name, TaskType.WORKFLOW_TASK);

    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.WORKFLOW;
    }

    @Override
    public boolean visibleInContract() {
        return true;
    }
}
