package com.reltio.services.pms.clients.external.aws;

import com.reltio.sdk.credentials.AWSRoleFeatureSupportUtil;
import com.reltio.services.pms.clients.external.aws.credentials.PlatformManagementAwsCredentials;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.auth.credentials.WebIdentityTokenFileCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.auth.StsAssumeRoleCredentialsProvider;

import java.util.UUID;

@Slf4j
public class AwsCredentialsFactory {

    public static AwsCredentialsProvider createAwsCredentialsProvider(PlatformManagementAwsCredentials credentials,
            boolean isIrsaEnabled) {

        log.info("IRSA Enabled: {}", isIrsaEnabled);
        if (isIrsaEnabled && StringUtils.isNotEmpty(credentials.awsRoleArn())) {
            log.info("IRSA is enabled. Will create WebIdentity provider and assume role: {}", credentials.awsRoleArn());

            AwsCredentialsProvider irsaProvider = WebIdentityTokenFileCredentialsProvider.create();

            Region region = Region.of(credentials.getAWSRegion());
            StsClient stsClient = StsClient.builder()
                    .credentialsProvider(irsaProvider)
                    .region(region)
                    .build();

            return StsAssumeRoleCredentialsProvider.builder()
                    .stsClient(stsClient)
                    .refreshRequest(r -> r
                            .roleArn(credentials.awsRoleArn())
                            .roleSessionName("Session-" + UUID.randomUUID()))
                    .build();

        } else {
            log.info("IRSA disabled or not set. Using static credentials from PlatformManagementAwsCredentials.");
            return StaticCredentialsProvider.create(credentials);
        }
    }

    public static AwsCredentialsProvider createAwsCredentialsProvider(PlatformManagementAwsCredentials credentials) {
        boolean isIrsaEnabled = AWSRoleFeatureSupportUtil.isFeatureEnabled();
        return createAwsCredentialsProvider(credentials, isIrsaEnabled);
    }

}
