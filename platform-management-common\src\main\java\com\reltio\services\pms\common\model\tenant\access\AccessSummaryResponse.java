package com.reltio.services.pms.common.model.tenant.access;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccessSummaryResponse {

    @JsonProperty("individualRequests")
    private List<AccessRequest> individualRequests;

    @JsonProperty("overallStatus")
    private AccessStatus overallStatus;

    @JsonProperty("summaryMessage")
    private String summaryMessage;
}
