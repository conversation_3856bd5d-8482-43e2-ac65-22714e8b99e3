package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.ContractStatus;
import com.reltio.services.pms.common.sales.ProvisioningStatus;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContractSummary {

    @JsonProperty("contractId")
    private String contractId;
    @JsonProperty("status")
    private ContractStatus status;

    @JsonProperty("provisioningStatus")
    private ProvisioningStatus provisioningStatus;

    @JsonProperty("opportunityName")
    private String opportunityName;

    public ContractSummary() {
    }

    public ContractSummary(String contractId, ContractStatus status, ProvisioningStatus provisioningStatus, String opportunityName) {
        this.contractId = contractId;
        this.status = status;
        this.provisioningStatus = provisioningStatus;
        this.opportunityName = opportunityName;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public ContractStatus getStatus() {
        return status;
    }

    public void setStatus(ContractStatus status) {
        this.status = status;
    }

    public ProvisioningStatus getProvisioningStatus() {
        return provisioningStatus;
    }

    public void setProvisioningStatus(ProvisioningStatus provisioningStatus) {
        this.provisioningStatus = provisioningStatus;
    }

    public String getOpportunityName() {
        return opportunityName;
    }

    public void setOpportunityName(String opportunityName) {
        this.opportunityName = opportunityName;
    }
}
