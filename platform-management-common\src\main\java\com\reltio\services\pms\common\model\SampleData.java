package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.List;
import java.util.Objects;

public class SampleData {
    @JsonProperty("entitiesData")
    private final List<JsonNode> entitiesData;

    @JsonProperty("relationsData")
    private final List<JsonNode> relationsData;

    @JsonProperty("interactiosnData")
    private final List<JsonNode> interactiosnData;

    @JsonCreator
    public SampleData(@JsonProperty("entitiesData") List<JsonNode> entitiesData,
                      @JsonProperty("relationsData") List<JsonNode> relationsData,
                      @JsonProperty("interactiosnData") List<JsonNode> interactiosnData) {
        this.entitiesData = entitiesData;
        this.relationsData = relationsData;
        this.interactiosnData = interactiosnData;
    }

    public List<JsonNode> getEntitiesData() {
        return entitiesData;
    }

    public List<JsonNode> getRelationsData() {
        return relationsData;
    }

    public List<JsonNode> getInteractiosnData() {
        return interactiosnData;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SampleData)) {
            return false;
        }
        SampleData that = (SampleData) o;
        return Objects.equals(getEntitiesData(), that.getEntitiesData()) &&
                Objects.equals(getRelationsData(), that.getRelationsData()) &&
                Objects.equals(getInteractiosnData(), that.getInteractiosnData());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getEntitiesData(), getRelationsData(), getInteractiosnData());
    }

    @Override
    public String toString() {
        return "SampleData{" +
                "entitiesData=" + entitiesData +
                ", relationsData=" + relationsData +
                ", interactiosnData=" + interactiosnData +
                '}';
    }
}

