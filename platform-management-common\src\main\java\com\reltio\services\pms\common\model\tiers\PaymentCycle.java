package com.reltio.services.pms.common.model.tiers;

import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.util.StdConverter;

import java.util.Arrays;

@JsonDeserialize(converter = PaymentCycle.TypeConverter.class)
public enum PaymentCycle {
    MONTHLY("month"),
    YEARLY("year");

    private final String value;

    PaymentCycle(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return value;
    }

    public static PaymentCycle convertFromString(String value) {
        return Arrays.stream(PaymentCycle.values())
                .filter(e -> e.value.equals(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid value '" + value + "'"));

    }

    public static final class TypeConverter extends StdConverter<String, PaymentCycle> {
        @Override
        public PaymentCycle convert(String value) {
            return PaymentCycle.convertFromString(value);

        }
    }
}