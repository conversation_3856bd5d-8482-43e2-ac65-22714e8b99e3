package com.reltio.services.pms.common.model.jobs.free.tier;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.jobs.Job;
import com.reltio.services.pms.common.model.jobs.JobStatus;
import com.reltio.services.pms.common.model.pipeline.PipelineType;
import com.reltio.services.pms.common.model.pipeline.SkipReason;
import com.reltio.services.pms.common.sales.PMSProductName;

import java.util.ArrayList;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.List;

public class FreeTierTenantProvisioningJob extends Job {

    private final PipelineType type = PipelineType.PROVISION_FREE_TIER_TENANT;

    public FreeTierTenantProvisioningJob(@JsonProperty(value = "parentJobId") String parentJobId,
                                         @JsonProperty(value = "jobId") String jobId,
                                         @JsonProperty(value = "pipelineId") String pipelineId,
                                         @JsonProperty(value = "envId") String envId,
                                         @JsonProperty(value = "startTime") Long startTime,
                                         @JsonProperty(value = "finishTime") Long finishTime,
                                         @JsonProperty(value = "jobStatus") JobStatus jobStatus,
                                         @JsonProperty(value = "tasks") ArrayList<String> tasks,
                                         @JsonProperty(value = "skippedTasks") EnumMap<SkipReason, EnumSet<PMSProductName>> skippedTasks,
                                         @JsonProperty(value = "owners")List<String> owners) {
        super(parentJobId,jobId, pipelineId, startTime, finishTime, jobStatus, tasks, envId, skippedTasks,owners,false);
    }

    public PipelineType getType() {
        return type;
    }
}
