package com.reltio.services.pms.common.model.supportability.result.combiner;

import com.reltio.services.pms.common.model.supportability.SpannerDBStatsDto;
import com.reltio.services.pms.common.model.supportability.result.StatsCombiner;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;

/**
 * The type Event stats combiner.
 */
public class SpannerDBStatsCombiner implements StatsCombiner<SpannerDBStatsDto> {
    /**
     * mergeAllRestRows method is used to merge all the provided rows from GBQ into single record by sum/avg based on the column in GBQ.
     *
     * @param dtoList the dto list
     * @return the list
     */
    @Override
    public SpannerDBStatsDto mergeAllRows(List<SpannerDBStatsDto> dtoList) {
        SpannerDBStatsDto response = dtoList.get(NumberUtils.INTEGER_ZERO);
        SpannerDBStatsDto lastResponse = dtoList.get(dtoList.size() - NumberUtils.INTEGER_ONE);
        long startTime = response.getStartTime();
        long endTime = lastResponse.getEndTime();
        int totalRows = dtoList.size();
        float cpu = NumberUtils.FLOAT_ZERO;
        int nodesCount = NumberUtils.INTEGER_ZERO;
        int sessionCount = NumberUtils.INTEGER_ZERO;
        float storageUtilization = NumberUtils.FLOAT_ZERO;
        float lockWaitTime = NumberUtils.FLOAT_ZERO;
        float cpuTime = NumberUtils.FLOAT_ZERO;
        long queryCount = NumberUtils.LONG_ZERO;
        long apiRequestCount = NumberUtils.LONG_ZERO;
        long failedExecutionCount = NumberUtils.LONG_ZERO;
        float queryLatencies = NumberUtils.FLOAT_ZERO;
        for (SpannerDBStatsDto dto : dtoList) {
            float currentQueryCount = dto.getQueryCount();
            cpu += dto.getCpu();
            nodesCount += dto.getNodesCount();
            sessionCount += dto.getSessionCount();
            storageUtilization += dto.getStorageUtilization();
            lockWaitTime = dto.getLockWaitTime()*currentQueryCount;
            cpuTime += dto.getCpuTime();
            queryCount += currentQueryCount;
            apiRequestCount += dto.getApiRequestCount();
            failedExecutionCount += dto.getFailedExecutionCount();
            queryLatencies += dto.getQueryLatencies()*currentQueryCount;
        }
        cpu = roundOff2Digits(cpu / totalRows);
        lockWaitTime = roundOff2Digits(lockWaitTime / queryCount);
        cpuTime = roundOff2Digits(cpuTime / totalRows);
        queryLatencies = roundOff2Digits(queryLatencies / queryCount);

        return SpannerDBStatsDto.builder()
                .startTime(startTime)
                .endTime(endTime)
                .databaseId(response.getDatabaseId())
                .projectId(response.getProjectId())
                .cpu(cpu)
                .nodesCount(nodesCount)
                .sessionCount(sessionCount)
                .storageUtilization(storageUtilization)
                .lockWaitTime(lockWaitTime)
                .cpuTime(cpuTime)
                .queryCount(queryCount)
                .apiRequestCount(apiRequestCount)
                .failedExecutionCount(failedExecutionCount)
                .queryLatencies(queryLatencies).build();
    }
}
