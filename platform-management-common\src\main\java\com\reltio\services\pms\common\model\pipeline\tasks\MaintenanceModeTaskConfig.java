package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

public class MaintenanceModeTaskConfig extends AbstractPipelineTaskConfig {

    @JsonProperty(value = "valueToSet")
    private Boolean valueToSet;

    @JsonProperty(value = "secondsDelay")
    private int secondsDelay;

    @JsonCreator
    public MaintenanceModeTaskConfig(
            @JsonProperty(value = "name") String name,
            @JsonProperty(value = "valueToSet", required = true) Boolean valueToSet,
            @JsonProperty(value = "secondsDelay", required = true) int secondsDelay) {
        super(name, TaskType.MAINTENANCE_MODE_TASK);
        this.valueToSet = valueToSet;
        this.secondsDelay = secondsDelay;
    }

    public Boolean getValueToSet() {
        return valueToSet;
    }

    public int getSecondsDelay() {
        return secondsDelay;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        MaintenanceModeTaskConfig that = (MaintenanceModeTaskConfig) o;
        return secondsDelay == that.secondsDelay && Objects.equals(valueToSet, that.valueToSet);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), valueToSet, secondsDelay);
    }
}
