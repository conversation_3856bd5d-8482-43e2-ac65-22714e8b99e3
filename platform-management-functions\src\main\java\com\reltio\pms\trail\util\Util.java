package com.reltio.pms.trail.util;


import com.reltio.pms.trail.service.ReltioAPIService;
import com.reltio.pms.trail.service.TokenGeneratorService;
import com.reltio.pms.trail.service.impl.SimpleReltioAPIServiceImpl;
import com.reltio.pms.trail.service.impl.TokenGeneratorServiceImpl;

import java.io.FileReader;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.Future;
import java.util.logging.Logger;

public abstract class Util {
    private static final Logger logger = Logger.getLogger(Util.class.getName());

    public static boolean isEmpty(Collection<?> collection) {

        return collection == null || collection.isEmpty();

    }

    public static boolean isNotEmpty(Collection<?> collection) {

        return collection != null && !collection.isEmpty();
    }

    public static boolean isEmpty(String data) {

        return data == null || data.trim().length() == 0;

    }

    public static void close(AutoCloseable... resources) {

        if (resources != null) {

            for (AutoCloseable resource : resources) {

                try {

                    if (resource != null) {
                        resource.close();
                    }
                } catch (Exception e) {
                    logger.info("failed to close resource " + e.getMessage());
                }
            }
        }

    }

    public static Properties getProperties(String propertyFilePath) throws Exception {

        FileReader in = new FileReader(propertyFilePath);
        Properties config = new Properties();

        try {
            config.load(in);
        } catch (Exception e) {
            logger.info("failed to load property file" + propertyFilePath);
            logger.info(e.getMessage());
            throw e;
        } finally {
            close(in);
        }

        return config;
    }


    public static ReltioAPIService getReltioService(Properties config) throws Exception {

        TokenGeneratorService service = null;
        String authUrl = config.getProperty("AUTH_URL");
        String clinetId = config.getProperty("CLIENT_ID");
        String clientSecret = config.getProperty("CLIENT_SECRET");


            service = new TokenGeneratorServiceImpl(clinetId, clientSecret, authUrl);


        ReltioAPIService restApi = new SimpleReltioAPIServiceImpl(service);

        return restApi;
    }

    public static ReltioAPIService getReltioService(String username, String password, String authUrl) throws Exception {

        TokenGeneratorService service = new TokenGeneratorServiceImpl(username, password, authUrl);
        ReltioAPIService restApi = new SimpleReltioAPIServiceImpl(service);

        return restApi;
    }

    public static List<String> listMissingProperties(Properties properties, Collection<String> requiredProps) {

        List<String> missingKeys = new ArrayList<>();

        for (String key : requiredProps) {
            if (isEmpty(properties.getProperty(key))) {
                missingKeys.add(key);
            }
        }

        return missingKeys;
    }

    public static List<String> listMissingProperties(Properties properties, Collection<String> requiredProps, Map<List<String>, List<String>> mutualExclusiveProps) {

        List<String> missingKeys = new ArrayList<>();

        missingKeys.addAll(getMissingProperties(properties, requiredProps));

        /*
         * Mutual Exclusive properties missing check
         */
        if (mutualExclusiveProps != null && mutualExclusiveProps.size() > 0) {
            for (Map.Entry<List<String>, List<String>> entry : mutualExclusiveProps.entrySet()) {
                List<String> propKeys = getMissingProperties(properties, entry.getKey());
                List<String> propValues = getMissingProperties(properties, entry.getValue());


                if (!isEmpty(propKeys) && !isEmpty(propValues)) {
                    missingKeys.addAll(propKeys);
                }
            }
        }

        return missingKeys;
    }

    /**
     * Get missing properties
     *
     * @param properties
     * @param props
     * @return
     */
    private static List<String> getMissingProperties(Properties properties, Collection<String> props) {
        List<String> missingKeys = new ArrayList<>();

        if (!isEmpty(props)) {
            for (String key : props) {
                if (isEmpty(properties.getProperty(key))) {
                    missingKeys.add(key);
                }
            }
        }

        return missingKeys;
    }

    public static void waitForTasksReady(Collection<Future<Long>> futures, int maxNumberInList) throws Exception {
        while (futures.size() > maxNumberInList) {
            Thread.sleep(20);
            futures.removeIf(Future::isDone);
        }
    }


    /**
     * Set the http proxy as system parameters.
     *
     * @param properties
     */
    public static void setHttpProxy(Properties properties) {
        if (properties.getProperty("HTTP_PROXY_HOST") != null && properties.getProperty("HTTP_PROXY_PORT") != null) {
            System.setProperty("https.proxyHost", properties.getProperty("HTTP_PROXY_HOST"));
            System.setProperty("https.proxyPort", properties.getProperty("HTTP_PROXY_PORT"));
            System.setProperty("http.proxyHost", properties.getProperty("HTTP_PROXY_HOST"));
            System.setProperty("http.proxyPort", properties.getProperty("HTTP_PROXY_PORT"));
        }

    }


}