package com.reltio.services.pms.common.model.jobs.tasks.update.tenant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServiceEnablementBaseTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;


public class UpdateTenantTaskInstance extends ServiceEnablementBaseTaskInstance {


    @JsonProperty(value = "cleanseRegions")
    private final List<String> cleanseRegions;
    @JsonProperty(value = "tenantSize")
    private final TenantSize tenantSize;
    @JsonProperty(value = "loqateProcesses")
    private String loqateProcesses;


    @JsonCreator
    public UpdateTenantTaskInstance(@JsonProperty(value = "id") String id,
                                    @JsonProperty(value = "name") String name,
                                    @JsonProperty(value = "jobId") String jobId,
                                    @JsonProperty(value = "startTime") Long startTime,
                                    @JsonProperty(value = "finishTime") Long finishTime,
                                    @JsonProperty(value = "status") TaskStatus status,
                                    @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                    @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                    @JsonProperty(value = "executingNodeName") String executingNodeName,
                                    @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                    @JsonProperty(value = "envId") String envId,
                                    @JsonProperty(value = "failedTenants") Set<String> failedTenants,
                                    @JsonProperty(value = "totalProcessedTenants", defaultValue = "0") int totalProcessedTenants,
                                    @JsonProperty(value = "tenantsForServiceEnablement") Set<String> tenantsForServiceEnablement,
                                    @JsonProperty(value="events") Map<String, Set<String>> events,
                                    @JsonProperty(value = "loqateProcesses") String loqateProcesses,
                                    @JsonProperty(value = "cleanseRegions") List<String> cleanseRegions,
                                    @JsonProperty(value = "tenantSize") TenantSize tenantSize) {
        super(id, name, jobId, startTime, finishTime, TaskType.UPDATE_TENANT_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId, failedTenants, totalProcessedTenants,
                tenantsForServiceEnablement,events);
        this.loqateProcesses = loqateProcesses;
        this.cleanseRegions = cleanseRegions == null ? Collections.emptyList() : cleanseRegions;
        this.tenantSize = tenantSize;
    }


    public String getLoqateProcesses() {
        return loqateProcesses;
    }

    public List<String> getCleanseRegions() {
        return cleanseRegions;
    }

    public TenantSize getTenantSize() {
        return tenantSize;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof UpdateTenantTaskInstance)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        UpdateTenantTaskInstance that = (UpdateTenantTaskInstance) o;
        return getLoqateProcesses().equals(that.getLoqateProcesses()) && getCleanseRegions().equals(that.getCleanseRegions());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getLoqateProcesses(), getCleanseRegions(), getTenantSize());
    }

}
