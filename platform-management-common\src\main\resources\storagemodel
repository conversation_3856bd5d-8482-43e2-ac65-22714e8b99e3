{"type": "PLATFORM", "tenantId": "#tenantId#", "tenantName": "#tenantName#", "customerName": "#customerName#", "analyticsConfig": {"analyticsEnabled": false, "maxIncrementalWindow": 120, "maxIncrementalWindowOverlap": 48, "regularJobQuota": 100, "cassandraJobQuota": 20, "maxClusterSize": 20, "useParquetIDA": false, "consistencyFlyCheck": false, "consistencyCheckInterval": -1, "mergeOnTheFly": false, "inactive": false}, "dataStorageConfig": {"useMatchingMultiTenantFix": false, "isolated": true, "historyGBTMigration": false, "tableProperties": {"matchCF": {"dynamoDBProperties": {"initialReadCapacity": 2000, "initialWriteCapacity": 2000, "capacityMode": {"type": "AutoScaling", "minReadCapacity": 10, "maxReadCapacity": 2000, "readTargetUtilization": 50.0, "minWriteCapacity": 10, "maxWriteCapacity": 2000, "writeTargetUtilization": 50.0}}}, "matchDocumentsCF": {"dynamoDBProperties": {"initialReadCapacity": 2000, "initialWriteCapacity": 2000, "capacityMode": {"type": "AutoScaling", "minReadCapacity": 10, "maxReadCapacity": 2000, "readTargetUtilization": 50.0, "minWriteCapacity": 10, "maxWriteCapacity": 2000, "writeTargetUtilization": 50.0}}}, "entityMatchesCF": {"dynamoDBProperties": {"initialReadCapacity": 2000, "initialWriteCapacity": 2000, "capacityMode": {"type": "AutoScaling", "minReadCapacity": 10, "maxReadCapacity": 2000, "readTargetUtilization": 50.0, "minWriteCapacity": 10, "maxWriteCapacity": 2000, "writeTargetUtilization": 50.0}}}}, "entityMergeTreeStoringMode": "writeOld_readOld", "dataStorages": [{"id": "#matchStorageId#", "type": "DynamoDB", "dynamoDBConfig": {"awsRegion": "#dynamoRegion#", "awsIAMExternalRole": "#dynamoRole#", "defaultTableProperties": {"initialReadCapacity": 2000, "initialWriteCapacity": 2000, "capacityMode": {"type": "AutoScaling", "minReadCapacity": 1, "maxReadCapacity": 1000, "readTargetUtilization": 80.0, "minWriteCapacity": 1, "maxWriteCapacity": 1000, "writeTargetUtilization": 80.0}}, "daoVersion": "v2"}}], "dataKeyspaceConfig": {"keyspaceName": "#dataks#DHM", "host": "#dataHost#", "clusterName": "#dataCluster#", "replicationFactor": 3, "cassandraVersion": "CASSANDRA_1_1", "fetchBufferSize": 150, "maxInsertSizeInMb": 32}, "historyBigTableConfig": {"memory": false, "project": "#historyProject#", "instanceId": "#historyInstance#", "tableName": "#historyTableName#", "ttl": "P1M"}, "matchKeyspaceConfig": {"dataStorageId": "#matchStorageId#"}, "entitiesCF": "ENTITIES", "entityLinksCF": "ENTITY_LINKS", "entitiesCompressedCF": "ENTITIES_COMPRESSED_VALUES", "entityMergeTreeCF": "ENTITY_MERGE_TREE", "relationsCF": "RELATIONS", "relationsOneHopsCF": "RELATIONS_ONE_HOP_NEW", "relationsStartEndCF": "RELATIONS_START_END", "relationMergeTreeCF": "RELATION_MERGE_TREE", "relationsCompressedCF": "RELATIONS_COMPRESSED_VALUES", "entityGroupsCF": "ENTITY_GROUPS", "groupsCF": "GROUPS", "groupMembersCF": "GROUP_MEMBERS", "entityGraphsCF": "ENTITY_GRAPHS", "graphsCF": "GRAPHS", "graphMembersCF": "GRAPH_MEMBERS", "userPreferencesCF": "USER_PREFERENCES", "categoriesCF": "CATEGORIES", "categoriesLinksCF": "CATEGORY_LINKS", "activityRecordsCF": "ACTIVITY_RECORDS", "activitiesCF": "ACTIVITIES", "activitiesV2CF": "ACTIVITIES_V2", "businessProcessesCF": "BUSINESS_PROCESSES", "entityHistoryExCF": "ENTITY_HISTORY_EX", "entityHistoryCacheCF": "ENTITY_HISTORY_CACHE", "configurationHistoryCF": "CONFIGURATION_HISTORY", "attributesDeltaHistoryCF": "ATTRIBUTES_DELTA_HISTORY", "physicalConfigurationHistoryCF": "PHYSICAL_CONFIGURATION_HISTORY", "matchCF": "MATCH_TOKENS", "matchDocumentsCF": "MATCH_DOCUMENTS", "potentialMatchesCF": "POTENTIAL_DUPLICATES", "autoMatchesCF": "AUTO_MATCHES", "notMatchCF": "NOT_MATCH", "autoNotMatchesCF": "AUTO_NOT_MATCHES", "manuallyMarkedAsMatchCF": "AS_MATCH", "entityMatchesCF": "MATCHES", "entityToInteractionsCF": "ENTITY_TO_INTERACTIONS", "newInteractionsCF": "INTERACTIONS_NEW", "externalCrosswalkCF": "EXTERNAL_CROSSWALKS", "externalRelationCrosswalkCF": "EXTERNAL_RELATION_CROSSWALKS", "analyticsAttributesCF": "ANALYTICS_ATTRIBUTES", "changeRequestsCF": "CHANGE_REQUESTS", "changeRequestIndexCF": "CHANGE_REQUEST_INDEX", "historyDataStorageMode": "PRIMARY", "historyAttributeChangeStorage": "HISTORY"}, "searchStorageConfiguration": {"esClusterName": "#esCluster#", "esHosts": "#esHost#", "numberOfShards": 1, "numberOrReplicas": 1, "maxNumberOfResults": 200, "maxNumberOfIndexRequests": 10, "indexRelations": false, "indexOvStrategy": "#indexOvStrategy#", "indexActivityDelta": false, "indexMLRules": false, "indexDocumentSources": false, "indexOnlySearchableAttributes": false, "indexRDMLookups": false, "preserveCursor": false, "maxAttributeLength": 256, "backpressureEnabled": true, "enableBufferedCluster": false, "refreshIndexOnInserts": false, "esIndexName": "#dataks#-index", "esVersion": 6}, "streamingConfig": {"streamingEnabled": false, "streamingAPIEnabled": false, "analyzeOvChanges": false, "emptyStartEndRelationCrosswalks": false, "JMSEventsTimeToLive": 3600000, "JMSEventsFilteringFields": ["updatedTime", "updatedBy", "created<PERSON>y", "createdTime", "type", "uri"], "JMSIncludeMergeTime": false}, "matchingConfiguration": {"strategy": "#matchingStrategy#", "resolveLookupStrategy": "LOOKUP_CODE", "generateMatchTokensMapping": false, "generateTokensForExactOrAllNull": false, "generateSuspectByNegativeRules": true, "stripDiacritics": false, "hashTokens": true, "tokenCollisionsLimit": 300}, "objectSizeLimits": {"maxAttributeValues": 200, "maxAttributeValueSize": 10240, "maxCrosswalks": 200, "maxSubNestedAttributesPerEntity": 20000, "maxSubReferenceAttributesPerEntity": 20000, "maxReferenceCrosswalksPerEntity": 2000}, "defaultCrosswalk": "<PERSON><PERSON><PERSON>", "defaultTenant": "#tenantId#", "resolveLookupCode": false, "maxChangedObjects": -1, "maxInteractionsToFetchPerEntity": 100000, "maxFetchSizeForOneHopRow": 500000, "maxOneHopTotalFetchSize": 1000000, "objectsInTreeLimit": 1500, "traverseStepLimit": 500, "rdmConfig": {"serviceUri": "https://rdm.reltio.com/", "oauthInstance": "https://auth.reltio.com/oauth/token", "cache": {"enabled": true, "type": "TranscodeInMemory"}, "publishRDMErrors": false}, "ignoreMultipleContributorsError": false, "logRequestsBody": false, "maxTaskPartsCount": 8, "updateAttributesEntitiesBatchSize": 200, "useEntityLinksCF": true, "resolveEntityLinksUsingEntityLinksCF": false, "maxLookupsCacheSize": 300000, "permanentLookupsStore": false, "useActivityLogInTasks": false, "gdprComplianceRequired": false, "exportConfig": {"exportVersion": "v2", "fileCompression": "GZIP", "storageType": "S3", "analyticsClusterSizeFactor": 0.9, "smartExport": {"maxNumberOfObjectsForEsFilteringFactor": 0.5, "maxNumberOfObjectsForAlwaysEsFiltering": 200000, "maxNumberOfObjectsForEsFiltering": 8000000, "maxNumberOfObjectsPerTask": 1000000, "taskPartsCountForUnknownEstimatedTotal": 4, "maxNumberOfActivityItemsPerTask": 5000000, "relationsToEntitiesRatio": 0.5, "maxNumberOfRelationsForEsFiltering": 8000000, "maxNumberOfRelationsForAlwaysEsFiltering": 200000}, "writeManifest": true, "tasksLimit": 20, "appendExtension": true}, "updateAttributeUpdateDatesOnActualChanges": false, "enforceAsyncThrottling": true, "enforceSyncThrottling": true, "enforcePriorityThrottling": true, "maxHistoryBufferSize": 50000, "userPreferencesCacheEnabled": true, "physicalConfigValidationEnabled": true, "businessConfigValidationEnabled": true, "activityLogConfig": {"ttl": "PT744H", "returnObjectLabelsInScan": true, "convertNullsToEmptyLabelsInCassandra": false, "longTermStorage": {"enabled": false, "gbqLimitBytes": 1099511627776}}, "enableNestedPartialOverride": false, "enableTenantPerRoleSecurity": false, "historyCache": {"historyCacheEnabled": false, "historyCacheTaskPartCount": 1}}