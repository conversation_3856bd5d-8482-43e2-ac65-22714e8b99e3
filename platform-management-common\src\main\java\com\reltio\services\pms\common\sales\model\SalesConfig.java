package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class SalesConfig implements Serializable {

    @JsonProperty("subscriptionId")
    private String subscriptionId;
    @JsonProperty("productCode")
    private String productCode;
    @JsonProperty("productName")
    private String productName;
    @JsonProperty("subscriptionName")
    private String subscriptionName;
    @JsonProperty("startDate")
    private Date startDate;
    @JsonProperty("endDate")
    private Date endDate;
    @JsonProperty("quantity")
    private Long quantity;
    @JsonProperty("parentSubscriptionId")
    private String parentSubscriptionId;
    @JsonProperty("isActive")
    private boolean isActive;

    @JsonProperty("velocityPack")
    private String velocityPack;
    @JsonProperty("deploymentRegion")
    private String deploymentRegion;
    @JsonProperty("hipaaFinance")
    private boolean hipaaFinance;
    @JsonProperty("mdmCloudProvider")
    private String mdmCloudProvider;
    @JsonProperty("rdmCloudProvider")
    private String rdmCloudProvider;
    @JsonProperty("consolidatedProfiles")
    private Long consolidatedProfiles;
    @JsonProperty("packageLink")
    private String packageLink;
    @JsonProperty("revisedSub")
    private String revisedSub;

    @JsonProperty("salesTenantId")
    private String salesTenantId;

    private DeltaStatus deltaStatus;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map<String, Object> changeProperties;

    public SalesConfig() {
    }

    public SalesConfig(String subscriptionId, String productCode, String productName, String subscriptionName, Date startDate, Date endDate, Long quantity, String parentSubscriptionId, boolean isActive, String salesTenantId) {
        this.subscriptionId = subscriptionId;
        this.productCode = productCode;
        this.productName = productName;
        this.subscriptionName = subscriptionName;
        this.startDate = startDate;
        this.endDate = endDate;
        this.quantity = quantity;
        this.parentSubscriptionId = parentSubscriptionId;
        this.isActive = isActive;
        this.salesTenantId = salesTenantId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SalesConfig that = (SalesConfig) o;
        return Objects.equals(subscriptionId, that.subscriptionId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(subscriptionId);
    }
}
