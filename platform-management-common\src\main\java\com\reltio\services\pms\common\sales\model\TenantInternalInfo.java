package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Set;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TenantInternalInfo {


    //TODO: Remove these fields getting used in the GET tenant details in Phase 2
    @JsonProperty("mdmJobId")
    private String mdmJobId;

    //TODO: Remove these fields getting used in the GET tenant details in Phase 2
    @JsonProperty("allJobsForTenant")
    private Set<String> allJobsForTenant;


}
