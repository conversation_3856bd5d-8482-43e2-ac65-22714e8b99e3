package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * The type Repository details.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class RepositoryDetails {

    @JsonProperty("repoName")
    private String repoName;

    @JsonProperty("uri")
    private String uri;

    @JsonProperty("branchDetails")
    private Map<String, String> branchDetails;

    @JsonProperty("files")
    private List<String> files;

    @JsonProperty("folders")
    private List<String> folders;

}