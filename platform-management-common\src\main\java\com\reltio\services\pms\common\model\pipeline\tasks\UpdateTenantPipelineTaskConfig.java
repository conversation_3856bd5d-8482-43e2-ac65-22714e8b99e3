package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;


public class UpdateTenantPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonCreator
    public UpdateTenantPipelineTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.UPDATE_TENANT_TASK);
    }

    @Override
    public String toString() {
        return "UPDATE_TENANT_TASK{}";
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.TENANT_SIZE;
    }

    @Override
    public boolean visibleInContract() {
        return true;
    }
}
