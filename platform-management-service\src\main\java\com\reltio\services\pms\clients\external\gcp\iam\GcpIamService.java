package com.reltio.services.pms.clients.external.gcp.iam;

import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.googleapis.json.GoogleJsonResponseException;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.gax.core.FixedCredentialsProvider;
import com.google.api.gax.rpc.NotFoundException;
import com.google.api.services.iam.v1.Iam;
import com.google.api.services.iam.v1.model.CreateServiceAccountKeyRequest;
import com.google.api.services.iam.v1.model.CreateServiceAccountRequest;
import com.google.api.services.iam.v1.model.ServiceAccount;
import com.google.api.services.iam.v1.model.ServiceAccountKey;
import com.google.auth.Credentials;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.cloud.pubsub.v1.SubscriptionAdminClient;
import com.google.cloud.pubsub.v1.SubscriptionAdminSettings;
import com.google.cloud.pubsub.v1.TopicAdminClient;
import com.google.cloud.pubsub.v1.TopicAdminSettings;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.iam.v1.Binding;
import com.google.iam.v1.GetIamPolicyRequest;
import com.google.iam.v1.Policy;
import com.google.iam.v1.SetIamPolicyRequest;
import com.google.pubsub.v1.ProjectSubscriptionName;
import com.google.pubsub.v1.ProjectTopicName;
import com.google.pubsub.v1.TopicName;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.config.gcp.ServiceAccountKeyCredentialsProvider;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Service
public class GcpIamService implements IGcpIamService {

    private static final Logger LOGGER = Logger.getLogger(GcpIamService.class);

    private Iam iam;
    private TopicAdminSettings topicAdminSettings;
    private SubscriptionAdminSettings subscriptionAdminSettings;

    private static final Map<GCPResourceType, Set<String>> gcpResourceRoles = ImmutableMap.of(GCPResourceType.TOPIC, ImmutableSet.of("roles/pubsub.publisher", "roles/pubsub.viewer"),
            GCPResourceType.SUBSCRIPTION, ImmutableSet.of("roles/pubsub.subscriber"));

    @Autowired
    public GcpIamService(@Value("${projects.service.account.credentials}") String credentials) throws Exception {
        setUpAdminSettings(credentials);
        Set<String> properties=new HashSet<>();
        properties.add(credentials);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    protected GcpIamService() {}

    public final void setUpAdminSettings(String credentials) throws Exception {
        List<String> scope = Arrays.asList("https://www.googleapis.com/auth/cloud-platform");
        Credentials creds = new ServiceAccountKeyCredentialsProvider(credentials).getScopedCredentials(scope);
        topicAdminSettings =
                TopicAdminSettings.newBuilder()
                        .setCredentialsProvider(FixedCredentialsProvider.create(creds))
                        .build();
        subscriptionAdminSettings =
                SubscriptionAdminSettings.newBuilder()
                        .setCredentialsProvider(FixedCredentialsProvider.create(creds))
                        .build();
        iam = new Iam.Builder(
                GoogleNetHttpTransport.newTrustedTransport(),
                JacksonFactory.getDefaultInstance(),
                new HttpCredentialsAdapter(creds))
                .build();
    }

    @Override
    public ServiceAccount createServiceAccount(String projectId, String serviceAccountName) {
        try {
            ServiceAccount serviceAccount = new ServiceAccount();
            serviceAccount.setDisplayName(serviceAccountName);
            CreateServiceAccountRequest request = new CreateServiceAccountRequest();
            request.setAccountId(serviceAccountName);
            request.setServiceAccount(serviceAccount);
            LOGGER.error(String.format("Creating service account %s", serviceAccountName));
            serviceAccount =
                    iam.projects().serviceAccounts().create("projects/" + projectId, request).execute();
            return serviceAccount;
        } catch (IOException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.UNABLE_TO_CREATE_SERVICE_ACCOUNT, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
        }
    }

    public void deleteServiceAccount(String projectId, String serviceAccountName) {
        String name = String.format("projects/%s/serviceAccounts/%s@%s.iam.gserviceaccount.com", projectId, serviceAccountName, projectId);
        try {
            iam.projects().serviceAccounts().delete(name).execute();
        } catch (IOException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.UNABLE_TO_DELETE_SERVICE_ACCOUNT, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
        }
    }

    @Override
    public ServiceAccountKey createServiceAccountKey(String projectId, String serviceAccountName) {
        String serviceAccountEmail = serviceAccountName + "@" + projectId + ".iam.gserviceaccount.com";
        try {
            ServiceAccountKey key =
                    iam
                            .projects()
                            .serviceAccounts()
                            .keys()
                            .create(
                                    "projects/-/serviceAccounts/" + serviceAccountEmail,
                                    new CreateServiceAccountKeyRequest())
                            .execute();
            return key;
        } catch (IOException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.UNABLE_TO_CREATE_SERVICE_ACCOUNT_KEY, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
        }
    }

    @VisibleForTesting
    public void deleteServiceAccountKey(String projectId, String serviceAccountName) {
        String serviceAccountEmail = serviceAccountName + "@" + projectId + ".iam.gserviceaccount.com";
        try {
            List<ServiceAccountKey> keys =
                    iam
                            .projects()
                            .serviceAccounts()
                            .keys()
                            .list("projects/-/serviceAccounts/" + serviceAccountEmail)
                            .execute()
                            .getKeys();
            Optional<ServiceAccountKey> keyToDelete = keys.stream().filter(e -> e.getKeyType().equals("USER_MANAGED")).findFirst();

            // Then you can delete the key
            if(keyToDelete.isPresent()) {
                iam.projects().serviceAccounts().keys().delete(keyToDelete.get().getName()).execute();
            }
        } catch (IOException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.UNABLE_TO_MANIPULATE_WITH_SERVICE_ACCOUNT_KEY, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
        }
    }

    @Override
    public Boolean isAccountExist(String projectId, String serviceAccountName) {
        String name = String.format("projects/%s/serviceAccounts/%s@%s.iam.gserviceaccount.com", projectId, serviceAccountName, projectId);
        try {
            iam.projects().serviceAccounts().get(name).execute();
            return true;
        } catch (IOException e) {
            if (e instanceof GoogleJsonResponseException && ((GoogleJsonResponseException) e).getDetails().getCode() == 404) {
                return false;
            }
            throw new PlatformManagementException(PlatformManagementErrorCode.UNABLE_TO_GET_SERVICE_ACCOUNT, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
        }
    }

    @Override
    public void assignTopicPermission(String projectId, String resourceName, String email, boolean clean) throws IOException {
        TopicName topicName = ProjectTopicName.of(projectId, resourceName);
        SetIamPolicyRequest setIamPolicyRequest = null;
        if (!clean) {
            Policy oldPolicy = getTopicPolicy(projectId, resourceName);
            setIamPolicyRequest = preparePolicyRequest(oldPolicy, GCPResourceType.TOPIC, topicName.toString(), email);
        } else {
            setIamPolicyRequest = SetIamPolicyRequest.newBuilder()
                    .setResource(topicName.toString())
                    .setPolicy(Policy.newBuilder().build())
                    .build();
        }
        try (TopicAdminClient topicAdminClient = TopicAdminClient.create(topicAdminSettings)) {
            topicAdminClient.setIamPolicy(setIamPolicyRequest);
        }
    }

    @Override
    public void assignSubscriptionPermission(String projectId, String resourceName, String email, boolean clean) throws IOException {
        ProjectSubscriptionName subscriptionName = ProjectSubscriptionName.of(projectId, resourceName);
        SetIamPolicyRequest setIamPolicyRequest = null;
        if (!clean) {
            Policy oldPolicy = getSubscriptionPolicy(projectId, resourceName);
            setIamPolicyRequest = preparePolicyRequest(oldPolicy, GCPResourceType.SUBSCRIPTION, subscriptionName.toString(), email);
        } else {
            setIamPolicyRequest = SetIamPolicyRequest.newBuilder()
                    .setResource(subscriptionName.toString())
                    .setPolicy(Policy.newBuilder().build())
                    .build();
        }
        try (SubscriptionAdminClient subscriptionAdminClient = SubscriptionAdminClient.create(subscriptionAdminSettings)) {
            subscriptionAdminClient.setIamPolicy(setIamPolicyRequest);
        }
    }

    @Override
    public Policy getSubscriptionPolicy(String projectId, String resourceName) throws IOException {
        try (SubscriptionAdminClient subscriptionAdminClient = SubscriptionAdminClient.create(subscriptionAdminSettings)) {
            ProjectSubscriptionName subscriptionName =
                    ProjectSubscriptionName.of(projectId, resourceName);
            GetIamPolicyRequest getIamPolicyRequest =
                    GetIamPolicyRequest.newBuilder().setResource(subscriptionName.toString()).build();
            return subscriptionAdminClient.getIamPolicy(getIamPolicyRequest);
        }
    }

    @Override
    public Policy getTopicPolicy(String projectId, String resourceName) throws IOException {
        try (TopicAdminClient topicAdminClient = TopicAdminClient.create(topicAdminSettings)) {
            TopicName topicName = ProjectTopicName.of(projectId, resourceName);
            GetIamPolicyRequest getIamPolicyRequest =
                    GetIamPolicyRequest.newBuilder().setResource(topicName.toString()).build();
            return topicAdminClient.getIamPolicy(getIamPolicyRequest);
        }
    }

    private static SetIamPolicyRequest preparePolicyRequest(Policy oldPolicy, GCPResourceType type, String resourceName, String email) {
        String member = String.format("serviceAccount:%s", email);
        // Create new roles -> member binding
        Set<Binding> bindings = new HashSet<>();
        for (String role : gcpResourceRoles.get(type)) {
            bindings.add(Binding.newBuilder().setRole(role).addMembers(member).build());
        }
        // Add new binding to updated policy
        Policy updatedPolicy = Policy.newBuilder(oldPolicy).addAllBindings(bindings).build();

        return
                SetIamPolicyRequest.newBuilder()
                        .setResource(resourceName)
                        .setPolicy(updatedPolicy)
                        .build();
    }

    @Override
    public Map<GCPResourceType, Set<String>> getGCPResourceRoles() {
        return gcpResourceRoles;
    }

    public static String getClientEmail(String gcpProjectName, String serviceAccountName) {
        return String.format("%s@%s.iam.gserviceaccount.com", serviceAccountName, gcpProjectName);
    }

    public boolean doesSubscriptionExist(String projectId, String subscriptionId) {
        ProjectSubscriptionName subscriptionName = ProjectSubscriptionName.of(projectId, subscriptionId);
        try (SubscriptionAdminClient subscriptionAdminClient = SubscriptionAdminClient.create(subscriptionAdminSettings)) {
            subscriptionAdminClient.getSubscription(subscriptionName);
            return true;
        } catch (NotFoundException e) {
            LOGGER.warn("No subscription with ID " + subscriptionId + " and projectId " + projectId + " found.");
            return false;
        } catch (IOException ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
        }
    }
}
