package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.Getter;

@Getter
public class RetrieveAndStoreTenantConfigsPipeline extends AbstractPipelineTaskConfig{

    @JsonProperty("deleteAfterDays")
    private int deleteAfterDays;

    @JsonCreator
    public RetrieveAndStoreTenantConfigsPipeline(@JsonProperty(value = "name") String name , @JsonProperty(value = "deleteAfterDays") Integer deleteAfterDays) {
        super(name, TaskType.RETRIEVE_AND_STORE_TENANT_CONFIGS_TASK);
        this.deleteAfterDays = deleteAfterDays !=null ? deleteAfterDays:-1;
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.STORE_CONFIG;
    }
}
