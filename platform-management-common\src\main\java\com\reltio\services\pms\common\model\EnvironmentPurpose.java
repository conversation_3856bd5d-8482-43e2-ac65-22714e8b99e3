package com.reltio.services.pms.common.model;

import com.reltio.services.pms.common.sales.TenantPurpose;

public enum EnvironmentPurpose {
    DEV,
    TEST,
    PROD;

    public static EnvironmentPurpose convertFromTenantPurpose(TenantPurpose tenantPurpose) {
        switch (tenantPurpose) {
            case DEV:
                return EnvironmentPurpose.DEV;
            case TEST:
                return EnvironmentPurpose.TEST;
            case PROD:
                return EnvironmentPurpose.PROD;
            default:
                return null;
        }
    }
}
