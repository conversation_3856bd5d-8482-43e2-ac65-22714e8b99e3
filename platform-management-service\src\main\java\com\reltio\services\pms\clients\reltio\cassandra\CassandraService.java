package com.reltio.services.pms.clients.reltio.cassandra;

import org.apache.commons.configuration.Configuration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Service
public class CassandraService {

    private final int TIMEOUT_SECONDS;

    @Autowired
    public CassandraService(Configuration configuration) {
        this.TIMEOUT_SECONDS = configuration.getInt("cassandra.timeout.seconds", 60) * 1000;
    }

    public CassandraManager getCassandraManager(String hosts, Map<String, String> privatePublicIps) throws CassandraException {
        List<String> hostList = Arrays.stream(hosts.split(",")).map(String::trim).collect(Collectors.toList());
        return new CassandraManager(hostList, privatePublicIps, TIMEOUT_SECONDS);
    }

    public void truncateCFsUsingJavaDriver(String hosts, Map<String, String> privatePublicMap, String keyspace, Collection<String> cfNames) throws CassandraException {
        truncateOrDropCFsUsingJavaDriver(hosts, privatePublicMap, keyspace, cfNames, false);
    }

    public void dropCFsUsingJavaDriver(String hosts, Map<String, String> privatePublicMap, String keyspace, Collection<String> cfNames) throws CassandraException {
        truncateOrDropCFsUsingJavaDriver(hosts, privatePublicMap, keyspace, cfNames, true);
    }

    public void truncateOrDropCFsUsingJavaDriver(String hosts, Map<String, String> privatePublicMap, String keyspace, Collection<String> cfNames, boolean drop) throws CassandraException {
        try (CassandraManager cManager = getCassandraManager(hosts, privatePublicMap)) {
            if (drop) {
                cManager.dropCFs(keyspace, cfNames);
            } else {
                cManager.truncateCFs(keyspace, cfNames);
            }
        }
    }

    /**
     * Delete keyspace from Cassandra
     *
     * @param hosts
     * @param privatePublicMap
     * @param keyspace
     * @throws Exception
     */
    public void deleteKeyspaceUsingJavaDriver(String hosts, String keyspace, Map<String, String> privatePublicMap) throws CassandraException {
        try (CassandraManager cManager = getCassandraManager(hosts, privatePublicMap)) {
            cManager.dropKeySpace(keyspace);
        }
    }

    /**
     * Delete Tenant from System Keyspace using java driver.
     *
     * @param hosts
     * @param privatePublicMap
     * @param sysKeyspace
     * @param tenantId
     * @throws CassandraException
     */
    public void deleteTenantFromSysKeyspaceUsingJavaDriver(String hosts, Map<String, String> privatePublicMap, String sysKeyspace, String tenantId) throws CassandraException {
        try (CassandraManager cManager = getCassandraManager(hosts, privatePublicMap)) {
            cManager.deleteTenant(sysKeyspace, tenantId);
        }
    }
}
