package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class TenantsManagementLogs extends BaseFirestoreEntity {

    @JsonProperty(value = "docId")
    private String docId;

    @JsonProperty(value = "tenantId")
    private String tenantId;

    @JsonProperty(value = "envId")
    private String envId;

    @JsonProperty(value = "requesterEmail")
    private String requesterEmail;

    @JsonProperty(value = "updatedParams")
    private List<Map<String,Object>> updatedParams;

    @JsonProperty(value = "updatedParamsTimestamp")
    private Timestamp updatedParamsTimestamp;

    @JsonProperty(value = "updatedParamsDate")
    private String updatedParamsDate;

    public TenantsManagementLogs() {
    }

    @JsonCreator
    public TenantsManagementLogs(
                                 @JsonProperty(value = "requesterEmail")String requesterEmail,
                                 @JsonProperty(value = "updatedParams")List<Map<String,Object>> updatedParams,
                                 @JsonProperty(value = "updatedAt")Timestamp updatedParamsTimestamp){
        this.requesterEmail = requesterEmail;
        this.updatedParams = updatedParams;
        this.updatedParamsTimestamp = updatedParamsTimestamp;
    }

    @Override
    public String getID() {
        return docId;
    }
}