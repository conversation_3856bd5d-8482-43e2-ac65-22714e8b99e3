package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.sales.model.SpannerRestoreInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class SpannerRestoreDAO extends AbstractRootCollectionDao<SpannerRestoreInfo> {

    private static final String RESTORE_COLLECTION_NAME = "PMS_SPANNER_RESTORE";

    @Autowired
    public SpannerRestoreDAO(CredentialsProvider provider, @Value("${firestore.env.name}") String deployedEnv,ReltioUserHolder reltioUserHolder) {
        super(provider, RESTORE_COLLECTION_NAME, deployedEnv, reltioUserHolder);
    }

    @Override
    protected TypeReference<SpannerRestoreInfo> getTypeReference() {
        return new TypeReference<>() {
        };
    }
}
