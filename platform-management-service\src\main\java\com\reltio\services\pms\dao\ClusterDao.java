package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.core.ApiFuture;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.FieldValue;
import com.google.cloud.firestore.Query;
import com.google.cloud.firestore.QuerySnapshot;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.Cluster;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

@Service
public class ClusterDao extends AbstractRootCollectionDao<Cluster> {

    private static final String CLUSTERS_COLLECTION_NAME = "PMS_CLUSTERS";
    private static final Logger LOGGER = Logger.getLogger(ClusterDao.class);

    @Autowired
    public ClusterDao(CredentialsProvider provider, @Value("${firestore.env.name}") String deployedEnv, ReltioUserHolder reltioUserHolder) {
        super(provider, CLUSTERS_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    protected TypeReference<Cluster> getTypeReference() {
        return new TypeReference<Cluster>() {
        };
    }

    public Collection<Cluster> getClustersWithFilter(String property, String value) {
        Query clusterQuery = prepareQuery(property, value);
        return getResultFromQuery(clusterQuery);
    }

    public Set<String> getClusterIdsWithFilter(String property, String value) {
        Set<String> result = new HashSet<>();
        Query clusterQuery = prepareQuery(property, value);
        try {
            ApiFuture<QuerySnapshot> querySnapshot = clusterQuery.get();
            for (DocumentSnapshot documentSnapshot : querySnapshot.get().getDocuments()) {
                result.add(documentSnapshot.getId());
            }
            return result;
        }
        catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    private Query prepareQuery(String property, String value) {
        Query clusterQuery;
        switch (property) {
            case "name":
            case "type":
            case "service":
            case "cloud":
            case "region":
                clusterQuery = getBaseCollection().whereEqualTo(property, value);
                break;
            case "environments":
            case "tenants":
            case "url":
            case "customers":
                clusterQuery = getBaseCollection().whereArrayContains(property, value);
                break;
            default:
                throw new PlatformManagementException(PlatformManagementErrorCode.PROPERTY_IS_NOT_SEARCH_SUPPORTED, HttpStatus.INTERNAL_SERVER_ERROR.value(), property);
        }
        return clusterQuery;
    }

    public Cluster deleteEnvironment(String clusterId, String environment) {
        getBaseCollection().document(clusterId).update("environments",
                FieldValue.arrayRemove(environment));
        try {
            return get(getBaseCollection(), clusterId);
        } catch (InvalidDocumentIdException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CLUSTER_NOT_FOUND, HttpStatus.NOT_FOUND.value());
        }
    }

    @Override
    public Cluster update(Cluster c) {
        if (c.getType().equals("general")) {
            return override(c);
        }
        else {
            return super.update(c);
        }
    }
}
