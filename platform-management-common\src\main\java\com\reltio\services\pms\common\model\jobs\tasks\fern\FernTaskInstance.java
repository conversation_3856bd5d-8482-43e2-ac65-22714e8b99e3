package com.reltio.services.pms.common.model.jobs.tasks.fern;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServiceEnablementBaseTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
public class FernTaskInstance extends ServiceEnablementBaseTaskInstance {

    @JsonProperty(value = "tenantId")
    private String tenantId;

    @JsonCreator
    public FernTaskInstance(@JsonProperty(value = "id") String id,
                            @JsonProperty(value = "name") String name,
                            @JsonProperty(value = "jobId") String jobId,
                            @JsonProperty(value = "startTime") Long startTime,
                            @JsonProperty(value = "finishTime") Long finishTime,
                            @JsonProperty(value = "status") TaskStatus status,
                            @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                            @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                            @JsonProperty(value = "executingNodeName") String executingNodeName,
                            @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                            @JsonProperty(value = "envId") String envId,
                            @JsonProperty(value = "tenantId") String tenantId,
                            @JsonProperty(value = "failedTenants") Set<String> failedTenants,
                            @JsonProperty(value = "totalProcessedTenants", defaultValue = "0") int totalProcessedTenants,
                            @JsonProperty(value = "tenantsForServiceEnablement") Set<String> tenantsForServiceEnablement,
                            @JsonProperty(value = "events") Map<String, Set<String>> events) {
        super(id, name, jobId, startTime, finishTime, TaskType.FERN_TASK, status, lastUpdatedTime,
                taskFailureContext, executingNodeName, serviceNodeStatus, envId, failedTenants, totalProcessedTenants,
                tenantsForServiceEnablement, events);
        this.tenantId = tenantId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof FernTaskInstance)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        FernTaskInstance that = (FernTaskInstance) o;
        return getTenantId().equals(that.getTenantId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getTenantId());
    }

}
