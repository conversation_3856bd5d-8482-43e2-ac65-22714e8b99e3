package com.reltio.services.pms.common.model.jobs.tasks.clean.tenant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MetaDataCleanUp {
    @JsonProperty(value = "lookUpsClean")
    private boolean lookUpsClean;

    @JsonCreator
    public MetaDataCleanUp(@JsonProperty(value = "lookUpsClean") boolean lookUpsClean) {
        this.lookUpsClean = lookUpsClean;
    }

    public boolean isLookUpsClean() {
        return lookUpsClean;
    }

    public void setLookUpsClean(boolean lookUpsClean) {
        this.lookUpsClean = lookUpsClean;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if(o == null || this.getClass() != o.getClass()) return false;

        MetaDataCleanUp that = (MetaDataCleanUp) o;
        return isLookUpsClean() == that.isLookUpsClean();
    }

    @Override
    public int hashCode() {
        return Objects.hash(isLookUpsClean());
    }

    @Override
    public String toString() {
        return "MetaDataCleanUp{" +
                "lookUpsClean=" + lookUpsClean +
                '}';
    }
}
