package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.sales.model.SpannerBackupInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class SpannerBackupDAO extends AbstractRootCollectionDao<SpannerBackupInfo> {

    private static final String BACKUP_COLLECTION_NAME = "PMS_SPANNER_BACKUP";

    @Autowired
    public SpannerBackupDAO(CredentialsProvider provider, @Value("${firestore.env.name}") String deployedEnv,ReltioUserHolder reltioUserHolder) {
        super(provider, BACKUP_COLLECTION_NAME, deployedEnv, reltioUserHolder);
    }

    @Override
    protected TypeReference<SpannerBackupInfo> getTypeReference() {
        return new TypeReference<>() {
        };
    }
}
