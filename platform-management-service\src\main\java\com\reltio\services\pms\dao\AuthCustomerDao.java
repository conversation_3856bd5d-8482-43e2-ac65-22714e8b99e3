package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.Query;
import com.google.cloud.firestore.QuerySnapshot;
import com.google.common.collect.Lists;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.auth.AuthCustomer;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.NoSuchElementException;
import java.util.Set;
import java.util.List;
import java.util.ArrayList;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class AuthCustomerDao extends AbstractRootCollectionDao<AuthCustomer> {
    private static final String CUSTOMERS_COLLECTION_NAME = "PMS_AUTH_CUSTOMERS";
    /**
     * The constant MAX_LIMIT.
     */
    private static final int MAX_LIMIT = 30;
    private static final Logger LOGGER = Logger.getLogger(AuthCustomerDao.class);

    @Autowired
    public AuthCustomerDao(CredentialsProvider provider,
                           @Value("${firestore.env.name}") String deployedEnv,
                           ReltioUserHolder reltioUserHolder) {
        super(provider, CUSTOMERS_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    protected TypeReference<AuthCustomer> getTypeReference() {
        return new TypeReference<AuthCustomer>() {
        };
    }

    public AuthCustomer getCustomerForTenant(String tenantId) {
        Query query = getBaseCollection().whereArrayContains("tenants", tenantId);
        Collection<AuthCustomer> authCustomers = getResultFromQuery(query);
        Set<AuthCustomer> authCustomerSet = new HashSet<>(authCustomers);
        if (authCustomerSet.isEmpty()) {
            throw new PlatformManagementException(PlatformManagementErrorCode.THE_TENANT_HAS_NO_CUSTOMER_ASSIGNED, HttpStatus.INTERNAL_SERVER_ERROR.value(), tenantId);
        } else if (authCustomerSet.size() > 1) {
            StringBuilder stringBuilder = new StringBuilder();
            for (AuthCustomer authCustomer : authCustomerSet) {
                stringBuilder.append(authCustomer.getID()).append(" ");
            }
            throw new PlatformManagementException(PlatformManagementErrorCode.THE_TENANT_BELONGS_TO_SEVERAL_CUSTOMERS, HttpStatus.INTERNAL_SERVER_ERROR.value(), tenantId, stringBuilder.toString());
        } else {
            return authCustomerSet.iterator().next();
        }
    }

    public Collection<AuthCustomer> getAuthCustomerBySalesAccountId(String salesAccountId) {
        try {
            Query query = getBaseCollection().whereEqualTo("salesAccountId", salesAccountId);
            return getResultFromQuery(query);
        } catch (NoSuchElementException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.BUSINESS_CUSTOMER_NOT_FOUND, HttpStatus.NOT_FOUND.value(), salesAccountId);
        }
    }


    public Set<String> getClientIdsForTenant(String tenantId) {
        Set<String> clientIds = getCustomerForTenant(tenantId).getClients();
        if (clientIds.isEmpty()) {
            throw new PlatformManagementException(PlatformManagementErrorCode.NO_CLIENT_EXISTS_FOR_THE_CUSTOMER, HttpStatus.INTERNAL_SERVER_ERROR.value(), getCustomerForTenant(tenantId).getID());
        }
        return clientIds;
    }

     public Collection<AuthCustomer> getAuthCustomers(Integer size, Integer offset) {
        Query query = getBaseCollection().limit(size).offset(offset);
        return getResultFromQuery(query);
    }

    public Collection<AuthCustomer> getAuthCustomerByFieldIdList(String fieldId, Set<String> idList) {
        try {
            if (!idList.isEmpty()) {
                List<String> values = new ArrayList<>(idList);
                if (values.size() >= MAX_LIMIT) {
                    return getAuthCustomerByPartition(fieldId, values);
                } else {
                    return getResultFromQuery(getCurrentCollectionGroup().whereIn(fieldId, values));
                }
            }
        } catch (Exception exception) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.EXPECTATION_FAILED.value(), exception);
        }
        return Collections.emptyList();
    }

    private Collection<AuthCustomer> getAuthCustomerByPartition(String fieldId, List<String> values) {
        List<List<String>> valueBatches = Lists.partition(values, MAX_LIMIT);
        List<CompletableFuture<List<QuerySnapshot>>> completableFutureList = valueBatches.parallelStream()
                .map(batch -> CompletableFuture.supplyAsync(() -> processBatch(fieldId, batch))).collect(Collectors.toList());
        return CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]))
                .thenApply(apply -> completableFutureList.stream()
                        .map(CompletableFuture::join)
                        .map(getResult())
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList()))
                .join();

    }

    @NotNull
    private Function<List<QuerySnapshot>, List<AuthCustomer>> getResult() {
        return query -> query.stream().map(instance -> getResultFromQuery(instance.getQuery())).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<QuerySnapshot> processBatch(String fieldId, List<String> valueBatch) {
        Query query = getCurrentCollectionGroup().whereIn(fieldId, valueBatch).limit(MAX_LIMIT);
        List<QuerySnapshot> querySnapshots = new ArrayList<>();
        while (true) {
            QuerySnapshot snapshot = null;
            try {
                snapshot = query.get().get();
            } catch (ExecutionException executionException) {
                throw new RuntimeException(executionException.getCause());
            } catch (InterruptedException interruptedException) {
                LOGGER.error("Interrupted", interruptedException);
                Thread.currentThread().interrupt();
            }
            if (Objects.isNull(snapshot) || snapshot.isEmpty()) {
                break;
            }
            querySnapshots.add(snapshot);
            DocumentSnapshot lastVisible = snapshot.getDocuments().get(snapshot.size() - 1);
            query = getCurrentCollectionGroup().whereIn(fieldId, valueBatch).startAfter(lastVisible).limit(MAX_LIMIT);
        }
        return querySnapshots;
    }
}

