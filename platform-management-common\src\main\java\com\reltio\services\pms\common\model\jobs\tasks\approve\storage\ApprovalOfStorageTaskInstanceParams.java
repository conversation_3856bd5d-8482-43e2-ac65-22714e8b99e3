package com.reltio.services.pms.common.model.jobs.tasks.approve.storage;

import com.reltio.services.pms.common.model.jobs.tasks.TaskInstanceParams;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.TenantPurpose;

public interface ApprovalOfStorageTaskInstanceParams extends TaskInstanceParams {

    default String getDefaultStorageTemplate() {
        return null;
    }

    String getCustomerName();

    String getTenantId();

    TenantSize getTenantSize();

    String getDataStorageArn();

    String getMatchStorageArn();

    TenantPurpose getTenantPurpose();



}
