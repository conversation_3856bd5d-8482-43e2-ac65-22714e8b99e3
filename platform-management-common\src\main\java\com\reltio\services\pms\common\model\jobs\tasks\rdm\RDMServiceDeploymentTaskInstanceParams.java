package com.reltio.services.pms.common.model.jobs.tasks.rdm;

import com.reltio.services.pms.common.model.jobs.tasks.TaskInstanceParams;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;

import java.util.List;

public interface RDMServiceDeploymentTaskInstanceParams extends TaskInstanceParams {
    String getCustomerId();

    String getCustomerName();

    String getRdmTenantId();

    List<String> getMdmTenantId();

    Boolean reuseRdmTenant();

    String getIndustry();

    String getProductEdition();

    Boolean validateSameCustomerId();

    String getRdmHostNameUrl();

    ReltioPackageType getReltioPackageType();

    String getAwsAccountId();

    String getGitBranchName();
}
