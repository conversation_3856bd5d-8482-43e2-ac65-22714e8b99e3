package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class FreeTierEmailNotificationPipelineTaskConfig extends AbstractPipelineTaskConfig{

    private final List<String> cc;
    private final String overwriteToEmail;

    @JsonCreator
    public FreeTierEmailNotificationPipelineTaskConfig(@JsonProperty(value = "name") String name,
                                                       @JsonProperty(value = "cc") List<String> cc,
                                                       @JsonProperty(value = "overwriteToEmail") String overwriteToEmail) {
        super(name, TaskType.FREE_TIER_EMAIL_NOTIFICATION_TASK);
        this.cc = cc;
        this.overwriteToEmail = overwriteToEmail;
    }

    @Override
    public String toString() {
        return "FreeTierEmailNotificationPipelineTaskConfig{" +
                "cc=" + cc +
                ", overwriteToEmail='" + overwriteToEmail + '\'' +
                '}';
    }

    public List<String> getCc() {
        return cc;
    }

    public String getOverwriteToEmail() {
        return overwriteToEmail;
    }
}
