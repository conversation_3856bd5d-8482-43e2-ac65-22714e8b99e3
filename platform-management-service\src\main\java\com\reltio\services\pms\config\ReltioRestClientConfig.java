package com.reltio.services.pms.config;

import com.reltio.services.pms.common.IPMSRestTemplate;
import com.reltio.services.pms.config.rest.InMemoryRestIntegrationRepository;
import com.reltio.services.pms.config.rest.OAuth2RestTemplateImpl;
import com.reltio.services.pms.config.rest.RestIntegrationRegistration;
import com.reltio.services.pms.config.rest.RestIntegrationRepository;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.security.oauth2.client.AuthorizedClientServiceOAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.InMemoryOAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientProvider;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientProviderBuilder;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Configuration
public class ReltioRestClientConfig {

    private static final String AUTH_CLIENT_ID = "AUTH";
    private static final String SALESFORCE_CLIENT_ID = "SALESFORCE";
    private static final Logger LOGGER = Logger.getLogger(ReltioRestClientConfig.class);
    @Value("${reltio.auth.server.url}/oauth/token")
    private String accessTokenUri;

    @Value("${reltio.auth.client.id}")
    private String clientId;

    @Value("${reltio.auth.client.secret}")
    private String clientSecret;

    @Value("${salesforce.server.url}/services/oauth2/token")
    private String salesforceUrl;

    @Value("${salesforce.username}")
    private String salesforceUsername;

    @Value("${salesforce.password}")
    private String salesforcePassword;

    @Value("${salesforce.client.id}")
    private String salesforceClientId;

    @Value("${salesforce.client.secret}")
    private String salesforceSecret;

    @Autowired
    private ClientHttpRequestFactory clientHttpRequestFactory;

    @Bean
    @Primary
    RestIntegrationRepository userRegistrationRepository() {
        return new InMemoryRestIntegrationRepository(getAuthIntegrationRegistration(), getSalesforceIntegrationRegistration());
    }


    @Bean
    @Primary
    OAuth2AuthorizedClientService authorizedClientService(ClientRegistrationRepository clientRegistrationRepository){
        return new InMemoryOAuth2AuthorizedClientService(clientRegistrationRepository);
    }


    @Bean
    AuthorizedClientServiceOAuth2AuthorizedClientManager authorizedClientManager(ClientRegistrationRepository clientRegistrationRepository,OAuth2AuthorizedClientService authorizedClientService) {
        OAuth2AuthorizedClientProvider authorizedClientProvider = OAuth2AuthorizedClientProviderBuilder.builder()
                .password()
                .clientCredentials()
                .refreshToken()
                .build();

        AuthorizedClientServiceOAuth2AuthorizedClientManager authorizedClientManager = new AuthorizedClientServiceOAuth2AuthorizedClientManager(clientRegistrationRepository, authorizedClientService);
        authorizedClientManager.setAuthorizedClientProvider(authorizedClientProvider);
        return authorizedClientManager;
    }

    @Bean
    @Primary
    IPMSRestTemplate createRestTemplate(RestIntegrationRepository restIntegrationRepository,
                                        AuthorizedClientServiceOAuth2AuthorizedClientManager authorizedClientManager,OAuth2AuthorizedClientService authorizedClientService) {
        return OAuth2RestTemplateImpl.buildRestTemplateWithRequestFactory(restIntegrationRepository, authorizedClientManager,authorizedClientService, clientHttpRequestFactory, AUTH_CLIENT_ID);
    }

    @Bean
    @Qualifier("console")
    IPMSRestTemplate createRestTemplateForConsole(RestIntegrationRepository restIntegrationRepository,
                                                  AuthorizedClientServiceOAuth2AuthorizedClientManager authorizedClientManager) {
        return OAuth2RestTemplateImpl.buildRestTemplate(restIntegrationRepository, authorizedClientManager, AUTH_CLIENT_ID);
    }

    private RestIntegrationRegistration getAuthIntegrationRegistration() {
        Set<String> properties = new HashSet<>(Arrays.asList(accessTokenUri, clientId, clientSecret));
        PropertiesValidator.validateProperties(properties, LOGGER);
        return RestIntegrationRegistration.withId(AUTH_CLIENT_ID)
                .setAccessTokenUri(accessTokenUri)
                .setClientId(clientId)
                .setClientSecret(clientSecret)
                .setGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
                .setAuthorizationMethod(ClientAuthenticationMethod.CLIENT_SECRET_POST)
                .build();
    }

    private RestIntegrationRegistration getSalesforceIntegrationRegistration() {
        Set<String> properties = new HashSet<>(Arrays.asList(salesforceUrl, salesforceClientId, salesforceSecret, salesforceUsername, salesforcePassword));
        PropertiesValidator.validateProperties(properties, LOGGER);
        return RestIntegrationRegistration.withId(SALESFORCE_CLIENT_ID)
                .setAccessTokenUri(salesforceUrl)
                .setClientId(salesforceClientId)
                .setClientSecret(salesforceSecret)
                .setGrantType(AuthorizationGrantType.PASSWORD)
                .setUsername(salesforceUsername)
                .setPassword(salesforcePassword)
                .setAuthorizationMethod(ClientAuthenticationMethod.CLIENT_SECRET_POST)
                .build();
    }

}

