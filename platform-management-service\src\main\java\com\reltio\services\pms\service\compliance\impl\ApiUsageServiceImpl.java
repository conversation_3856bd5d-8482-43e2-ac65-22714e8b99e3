package com.reltio.services.pms.service.compliance.impl;

import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByEndpointModel;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByEndpointResponse;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByTenantModel;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByTenantResponse;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.service.compliance.ApiUsageService;
import com.reltio.services.pms.service.compliance.util.ComplianceUtil;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * The type Api usage service.
 */
@Service
public class ApiUsageServiceImpl extends ComplianceUtil implements ApiUsageService {
    /**
     * Gets api usage by tenant.
     *
     * @param tenantCollection the tenant collection
     * @param response         the response
     * @return the api usage by tenant
     */
    @Override
    public ApiUsageByTenantResponse getApiUsageByTenant(Collection<ReltioTenant> tenantCollection, ApiUsageByTenantResponse response) {
        List<ApiUsageByTenantModel> byTenantList = response.getApiUsageByTenantModelList();
        for (ApiUsageByTenantModel tenantModel : byTenantList) {
            tenantModel.setEnvironment(getPurpose(tenantModel.getTenantId(), tenantCollection));
        }
        response.setApiUsageByTenantModelList(byTenantList);
        return response;
    }

    @Override
    public ApiUsageByEndpointResponse getApiUsageByEndpoint(Collection<ReltioTenant> tenantCollection, ApiUsageByEndpointResponse response) {
        List<ApiUsageByEndpointModel> byEndpointModelList = response.getApiUsageByEndpointModelList();
        for (ApiUsageByEndpointModel endpointModel : byEndpointModelList) {
            endpointModel.setEnvironment(getPurpose(endpointModel.getTenantId(), tenantCollection));
        }
        response.setApiUsageByEndpointModelList(byEndpointModelList);
        return response;
    }
}
