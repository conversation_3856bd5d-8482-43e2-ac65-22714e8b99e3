package com.reltio.services.pms.common.model.jobs.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

public class SleepTaskInstance extends TaskInstance {
    private final Long sleepTime;

    @JsonCreator
    public SleepTaskInstance(@JsonProperty(value = "id") String id,
                             @JsonProperty(value = "name") String name,
                             @JsonProperty(value = "jobId") String jobId,
                             @JsonProperty(value = "startTime") Long startTime,
                             @JsonProperty(value = "finishTime") Long finishTime,
                             @JsonProperty(value = "status") TaskStatus status,
                             @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                             @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                             @JsonProperty(value = "executingNodeName") String executingNodeName,
                             @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                             @JsonProperty(value = "sleepTime") Long sleepTime,
                             @JsonProperty(value = "envId") String envId) {
        super(id, name, jobId, startTime, finishTime, TaskType.SLEEP_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.sleepTime = sleepTime;
    }

    public Long getSleepTime() {
        return sleepTime;
    }
}
