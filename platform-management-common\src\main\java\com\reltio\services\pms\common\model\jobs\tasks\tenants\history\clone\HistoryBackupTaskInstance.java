package com.reltio.services.pms.common.model.jobs.tasks.tenants.history.clone;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
public class HistoryBackupTaskInstance extends TaskInstance {
    @JsonProperty(value = "sourceTenantId")
    private final String sourceTenantId;

    @JsonProperty(value = "sourceEnvId")
    private final String sourceEnvId;

    @JsonProperty(value = "ticketId")
    private final String ticketId;

    @JsonProperty(value = "backupRetentionPeriod")
    private final Integer backupRetentionPeriod;

    @JsonProperty(value = "events")
    private final List<String> events;

    @JsonCreator
    public HistoryBackupTaskInstance(@JsonProperty(value = "id") String id,
                                  @JsonProperty(value = "name") String name,
                                  @JsonProperty(value = "envId") String envId,
                                  @JsonProperty(value = "jobId") String jobId,
                                  @JsonProperty(value = "startTime") Long startTime,
                                  @JsonProperty(value = "finishTime") Long finishTime,
                                  @JsonProperty(value = "status") TaskStatus status,
                                  @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                  @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                  @JsonProperty(value = "executingNodeName") String executingNodeName,
                                  @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                  @JsonProperty(value = "sourceTenantId") String sourceTenantId,
                                  @JsonProperty(value = "sourceEnvId") String sourceEnvId,
                                  @JsonProperty (value = "backupRetentionPeriod") Integer backupRetentionPeriod,
                                  @JsonProperty (value = "ticketId") String ticketId,
                                  @JsonProperty(value = "events") List<String> events){

        super(id, name, jobId, startTime, finishTime, TaskType.HISTORY_BACKUP_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.sourceTenantId = sourceTenantId;
        this.sourceEnvId = sourceEnvId;
        this.ticketId = ticketId;
        this.backupRetentionPeriod = Objects.requireNonNullElse(backupRetentionPeriod, 30);
        this.events = events == null ? new ArrayList<>() : new ArrayList<>(events);

    }

    public void addEvent(String event) {
        events.add(event);
    }
}
