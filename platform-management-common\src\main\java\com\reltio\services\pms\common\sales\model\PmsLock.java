package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PmsLock extends BaseFirestoreEntity {

    private String lockId;

    private Boolean isLocked;

    private String lockedBy;

    public PmsLock(String lockId , Boolean isLocked, String lockedBy){
        this.lockId = lockId;
        this.isLocked = isLocked == null ? Boolean.FALSE : isLocked;
        this.lockedBy=lockedBy;
    }

    @Override
    public String getID() {
        return lockId;
    }
}
