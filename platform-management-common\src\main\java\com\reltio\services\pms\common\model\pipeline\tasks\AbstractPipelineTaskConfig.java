package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.Getter;

import java.util.Objects;

@Getter
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(value = SleepPipelineTaskConfig.class, name = TaskType.SLEEP_TASK_NAME),
        @JsonSubTypes.Type(value = MultiSleepPipelineTaskConfig.class, name = TaskType.MULTI_SLEEP_TASK_NAME),
        @JsonSubTypes.Type(value = ApprovalPipelineTaskConfig.class, name = TaskType.APPROVAL_TASK_NAME),
        @JsonSubTypes.Type(value = MdmTaskConfig.class, name = TaskType.MDM_TASK_NAME),
        @JsonSubTypes.Type(value = AuthPipelineTaskConfig.class, name = TaskType.AUTH_TASK_NAME),
        @JsonSubTypes.Type(value = FreeTierEmailNotificationPipelineTaskConfig.class, name = TaskType.FREE_TIER_EMAIL_NOTIFICATION_TASK_NAME),
        @JsonSubTypes.Type(value = EmailVerificationPipelineTaskConfig.class, name = TaskType.EMAIL_VERIFICATION_TASK_NAME),
        @JsonSubTypes.Type(value = R360DataSyncTaskConfig.class, name = TaskType.R360_DATA_SYNC_TASK_NAME),
        @JsonSubTypes.Type(value = WorkflowPipelineTaskConfig.class, name = TaskType.WORKFLOW_TASK_NAME),
        @JsonSubTypes.Type(value = MatchIQPipelineTaskConfig.class, name = TaskType.MATCH_IQ_TASK_NAME),
        @JsonSubTypes.Type(value = StreamingPipelineTaskConfig.class, name = TaskType.STREAMING_TASK_NAME),
        @JsonSubTypes.Type(value = ApprovalOfStoragePipelineTaskConfig.class, name = TaskType.APPROVAL_OF_STORAGE_TASK_NAME),
        @JsonSubTypes.Type(value = RdmTaskConfig.class, name = TaskType.RDM_TASK_NAME),
        @JsonSubTypes.Type(value = RDMServiceDeploymentTaskConfig.class, name = TaskType.RDM_SERVICE_DEPLOYMENT_NAME),
        @JsonSubTypes.Type(value = WorkatoEnablementPipelineTaskConfig.class, name = TaskType.WORKATO_ENABLEMENT_TASK_NAME),
        @JsonSubTypes.Type(value = ImageHostingPipelineTaskConfig.class, name = TaskType.IMAGE_HOSTING_ENABLEMENT_TASK_NAME),
        @JsonSubTypes.Type(value = EnterpriseNotificationPipelineTaskConfig.class, name = TaskType.ENTERPRISE_NOTIFICATION_TASK_NAME),
        @JsonSubTypes.Type(value = DNBConnectorPipelineTaskConfig.class, name = TaskType.DNB_CONNECTOR_TASK_NAME),
        @JsonSubTypes.Type(value = DTSSPipelineTaskConfig.class, name = TaskType.DTSS_TASK_NAME),
        @JsonSubTypes.Type(value = SFDCConnectorEnablementPipelineTaskConfig.class, name = TaskType.SFDC_CONNECTOR_ENABLEMENT_TASK_NAME),
        @JsonSubTypes.Type(value = ShieldEnablementPipelineTaskConfig.class, name = TaskType.SHIELD_ENABLEMENT_TASK_NAME),
        @JsonSubTypes.Type(value = UpdateTenantPipelineTaskConfig.class, name = TaskType.UPDATE_TENANT_TASK_NAME),
        @JsonSubTypes.Type(value = CleanTenantTaskConfig.class, name = TaskType.CLEAN_TENANT_TASK_NAME),
        @JsonSubTypes.Type(value = CleanTenantExecutionTaskConfig.class, name =
                TaskType.CLEAN_TENANT_EXECUTION_TASK_NAME),
        @JsonSubTypes.Type(value = CleanTenantValidationTaskConfig.class, name =
                TaskType.CLEAN_TENANT_VALIDATION_TASK_NAME),
        @JsonSubTypes.Type(value = MaintenanceModeTaskConfig.class, name = TaskType.MAINTENANCE_MODE_TASK_NAME),
        @JsonSubTypes.Type(value = TenantsSyncPipelineTaskConfig.class, name = TaskType.TENANTS_SYNC_TASK_NAME),
        @JsonSubTypes.Type(value = FernPipelineTaskConfig.class, name = TaskType.FERN_TASK_NAME),
        @JsonSubTypes.Type(value = FernDeProvisionPipelineTaskConfig.class, name = TaskType.FERN_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = AuthDeProvisionPipelineTaskConfig.class, name = TaskType.AUTH_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = EmailNotificationPipelineTaskConfig.class, name = TaskType.EMAIL_NOTIFICATION_TASK_NAME),
        @JsonSubTypes.Type(value = SfdcDeProvisionPipelineTaskConfig.class, name = TaskType.SFDC_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = EmailNotificationPipelineTaskConfig.class, name = TaskType.EMAIL_NOTIFICATION_TASK_NAME),
        @JsonSubTypes.Type(value = DnbDeProvisionPipelineTaskConfig.class, name = TaskType.DNB_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = DeleteMDMTenantTaskConfig.class, name = TaskType.DELETE_MDM_TENANT_TASK_NAME),
        @JsonSubTypes.Type(value = DeleteRDMTenantTaskConfig.class, name = TaskType.DELETE_RDM_TENANT_TASK_NAME),
        @JsonSubTypes.Type(value = StreamingDeProvisionPipelineTaskConfig.class, name = TaskType.STREAMING_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = RiqDeProvisionPipelineTaskConfig.class, name = TaskType.RIQ_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = ImageHostingDeProvisionTaskConfig.class, name = TaskType.IMAGE_HOSTING_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = WorkatoDeProvisionPipelineTaskConfig.class, name = TaskType.WORKATO_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = WorkflowDeProvisionPipelineTaskConfig.class, name = TaskType.WORKFLOW_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = AzureBlobStorageTaskConfig.class , name = TaskType.AZURE_BLOB_STORAGE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = DeProvisionDtssPipelineTaskConfig.class , name = TaskType.DTSS_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = DeProvisionMatchIqPipelineTaskConfig.class , name = TaskType.MATCH_IQ_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = RIHGenericPipelineTaskConfig.class , name = TaskType.RIH_GENERIC_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = DeProvisionMatchIqPipelineTaskConfig.class , name = TaskType.MATCH_IQ_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = RetrieveAndStoreTenantConfigsPipeline.class , name = TaskType.RETRIEVE_AND_STORE_TENANT_CONFIGS_TASK_NAME),
        @JsonSubTypes.Type(value = CloneTenantConfigsPipeline.class , name = TaskType.CLONE_TENANTS_CONFIGS_TASK_NAME),
        @JsonSubTypes.Type(value = DataBackupTaskConfig.class, name = TaskType.DATA_BACKUP_TASK_NAME),
        @JsonSubTypes.Type(value = DataRestoreTaskConfig.class, name = TaskType.DATA_RESTORE_TASK_NAME),
        @JsonSubTypes.Type(value = DeleteDynamoDbBackupConfig.class, name = TaskType.DELETE_DYNAMO_DB_BACKUP_TASK_NAME),
        @JsonSubTypes.Type(value = ConfigureStreamingAndAnalyticsTaskConfig.class, name = TaskType.CONFIGURE_STREAMING_AND_ANALYTICS_TASK_NAME),
        @JsonSubTypes.Type(value = HistoryBackupTaskConfig.class , name = TaskType.HISTORY_BACKUP_TASK_NAME),
        @JsonSubTypes.Type(value = HistoryRestoreTaskConfig.class, name = TaskType.HISTORY_RESTORE_TASK_NAME)

})
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public abstract class AbstractPipelineTaskConfig {

    @JsonProperty("name")
    private final String name;

    @JsonProperty("type")
    private final TaskType type;

    @JsonCreator
    protected AbstractPipelineTaskConfig(
            @JsonProperty(value = "name", required = true) String name,
            @JsonProperty(value = "type", required = true) TaskType type) {
        this.name = name;
        this.type = type;
    }

    public PMSProductName getProductName() {
        return null;
    }

    public boolean visibleInContract() {
        return false;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        AbstractPipelineTaskConfig that = (AbstractPipelineTaskConfig) o;
        return Objects.equals(getName(), that.getName()) &&
                getType() == that.getType();
    }

    @Override
    public int hashCode() {
        return Objects.hash(getName(), getType());
    }

    @Override
    public String toString() {
        return "AbstractPipelineTaskConfig{" +
                "name='" + name + '\'' +
                ", type=" + type +
                '}';
    }
}