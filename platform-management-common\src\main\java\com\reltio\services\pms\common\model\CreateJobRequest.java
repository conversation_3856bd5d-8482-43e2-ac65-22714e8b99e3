package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;

import java.util.EnumSet;

public class CreateJobRequest {

    @JsonProperty("pipelineId")
    private final String pipelineId;

    @JsonProperty("skipTasks")
    protected final EnumSet<PMSProductName> skipTasks;

    public CreateJobRequest(@JsonProperty("pipelineId") String pipelineId,
                            @JsonProperty("skipTasks") EnumSet<PMSProductName> skipTasks) {
        this.pipelineId = pipelineId;
        this.skipTasks = skipTasks;
    }

    public String getPipelineId() {
        return pipelineId;
    }

    public EnumSet<PMSProductName> getSkipTasks() {
        return skipTasks;
    }
}
