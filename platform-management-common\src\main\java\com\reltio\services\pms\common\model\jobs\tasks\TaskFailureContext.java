package com.reltio.services.pms.common.model.jobs.tasks;


import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Objects;

public class TaskFailureContext {

    @JsonProperty("stackTraceElements")
    private final List<String> stackTraceElements;

    @JsonProperty("messages")
    private final List<String> messages;


    @JsonCreator
    public TaskFailureContext(@JsonProperty("stackTraceElements") List<String> stackTraceElements,
                              @JsonProperty("messages") List<String> messages) {
        this.stackTraceElements = stackTraceElements;
        this.messages = messages;
    }

    public List<String> getStackTraceElements() {
        return stackTraceElements;
    }

    public List<String> getMessages() {
        return messages;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        TaskFailureContext that = (TaskFailureContext) o;
        return Objects.equals(getStackTraceElements(), that.getStackTraceElements()) &&
                Objects.equals(getMessages(), that.getMessages());
    }

    @Override
    public int hashCode() {
        return Objects.hash(stackTraceElements, messages);
    }

    @Override
    public String toString() {
        return "TaskFailureContext{" +
                "stackTraceElements=" + stackTraceElements +
                ", messages=" + messages +
                '}';
    }
}
