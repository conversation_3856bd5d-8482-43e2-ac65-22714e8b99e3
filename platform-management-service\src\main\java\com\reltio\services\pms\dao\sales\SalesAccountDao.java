package com.reltio.services.pms.dao.sales;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.DocumentSnapshot;
import com.google.cloud.firestore.Query;
import com.google.cloud.firestore.QuerySnapshot;
import com.google.common.collect.Lists;
import com.reltio.collection.CollectionUtils;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.sales.model.SalesAccount;
import com.reltio.services.pms.dao.AbstractRootCollectionDao;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import java.util.Collections;
import java.util.NoSuchElementException;
import java.util.Set;
import java.util.List;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class SalesAccountDao extends AbstractRootCollectionDao<SalesAccount> {

    private static final String SALES_ACCOUNT_COLLECTION_NAME = "PMS_SALES_ACCOUNT";
    private static final Logger LOGGER = Logger.getLogger(SalesAccountDao.class);
    private static final int MAX_LIMIT = 30;

    @Autowired
    public SalesAccountDao(CredentialsProvider provider,
                           @Value("${firestore.env.name}") String deployedEnv,
                           ReltioUserHolder reltioUserHolder) {
        super(provider, SALES_ACCOUNT_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    protected TypeReference<SalesAccount> getTypeReference() {
        return new TypeReference<SalesAccount>() {
        };
    }

    public SalesAccount getCustomerByFieldId(String fieldId,String value) {
        try {
            Query query = getBaseCollection().whereEqualTo(fieldId, value);
            return getResultFromQuery(query).stream().iterator().next();
        } catch  (NoSuchElementException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.BUSINESS_CUSTOMER_NOT_FOUND, HttpStatus.NOT_FOUND.value(), value);
        }
    }
    public SalesAccount getCustomerByContractId(String contractId) {
        try {//TODO add null check
            Query query = getBaseCollection().whereEqualTo("contracts.contractId", contractId);
            return getResultFromQuery(query).stream().iterator().next();
        } catch  (NoSuchElementException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.BUSINESS_CUSTOMER_NOT_FOUND, HttpStatus.NOT_FOUND.value(), contractId);
        }
    }

    public Collection<SalesAccount> getSalesAccountByFieldIdList(String fieldId, Set<String> idList) {
        try {
            if (CollectionUtils.isNotEmpty(idList)) {
                List<String> values = new ArrayList<>(idList);
                if (values.size() >= MAX_LIMIT) {
                    return getSalesAccountByPartition(fieldId, values);
                } else {
                    return getResultFromQuery(getCurrentCollectionGroup().whereIn(fieldId, values));
                }
            }
        } catch (Exception exception) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.EXPECTATION_FAILED.value(), exception);
        }
        return Collections.emptyList();
    }

    private Collection<SalesAccount> getSalesAccountByPartition(String fieldId, List<String> values) {
        List<List<String>> valueBatches = Lists.partition(values, MAX_LIMIT);
        List<CompletableFuture<List<QuerySnapshot>>> completableFutureList = valueBatches.parallelStream()
                .map(batch -> CompletableFuture.supplyAsync(() -> processBatch(fieldId, batch))).collect(Collectors.toList());
        return CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]))
                .thenApply(apply -> completableFutureList.stream()
                        .map(CompletableFuture::join)
                        .map(getResult())
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList()))
                .join();

    }

    @NotNull
    private Function<List<QuerySnapshot>, List<SalesAccount>> getResult() {
        return query -> query.stream().map(instance -> getResultFromQuery(instance.getQuery())).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<QuerySnapshot> processBatch(String fieldId, List<String> valueBatch) {
        Query query = getCurrentCollectionGroup().whereIn(fieldId, valueBatch).limit(MAX_LIMIT);
        List<QuerySnapshot> querySnapshots = new ArrayList<>();
        while (true) {
            QuerySnapshot snapshot = null;
            try {
                snapshot = query.get().get();
            } catch (ExecutionException executionException) {
                throw new RuntimeException(executionException.getCause());
            } catch (InterruptedException interruptedException) {
                LOGGER.error("Interrupted", interruptedException);
                Thread.currentThread().interrupt();
            }
            if (Objects.isNull(snapshot) || snapshot.isEmpty()) {
                break;
            }
            querySnapshots.add(snapshot);
            DocumentSnapshot lastVisible = snapshot.getDocuments().get(snapshot.size() - 1);
            query = getCurrentCollectionGroup().whereIn(fieldId, valueBatch).startAfter(lastVisible).limit(MAX_LIMIT);
        }
        return querySnapshots;
    }

}
