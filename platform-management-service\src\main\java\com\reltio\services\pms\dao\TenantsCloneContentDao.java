package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.TenantsCloneContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class TenantsCloneContentDao extends AbstractRootCollectionDao<TenantsCloneContent>{

    private static final String CLONE_COLLECTION_NAME = "PMS_CLONE_CONFIG";

    @Autowired
    public TenantsCloneContentDao(CredentialsProvider provider, @Value("${firestore.env.name}") String deployedEnv,
                           ReltioUserHolder reltioUserHolder) {
        super(provider, CLONE_COLLECTION_NAME, deployedEnv, reltioUserHolder);
    }

    @Override
    protected TypeReference<TenantsCloneContent> getTypeReference() {
        return new TypeReference<>() {};
    }

}
