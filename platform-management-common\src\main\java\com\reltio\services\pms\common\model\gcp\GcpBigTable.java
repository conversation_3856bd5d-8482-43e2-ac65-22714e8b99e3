package com.reltio.services.pms.common.model.gcp;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class GcpBigTable {
    @JsonProperty("projectId")
    private String projectId;

    @JsonProperty("instanceId")
    private String instanceId;

    @JsonProperty("tableName")
    private String tableName;

    @JsonCreator
    public GcpBigTable(@JsonProperty("projectId") String projectId, @JsonProperty("instanceId") String instanceId,
                       @JsonProperty("tableName") String tableName) {
        this.projectId = projectId;
        this.instanceId = instanceId;
        this.tableName = tableName;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}
