package com.reltio.services.pms.common.model.jobs.tasks;

import com.reltio.services.pms.common.model.jobs.JobStatus;

public enum TaskStatus {
    SCHEDULED,
    PROCESSING,
    CANCELING,
    CANCELED,
    COMPLETED,
    FAILED,
    WAITING,
    COMPLETED_WITH_ERROR,
    NOTHING_TO_UPDATE;

    public static TaskStatus fromJob(JobStatus status) {
        if (status == null) {
            return null;
        }
        switch (status) {
            case SCHEDULED:
                return SCHEDULED;
            case INPROGRESS:
                return PROCESSING;
            case CANCELLING:
                return CANCELING;
            case CANCELLED:
                return CANCELED;
            case COMPLETED:
                return COMPLETED;
            case FAILED:
                return FAILED;
            default:
                return null;
        }
    }
}
