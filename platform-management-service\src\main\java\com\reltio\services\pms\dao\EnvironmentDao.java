package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.CollectionReference;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.Environment;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import java.util.Collections;
import java.util.Map;
import java.util.Set;

@Service
public class EnvironmentDao extends AbstractRootCollectionDao<Environment> {

    private static final String ENVIRONMENTS_COLLECTION_NAME = "PMS_ENVIRONMENTS";
    private static final Logger LOGGER = Logger.getLogger(EnvironmentDao.class);


    @Autowired
    public EnvironmentDao(CredentialsProvider provider,
                          @Value("${firestore.env.name}") String deployedEnv, ReltioUserHolder reltioUserHolder) {
        super(provider, ENVIRONMENTS_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    protected TypeReference<Environment> getTypeReference() {
        return new TypeReference<Environment>() {
        };
    }


    public Environment updateStoragePriorityList(Environment entity) {
        String username = getUsername();
        Long time = System.currentTimeMillis();
        entity.setUpdatedBy(username);
        entity.setUpdatedTime(time);
        return createOrUpdate(getBaseCollection(), entity);
    }

      // Please do not update abstract dao access specifier for createUpdate
    protected Environment createOrUpdate(CollectionReference collectionReference, Environment entity) {
        try {
            String id = entity.getID();
            Map<String, Object> objectMap = convertToMap(entity);
            collectionReference.document(id).set(objectMap);
            return entity;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }


}
