package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;


@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
public abstract class AbstractProductConfig {

    @JsonProperty("salesConfigs")
    private Set<SalesConfig> salesConfigs;

    public AbstractProductConfig() {
    }

    public AbstractProductConfig(Set<SalesConfig> salesConfigs) {
        this.salesConfigs = salesConfigs;
    }


    public void addSalesConfigs(SalesConfig salesConfig) {
        if (this.salesConfigs == null) {
            this.salesConfigs = new HashSet<>();
        }
       this.salesConfigs.add(salesConfig);
    }

    public void addAllSalesConfigs(Set<SalesConfig> salesConfigs) {
        if (this.salesConfigs == null) {
            this.salesConfigs = new HashSet<>();
        }
        this.salesConfigs.addAll(salesConfigs);
    }
}
