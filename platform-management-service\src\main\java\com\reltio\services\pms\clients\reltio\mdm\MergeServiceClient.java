package com.reltio.services.pms.clients.reltio.mdm;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.reltio.services.pms.dto.MergeResponse;
import com.reltio.services.pms.enums.ResolveConflictStrategyEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
@Slf4j
@Service
public class MergeServiceClient {
  private static final ObjectMapper objectMapper = new ObjectMapper();

  public MergeResponse merge(final JsonNode source, final JsonNode destination,
                             final ResolveConflictStrategyEnum strategy) {
    ArrayNode conflicts = objectMapper.createArrayNode();
    JsonNode result = mergeRecursive(source, destination, strategy, conflicts, true);

    return new MergeResponse(result, conflicts);
  }

  private static JsonNode mergeRecursive(final JsonNode source, final JsonNode destination,
                                  final ResolveConflictStrategyEnum strategy, final ArrayNode conflicts,
                                  final boolean isRoot) {
    if (source.isObject() && destination.isObject()) {
      ObjectNode merged = destination.deepCopy();
      Iterator<Map.Entry<String, JsonNode>> fields = source.fields();
      while (fields.hasNext()) {
        Map.Entry<String, JsonNode> field = fields.next();
        String fieldName = field.getKey();
        JsonNode sourceValue = field.getValue();

        if (isRoot && "uri".equals(fieldName)) {
          continue;
        }

        JsonNode destinationValue = destination.get(fieldName);
        if (destinationValue != null) {
          if (sourceValue.isObject() && destinationValue.isObject()) {
            JsonNode mergedValue = mergeRecursive(sourceValue, destinationValue, strategy, conflicts, false);
            merged.set(fieldName, mergedValue);
          } else if (sourceValue.isArray() && destinationValue.isArray()) {
            ArrayNode mergedArray = mergeArrays((ArrayNode) sourceValue, (ArrayNode) destinationValue, conflicts, fieldName, strategy);
            merged.set(fieldName, mergedArray);
          } else {
            merged.set(fieldName, resolveField(sourceValue, destinationValue, strategy, conflicts, fieldName));
          }
        } else {
          merged.set(fieldName, sourceValue);
        }
      }
      return merged;
    } else {
      return destination;
    }
  }

  private static ArrayNode mergeArrays(final ArrayNode source, final ArrayNode destination, final ArrayNode conflicts,
                                final String fieldName, final ResolveConflictStrategyEnum strategy) {
    ArrayNode mergedArray = objectMapper.createArrayNode();
    Map<String, JsonNode> destinationMap = new HashMap<>();
    List<JsonNode> unmatchedNodes = new ArrayList<>();

    for (JsonNode node : destination) {
      if (node.has("uri")) {
        destinationMap.put(node.get("uri").asText(), node);
      } else {
        unmatchedNodes.add(node);
      }
    }

    for (JsonNode sourceNode : source) {
      if (sourceNode.has("uri")) {
        String uri = sourceNode.get("uri").asText();
        JsonNode destinationNode = destinationMap.get(uri);
        if (destinationNode != null && strategy != ResolveConflictStrategyEnum.ADD) {
          ArrayNode subConflicts = objectMapper.createArrayNode();
          JsonNode mergedObject = mergeRecursive(sourceNode, destinationNode, strategy, subConflicts, false);

          if (!subConflicts.isEmpty()) {
            ObjectNode conflict = objectMapper.createObjectNode();
            conflict.put("field", fieldName);
            conflict.put("uri", uri);
            conflict.set("conflicts", subConflicts);
            conflicts.add(conflict);
          }

          mergedArray.add(mergedObject);
        } else {
          mergedArray.add(sourceNode);
        }
      } else {
        boolean exists = false;
        for (JsonNode existingNode : destination) {
          if (existingNode.equals(sourceNode)) {
            exists = true;
            break;
          }
        }
        if (!exists) {
          mergedArray.add(sourceNode);
        }
      }
    }


    destinationMap.forEach((destinationURI, destinationNode) -> {
      if (!containsUri(mergedArray, destinationURI)) {
        mergedArray.add(destinationNode);
      }
    });

    mergedArray.addAll(unmatchedNodes);
    return mergedArray;
  }

  private static boolean containsUri(ArrayNode arrayNode, String uri) {
    for (JsonNode node : arrayNode) {
      if (node.has("uri") && node.get("uri").asText().equals(uri)) {
        return true;
      }
    }
    return false;
  }

  private static JsonNode resolveField(final JsonNode sourceValue, final JsonNode destinationValue,
                                final ResolveConflictStrategyEnum strategy, final ArrayNode conflicts,
                                final String fieldName) {
    if (!sourceValue.equals(destinationValue)) {
      ObjectNode conflict = objectMapper.createObjectNode();
      conflict.put("field", fieldName);
      conflict.set("sourceValue", sourceValue);
      conflict.set("destinationValue", destinationValue);

      return valueBasedOnStrategy(sourceValue, destinationValue, strategy, conflicts, conflict);
    }

    return destinationValue;
  }

  private static JsonNode valueBasedOnStrategy(final JsonNode sourceValue, final JsonNode destinationValue, final ResolveConflictStrategyEnum strategy, final ArrayNode conflicts, final ObjectNode conflict){
    return switch (strategy) {
      case WARN -> {
        conflicts.add(conflict);
        yield destinationValue;
      }
      case IGNORE, ADD -> sourceValue;
      case MERGE -> destinationValue;
    };
  }
}
