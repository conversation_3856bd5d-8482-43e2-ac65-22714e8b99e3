package com.reltio.services.pms.clients.external.gcp.secret;

import java.util.List;

public interface SecretsManagerService {
    void saveSecret(String secretKeyId, byte[] secretKey);

    String getSecret(String secretKeyId);

    void deleteSecret(String secretKeyId);

    boolean doesSecretExist(String secretKeyId);

    void updateSecret(String secretKeyId, byte[] secretKey);

    List<String> listKeys(String keyStartWith);
}
