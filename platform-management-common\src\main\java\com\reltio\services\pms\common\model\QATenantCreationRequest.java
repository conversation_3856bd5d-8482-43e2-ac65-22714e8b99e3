package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.common.config.MatchingStrategy;
import com.reltio.devops.common.environment.ServicePurpose;
import com.reltio.devops.common.environment.ServiceType;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import lombok.Getter;

import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Getter
public class QATenantCreationRequest extends TenantCreationRequest {
    @JsonProperty("matchingStrategy")
    private final MatchingStrategy matchingStrategy;
    @JsonProperty("replicationFactor")
    private final Integer replicationFactor;
    @JsonProperty("tenantInternalId")
    private final Integer tenantInternalId;
    @JsonProperty("storagePriorityList")
    private final Map<ServicePurpose, List<ServiceType>> storagePriorityList;

    @JsonCreator
    public QATenantCreationRequest(@JsonProperty("pipelineId") String pipelineId,
                                   @JsonProperty("tenantSize") TenantSize tenantSize,
                                   @JsonProperty("mdmTenantId") String mdmTenantId,
                                   @JsonProperty("matchingStrategy") MatchingStrategy matchingStrategy,
                                   @JsonProperty("replicationFactor") Integer replicationFactor,
                                   @JsonProperty("tenantInternalId") Integer tenantInternalId,
                                   @JsonProperty("storagePriorityList") Map<ServicePurpose, List<ServiceType>> storagePriorityList,
                                   @JsonProperty("useSpannerCloudFunction") Boolean useSpannerCloudFunction,
                                   @JsonProperty(value = "isQaAutomation", defaultValue = "false") Boolean isQaAutomation,
                                   @JsonProperty("newInstanceId") String newInstanceId,
                                   @JsonProperty("reltioPackageType") ReltioPackageType reltioPackageType,
                                   @JsonProperty("skipTasks") EnumSet<PMSProductName> skipTasks) {
        super(pipelineId == null ? "MDMonly" : pipelineId, tenantSize == null ? TenantSize.X_SMALL : tenantSize,
                mdmTenantId, isQaAutomation == null || isQaAutomation, newInstanceId,
                useSpannerCloudFunction == null ? Boolean.FALSE : useSpannerCloudFunction, reltioPackageType, skipTasks);
        this.matchingStrategy = matchingStrategy == null ? MatchingStrategy.INCREMENTAL : matchingStrategy;
        this.replicationFactor = replicationFactor;
        this.tenantInternalId = tenantInternalId;
        this.storagePriorityList = storagePriorityList;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        QATenantCreationRequest that = (QATenantCreationRequest) o;
        return getPipelineId().equals(that.getPipelineId()) &&
                getTenantSize() == that.getTenantSize() &&
                getMdmTenantId().equals(that.getMdmTenantId()) &&
                getMatchingStrategy() == that.getMatchingStrategy() &&
                getReplicationFactor().equals(that.getReplicationFactor()) &&
                getTenantInternalId().equals(that.getTenantInternalId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getPipelineId(), getTenantSize(), getMdmTenantId(), getMatchingStrategy(), getReplicationFactor(), getTenantInternalId(), getStoragePriorityList(), getUseSpannerCloudFunction(), getIsQaAutomation());
    }

    @Override
    public String toString() {
        return "QATenantCreationRequest{" + "matchingStrategy='" + matchingStrategy + '\'' + ", replicationFactor=" + replicationFactor + ", tenantInternalId=" + tenantInternalId + ", storagePriorityList=" + storagePriorityList + ", useSpannerCloudFunction=" + super.getUseSpannerCloudFunction() + ", isQaAutomation=" + super.getIsQaAutomation() + '}';
    }
}
