package com.reltio.services.pms.common.model.jobs.sync;

import com.reltio.services.pms.common.model.jobs.tasks.contracts.sync.ContractsSyncTaskInstanceParams;

public class SyncJobInstanceParams implements ContractsSyncTaskInstanceParams {

    Long activatedTime;

    public SyncJobInstanceParams(Long activatedTime) {
        this.activatedTime = activatedTime;
    }

    @Override
    public Long getActivatedTime() {
        return activatedTime;
    }
}
