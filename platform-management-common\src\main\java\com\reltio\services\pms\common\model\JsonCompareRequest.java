package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;

@Getter
public class JsonCompareRequest {

    @JsonProperty("schema")
    private final String schema;

    @JsonProperty("expected")
    private final JsonNode expected;

    @JsonProperty("actual")
    private final JsonNode actual;

    public JsonCompareRequest(@JsonProperty(value = "schema", required = true) String schema,
                              @JsonProperty(value = "expected", required = true) JsonNode expected,
                              @JsonProperty(value = "actual", required = true) JsonNode actual) {
        this.expected = expected;
        this.actual = actual;
        this.schema = schema;
    }

}
