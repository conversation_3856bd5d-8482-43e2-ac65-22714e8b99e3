package com.reltio.services.pms.common.model.compliance.request;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * The type Compliance report request.
 */
@NoArgsConstructor
@Getter
@Setter
public class ApiUsageReportRequest extends ComplianceReportRequest {

    /**
     * The endpoint
     */
    String endPoint;
    /**
     * The Http Method
     */
    String httpMethod;

    /**
     * The Source System
     */
    String[] sourceSystem;

    public ApiUsageReportRequest(String startDate, String endDate, Integer size, Integer offset, String sortOrder, String sortField, List<String> accountIds, String tenantId, String tenantPurpose, Boolean includeAllTenants, String endPoint, String httpMethod, String[] sourceSystem) {
        super(startDate, endDate, size, offset, sortOrder, sortField, accountIds, tenantId, tenantPurpose,includeAllTenants);
        this.endPoint = endPoint;
        this.httpMethod = httpMethod;
        this.sourceSystem = sourceSystem;
    }
}
