package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class HistoryBackup extends BaseFirestoreEntity {
    @JsonProperty(value = "id")
    private String id;

    @JsonProperty(value = "gbtBackupId")
    private String gbtBackupId;

    @JsonProperty(value = "sourceTenantId")
    private String sourceTenantId;

    @JsonProperty(value = "sourceEnvId")
    private String sourceEnvId;

    @JsonProperty(value = "ticketId")
    private String ticketId;

    @JsonProperty(value = "jobId")
    private String jobId;

    @JsonProperty(value = "taskId")
    private String taskId;

    @JsonProperty(value = "retentionPeriod")
    private long retentionPeriod;

    @JsonProperty(value = "projectId")
    private String projectId;

    @JsonProperty(value = "instanceId")
    private String instanceId;

    @JsonProperty(value = "tableName")
    private String tableName;


    @Override
    public String getID() {
        return id;
    }
}
