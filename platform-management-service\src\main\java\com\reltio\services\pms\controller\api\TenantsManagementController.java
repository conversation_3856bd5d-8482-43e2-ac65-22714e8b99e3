package com.reltio.services.pms.controller.api;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.auth.domain.ReltioPrivileges;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.model.TenantsManagementLogsRequest;
import com.reltio.services.pms.common.sales.model.TenantsManagementLogs;
import com.reltio.services.pms.common.sales.model.TenantsManagementLogsFilters;
import com.reltio.services.pms.service.TenantsManagementService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@RestController
@RequestMapping(value = "/api/v1/environments/{envId}", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Self Service Tenants Management")
public class  TenantsManagementController {
    private final TenantsManagementService tenantManagementService;

    @Autowired
    public TenantsManagementController(TenantsManagementService tenantManagementService) {
        this.tenantManagementService = tenantManagementService;
    }

    @PostMapping(value = "/tenantManagementLogs/{tenantId}", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Environment.TenantManagementSelfService.class, privileges = ReltioPrivileges.CREATE)
    public TenantsManagementLogs manageSelfServiceLogs(@PathVariable String envId, @PathVariable String tenantId,
                                                       @RequestBody TenantsManagementLogsRequest tenantsManagementLogsRequest) {
        return tenantManagementService.addLogsToPms(envId, tenantId, tenantsManagementLogsRequest);
    }

    @GetMapping(value = "/getPhysicalConfigParams/{tenantId}")
    @ReltioSecured(resourceClass = Pms.Environment.TenantManagementSelfService.class, privileges = ReltioPrivileges.READ)
    public Map<String, Object> getPhysicalConfigParams(@PathVariable String envId, @PathVariable String tenantId)
            throws InvalidDocumentIdException, JsonProcessingException {
        return tenantManagementService.getPhysicalConfigParams(envId, tenantId);
    }

    /**
     * Get tenant management configuration for a given tenant ID and environment ID.
     *
     * @param envId    the environment ID
     * @param tenantId the tenant ID
     * @return the tenant management configuration as a map wrapped in a
     *         ResponseEntity
     */
    @GetMapping(value = "/tenantManagementConfig/{tenantId}")
    public ResponseEntity<Map<String, Object>> getTenantManagementConfig(@PathVariable String envId,
            @PathVariable String tenantId) throws InvalidDocumentIdException {
        Map<String, Object> config = tenantManagementService.getTenantManagementConfig();
        return ResponseEntity.ok(config);
    }

    @PutMapping(value = "/updatePhysicalConfigParams/{tenantId}")
    @ReltioSecured(resourceClass = Pms.Environment.TenantManagementSelfService.class, privileges = ReltioPrivileges.UPDATE)
    public Map<String,Object> updatePhysicalConfigParams(@RequestBody Map<String,Object> updatedParamsMap ,@PathVariable String envId,@PathVariable String tenantId) throws IOException, InvalidDocumentIdException {
        return tenantManagementService.updatePhysicalConfigParams(envId ,tenantId ,updatedParamsMap);
    }
    
   /**
     * Update tenant management configuration for a given tenant ID and environment ID.
     *
     * @param updatedConfigMap the updated tenant management configuration
     * @param envId            the environment ID
     * @param tenantId         the tenant ID
     * @return the updated tenant management configuration as a map wrapped in a
     *         ResponseEntity
     */
    @PutMapping(value = "/tenantManagementConfig/{tenantId}")
    public Map<String,Object> updateTenantManagementConfig(@RequestBody Map<String,Object> updatedConfigMap ,@PathVariable String envId,@PathVariable String tenantId) throws IOException, InvalidDocumentIdException {
        return tenantManagementService.updateTenantManagementConfig(updatedConfigMap);
    }

    @GetMapping(value = "/tenantManagementLogs/{tenantId}")
    public List<TenantsManagementLogs> getTenantManagementLogs(
            @PathVariable String envId,
            @PathVariable String tenantId,
            @RequestParam(required = false) String requesterEmail,
            @RequestParam(required = false) Long createdTime
            ) throws ExecutionException, InterruptedException {

        TenantsManagementLogsFilters filters = new TenantsManagementLogsFilters();
        filters.setEnvId(envId);
        filters.setTenantId(tenantId);
        filters.setRequesterEmail(requesterEmail);
        if (createdTime != null) {
            filters.setCreatedTime(createdTime);
        }

        return tenantManagementService.getTenantManagementLogs(filters);
    }

}
