package com.reltio.services.pms.common.model.enterprise.contracts;

import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.Getter;

import java.util.Arrays;

@Getter
public enum DTSSType {
    NPI("Data Connector NPI", PMSProductName.DT_NPI),
    DEA("Data Connector DEA", PMSProductName.DT_DEA),
    DT340B("Data Connector 340B", PMSProductName.DT_340B),
    DEFAULT("Data Connector Default", PMSProductName.DNB_CONNECTOR);

    private final String value;

    private final PMSProductName pmsProductName;

    DTSSType(String value, PMSProductName pmsProductName) {
        this.value = value;
        this.pmsProductName = pmsProductName;
    }

    public static DTSSType convertFromString(String value) {

        return Arrays.stream(DTSSType.values())
                .filter(e -> e.value.equals(value) || e.name().equals(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid value '" + value + "'"));

    }

    public static PMSProductName getPMSProductNameByDTSSType(DTSSType type) {
        return switch (type) {
            case NPI -> PMSProductName.DT_NPI;
            case DEA -> PMSProductName.DT_DEA;
            case DT340B -> PMSProductName.DT_340B;
            default -> PMSProductName.DNB_CONNECTOR;
        };
    }

}
