package com.reltio.services.pms.clients.external.aws.sqs;

import com.reltio.devops.common.environment.CloudProvider;
import com.reltio.services.pms.clients.external.aws.credentials.PlatformManagementAwsCredentials;
import com.reltio.services.pms.service.PMSConfigService;
import com.reltio.services.pms.service.Util;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.CreateQueueRequest;
import software.amazon.awssdk.services.sqs.model.DeleteQueueRequest;
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest;
import software.amazon.awssdk.services.sqs.model.QueueDoesNotExistException;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.model.GetCallerIdentityRequest;
import software.amazon.awssdk.services.sts.model.GetCallerIdentityResponse;

import java.util.Map;

@Service
public class AwsSqsService {
    private static final String SQS_ARN_FORMAT = "arn:aws:sqs:%s:%s:%s";
    private static final String SQS_URL_FORMAT = "https://sqs.%s.amazonaws.com/%s/%s";
    private static final String POLICY_ARN_FORMAT = "arn:aws:iam::%s:policy/%s";

    private final AwsCredentialsProvider provider;
    private final StsClient sts;
    private String defaultAccount;
    private final PMSConfigService pmsConfigService;

    @Autowired
    public AwsSqsService(PlatformManagementAwsCredentials credentials, SqsClientConfig sqsClientConfig, PMSConfigService pmsConfigService) {
        this.provider = sqsClientConfig.getCredentialsProvider();
        this.sts = StsClient.builder()
                .credentialsProvider(provider)
                .region(Region.of(credentials.getAWSRegion()))
                .build();
        this.pmsConfigService = pmsConfigService;
    }

    public boolean doesQueueExist(String region, String queueName) {
        SqsClient sqs = buildSqsClient(region);
        try {
            GetQueueUrlRequest getQueueUrlRequest = GetQueueUrlRequest.builder()
                    .queueName(queueName)
                    .build();
            sqs.getQueueUrl(getQueueUrlRequest);
            return true;
        } catch (QueueDoesNotExistException exception) {
            return false;
        }
    }

    public void createQueue(String region, String queueName) {
        SqsClient sqs = buildSqsClient(region);
        CreateQueueRequest request = CreateQueueRequest.builder().
                queueName(queueName).build();
        //Todo: Revisit add tag and attributes if needed
        sqs.createQueue(request);
    }

    private SqsClient buildSqsClient(String region) {
        return SqsClient.builder().credentialsProvider(provider).region(Region.of(region)).build();
    }

    public String getQueueUrl(String region, String queueName) {
        return String.format(SQS_URL_FORMAT, region, getDefaultAccount(), queueName);
    }

    public String getQueueArn(String region, String queueName) {
        return String.format(Util.processChinaArn(SQS_ARN_FORMAT, region), region, getDefaultAccount(), queueName);
    }

    public void deleteQueue(String region, String queueName) {
        SqsClient sqs = buildSqsClient(region);
        String queueUrl = getQueueUrl(region, queueName);
        DeleteQueueRequest request = DeleteQueueRequest.builder()
                .queueUrl(queueUrl)
                .build();

        sqs.deleteQueue(request);
    }

    private synchronized String getDefaultAccount() {
        if (defaultAccount == null) {
            GetCallerIdentityRequest getCallerIdentityRequest = GetCallerIdentityRequest.builder().build();
            GetCallerIdentityResponse result = sts.getCallerIdentity(getCallerIdentityRequest);
            defaultAccount = result.account();
        }
        return defaultAccount;
    }

    public String getPolicyArn(String policyName, String region) {
        return String.format(Util.processChinaArn(POLICY_ARN_FORMAT, region), getDefaultAccount(), policyName);
    }

    public String getAWSRegion(String region, String cloud) {
        String awsRegion;
        Map<String, String> mapping;

        if (cloud.equalsIgnoreCase(CloudProvider.GCP.toString())) {
            mapping = pmsConfigService.getGcpToAwsMapping();
            awsRegion = mapping.get(region);
        } else if (cloud.equalsIgnoreCase(CloudProvider.AZURE.toString())) {
            mapping = pmsConfigService.getAzureToAwsMapping();
            awsRegion = mapping.get(region);
        } else {
            awsRegion = region;
        }
        return awsRegion;
    }
}
