package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.TenantStorageConfig;
import org.springframework.stereotype.Service;

@Service
public class TenantStorageDao extends AbstractLevel1CollectionDao<TenantStorageConfig> {

    private static final String TENANT_STORAGE_CONF_COLLECTION_NAME = "TENANT_PHYSICAL_CONFIG";

    public TenantStorageDao(CredentialsProvider provider, EnvironmentDao environmentDao, ReltioUserHolder reltioUserHolder) {
        super(provider, environmentDao, TENANT_STORAGE_CONF_COLLECTION_NAME, reltioUserHolder);
    }

    @Override
    protected TypeReference<TenantStorageConfig> getTypeReference() {
        return new TypeReference<TenantStorageConfig>() {
        };
    }

}
