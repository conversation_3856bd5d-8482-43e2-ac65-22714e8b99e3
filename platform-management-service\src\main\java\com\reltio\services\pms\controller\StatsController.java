package com.reltio.services.pms.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.service.StatsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/v1", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name= "Provide Stats")
@ReltioSecured(resourceClass = Pms.Environment.class)
public class StatsController {
    private final StatsService statsService;

    @Autowired
    public StatsController(StatsService statsService) {
        this.statsService = statsService;
    }

    @GetMapping(value = "/getFreemiumStats")
    public JsonNode getFreemiumSignupStats() {
        return statsService.getFreemiumSignUpStats();
    }

    @GetMapping(value = "/getEnterpriseJobStats")
    public JsonNode getEnterpriseJobStats(@RequestParam(value = "start", required = false, defaultValue = "1633089537000") Long start,
                                          @RequestParam(value = "end", required = false) Long end) {
        if (end == null) {
            end = System.currentTimeMillis();
        }
        return statsService.getEnterpriseJobsStats(start, end);
    }

    @GetMapping(value = "/getDetailedTaskStats")
    public JsonNode getEnterpriseStats(@RequestParam(value = "start", required = false, defaultValue = "1633089537000") Long start,
                                       @RequestParam(value = "end", required = false) Long end,
                                       @RequestParam(value = "describeFailed", required = false, defaultValue = "false") boolean describeFailed) {
        if (end == null) {
            end = System.currentTimeMillis();
        }
        return statsService.getDetailedTaskStats(start, end, describeFailed);
    }
}
