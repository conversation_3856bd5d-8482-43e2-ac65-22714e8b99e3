package com.reltio.services.pms.common.model.supportability;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

/**
 * Indexation
 * Created by a<PERSON><PERSON><PERSON>
 */
@Value
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Indexation {
    IndexationStats cluster;
    IndexationStats tenant;
}
