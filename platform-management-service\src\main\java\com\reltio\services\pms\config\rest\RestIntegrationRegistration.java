package com.reltio.services.pms.config.rest;

import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;

/**
 * <AUTHOR>
 * @since 18.11.2022
 */
public class RestIntegrationRegistration {
    private final String id;
    private final String accessTokenUri;
    private final String clientId;
    private final String clientSecret;
    private final AuthorizationGrantType grantType;
    private final String username;
    private final String password;
    private final ClientAuthenticationMethod authorizationMethod;

    private RestIntegrationRegistration(String id,
                                        String accessTokenUri,
                                        String clientId,
                                        String clientSecret,
                                        AuthorizationGrantType grantType,
                                        String username,
                                        String password,
                                        ClientAuthenticationMethod authorizationMethod) {
        this.id = id;
        this.accessTokenUri = accessTokenUri;
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.grantType = grantType;
        this.username = username;
        this.password = password;
        this.authorizationMethod = authorizationMethod;
    }

    public String getId() {
        return id;
    }

    public String getAccessTokenUri() {
        return accessTokenUri;
    }

    public String getClientId() {
        return clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public AuthorizationGrantType getGrantType() {
        return grantType;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public ClientAuthenticationMethod getAuthorizationMethod() {
        return authorizationMethod;
    }

    public static Builder withId(String id) {
        return new Builder(id);
    }

    public static class Builder {
        private String id;
        private String accessTokenUri;
        private String clientId;
        private String clientSecret;
        private AuthorizationGrantType grantType;
        private String username;
        private String password;
        private ClientAuthenticationMethod authorizationMethod;

        private Builder(String id) {
            this.id = id;
        }

        public Builder setAccessTokenUri(String accessTokenUri) {
            this.accessTokenUri = accessTokenUri;
            return this;
        }

        public Builder setClientId(String clientId) {
            this.clientId = clientId;
            return this;
        }

        public Builder setClientSecret(String clientSecret) {
            this.clientSecret = clientSecret;
            return this;
        }

        public Builder setGrantType(AuthorizationGrantType grantType) {
            this.grantType = grantType;
            return this;
        }

        public Builder setUsername(String username) {
            this.username = username;
            return this;
        }

        public Builder setPassword(String password) {
            this.password = password;
            return this;
        }

        public Builder setAuthorizationMethod(ClientAuthenticationMethod authorizationMethod) {
            this.authorizationMethod = authorizationMethod;
            return this;
        }

        public RestIntegrationRegistration build() {
            return new RestIntegrationRegistration(id, accessTokenUri, clientId, clientSecret, grantType, username, password, authorizationMethod);
        }
    }
}
