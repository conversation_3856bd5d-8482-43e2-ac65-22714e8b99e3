package com.reltio.services.pms.clients.reltio.auth;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.reltio.services.pms.common.PMSRestTemplate;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.OfferType;
import com.reltio.services.pms.common.model.User;
import com.reltio.services.pms.common.model.auth.AuthCustomer;
import com.reltio.services.pms.common.model.client.Client;
import com.reltio.services.pms.common.model.config.MFA;
import com.reltio.services.pms.common.sales.model.AccountStatus;
import com.reltio.services.pms.service.Util;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class ReltioAuthClient {

    private static final String USER_URL_PATTERN = "%s/oauth/users";
    private static final String CUSTOMER_URL_PATTERN = "%s/oauth/customers";
    private static final String CLIENT_URL_PATTERN = "%s/oauth/clients";
    private static final String CLIENT_URL_WITH_CUSTOMER_ID_PATTERN = "%s/oauth/customers/%s/clients";
    private static final String CUSTOMER_NAME_PATTERN = "%s-%s";
    private static final String GROUP_URL_PATTERN = "%s/oauth/customers/%s/groups/%s";
    private static final String GROUP_PATTERN = "%s/oauth/customers/%s/groups/";
    private static final String ROLE_URL_PATTERN = "%s/oauth/customers/%s/roles/permissions";
    private static final String USER_ROLES_URL_SUFFIX = "/tenantRoles";
    private static final String TENANT_SAML_CONFIGURATION_URL_PATTERN = "%s/%s/%s";
    private static final String SKIP_TOKEN_REVOCATION  = "?skipTokenRevocation=true";
    private static final String ROLES = "roles";
    private static final Pattern EMAIL_REGEX = Pattern.compile("^[\\p{L}0-9!#$%&'*+/=?^_`{|}~-][\\p{L}0-9.!#$%&'*+/=?^_`{|}~-]{0,63}@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    private static final Pattern USER_REGEX = Pattern.compile("^[\\p{L}0-9!#$%&'*+\\/=?^_`{|}~-][\\p{L}0-9.!#$%&'*+\\/=?^_`{|}~-]{0,63}");

    private static final int LENGTH_POSTFIX_CUSTID = 4;
    private static final int DEFAULT_LENGTH_CUSTID = 8;
    private static final Logger LOGGER = Logger.getLogger(ReltioAuthClient.class);
    private final PMSRestTemplate pmsRest;

    private final ObjectMapper mapper;

    @Value("${reltio.auth.server.url}")
    private final String authUrl;

    @Autowired
    public ReltioAuthClient(@Value("${reltio.auth.server.url}") String authUrl, PMSRestTemplate pmsRest) {
        this.authUrl = authUrl;
        this.mapper = new ObjectMapper();
        Set<String> properties = Collections.singleton(authUrl);
        PropertiesValidator.validateProperties(properties, LOGGER);
        this.pmsRest = pmsRest;
    }

    public boolean doesCustomerExist(String customerName) {
        return getCustomer(customerName) != null;
    }

    public AuthCustomer getCustomer(String customerName) {
        String url = String.format(CUSTOMER_URL_PATTERN, authUrl) + "/" + customerName;
        try {
            ResponseEntity<JsonNode> response = pmsRest.getEntity(url, JsonNode.class);
            return mapper.convertValue(response.getBody(), AuthCustomer.class);
        } catch (HttpClientErrorException.NotFound | HttpClientErrorException.BadRequest exception) {
            LOGGER.warn(String.format("HTTP Error while get Auth Customer %s, Error: %s", customerName,
                    exception.getMessage()), exception);
            return null;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public AuthCustomer createCustomer(AuthCustomer authCustomer) {
        String url = String.format(CUSTOMER_URL_PATTERN, authUrl);
        try {
            AuthCustomer proxyAuthCustomer = getProxyAuthCustomer(authCustomer);
            AuthCustomer[] authCustomers = pmsRest.postForObject(url, Collections.singletonList(proxyAuthCustomer), AuthCustomer[].class);
            Objects.requireNonNull(authCustomers, "Got Null as response from Auth Server");
            return authCustomers[0];
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    @NotNull
    private static AuthCustomer getProxyAuthCustomer(AuthCustomer authCustomer) {
        MFA mfa = new MFA("ENFORCE", Arrays.asList("EMAIL","AUTHENTICATOR"));
        return new AuthCustomer(authCustomer.getID(), authCustomer.getTenants(), authCustomer.getClients(), authCustomer.getCustomSettings(), authCustomer.isCaseSensitiveLoginEnabled(),
                        authCustomer.getSalesAccountId(), authCustomer.getCustomerType(), authCustomer.getSalesContractId(), authCustomer.getOwners(), authCustomer.getDivision(), authCustomer.getDepartment(), authCustomer.getCostCenter(), AccountStatus.ACTIVE, mfa);
    }

    public Set<String> getClients() {
        String url = String.format(CLIENT_URL_PATTERN, authUrl);
        try {
            Client[] clients = pmsRest.getForObject(url, Client[].class);
            Objects.requireNonNull(clients, "Got Null as response from Auth Server");
            return Arrays.stream(clients).map(Client::getClientId).collect(Collectors.toSet());
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public boolean doesClientExist(String clientId) {
        return getClients().contains(clientId);
    }

    public Client createClient(Client client) {
        String url = String.format(CLIENT_URL_PATTERN, authUrl);
        try {
            Client[] response = pmsRest.postForObject(url, Collections.singletonList(client), Client[].class);
            Objects.requireNonNull(response, "Got Null as response from Auth Server");
            return response[0];
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public Client createClientAndAddToCustomer(Client client,String customerId) {
        String url = String.format(CLIENT_URL_WITH_CUSTOMER_ID_PATTERN, authUrl,customerId);
        try {
            Client[] response = pmsRest.postForObject(url, Collections.singletonList(client), Client[].class);
            Objects.requireNonNull(response, "Got Null as response from Auth Server");
            return response[0];
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public void updateCustomerTenants(AuthCustomer authCustomer) {
        String url = String.format(CUSTOMER_URL_PATTERN, authUrl) + "/" + authCustomer.getID();
        try {
            Map<String, Object> customerDetails = pmsRest.getForObject(url, Map.class);
            Objects.requireNonNull(customerDetails, "Got Null as response from Auth Server");
            List<String> currentTenants = (List<String>) customerDetails.get("tenants");
            currentTenants.addAll(authCustomer.getTenants());
            customerDetails.put("tenants", currentTenants);
            pmsRest.put(url, customerDetails);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public boolean doesUserExist(String userName) {
        return getUser(userName) != null;
    }

    public boolean doesUserExist(User user) {
        return user != null;
    }

    public User getUser(String userName) {
        if (StringUtils.isBlank(userName)) {
            return null;
        }

        if (!EMAIL_REGEX.matcher(userName).matches() && !USER_REGEX.matcher(userName).matches()) {
            return null;
        }

        try {
            String url = String.format(USER_URL_PATTERN, authUrl) + "/" + userName;
            return pmsRest.getObject(url, User.class);
        } catch (HttpClientErrorException.NotFound | HttpClientErrorException.BadRequest  exception) {
            LOGGER.warn(String.format("HTTP Error while get User Name %s, Error: %s", userName,
                    exception.getMessage()), exception);
            return null;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    @Cacheable(cacheNames = "allUsers")
    public List<User> getAllUsers() {
        String url = String.format(USER_URL_PATTERN, authUrl);
        try {
            String users = String.valueOf(pmsRest.getForObject(url, String.class));
            return mapper.readValue(users, new TypeReference<>() {
            });
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public User createUser(User user) {
        String url = String.format(USER_URL_PATTERN, authUrl);
        try {
            User[] response = pmsRest.postForObject(url, Collections.singletonList(user), User[].class);
            Objects.requireNonNull(response, "Got Null as response from Auth Server");
            return response[0];
        } catch (Exception e) {
            LOGGER.error(String.format("Error while creating Auth user %s, Error: %s", user.getUsername(),
                    e.getMessage()), e);
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public User updateUser(User user) {
        String url = String.format(USER_URL_PATTERN, authUrl) + "/" + user.getEmail();
        try {
            pmsRest.put(url, user);
            return user;
        } catch (Exception e) {
            LOGGER.error(String.format("Error while updating Auth user %s, Error: %s", user.getUsername(),
                    e.getMessage()), e);
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public void updateUserSingleProperty(String userName, String property, String value) {
        String url = String.format(USER_URL_PATTERN, authUrl) + "/" + userName;
        try {
            Map<String, Object> userDetails = pmsRest.getForObject(url, Map.class);
            Objects.requireNonNull(userDetails, "Got Null as response from Auth Server");
            userDetails.put(property, value);
            pmsRest.put(url, userDetails);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public User updateUserRoles(User user, boolean skipTokenRevocation) {
        String url = String.format(USER_URL_PATTERN, authUrl) + "/" + user.getEmail() + USER_ROLES_URL_SUFFIX;
        if (skipTokenRevocation) {
            url = url + SKIP_TOKEN_REVOCATION;
        }
        Map<String, Map<String, Set<String>>> tenantRoles = new HashMap<>();
        tenantRoles.put(ROLES, user.getUserPermissions().getRoles());
        try {
            pmsRest.put(url, tenantRoles);
            return user;
        } catch (Exception e) {
            LOGGER.error(String.format("Error while creating User Roles for user %s, Error: %s", user.getUsername(),
                    e.getMessage()), e);
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public String getUniqueCustId(String prefix) {
        String custId;
        if (prefix != null && !prefix.isEmpty()) {
            custId = String.format(CUSTOMER_NAME_PATTERN, prefix.toLowerCase(), Util.getRandomAlfanumeric(LENGTH_POSTFIX_CUSTID));
        } else {
            custId = Util.getRandomAlfanumeric(DEFAULT_LENGTH_CUSTID);
        }
        if (!doesCustomerExist(custId)) {
            return custId;
        } else {
            return getUniqueCustId(prefix);
        }
    }

    public JsonNode createCustomerRole(String custId, JsonNode roleBody) {
        String url = String.format(ROLE_URL_PATTERN, authUrl, custId);
        try {
            ResponseEntity<JsonNode> response = pmsRest.postForEntity(url, roleBody, JsonNode.class);
            return mapper.convertValue(response.getBody(), JsonNode.class);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public void dissociateApplicationClientsFromAuthCustomer(String authCustomerId) {
        String url = String.format(CUSTOMER_URL_PATTERN, authUrl) + "/" + authCustomerId;
        try {
            Map<String, Object> customerDetails = pmsRest.getForObject(url, Map.class);
            Objects.requireNonNull(customerDetails, "Got Null as response from Auth Server");
            List<String> currentClients = (List<String>) customerDetails.get("applicationClients");
            currentClients.clear();
            pmsRest.put(url, customerDetails);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public void deleteClient(String name) {
        String url = String.format(CLIENT_URL_PATTERN, authUrl) + "/" + name;
        deleteResource(url);
    }

    public void deleteCustomer(String name) {
        String url = String.format(CUSTOMER_URL_PATTERN, authUrl) + "/" + name;
        deleteResource(url);
    }

    public void deleteUser(String name) {
        String url = String.format(USER_URL_PATTERN, authUrl) + "/" + name;
        deleteResource(url);
    }

    private void deleteResource(String url) {
        try {
            pmsRest.delete(url);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public boolean isFreemiumCustomer(String customerName) {
        List<String> freemiumCustNames = Arrays.stream(OfferType.values()).map(o -> o.toString().toLowerCase(Locale.ROOT)).toList();
        return freemiumCustNames.stream().anyMatch(customerName::contains);
    }

    public boolean isUserFreemiumOnly(User user) {
        return isFreemiumCustomer(user.getCustomer());
    }

    @SuppressWarnings("Duplicates")
    public JsonNode getCustomerObjects() {
        String url = String.format(CUSTOMER_URL_PATTERN, authUrl);
        try {
            ResponseEntity<JsonNode> response = pmsRest.getForEntity(url, JsonNode.class);
            return response.getBody();
        } catch (HttpClientErrorException.NotFound | HttpClientErrorException.BadRequest exception) {
            LOGGER.warn(String.format("HTTP Error while get Auth Customer Objects %s, Error: %s", url,
                    exception.getMessage()), exception);
            return null;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    @SuppressWarnings("Duplicates")
    public JsonNode getClientsObjects() {
        String url = String.format(CLIENT_URL_PATTERN, authUrl);
        try {
            ResponseEntity<JsonNode> response = pmsRest.getForEntity(url, JsonNode.class);
            return response.getBody();
        } catch (HttpClientErrorException.NotFound | HttpClientErrorException.BadRequest exception) {
            LOGGER.warn(String.format("HTTP Error while get Auth Client Objects %s, Error: %s", url,
                    exception.getMessage()), exception);
            return null;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public void assignTenantRolesToUser(String userName, Set<String> roles, String tenantId) {
        updateUserSingleProperty(userName,"enabled", "true");
        User user = getUser(userName);
        user.addUserPermissions(roles, Collections.singleton(tenantId));
        updateUserRoles(user, true);
    }

    public void assignTenantListRolesToUser(String userName, Set<String> roles, Set<String> tenantIds) {
        User user = getUser(userName);
        if (user == null) {
            throw new PlatformManagementException(PlatformManagementErrorCode.USER_FOR_ZENDESK_NOT_FOUND, HttpStatus.NOT_FOUND.value());
        }
        user.addUserPermissions(roles, tenantIds);
        updateUserRoles(user, false);
    }

    public void removeTenantRolesToUser(String userName, Set<String> roles, String tenantId, boolean skipTokenRevocation) {
        User user = getUser(userName);
        user.removeUserPermissions(roles, Collections.singleton(tenantId));
        updateUserRoles(user, skipTokenRevocation);
    }

    public JsonNode getUsersForTenant(String tenantId) {
        String url = String.format(USER_URL_PATTERN, authUrl) + "/tenant/" + tenantId;
        try {
            ResponseEntity<JsonNode> response = pmsRest.getForEntity(url, JsonNode.class);
            return response.getBody();
        } catch (HttpClientErrorException.NotFound | HttpClientErrorException.BadRequest exception) {
            LOGGER.warn(String.format("HTTP Error while get Users for Tenants %s, Error: %s", url,
                    exception.getMessage()), exception);
            return null;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public List<String> getAllMatchingUsers(String user) {
        List<String> matchingUser = new ArrayList<>();
        List<User> allExistingUsers = getAllUsers();
        for (User existingUser : allExistingUsers) {
            if (existingUser.getEmail().equalsIgnoreCase(user) || existingUser.getUsername().equalsIgnoreCase(user)) {
                matchingUser.add(existingUser.getEmail());
            }
        }
        return matchingUser;
    }

    public void updateGroupForCustomerWithTenants(String custId, String groupId, Set<String> rolesToModify, Set<String> tenants) {
        String url = String.format(GROUP_URL_PATTERN, authUrl, custId, groupId) + SKIP_TOKEN_REVOCATION;
        try {
            ResponseEntity<JsonNode> response = pmsRest.getForEntity(url, JsonNode.class);
            Map<String, Object> responseBody = mapper.convertValue(response.getBody(), Map.class);
            Map<String, Object> roles = (Map<String, Object>) responseBody.get(ROLES);
            for (String role : rolesToModify) {
                List<String> addTenantToRole = (List<String>) roles.get(role);
                addTenantToRole.addAll(tenants);
            }
            pmsRest.put(url, responseBody);
        } catch (HttpClientErrorException.NotFound | HttpClientErrorException.BadRequest exception) {
            LOGGER.warn(String.format("Group %s does not exist for customer %s", groupId, custId));
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public List<User> getAuthUsersListFromTenant(String tenantId) {
        JsonNode jsonNode = getUsersForTenant(tenantId);
        List<User> users = new ArrayList<>();
        if (jsonNode != null) {
            Iterator<JsonNode> elements = jsonNode.elements();
            while (elements.hasNext()) {
                JsonNode userNode = elements.next();
                User user = mapper.convertValue(userNode, User.class);
                users.add(user);
            }
        }
        return users;
    }

    public Set<String> getCustomersForTenant(String tenantId) {
        String url = String.format(CUSTOMER_URL_PATTERN, authUrl);

        try {
            AuthCustomer[] allCustomers = pmsRest.getForObject(url, AuthCustomer[].class);

            if (allCustomers == null || allCustomers.length == 0) {
                LOGGER.warn("No customers were returned from Auth server, or got null response.");
                return Collections.emptySet();
            }

            Set<String> matchingCustomers = new HashSet<>();
            for (AuthCustomer customer : allCustomers) {
                if (customer.getTenants() != null && customer.getTenants().contains(tenantId)) {
                    matchingCustomers.add(customer.getID());
                }
            }
            return matchingCustomers;

        } catch (Exception e) {
            LOGGER.error("Error while fetching or parsing Auth customers from Auth server", e);
            throw new PlatformManagementException(
                    PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e
            );
        }
    }

    public JsonNode getGroupForCustomer(String customerId) {
        String url = String.format(GROUP_PATTERN, authUrl, customerId);

        try {
            ResponseEntity<JsonNode> response = pmsRest.getForEntity(url, JsonNode.class);
            return response.getBody();
        }catch (Exception e) {
            throw new PlatformManagementException(
                    PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(),
                    e
            );
        }
    }

    public Map<String, Set<String>> getGroupsForTheTenant(String tenantId) {
        Map<String, Set<String>> groupsWithCustomer = new HashMap<>();
        JsonNode jsonNode = getUsersForTenant(tenantId);
        if (jsonNode != null) {
            for (JsonNode userNode : jsonNode) {
                Set<String> groups = new HashSet<>();
                JsonNode groupNode = userNode.get("groups");
                String customer = userNode.get("customer").asText();
                if (groupNode != null && groupNode.isArray()) {
                    for (JsonNode group : groupNode) {
                        groups.add(group.asText());
                    }
                    groupsWithCustomer.put(customer, groups);
                }
            }
        }
        return groupsWithCustomer;
    }

    public void removeTenantFromCustomer(String authCustomerId, String tenantId, String samlConfigUrl) {
        String url = String.format(CUSTOMER_URL_PATTERN, authUrl) + "/" + authCustomerId;
        try {
            Map<String, Object> customerDetails = pmsRest.getForObject(url, Map.class);
            Objects.requireNonNull(customerDetails, "Got Null as response from Auth Server");
            List<String> currentTenants = (List<String>) customerDetails.get("tenants");
            removeConfigurationsFromTenant(customerDetails, tenantId, samlConfigUrl);
            currentTenants.remove(tenantId);
            customerDetails.put("tenants", currentTenants);
            pmsRest.put(url, customerDetails);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public void removeTenantFromGroups(String custId, String groupId, String tenantId) {
        String url = String.format(GROUP_URL_PATTERN, authUrl, custId, groupId);
        try {
            ResponseEntity<JsonNode> response = pmsRest.getForEntity(url, JsonNode.class);
            Map<String, Object> responseBody = mapper.convertValue(response.getBody(), Map.class);
            Map<String, List<String>> roles = (Map<String, List<String>>) responseBody.get(ROLES);
            for (Map.Entry<String, List<String>> entry : roles.entrySet()) {
                List<String> tenants = entry.getValue();
                tenants.remove(tenantId);
            }
            pmsRest.put(url + SKIP_TOKEN_REVOCATION, responseBody);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public void removeConfigurationsFromTenant(Map<String, Object> customerDetails, String tenantId, String samlConfigUrl) {
        String customerId = (String) customerDetails.get("id");
        List<Map<String, Object>> externalProviderConfigList = (List<Map<String, Object>>) customerDetails.get("externalProviderConfig");
        if (!externalProviderConfigList.isEmpty()) {
            for (Map<String, Object> externalProviderConfig : externalProviderConfigList) {
                if (externalProviderConfig.get("tenants") != null) {
                    List<String> tenants = (List<String>) externalProviderConfig.get("tenants");
                    if (tenants.contains(tenantId)) {
                        String vendor = (String) externalProviderConfig.get("vendor");
                        if (vendor.equals("cognito")) {
                            removeSamlConfiguration(customerId, tenantId, externalProviderConfig, externalProviderConfigList, samlConfigUrl);
                        } else {
                            removeOidcConfiguration(tenants, tenantId, externalProviderConfig, externalProviderConfigList);
                        }
                        break;
                    }
                }
            }
        }

    }

    public void removeSamlConfiguration(String customerId, String tenantId, Map<String, Object> externalProviderConfig,
                                        List<Map<String, Object>> externalProviderConfigList, String samlConfigUrl) {
        String tenantSamlConfigurationUrl = String.format(TENANT_SAML_CONFIGURATION_URL_PATTERN, samlConfigUrl, customerId, tenantId);
        try {
            pmsRest.delete(tenantSamlConfigurationUrl);
            externalProviderConfigList.remove(externalProviderConfig);

        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

    public void removeOidcConfiguration(List<String> tenants, String tenantId,
                                        Map<String, Object> externalProviderConfig,
                                        List<Map<String, Object>> externalProviderConfigList) {
        tenants.remove(tenantId);
        if (tenants.isEmpty()) {
            externalProviderConfigList.remove(externalProviderConfig);
        }
    }

    public void setPasswordForAuthUser(String passwordForAuthUser, String user) {
        String url = String.format(USER_URL_PATTERN, authUrl) + "/" + user;
        try {
            Map<String, Object> userDetails = pmsRest.getForObject(url, Map.class);
            Objects.requireNonNull(userDetails, "Got Null as response from Auth Server");
            userDetails.put("password", passwordForAuthUser);
            pmsRest.put(url, userDetails);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }

    }

    public void deleteGroup(String customerId, String groupId) {
        String url = String.format(GROUP_URL_PATTERN, authUrl, customerId, groupId);
        try {
            pmsRest.delete(url);
        } catch (Exception e) {
            throw new PlatformManagementException(
                    PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(),
                    e, e.getMessage()
            );
        }
    }

    public boolean isGroupEmpty(String customerId, String groupId) {
        String url = String.format(GROUP_URL_PATTERN, authUrl, customerId, groupId);
        boolean isEmpty = true;

        try {
            ResponseEntity<JsonNode> response = pmsRest.getForEntity(url, JsonNode.class);
            JsonNode groupJson = response.getBody();

            if (groupJson != null) {
                JsonNode rolesNode = groupJson.get("roles");
                if (rolesNode != null && rolesNode.isObject()) {
                    Iterator<String> roleNames = rolesNode.fieldNames();
                    while (roleNames.hasNext()) {
                        String role = roleNames.next();
                        JsonNode tenantsArray = rolesNode.get(role);
                        if (tenantsArray.isArray() && !tenantsArray.isEmpty()) {
                            isEmpty = false;
                            break;
                        }
                    }
                }
            }
        } catch (HttpClientErrorException.NotFound | HttpClientErrorException.BadRequest exception) {
            LOGGER.warn("Group " + groupId + " not found for customer " + customerId
                    + " . Considering it empty. Error: "
                    + exception.getMessage(), exception);
        } catch (Exception e) {
            throw new PlatformManagementException(
                    PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(),
                    e
            );
        }

        return isEmpty;
    }


}
