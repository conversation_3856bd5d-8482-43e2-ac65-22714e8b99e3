package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.tenant.Invitee;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class UserDao extends AbstractLevel1CollectionDao<Invitee> {
    private static final String USERS_COLLECTION_NAME = "USERS";

    @Autowired
    public UserDao(CredentialsProvider provider, ReltioTenantDao reltioTenantDao,
                   ReltioUserHolder reltioUserHolder) {
        super(provider, reltioTenantDao, USERS_COLLECTION_NAME, reltioUserHolder);
    }

    @Override
    protected TypeReference<Invitee> getTypeReference() {
        return new TypeReference<Invitee>() {
        };
    }

    public Map<String, Integer> getFilteredWithActivenessCounts(String rootDocId, String filterField, String value) {
        Collection<Invitee> allFiltered = getAllFiltered(rootDocId, filterField, value);
        Map<String, Integer> counts = new HashMap<>();
        counts.put("total", allFiltered.size());
        counts.put("active", allFiltered.stream().filter(Invitee::isActive).collect(Collectors.toList()).size());
        counts.put("inactive", allFiltered.stream().filter(user -> !user.isActive()).collect(Collectors.toList()).size());
        return counts;
    }

    public Map<String, Integer> getAllWithActivenessCounts(String rootDocId) {
        Collection<Invitee> all = getAll(rootDocId);
        Map<String, Integer> counts = new HashMap<>();
        counts.put("total", all.size());
        counts.put("active", all.stream().filter(Invitee::isActive).collect(Collectors.toList()).size());
        counts.put("inactive", all.stream().filter(user -> !user.isActive()).collect(Collectors.toList()).size());
        return counts;
    }
}
