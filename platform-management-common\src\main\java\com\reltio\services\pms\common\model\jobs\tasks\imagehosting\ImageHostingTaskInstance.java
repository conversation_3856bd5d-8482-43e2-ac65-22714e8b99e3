package com.reltio.services.pms.common.model.jobs.tasks.imagehosting;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServiceEnablementBaseTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.common.sales.TenantPurpose;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
@JsonDeserialize(using = ImageHostingTaskInstanceDeserializer.class)
public class ImageHostingTaskInstance extends ServiceEnablementBaseTaskInstance {

    @JsonProperty(value = "tenantPurpose")
    private final TenantPurpose tenantPurpose;
    @JsonProperty(value = "createFoldersIfFailedWhileBucketCreated")
    private final List<Boolean> createFoldersIfFailedWhileBucketCreated;
    @JsonProperty(value = "tenantId")
    private String tenantId;
    @JsonProperty(value = "customerId")
    private String customerId;

    @JsonCreator
    public ImageHostingTaskInstance(@JsonProperty(value = "id") String id,
                                    @JsonProperty(value = "name") String name,
                                    @JsonProperty(value = "jobId") String jobId,
                                    @JsonProperty(value = "startTime") Long startTime,
                                    @JsonProperty(value = "finishTime") Long finishTime,
                                    @JsonProperty(value = "status") TaskStatus status,
                                    @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                    @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                    @JsonProperty(value = "executingNodeName") String executingNodeName,
                                    @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                    @JsonProperty(value = "envId") String envId,
                                    @JsonProperty(value = "tenantId") String tenantId,
                                    @JsonProperty(value = "customerId") String customerId,
                                    @JsonProperty(value = "tenantPurpose") TenantPurpose tenantPurpose,
                                    @JsonProperty(value = "failedTenants") Set<String> failedTenants,
                                    @JsonProperty(value = "totalProcessedTenants", defaultValue = "0") int totalProcessedTenants,
                                    @JsonProperty(value = "tenantsForServiceEnablement") Set<String> tenantsForServiceEnablement,
                                    @JsonProperty(value = "events") Map<String, Set<String>> events,
                                    @JsonProperty(value = "createFoldersIfFailedWhileBucketCreated") List<Boolean> createFoldersIfFailedWhileBucketCreated) {
        super(id, name, jobId, startTime, finishTime, TaskType.IMAGE_HOSTING_ENABLEMENT_TASK, status, lastUpdatedTime,
                taskFailureContext, executingNodeName, serviceNodeStatus, envId, failedTenants, totalProcessedTenants,
                tenantsForServiceEnablement, events);
        this.customerId = customerId;
        this.tenantId = tenantId;
        this.tenantPurpose = tenantPurpose;
        this.createFoldersIfFailedWhileBucketCreated = createFoldersIfFailedWhileBucketCreated == null ? new ArrayList<>() : new ArrayList<>(createFoldersIfFailedWhileBucketCreated);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ImageHostingTaskInstance)) return false;
        if (!super.equals(o)) return false;
        ImageHostingTaskInstance that = (ImageHostingTaskInstance) o;
        return getTenantId().equals(that.getTenantId()) && getCustomerId().equals(that.getCustomerId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getTenantId(), getCustomerId());
    }

    public void addCreateFoldersIfFailedWhileBucketCreated(Boolean value) {
        createFoldersIfFailedWhileBucketCreated.add(value);
    }

}
