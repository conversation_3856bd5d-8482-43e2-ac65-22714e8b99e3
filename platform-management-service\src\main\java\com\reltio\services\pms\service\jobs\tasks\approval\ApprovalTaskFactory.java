package com.reltio.services.pms.service.jobs.tasks.approval;

import com.reltio.services.pms.common.model.jobs.tasks.approve.ApprovalTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.common.model.proxy.approve.ApprovalSecureAction;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.TaskFactory;
import com.reltio.services.pms.service.notification.EmailNotificationService;
import com.reltio.services.pms.service.proxy.SecureActionProxyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ApprovalTaskFactory implements TaskFactory<ApprovalTaskInstance, ApprovalTaskExecutionService> {

    @Autowired
    private EmailNotificationService emailNotificationService;

    @Autowired
    private SecureActionProxyService<ApprovalSecureAction> secureActionProxyService;

    @Override
    public TaskType getTaskType() {
        return TaskType.APPROVAL_TASK;
    }

    @Override
    public ApprovalTaskExecutionService createTask(String jobId, ApprovalTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService) {
        return new ApprovalTaskExecutionService(jobId, taskDetail, grafanaDashboardGBQService, emailNotificationService, secureActionProxyService);
    }
}
