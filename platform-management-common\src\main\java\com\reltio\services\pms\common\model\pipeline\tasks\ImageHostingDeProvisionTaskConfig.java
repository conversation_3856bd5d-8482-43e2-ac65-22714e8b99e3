package com.reltio.services.pms.common.model.pipeline.tasks;

import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.EqualsAndHashCode;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

@EqualsAndHashCode(callSuper = false)
public class ImageHostingDeProvisionTaskConfig extends AbstractPipelineTaskConfig {

    @JsonCreator
    public ImageHostingDeProvisionTaskConfig(@JsonProperty(value = "name")String name) {
        super(name,TaskType.IMAGE_HOSTING_DE_PROVISION_TASK);
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.IMAGE_HOSTING;
    }

    @Override
    public String toString() {
        return TaskType.IMAGE_HOSTING_DE_PROVISION_TASK_NAME + "{}";
    }
}
