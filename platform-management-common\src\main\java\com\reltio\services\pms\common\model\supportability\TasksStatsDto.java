package com.reltio.services.pms.common.model.supportability;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;
import java.util.Objects;

/**
 * TasksStats
 * Created by apylkov
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class TasksStatsDto implements StatsDto {
    @JsonProperty("startTime")
    private Long startTime;
    @JsonProperty("endTime")
    private Long endTime;
    @JsonProperty("completed")
    private Long completed;
    @JsonProperty("processing")
    private Long processing;
    @JsonProperty("scheduled")
    private Long scheduled;
    @JsonProperty("failed")
    private Long failed;
    @JsonProperty("canceled")
    private Long canceled;


    public TasksStatsDto(Long startTime, Long endTime, Map<TaskStatus, Long> data) {
        this(startTime, endTime,
                data.getOrDefault(TaskStatus.COMPLETED, 0L),
                data.getOrDefault(TaskStatus.PROCESSING, 0L),
                data.getOrDefault(TaskStatus.SCHEDULED, 0L),
                data.getOrDefault(TaskStatus.FAILED, 0L),
                data.getOrDefault(TaskStatus.CANCELED, 0L));
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TasksStatsDto tasksStatsDto = (TasksStatsDto) o;
        return Objects.equals(getStartTime(), tasksStatsDto.getStartTime()) &&
                Objects.equals(getEndTime(), tasksStatsDto.getEndTime()) &&
                Objects.equals(getFailed(), tasksStatsDto.getFailed()) &&
                Objects.equals(getCompleted(), tasksStatsDto.getCompleted()) &&
                Objects.equals(getScheduled(), tasksStatsDto.getScheduled()) &&
                Objects.equals(getProcessing(), tasksStatsDto.getProcessing()) &&
                Objects.equals(getCanceled(), tasksStatsDto.getCanceled());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getStartTime(), getEndTime(), getFailed(), getCompleted(), getScheduled(), getProcessing());
    }
}
