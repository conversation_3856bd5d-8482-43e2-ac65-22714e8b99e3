package com.reltio.services.pms.common.model.supportability.result.combiner;

import com.reltio.collection.CollectionUtils;
import com.reltio.services.pms.common.model.supportability.ProcessorTypeGroupEventStatsDto;
import com.reltio.services.pms.common.model.supportability.Processors;
import com.reltio.services.pms.common.model.supportability.result.StatsCombiner;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * The type Event stats combiner.
 */
public class ProcessorTypeGroupEventStatsCombiner implements StatsCombiner<ProcessorTypeGroupEventStatsDto> {
    /**
     * Combine list.
     *
     * @param dtoList the dto list
     * @return the list
     */
    @Override
    public List<ProcessorTypeGroupEventStatsDto> combine(List<ProcessorTypeGroupEventStatsDto> dtoList) {
        List<ProcessorTypeGroupEventStatsDto> valueList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(dtoList)) {
            valueList.add(mergeAllRows(dtoList));
        }
        return valueList;
    }

    /**
     * mergeAllProcessorRows method is used to merge all the provided rows from GBQ into single record by sum/avg based on the column in GBQ.
     *
     * @param dtoList the dto list
     * @return the list
     */
    @Override
    public ProcessorTypeGroupEventStatsDto mergeAllRows(List<ProcessorTypeGroupEventStatsDto> dtoList) {
        ProcessorTypeGroupEventStatsDto response = dtoList.get(NumberUtils.INTEGER_ZERO);
        ProcessorTypeGroupEventStatsDto lastResponse = dtoList.get(dtoList.size() - NumberUtils.INTEGER_ONE);
        long startTime = response.getStartTime();
        long endTime = lastResponse.getEndTime();
        long totalCount = NumberUtils.INTEGER_ZERO;
        long failedCount = NumberUtils.INTEGER_ZERO;
        long successfulCount = NumberUtils.INTEGER_ZERO;
        long avgSum = NumberUtils.INTEGER_ZERO;
        List<Processors> byCount = new ArrayList<>();
        List<Processors> byLatency = new ArrayList<>();
        for (ProcessorTypeGroupEventStatsDto dto : dtoList) {
            byCount.addAll(dto.getProcessorsByCount());
            byLatency.addAll(dto.getProcessorsByLatency());
            totalCount += dto.getTotalCount();
            failedCount += dto.getFailedCount();
            successfulCount += dto.getSuccessfulCount();
            avgSum += (dto.getAvg()*dto.getTotalCount());
        }
        long avg = (totalCount > 0) ? (avgSum / totalCount) : 0;
        ProcessorTypeGroupEventStatsDto dto = new ProcessorTypeGroupEventStatsDto();
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setTotalCount(totalCount);
        dto.setFailedCount(failedCount);
        dto.setSuccessfulCount(successfulCount);
        dto.setAvg(avg);
        List<Processors> distinctByCount = groupBy(byCount);
        List<Processors> distinctByLatency = groupBy(byLatency);
        dto.setProcessorsByCount(distinctByCount.stream().sorted(Comparator.comparingLong(Processors::getFailedCount).reversed())
                .limit(5).collect(Collectors.toList()));
        dto.setProcessorsByLatency(distinctByLatency.stream().sorted(Comparator.comparingDouble(Processors::getAvg).reversed())
                .limit(5).collect(Collectors.toList()));
        return dto;
    }

    private static List<Processors> groupBy(List<Processors> dtoList) {
        List<Processors> result = new ArrayList<>();
        HashMap<String, Processors> processorsMap = new HashMap<>();
        for(Processors processor: dtoList) {
            String key = processor.getType()+":"+processor.getGroupType();
            Processors newProcessor = processorsMap.get(key);
            if (newProcessor == null) {
                newProcessor = new Processors(processor.getType(), 0L, 0L, 0L, 0L, processor.getGroupType());
                processorsMap.put(key, newProcessor);
            }

            newProcessor.setTotalCount(newProcessor.getTotalCount()+processor.getTotalCount());
            newProcessor.setFailedCount(newProcessor.getFailedCount()+processor.getFailedCount());
            newProcessor.setSuccessCount(newProcessor.getSuccessCount()+processor.getSuccessCount());
            // Storing sum of  totalcount*average in average.
            newProcessor.setAvg(newProcessor.getAvg() + (processor.getTotalCount()*processor.getAvg()));
        }

        for(Map.Entry<String, Processors> processorsEntry: processorsMap.entrySet()) {
            Processors processors = processorsEntry.getValue();
            long totalCount = processors.getTotalCount();
            // Calculate average based on totalcount*average sum
            long avg = (totalCount > 0) ? (processors.getAvg() / totalCount) : 0;
            processors.setAvg(avg);
            result.add(processors);
        }

        return result;
    }
}
