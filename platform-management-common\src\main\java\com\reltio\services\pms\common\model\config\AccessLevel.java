package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;
import java.util.Set;

public class AccessLevel extends PMSConfig {

    @JsonProperty("accessLevelToRolesMapping")
    private Map<String, Set<String>> accessLevelToRolesMapping;

    @JsonCreator
    public AccessLevel(@JsonProperty("configName") String configName,
                        @JsonProperty("accessLevelToRolesMapping") Map<String, Set<String>> accessLevelToRolesMapping) {
        super(configName);
        this.accessLevelToRolesMapping = accessLevelToRolesMapping;
    }

    @Override
    public String getID() {
        return super.getID();
    }

    public Map<String, Set<String>> getAccessLevelToRolesMapping() {
        return accessLevelToRolesMapping;
    }

    @Override
    public void setConfigName(String configName) {
        super.setConfigName(configName);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AccessLevel that = (AccessLevel) o;

        return accessLevelToRolesMapping.equals(that.accessLevelToRolesMapping);
    }

    @Override
    public int hashCode() {
        return accessLevelToRolesMapping.hashCode();
    }
}
