package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.common.config.LcaConfig;
import com.reltio.devops.common.environment.ServicePurpose;
import com.reltio.devops.common.environment.ServiceType;
import com.reltio.services.pms.common.model.ConfigOverrideElement;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Getter
public class MdmTaskConfig extends AbstractPipelineTaskConfig {

    public static final int LCA_NATIVE_TIME_OUT = 100;
    public static final int LCA_LAMBDA_TIME_OUT = 100;
    public static final int LCA_DVF_TIME_OUT = 1000;

    @JsonProperty(value = "physicalConfigTemplate")
    private final String physicalConfigTemplate;

    @JsonProperty(value = "addSampleData")
    private final boolean addSampleData;

    @JsonProperty(value = "productEdition")
    private final String productEdition;

    @JsonProperty(value = "postProvisionAPIs")
    private final List<RESTRequest> postProvisionAPIs;

    @JsonProperty(value = "preProvisionAPIs")
    private final List<RESTRequest> preProvisionAPIs;

    @JsonProperty(value = "matchingStrategy")
    private final MatchingStrategy matchingStrategy;

    @JsonProperty(value = "ttlMappingName")
    private final String ttlMappingName;

    @JsonProperty("configOverrideElements")
    private final List<ConfigOverrideElement> configOverrideElements;

    @JsonProperty("storagePriorityList")
    private final Map<ServicePurpose, List<ServiceType>> storagePriorityList;

    @JsonProperty("lcaConfig")
    private final LcaConfig lcaConfig;

    @JsonProperty("uiFilesToSkip")
    private final Set<String> uiFilesToSkip;

    @JsonCreator
    public MdmTaskConfig(
            @JsonProperty(value = "name") String name,
            @JsonProperty(value = "physicalConfigTemplate") String physicalConfigTemplate,
            @JsonProperty(value = "addSampleData", defaultValue = "true") Boolean addSampleData,
            @JsonProperty(value = "productEdition") String productEdition,
            @JsonProperty(value = "postProvisionAPIs") List<RESTRequest> postProvisionAPIs,
            @JsonProperty(value = "preProvisionAPIs") List<RESTRequest> preProvisionAPIs,
            @JsonProperty(value = "matchingStrategy") MatchingStrategy matchingStrategy,
            @JsonProperty(value = "ttlMappingName") String ttlMappingName,
            @JsonProperty("configOverrideElements") List<ConfigOverrideElement> configOverrideElements,
            @JsonProperty("storagePriorityList") Map<ServicePurpose, List<ServiceType>> storagePriorityList,
            @JsonProperty("lcaConfig") LcaConfig lcaConfig,
            @JsonProperty("uiFilesToSkip") Set<String> uiFilesToSkip) {
        super(name, TaskType.MDM_TASK);
        this.physicalConfigTemplate = physicalConfigTemplate;
        this.addSampleData = addSampleData != null ? addSampleData : Boolean.TRUE;
        this.productEdition = productEdition;
        this.postProvisionAPIs = postProvisionAPIs;
        this.preProvisionAPIs = preProvisionAPIs;
        this.matchingStrategy = matchingStrategy != null ? matchingStrategy : MatchingStrategy.NONE;
        this.ttlMappingName = ttlMappingName;
        this.configOverrideElements = configOverrideElements != null ? configOverrideElements : new ArrayList<>();
        this.storagePriorityList = storagePriorityList;
        this.lcaConfig = getLcaConfigCopy(lcaConfig);
        this.uiFilesToSkip = uiFilesToSkip != null ? new HashSet<>(uiFilesToSkip) : null;
    }

    private LcaConfig getLcaConfigCopy(LcaConfig lcaConfig) {
        LcaConfig config = new LcaConfig();
        if (Objects.isNull(lcaConfig)) {
            config.setDvfTimeOut(LCA_DVF_TIME_OUT);
            config.setLambdaTimeOut(LCA_LAMBDA_TIME_OUT);
            config.setNativeTimeOut(LCA_NATIVE_TIME_OUT);
            return config;
        }
        config.setLambdaTimeOut(lcaConfig.getLambdaTimeOut());
        config.setNativeTimeOut(lcaConfig.getNativeTimeOut());
        config.setDvfTimeOut(lcaConfig.getDvfTimeOut());
        return config;
    }

    public LcaConfig getLcaConfig() {
        return getLcaConfigCopy(lcaConfig);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        MdmTaskConfig that = (MdmTaskConfig) o;
        return addSampleData == that.addSampleData && Objects.equals(physicalConfigTemplate, that.physicalConfigTemplate) && Objects.equals(productEdition, that.productEdition) && Objects.equals(postProvisionAPIs, that.postProvisionAPIs) && Objects.equals(preProvisionAPIs, that.preProvisionAPIs) && matchingStrategy == that.matchingStrategy && Objects.equals(ttlMappingName, that.ttlMappingName) && Objects.equals(configOverrideElements, that.configOverrideElements) && Objects.equals(storagePriorityList, that.storagePriorityList) && Objects.equals(lcaConfig, that.lcaConfig) && Objects.equals(uiFilesToSkip, that.uiFilesToSkip);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), physicalConfigTemplate, addSampleData, productEdition, postProvisionAPIs, preProvisionAPIs, matchingStrategy, ttlMappingName, configOverrideElements, storagePriorityList, lcaConfig, uiFilesToSkip);
    }
}
