package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.sales.model.HistoryBackup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class HistoryBackupDao extends AbstractRootCollectionDao<HistoryBackup> {
    private static final String HISTORY_BACKUP_COLLECTION_NAME = "PMS_BACKUP_HISTORY";

    @Autowired
    public HistoryBackupDao(CredentialsProvider provider, @Value("${firestore.env.name}") String deployedEnv, ReltioUserHolder reltioUserHolder) {
        super(provider, HISTORY_BACKUP_COLLECTION_NAME, deployedEnv, reltioUserHolder);
    }

    @Override
    protected TypeReference<HistoryBackup> getTypeReference() {
        return new TypeReference<>() {
        };
    }
}
