package com.reltio.services.pms.common.model.supportability.result.combiner;

import com.reltio.services.pms.common.model.supportability.LatencyByTime;
import com.reltio.services.pms.common.model.supportability.Requests;
import com.reltio.services.pms.common.model.supportability.RestStatsDto;
import com.reltio.services.pms.common.model.supportability.result.StatsCombiner;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * The type Rest stats combiner.
 */
public class RestStatsCombiner implements StatsCombiner<RestStatsDto> {

    /**
     * mergeAllRestRows method is used to merge all the provided rows from GBQ into single record by sum/avg based on the column in GBQ.
     *
     * @param dtoList the dto list
     * @return the list
     */
    @Override
    public RestStatsDto mergeAllRows(List<RestStatsDto> dtoList) {
        RestStatsDto response = dtoList.get(NumberUtils.INTEGER_ZERO);
        RestStatsDto lastResponse = dtoList.get(dtoList.size() - NumberUtils.INTEGER_ONE);
        long startTime = response.getStartTime();
        long endTime = lastResponse.getEndTime();
        int totalRows = dtoList.size();
        long totalCount = NumberUtils.INTEGER_ZERO;
        long failedRequestsSum = NumberUtils.INTEGER_ZERO;
        int latencyByTime1min = NumberUtils.INTEGER_ZERO;
        int latencyByTime10min = NumberUtils.INTEGER_ZERO;
        int latencyByTime30s = NumberUtils.INTEGER_ZERO;
        long avgSum = NumberUtils.INTEGER_ZERO;
        long p50Sum = NumberUtils.INTEGER_ZERO;
        long p90Sum = NumberUtils.INTEGER_ZERO;
        long p95Sum = NumberUtils.INTEGER_ZERO;
        long p99Sum = NumberUtils.INTEGER_ZERO;
        Set<Requests> byCount = new HashSet<>();
        Set<Requests> byLatency = new HashSet<>();
        for (RestStatsDto dto : dtoList) {
            byCount.addAll(dto.getRequestsByCount());
            byLatency.addAll(dto.getRequestsByLatency());
            totalCount += dto.getTotalCount();
            avgSum += dto.getAvgTime();
            latencyByTime1min += dto.getLatencyByTime().getOver1m();
            latencyByTime10min += dto.getLatencyByTime().getOver10m();
            latencyByTime30s += dto.getLatencyByTime().getOver30s();
            failedRequestsSum += dto.getFailedRequests();
            p50Sum += dto.getP50();
            p90Sum += dto.getP90();
            p95Sum += dto.getP95();
            p99Sum += dto.getP99();
        }
        long avgTime = avgSum / totalRows;
        long p50Avg = p50Sum / totalRows;
        long p90Avg = p90Sum / totalRows;
        long p95Avg = p95Sum / totalRows;
        long p99Avg = p99Sum / totalRows;
        RestStatsDto dto = new RestStatsDto();
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setAvgTime(avgTime);
        dto.setTotalCount(totalCount);
        LatencyByTime latencyByTimeAvg = new LatencyByTime(latencyByTime30s, latencyByTime1min, latencyByTime10min);
        dto.setLatencyByTime(latencyByTimeAvg);
        dto.setFailedRequests(failedRequestsSum);
        dto.setClusterType(response.getClusterType());
        dto.setClusterName(response.getClusterName());
        dto.setP50(p50Avg);
        dto.setP90(p90Avg);
        dto.setP95(p95Avg);
        dto.setP99(p99Avg);
        List<Requests> distinctByCount = byCount.stream().filter(filter -> Objects.nonNull(filter.getCount()))
                .collect(Collectors.collectingAndThen(Collectors.toMap(Requests::getRequest, Function.identity(), (r1, r2) -> r1.getCount() >= r2.getCount() ? r1 : r2), map -> new ArrayList<>(map.values())));
        List<Requests> distinctByLatency = byLatency.stream().filter(filter -> Objects.nonNull(filter.getP90()))
                .collect(Collectors.collectingAndThen(Collectors.toMap(Requests::getRequest, Function.identity(), (r1, r2) -> r1.getP90() >= r2.getP90() ? r1 : r2), map -> new ArrayList<>(map.values())));
        dto.setRequestsByCount(distinctByCount.stream().sorted(Comparator.comparingInt(Requests::getCount).reversed())
                .limit(5).collect(Collectors.toList()));
        dto.setRequestsByLatency(distinctByLatency.stream().sorted(Comparator.comparingInt(Requests::getP90).reversed())
                .limit(5).collect(Collectors.toList()));
        return dto;
    }
}
