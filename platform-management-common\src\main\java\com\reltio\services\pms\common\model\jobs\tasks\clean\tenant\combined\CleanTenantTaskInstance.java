package com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.combined;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.AnalyticsCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.DataCleanUpOptions;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.ExternalServicesCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.MetaDataCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.Parameters;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Deprecated
public class CleanTenantTaskInstance extends TaskInstance {

    @JsonProperty(value = "dataCleanUpOptions")
    private final DataCleanUpOptions dataCleanUpOptions;

    @JsonProperty(value = "analyticsCleanUp")
    private final AnalyticsCleanUp analyticsCleanUp;

    @JsonProperty(value = "metaDataCleanUp")
    private final MetaDataCleanUp metaDataCleanUp;

    @JsonProperty(value = "externalQueue")
    private final boolean externalQueue;

    @JsonProperty(value = "externalServicesCleanUp")
    private final ExternalServicesCleanUp externalServicesCleanUp;

    @JsonProperty(value = "tenantName")
    private final String tenantName;

    @JsonProperty(value = "events")
    private final List<String> events;

    @JsonProperty(value = "currentState")
    private final List<String> currentState;

    @JsonProperty(value = "parameters")
    private final Parameters parameters;

    private final Long duration;
    @JsonProperty(value = "qaAutomation")
    private boolean qaAutomation;

    @JsonCreator
    public CleanTenantTaskInstance(@JsonProperty(value = "taskId") String taskId,
                                   @JsonProperty(value = "name") String name,
                                   @JsonProperty(value = "jobId") String jobId,
                                   @JsonProperty(value = "startTime") Long startTime,
                                   @JsonProperty(value = "finishTime") Long finishTime,
                                   @JsonProperty(value = "status") TaskStatus status,
                                   @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                   @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                   @JsonProperty(value = "executingNodeName") String executingNodeName,
                                   @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                   @JsonProperty(value = "envId") String envId,
                                   @JsonProperty(value = "dataCleanUpOptions") DataCleanUpOptions dataCleanUpOptions,
                                   @JsonProperty(value = "externalServicesCleanUp") ExternalServicesCleanUp externalServicesCleanUp,
                                   @JsonProperty(value = "externalQueue") boolean externalQueue,
                                   @JsonProperty(value = "metaDataCleanUp") MetaDataCleanUp metaDataCleanUp,
                                   @JsonProperty(value = "analyticsCleanUp") AnalyticsCleanUp analyticsCleanUp,
                                   @JsonProperty(value = "tenantName") String tenantName,
                                   @JsonProperty(value = "events") List<String> events,
                                   @JsonProperty(value = "currentState") List<String> currentState,
                                   @JsonProperty(value = "parameters") Parameters parameters,
                                   @JsonProperty(value = "qaAutomation") boolean qaAutomation) {
        super(taskId, name, jobId, startTime, finishTime, TaskType.DELETE_TENANT_DATA_TASK, status, lastUpdatedTime,
                taskFailureContext, executingNodeName, serviceNodeStatus, envId);
        this.dataCleanUpOptions = dataCleanUpOptions;
        this.externalServicesCleanUp = externalServicesCleanUp;
        this.externalQueue = externalQueue;
        this.metaDataCleanUp = metaDataCleanUp;
        this.analyticsCleanUp = analyticsCleanUp;
        this.tenantName = tenantName;
        this.events = events == null ? new ArrayList<>() : new ArrayList<>(events);
        this.currentState = currentState == null ? new ArrayList<>() : new ArrayList<>(currentState);
        this.parameters = parameters;
        this.qaAutomation = qaAutomation;
        if (finishTime == null) {
            this.duration = startTime == 0 ? 0 : System.currentTimeMillis() - startTime;
        } else {
            this.duration = finishTime - startTime;
        }
    }

    public DataCleanUpOptions getDataCleanUpOptions() {
        return dataCleanUpOptions;
    }

    public AnalyticsCleanUp getAnalyticsCleanUp() {
        return analyticsCleanUp;
    }

    public MetaDataCleanUp getMetaDataCleanUp() {
        return metaDataCleanUp;
    }

    public boolean isExternalQueue() {
        return externalQueue;
    }

    public ExternalServicesCleanUp getExternalServicesCleanUp() {
        return externalServicesCleanUp;
    }

    public String getTenantName() {
        return tenantName;
    }

    public List<String> getEvents() {
        return events;
    }

    public void addEvent(String event) {
        events.add(event);
    }

    public List<String> getCurrentState() {
        return currentState;
    }

    public void addCurrentState(String currState) {
        currentState.add(currState);
    }

    public Parameters getParameters() {
        return parameters;
    }

    public boolean isQaAutomation() {
        return qaAutomation;
    }

    public Long getDuration() {
        return duration;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        if (!super.equals(o)) {
            return false;
        }

        CleanTenantTaskInstance that = (CleanTenantTaskInstance) o;
        return isExternalQueue() == that.isExternalQueue() &&
                Objects.equals(getTenantName(), that.getTenantName()) &&
                Objects.equals(getEvents(), that.getEvents()) &&
                Objects.equals(getCurrentState(), that.getCurrentState()) &&
                Objects.equals(getDataCleanUpOptions(), that.getDataCleanUpOptions()) &&
                Objects.equals(getAnalyticsCleanUp(), that.getAnalyticsCleanUp()) &&
                Objects.equals(getMetaDataCleanUp(), that.getMetaDataCleanUp()) &&
                Objects.equals(getExternalServicesCleanUp(), that.getExternalServicesCleanUp()) &&
                Objects.equals(getDuration(), that.getDuration());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getTenantName(), getEvents(), getDataCleanUpOptions(), getAnalyticsCleanUp(), getMetaDataCleanUp(), isExternalQueue(), getExternalServicesCleanUp(), getCurrentState(), getParameters(), getDuration());
    }
}
