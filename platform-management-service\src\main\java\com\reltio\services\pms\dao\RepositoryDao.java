package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.CollectionReference;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.Repository;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Map;
import java.util.Set;

/**
 * The type Repository dao.
 */
@Service
public class RepositoryDao extends AbstractRootCollectionDao<Repository> {

    private static final String ENVIRONMENTS_COLLECTION_NAME = "PMS_REPOSITORY";
    private static final Logger LOGGER = Logger.getLogger(RepositoryDao.class);


    /**
     * Instantiates a new Repository dao.
     *
     * @param provider         the provider
     * @param deployedEnv      the deployed env
     * @param reltioUserHolder the reltio user holder
     */
    @Autowired
    public RepositoryDao(CredentialsProvider provider,
                         @Value("${firestore.env.name}") String deployedEnv, ReltioUserHolder reltioUserHolder) {
        super(provider, ENVIRONMENTS_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties = Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties, LOGGER);
    }

    @Override
    protected TypeReference<Repository> getTypeReference() {
        return new TypeReference<Repository>() {
        };
    }


    protected Repository createOrUpdate(CollectionReference collectionReference, Repository entity) {
        try {
            String id = entity.getID();
            Map<String, Object> objectMap = convertToMap(entity);
            collectionReference.document(id).set(objectMap);
            return entity;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }


}
