package com.reltio.services.pms.common.model.pipeline.tasks;

public enum TaskType {

    SLEEP_TASK,
    MULTI_SLEEP_TASK,
    APPROVAL_TASK,
    AUTH_TASK,
    MDM_TASK,
    FREE_TIER_EMAIL_NOTIFICATION_TASK,
    EMAIL_VERIFICATION_TASK,
    R360_DATA_SYNC_TASK,
    WOR<PERSON>FLOW_TASK,
    MATCH_IQ_TASK,
    RDM_TASK,
    RDM_SERVICE_DEPLOYMENT_TASK,
    STREAMING_TASK,
    WORKATO_ENABLEMENT_TASK,
    APPROVAL_OF_STORAGE,
    IMAGE_HOSTING_ENABLEMENT_TASK,
    <PERSON><PERSON><PERSON><PERSON>ISE_NOTIFICATION_TASK,
    DNB_CONNECTOR_TASK,
    SFDC_CONNECTOR_ENABLEMENT_TASK,
    SHIELD_ENABLEMENT_TASK,
    UPDATE_TENANT_TASK,
    DTSS_TASK,
    CONTRACTS_SYNC_TASK,
    UPDATE_R360_TENANT_TASK,
    DELETE_TENANT_DATA_TASK,
    DELETE_TENANT_DATA_VALIDATION_TASK,
    DELETE_TENANT_DATA_EXECUTION_TASK,
    MAINTENANCE_MODE_TASK,
    TENANTS_SYNC_TASK,
    FERN_TASK,
    FERN_DE_PROVISION_TASK,
    AUTH_DE_PROVISION_TASK,
    EMAIL_NOTIFICATION_TASK,
    SFDC_DE_PROVISION_TASK,
    DNB_DE_PROVISION_TASK,
    DELETE_MDM_TENANT_TASK,
    WORKFLOW_DE_PROVISION_TASK,
    DELETE_RDM_TENANT_TASK,
    STREAMING_DE_PROVISION_TASK,
    IMAGE_HOSTING_DE_PROVISION_TASK,
    RIQ_DE_PROVISION_TASK,
    WORKATO_DE_PROVISION_TASK,
    AZURE_BLOB_STORAGE_PROVISION_TASK,
    DTSS_DE_PROVISION_TASK,
    MATCH_IQ_DE_PROVISION_TASK,
    RIH_GENERIC_PROVISION_TASK,
    RETRIEVE_AND_STORE_TENANT_CONFIGS_TASK,
    CLONE_TENANTS_CONFIGS_TASK,
    DATA_BACKUP_TASK,
    DATA_RESTORE_TASK,
    DELETE_DYNAMO_DB_BACKUP_TASK,
    CONFIGURE_STREAMING_AND_ANALYTICS_TASK,
    HISTORY_BACKUP_TASK,
    HISTORY_RESTORE_TASK;


    public static final String SLEEP_TASK_NAME = "SLEEP_TASK";
    public static final String MULTI_SLEEP_TASK_NAME = "MULTI_SLEEP_TASK";
    public static final String APPROVAL_TASK_NAME = "APPROVAL_TASK";
    public static final String AUTH_TASK_NAME = "AUTH_TASK";
    public static final String FREE_TIER_EMAIL_NOTIFICATION_TASK_NAME = "FREE_TIER_EMAIL_NOTIFICATION_TASK";
    public static final String MDM_TASK_NAME = "MDM_TASK";
    public static final String EMAIL_VERIFICATION_TASK_NAME = "EMAIL_VERIFICATION_TASK";
    public static final String R360_DATA_SYNC_TASK_NAME = "R360_DATA_SYNC_TASK";
    public static final String WORKFLOW_TASK_NAME = "WORKFLOW_TASK";
    public static final String MATCH_IQ_TASK_NAME = "MATCH_IQ_TASK";
    public static final String RDM_TASK_NAME = "RDM_TASK";
    public static final String RDM_SERVICE_DEPLOYMENT_NAME = "RDM_SERVICE_DEPLOYMENT_TASK";
    public static final String STREAMING_TASK_NAME = "STREAMING_TASK";
    public static final String STREAMING_DE_PROVISION_TASK_NAME = "STREAMING_DE_PROVISION_TASK";
    public static final String WORKATO_ENABLEMENT_TASK_NAME = "WORKATO_ENABLEMENT_TASK";
    public static final String CONTRACTS_SYNC_TASK_NAME = "CONTRACTS_SYNC_TASK";
    public static final String APPROVAL_OF_STORAGE_TASK_NAME = "APPROVAL_OF_STORAGE";
    public static final String IMAGE_HOSTING_ENABLEMENT_TASK_NAME = "IMAGE_HOSTING_ENABLEMENT_TASK";
    public static final String ENTERPRISE_NOTIFICATION_TASK_NAME = "ENTERPRISE_NOTIFICATION_TASK";
    public static final String DNB_CONNECTOR_TASK_NAME = "DNB_CONNECTOR_TASK";
    public static final String UPDATE_TENANT_TASK_NAME = "UPDATE_TENANT_TASK";
    public static final String SFDC_CONNECTOR_ENABLEMENT_TASK_NAME = "SFDC_CONNECTOR_ENABLEMENT_TASK";
    public static final String DTSS_TASK_NAME = "DTSS_TASK";
    public static final String SHIELD_ENABLEMENT_TASK_NAME = "SHIELD_ENABLEMENT_TASK";
    public static final String UPDATE_R360_TENANT_TASK_NAME = "UPDATE_R360_TENANT_TASK";
    public static final String CLEAN_TENANT_TASK_NAME = "DELETE_TENANT_DATA_TASK";
    public static final String CLEAN_TENANT_VALIDATION_TASK_NAME = "DELETE_TENANT_DATA_VALIDATION_TASK";
    public static final String CLEAN_TENANT_EXECUTION_TASK_NAME = "DELETE_TENANT_DATA_EXECUTION_TASK";
    public static final String MAINTENANCE_MODE_TASK_NAME = "MAINTENANCE_MODE_TASK";
    public static final String TENANTS_SYNC_TASK_NAME = "TENANTS_SYNC_TASK";
    public static final String FERN_TASK_NAME = "FERN_TASK";
    public static final String FERN_DE_PROVISION_TASK_NAME = "FERN_DE_PROVISION_TASK";
    public static final String AUTH_DE_PROVISION_TASK_NAME = "AUTH_DE_PROVISION_TASK";
    public static final String EMAIL_NOTIFICATION_TASK_NAME = "EMAIL_NOTIFICATION_TASK";
    public static final String SFDC_DE_PROVISION_TASK_NAME = "SFDC_DE_PROVISION_TASK";
    public static final String DELETE_MDM_TENANT_TASK_NAME = "DELETE_MDM_TENANT_TASK";
    public static final String DNB_DE_PROVISION_TASK_NAME = "DNB_DE_PROVISION_TASK";
    public static final String DELETE_RDM_TENANT_TASK_NAME = "DELETE_RDM_TENANT_TASK";
    public static final String WORKFLOW_DE_PROVISION_TASK_NAME = "WORKFLOW_DE_PROVISION_TASK";
    public static final String IMAGE_HOSTING_DE_PROVISION_TASK_NAME = "IMAGE_HOSTING_DE_PROVISION_TASK";
    public static final String RIQ_DE_PROVISION_TASK_NAME = "RIQ_DE_PROVISION_TASK";
    public static final String WORKATO_DE_PROVISION_TASK_NAME = "WORKATO_DE_PROVISION_TASK";
    public static final String AZURE_BLOB_STORAGE_PROVISION_TASK_NAME = "AZURE_BLOB_STORAGE_PROVISION_TASK";
    public static final String DTSS_DE_PROVISION_TASK_NAME = "DTSS_DE_PROVISION_TASK";
    public static final String MATCH_IQ_DE_PROVISION_TASK_NAME = "MATCH_IQ_DE_PROVISION_TASK";
    public static final String RIH_GENERIC_PROVISION_TASK_NAME = "RIH_GENERIC_PROVISION_TASK";
    public static final String RETRIEVE_AND_STORE_TENANT_CONFIGS_TASK_NAME = "RETRIEVE_AND_STORE_TENANT_CONFIGS_TASK";
    public static final String CLONE_TENANTS_CONFIGS_TASK_NAME = "CLONE_TENANTS_CONFIGS_TASK";
    public static final String DATA_BACKUP_TASK_NAME = "DATA_BACKUP_TASK";
    public static final String DATA_RESTORE_TASK_NAME = "DATA_RESTORE_TASK";
    public static final String DELETE_DYNAMO_DB_BACKUP_TASK_NAME = "DELETE_DYNAMO_DB_BACKUP_TASK";
    public static final String CONFIGURE_STREAMING_AND_ANALYTICS_TASK_NAME = "CONFIGURE_STREAMING_AND_ANALYTICS_TASK";
    public static final String HISTORY_BACKUP_TASK_NAME = "HISTORY_BACKUP_TASK";
    public static final String HISTORY_RESTORE_TASK_NAME = "HISTORY_RESTORE_TASK";
}



