package com.reltio.services.pms.service.compliance;

import com.reltio.services.pms.common.model.compliance.request.ComplianceReportRequest;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingByEnvironmentResponse;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByCustomerResponse;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByEnvironmentModel;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByTenantModel;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.SalesAccount;

import java.util.Collection;
import java.util.List;

/**
 * The interface Billing Report service.
 */
public interface BillingReportService {
    /**
     * Gets billing report by environment.
     *
     * @param accountCollection the account collection
     * @param tenantCollection  the tenant collection
     * @param billingReportByEnvironmentModelList   the billing report by tenant
     * @return the billing report by environment
     */
    BillingByEnvironmentResponse getBillingUsageByEnvironment(Collection<SalesAccount> accountCollection, Collection<ReltioTenant> tenantCollection, List<BillingReportByEnvironmentModel> billingReportByEnvironmentModelList, ComplianceReportRequest billingReportRequest, Boolean export);

    /**
     * Gets billing report by customer.
     *
     * @param accountCollection the account collection
     * @param tenantCollection  the tenant collection
     * @param billingReportByTenantModelList   the billing report by tenant
     * @return the billing report by customer
     */
    BillingReportByCustomerResponse getBillingUsageByCustomer(Collection<SalesAccount> accountCollection, Collection<ReltioTenant> tenantCollection, List<BillingReportByTenantModel> billingReportByTenantModelList, ComplianceReportRequest billingReportRequest, Boolean export);

}
