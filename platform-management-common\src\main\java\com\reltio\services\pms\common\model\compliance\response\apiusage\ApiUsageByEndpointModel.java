package com.reltio.services.pms.common.model.compliance.response.apiusage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The type Api usage by endpoint model.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ApiUsageByEndpointModel {
    /**
     * The Date.
     */
    @JsonProperty("date")
    String date;

    /**
     * The Tenant id.
     */
    @JsonProperty("tenantId")
    String tenantId;


    /**
     * The HTTP Method.
     */
    @JsonProperty("httpMethod")
    String httpMethod;

    /**
     * The Handler Mapping.
     */
    @JsonProperty("handlerMapping")
    String handlerMapping;

    /**
     * The Source System.
     */
    @JsonProperty("sourceSystem")
    String sourceSystem;

    /**
     * The Environment.
     */
    @JsonProperty("environment")
    String environment;

    /**
     * The Api usage.
     */
    @JsonProperty("apiUsage")
    String apiUsage;

}
