package com.reltio.services.pms.service.jobs.tasks.approval;

import com.reltio.services.pms.common.model.jobs.tasks.approve.ApprovalTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.approve.ApprovalTaskInstanceParams;
import com.reltio.services.pms.common.model.pipeline.tasks.ApprovalPipelineTaskConfig;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.jobs.tasks.TaskInstanceProvider;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class ApprovalTaskInstanceProvider implements TaskInstanceProvider<ApprovalTaskInstance, ApprovalPipelineTaskConfig, ApprovalTaskInstanceParams> {

    @Override
    public TaskType getTaskType() {
        return TaskType.APPROVAL_TASK;
    }

    @Override
    public ApprovalTaskInstance getTaskDetail(String envId, String jobId, ApprovalPipelineTaskConfig pipelineTaskConfig, ApprovalTaskInstanceParams taskInstanceParams) {
        return new ApprovalTaskInstance(
                UUID.randomUUID().toString(),
                TaskType.APPROVAL_TASK.toString(),
                jobId,
                null,
                null,
                null,
                null,
                null,
                pipelineTaskConfig.getApproverEmails(),
                null, taskInstanceParams.getRequester(),
                envId
        );
    }
}
