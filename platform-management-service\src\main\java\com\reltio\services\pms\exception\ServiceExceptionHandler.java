package com.reltio.services.pms.exception;

import com.google.common.collect.ImmutableMap;
import com.reltio.rest.operation.RestOperationExecutionContextHolder;
import com.reltio.rest.operation.RestOperationExecutionContextInterface;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.exception.PlatformManagementMultiException;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import org.springframework.web.util.WebUtils;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Special controller handler for exceptions
 */
@ControllerAdvice
public class ServiceExceptionHandler extends ResponseEntityExceptionHandler {
    private static final Logger LOG = Logger.getLogger(ServiceExceptionHandler.class);

    private static final Map<Class<?>, HttpStatus> statuses = ImmutableMap.<Class<?>, HttpStatus>builder()
            .put(PlatformManagementMultiException.class, HttpStatus.INTERNAL_SERVER_ERROR)
            .put(ServletException.class, HttpStatus.INTERNAL_SERVER_ERROR)
            .put(AccessDeniedException.class, HttpStatus.FORBIDDEN)
            .put(Exception.class, HttpStatus.INTERNAL_SERVER_ERROR)
            .build();
    @Value("${reltio.exceptions.include-stacktrace}")
    boolean includeStackTrace;


    @ExceptionHandler({
            PlatformManagementException.class,
            ServletException.class,
            AccessDeniedException.class,
            Exception.class
    })
    @ResponseBody
    protected ResponseEntity<Object> handleControllerException(HttpServletRequest req, Throwable ex) {
        return convert(ex, statuses.getOrDefault(ex.getClass(), HttpStatus.INTERNAL_SERVER_ERROR));
    }

    @Override
    protected ResponseEntity<Object> handleNoHandlerFoundException(NoHandlerFoundException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        return convert(ex, HttpStatus.NOT_FOUND);
    }

    private ResponseEntity<Object> convert(Throwable ex, HttpStatus defaultStatus) {
        //To appear in the log
        LOG.error(ex.getMessage(), ex);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> responseBody = getErrorAttributes(ex);
        if (ex instanceof PlatformManagementException) {
            PlatformManagementException serviceException = (PlatformManagementException) ex;
            return new ResponseEntity<>(responseBody, headers, HttpStatus.valueOf(serviceException.getHttpStatus()));
        }
        return new ResponseEntity<>(responseBody, headers, defaultStatus);
    }

    private Map<String, Object> getErrorAttributes(Throwable error) {
        Map<String, Object> errorAttributes = new LinkedHashMap<>();
        errorAttributes.put("severity", "Error");
        if (error != null) {
            errorAttributes.put("errorMessage", error.getMessage());
            if (error instanceof PlatformManagementException) {
                PlatformManagementException serviceException = (PlatformManagementException) error;
                errorAttributes.put("errorCode", serviceException.getCode()
                        .code());
                errorAttributes.put("errorDetailMessage", serviceException.getDetailMessage());
                if (error instanceof PlatformManagementMultiException) {
                    PlatformManagementMultiException multiException = (PlatformManagementMultiException) error;
                    Collection<Map<String, Object>> errors = multiException.getExceptions().stream().map(this::getErrorAttributes).collect(Collectors.toList());
                    errorAttributes.put("errors", errors);
                }
            }
            if (error instanceof MethodArgumentNotValidException) {
                MethodArgumentNotValidException validException = (MethodArgumentNotValidException) error;
                Map<String, String> errorsMap = new HashMap<>();
                for (FieldError errorFeild : validException.getBindingResult().getFieldErrors()) {
                    errorsMap.put(errorFeild.getField(), errorFeild.getDefaultMessage());
                }
                errorAttributes.put("errorMessage", errorsMap);
            }
            fillInnerErrorData(errorAttributes, error, includeStackTrace);
        }
        fillOperationId(errorAttributes);
        return errorAttributes;
    }

    private void fillOperationId(Map<String, Object> errorAttributes) {
        RestOperationExecutionContextInterface context = RestOperationExecutionContextHolder.getContext();
        if (context.getOperationID() != null) {
            errorAttributes.put("OperationID", context.getOperationID());
        }
    }

    private void fillInnerErrorData(Map<String, Object> errorAttributes, Throwable error, boolean includeStackTrace) {
        Map<String, Object> innerErrorData = new LinkedHashMap<>();
        innerErrorData.put("exception", error.getClass()
                .getCanonicalName() + ":" + error.getMessage());
        if (includeStackTrace) {
            fillStack(innerErrorData, error);
        }
        Throwable cause = error.getCause();
        if (cause != null && cause != error) {
            fillInnerErrorData(innerErrorData, cause, includeStackTrace);
        }
        errorAttributes.put("innerErrorData", innerErrorData);
    }

    private void fillStack(Map<String, Object> errorAttributes, Throwable error) {
        StackTraceElement[] trace = error.getStackTrace();
        if (trace != null) {
            List<String> result = new ArrayList<>();
            for (StackTraceElement next : trace) {
                result.add(next.getClassName() + "." + next.getMethodName() + " at " + next.getFileName() + ", line " + next.getLineNumber());
            }
            errorAttributes.put("stack", result);
        }
    }

    @Override
    protected ResponseEntity<Object> handleExceptionInternal(Exception ex, Object body, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        if (HttpStatus.INTERNAL_SERVER_ERROR == status) {
            request.setAttribute(WebUtils.ERROR_EXCEPTION_ATTRIBUTE, ex, WebRequest.SCOPE_REQUEST);
        }
        headers.setContentType(MediaType.APPLICATION_JSON);
        return new ResponseEntity<>(getErrorAttributes(ex), headers, status);
    }


    @ExceptionHandler(BindException.class)
    protected ResponseEntity<Object> handleBindException(
            BindException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        FieldError fieldError = ex.getFieldError();
        String message = fieldError != null ? String.format("%s has invalid value '%s'", fieldError.getField(), fieldError.getRejectedValue()) : "Bad Request";
        return handleExceptionInternal(new Exception(message, ex), null, headers, status, request);
    }

}
