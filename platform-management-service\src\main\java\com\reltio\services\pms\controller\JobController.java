package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.common.config.LcaConfig;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.BulkServicesEnablementJobRequest;
import com.reltio.services.pms.common.model.BulkTenantCreationRequest;
import com.reltio.services.pms.common.model.CreateJobRequest;
import com.reltio.services.pms.common.model.CustomerType;
import com.reltio.services.pms.common.model.DeProvisionTenantJobRequest;
import com.reltio.services.pms.common.model.EnterpriseTenantCreationRequest;
import com.reltio.services.pms.common.model.JobWithTask;
import com.reltio.services.pms.common.model.ProductEdition;
import com.reltio.services.pms.common.model.ProvisionFreeTierTenantJobRequest;
import com.reltio.services.pms.common.model.QATenantCreationRequest;
import com.reltio.services.pms.common.model.SyncJobRequest;
import com.reltio.services.pms.common.model.TenantsSyncRequest;
import com.reltio.services.pms.common.model.TrainingTenantCreationRequest;
import com.reltio.services.pms.common.model.UpdateJobStatusRequest;
import com.reltio.services.pms.common.model.jobs.BulkTenantCreationJob;
import com.reltio.services.pms.common.model.jobs.Job;
import com.reltio.services.pms.common.model.jobs.JobDetail;
import com.reltio.services.pms.common.model.jobs.JobStatus;
import com.reltio.services.pms.common.model.jobs.SortField;
import com.reltio.services.pms.common.model.jobs.SortOrder;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.maintenance.MaintenanceModeTaskInstance;
import com.reltio.services.pms.common.model.pipeline.Pipeline;
import com.reltio.services.pms.common.model.pipeline.PipelineType;
import com.reltio.services.pms.common.model.pipeline.SkipReason;
import com.reltio.services.pms.common.model.pipeline.tasks.MatchingStrategy;
import com.reltio.services.pms.common.model.pipeline.tasks.MdmTaskConfig;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.service.PipelineService;
import com.reltio.services.pms.service.ProductEditionsService;
import com.reltio.services.pms.service.jobs.BulkTenantsJobService;
import com.reltio.services.pms.service.jobs.DeProvisionTenantJobService;
import com.reltio.services.pms.service.jobs.EnterpriseTenantCreationJobService;
import com.reltio.services.pms.service.jobs.JobMappingService;
import com.reltio.services.pms.service.jobs.JobService;
import com.reltio.services.pms.service.jobs.ServicesEnablementJobService;
import com.reltio.services.pms.service.jobs.SyncJobService;
import com.reltio.services.pms.service.jobs.TaskService;
import com.reltio.services.pms.service.jobs.TenantProvisioningJobService;
import com.reltio.services.pms.service.jobs.TenantsSyncJobService;
import com.reltio.services.pms.service.jobs.TrainingTenantCreationJobService;
import com.reltio.services.pms.service.jobs.tasks.GenericTaskExecutionService;
import com.reltio.services.pms.service.reltiotenant.TenantsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.reltio.services.pms.common.exception.PlatformManagementErrorCode.BOTH_RDMTENANTTOREUSE_AND_RDMTENANTID_CANNOT_BE_USED;
import static com.reltio.services.pms.common.exception.PlatformManagementErrorCode.CUSTOMER_ID_LENGTH_IS_NOT_IN_RANGE;
import static com.reltio.services.pms.common.exception.PlatformManagementErrorCode.MULTIPLE_OWNERS_ARE_NOT_ALLOWED_IN_TRAINING_CUSTOMER_TYPE;

@RestController
@RequestMapping(value = "/api/v1/environments/{envId}/jobs", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Jobs Create and Execute")
@ReltioSecured(resourceClass = Pms.Environment.Jobs.class)
public class JobController {
    private static final int MIN_CUSTOMER_ID_LENGTH = 2;
    private static final int MAX_CUSTOMER_ID_LENGTH = 20;
    private static final String RELTIO = "Reltio";
    private final PipelineService pipelineService;
    private final ProductEditionsService productEditionsService;
    private final GenericTaskExecutionService genericTaskExecutionService;
    private final JobService jobService;
    private final JobMappingService jobMappingService;
    private final TenantProvisioningJobService tenantProvisioningJobService;
    private final ServicesEnablementJobService servicesEnablementJobService;
    private final SyncJobService syncJobService;
    private final EnterpriseTenantCreationJobService enterpriseTenantCreationJobService;
    private final TaskService taskService;
    private final TenantsService tenantsService;
    private final TenantsSyncJobService tenantsSyncJobService;
    private final DeProvisionTenantJobService deProvisionTenantJobService;
    private final TrainingTenantCreationJobService trainingTenantCreationJobService;
    private final BulkTenantsJobService bulkTenantsJobService;

    @Autowired
    public JobController(PipelineService pipelineService,
                         GenericTaskExecutionService genericTaskExecutionService,
                         JobService jobService,
                         JobMappingService jobMappingService,
                         TenantProvisioningJobService tenantProvisioningJobService,
                         ServicesEnablementJobService servicesEnablementJobService,
                         SyncJobService syncJobService,
                         EnterpriseTenantCreationJobService enterpriseTenantCreationJobService,
                         TaskService taskService, TenantsService tenantsService,
                         TenantsSyncJobService tenantsSyncJobService,
                         DeProvisionTenantJobService deProvisionTenantJobService,
                         TrainingTenantCreationJobService trainingTenantCreationJobService,
                         ProductEditionsService productEditionsService,
                         BulkTenantsJobService bulkTenantsCreationJobService) {
        this.pipelineService = pipelineService;
        this.genericTaskExecutionService = genericTaskExecutionService;
        this.jobService = jobService;
        this.jobMappingService = jobMappingService;
        this.tenantProvisioningJobService = tenantProvisioningJobService;
        this.servicesEnablementJobService = servicesEnablementJobService;
        this.syncJobService = syncJobService;
        this.enterpriseTenantCreationJobService = enterpriseTenantCreationJobService;
        this.taskService = taskService;
        this.tenantsService = tenantsService;
        this.tenantsSyncJobService = tenantsSyncJobService;
        this.deProvisionTenantJobService = deProvisionTenantJobService;
        this.trainingTenantCreationJobService = trainingTenantCreationJobService;
        this.productEditionsService = productEditionsService;
        this.bulkTenantsJobService = bulkTenantsCreationJobService;
    }

    @PostMapping(value = "", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Job createJob(@RequestBody CreateJobRequest createJobRequest, @PathVariable String envId) {
        Pipeline pipeline = pipelineService.getPipeline(envId, createJobRequest.getPipelineId());
        return jobService.createJob(envId, pipeline, null);
    }

    //TODO we are not using envId
    @PostMapping(value = "/contractsSync", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Job createJob(@PathVariable String envId, @RequestBody SyncJobRequest syncJobRequest) {
        return syncJobService.createJob("Service", syncJobRequest);
    }

    @PostMapping(value = "/provisionFreeTierTenant", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Job createFreeTierTenantJob(@RequestBody ProvisionFreeTierTenantJobRequest createJobRequest, @PathVariable String envId) {
        if (createJobRequest.getOfferType() == null) {
            throw new PlatformManagementException(PlatformManagementErrorCode.OFFERTYPE_IS_REQUIRED, HttpStatus.BAD_REQUEST.value());
        }
        return tenantProvisioningJobService.createTenantProvisioningJob(envId, createJobRequest, new EnumMap<>(SkipReason.class));
    }

    @PostMapping(value = "/bulkServicesEnablement", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Job createBulkServicesEnablement(@RequestBody BulkServicesEnablementJobRequest createJobRequest, @PathVariable String envId) {
        return servicesEnablementJobService.createBulkServiceEnablementJob(envId, createJobRequest, new EnumMap<>(SkipReason.class), null, null);
    }

    @PostMapping(value = "/_generateTenant", consumes = MediaType.APPLICATION_JSON_VALUE)
    public String generateTenant(@PathVariable String envId, @RequestBody QATenantCreationRequest qaTenant) {
        return tenantsService.generatePConfigForTenant(envId, qaTenant.getMdmTenantId(), qaTenant.getMatchingStrategy(),
                qaTenant.getTenantSize(), "P1M", RELTIO, qaTenant.getStoragePriorityList(),
                qaTenant.getIsQaAutomation(), qaTenant.getNewInstanceId(), qaTenant.getUseSpannerCloudFunction());
    }

    @PostMapping(value = "/_createTenant", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Job createTenant(@PathVariable String envId, @RequestBody QATenantCreationRequest qaTenant, @RequestParam(required = false, defaultValue = "false") boolean executeJob) {
        Pipeline pipeline;
        String productEdition = "Account360";
        productEditionsService.createProductEditionsIfNotExists(new ProductEdition(productEdition, null, null, null, null,
                null, null, null, null, null, null, null, null, null, null, false, qaTenant.getReltioPackageType()));

        String productEditionNameWithReltioPackageType = "Account360@MDM";

        if (qaTenant.getStoragePriorityList() != null) {
            pipeline = createPipeline(qaTenant, envId, productEditionNameWithReltioPackageType);
        } else {
            try {
                pipeline = pipelineService.getPipeline(envId, "MDMonly");
            } catch (PlatformManagementException e) {
                pipeline = createPipeline(qaTenant, envId, productEditionNameWithReltioPackageType);
            }
        }

        EnterpriseTenantCreationRequest createJobRequest = new EnterpriseTenantCreationRequest(pipeline.getPipelineId() != null ? pipeline.getPipelineId() : qaTenant.getPipelineId(), RELTIO, RELTIO,
                qaTenant.getTenantSize(), null, null, Collections.singletonList("<EMAIL>"), null, null,
                TenantPurpose.INTERNAL, null, productEdition, null, null,
                null, qaTenant.getMdmTenantId(), null, null, null, null, null, null,
                null, null, null, null, qaTenant.getIsQaAutomation(),
                qaTenant.getNewInstanceId(), null, CustomerType.RELTIO_INTERNAL,
                "Cloud and Customer Engineering", "Technical Operations",
                "51 Operations",
                LocalDate.now().plusDays(2).format(DateTimeFormatter.ofPattern("dd-MM-yyyy")),
                qaTenant.getReltioPackageType(), qaTenant.getSkipTasks(), qaTenant.getUseSpannerCloudFunction(),
                false, null);
        Job j = enterpriseTenantJobCreation(createJobRequest, envId, true, true);
        String jobId = j.getJobId();
        String taskId = j.getTasks().getFirst();
        if (executeJob && Boolean.TRUE.equals(createJobRequest.getIsQaAutomation())) {
            genericTaskExecutionService.executeTask(envId, jobId, taskId);
        }
        pipelineService.deletePipeline(envId, pipeline.getID());

        return j;
    }

    @PostMapping(value = "/provisionTenant", consumes = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("@pmsPermissionsManager.hasWriteAccessToEnvironment(#envId)")
    public Job createEnterpriseTenantCreationJob(@PathVariable String envId,
                                                 @RequestBody EnterpriseTenantCreationRequest createJobRequest,
                                                 @RequestParam(required = false, defaultValue = "false") boolean skipValidations,
                                                 @RequestParam(required = false, defaultValue = "true") boolean skipWarmup) {
        return enterpriseTenantJobCreation(createJobRequest, envId, skipValidations, skipWarmup);

    }


    @PostMapping(value = "/provisionTrainingTenant", consumes = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("@pmsPermissionsManager.hasWriteAccessToEnvironment(#envId)")
    public Job createTrainingTenantCreationJob(@PathVariable String envId,
                                               @RequestBody TrainingTenantCreationRequest createJobRequest,
                                               @RequestParam(required = false, defaultValue = "false") boolean skipValidations) {
        return trainingTenantCreationJobService.createTrainingTenantJob(envId, createJobRequest, new EnumMap<>(SkipReason.class), null, skipValidations);
    }

    @PostMapping(value = "/createTenantsSyncJob", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Job tenantsSync(@PathVariable String envId, @RequestBody TenantsSyncRequest tenantsSyncRequest) {
        return tenantsSyncJobService.createJob(envId, tenantsSyncRequest, EnumSet.noneOf(PMSProductName.class), null);
    }

    private Job enterpriseTenantJobCreation(EnterpriseTenantCreationRequest createJobRequest, String envId,
                                            boolean skipValidations, boolean skipWarmup) {
        String customerId = createJobRequest.getCustomerId();
        if ((customerId != null) && (customerId.length() < MIN_CUSTOMER_ID_LENGTH || customerId.length() > MAX_CUSTOMER_ID_LENGTH)) {
            throw new PlatformManagementException(CUSTOMER_ID_LENGTH_IS_NOT_IN_RANGE, HttpStatus.NOT_ACCEPTABLE.value());

        }
        if (Strings.isNotBlank(createJobRequest.getRdmTenantId()) && Strings.isNotBlank(createJobRequest.getRdmTenantToReuse())) {
            throw new PlatformManagementException(BOTH_RDMTENANTTOREUSE_AND_RDMTENANTID_CANNOT_BE_USED, HttpStatus.BAD_REQUEST.value());
        }
        if (createJobRequest.getCustomerType() != null && createJobRequest.getCustomerType() == CustomerType.TRAINING
                && createJobRequest.getOwners() != null && createJobRequest.getOwners().size() > 1) {
            throw new PlatformManagementException(MULTIPLE_OWNERS_ARE_NOT_ALLOWED_IN_TRAINING_CUSTOMER_TYPE, HttpStatus.BAD_REQUEST.value());
        }

        return enterpriseTenantCreationJobService.createJob(envId, createJobRequest, new EnumMap<>(SkipReason.class), null, skipValidations, null, true, skipWarmup);
    }

    @GetMapping(value = "/{jobId}")
    @PreAuthorize("@pmsPermissionsManager.hasReadAccessToEnvironment(#envId)")
    public Job getJob(@PathVariable String jobId,
                      @RequestParam(required = false, defaultValue = "false") boolean withTasks,
                      @PathVariable String envId) {
        Job job = jobService.getJob(envId, jobId);
        if (withTasks) {
            return jobService.getJobWithTask(envId, job);
        }
        return job;
    }

    @GetMapping(value = "")
    @PreAuthorize("@pmsPermissionsManager.hasReadAccessToEnvironment(#envId)")
    public Collection<Job> getAllJobs(@RequestParam(required = false, defaultValue = "false") boolean withTasks,
                                      @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                      @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
                                      @RequestParam(value = "sortField", required = false, defaultValue = "createdTime") SortField sortField,
                                      @RequestParam(value = "sortOrder", required = false, defaultValue = "DESCENDING") SortOrder sortOrder,
                                      @RequestParam(value = "statuses", required = false) Collection<JobStatus> jobStatuses,
                                      @RequestParam(value = "deProvisioning" , required = false) Boolean deProvisioning,
                                      @PathVariable String envId) {
        Collection<Job> jobs;
        if (jobStatuses == null || jobStatuses.isEmpty()) {
            jobs = jobService.getAllJobs(envId, size, offset, sortOrder.name(), sortField.name(),deProvisioning);
        } else {
            jobs = jobService.getJobsByStatus(envId, jobStatuses, size, offset, sortOrder.name(), sortField.name(),deProvisioning);
        }
        if (withTasks) {
            return jobs.stream().map(job -> jobService.getJobWithTask(envId, job)).collect(Collectors.toList());
        }
        return jobs;
    }

    @GetMapping(value = "/{jobId}/tasks")
    @PreAuthorize("@pmsPermissionsManager.hasReadAccessToEnvironment(#envId)")
    public Collection<TaskInstance> getJobTasks(@PathVariable String jobId, @PathVariable String envId) {
        return jobService.getAllTasks(envId, jobId);

    }

    @GetMapping(value = "/{jobId}/details")
    @PreAuthorize("@pmsPermissionsManager.hasReadAccessToEnvironment(#envId)")
    public JobDetail getJobDetail(@PathVariable String jobId, @PathVariable String envId) throws IOException, InvalidDocumentIdException {
        return jobService.getJobDetails(envId, jobId);
    }

    @GetMapping(value = "/jobDetailByTenant/{tenantId}")
    public Collection<Object> getJobDetailByTenant(@PathVariable String tenantId,
                                                   @PathVariable String envId,
                                                   @RequestParam(required = false, defaultValue = "false") boolean debug) {
        return jobService.getJobDetailByTenant(tenantId, envId, debug);
    }

    @GetMapping(value = "/jobsByTenant/{tenantId}")
    public List<Job> getJobFromTenant(@PathVariable String tenantId,
                                      @RequestParam(required = false, defaultValue = "false") boolean allJobs,
                                      @PathVariable String envId) {

        return jobService.getJobFromTenant(tenantId, allJobs, envId);
    }

    @GetMapping(value = "/jobsWithTasksByContractId/{contractId}")
    public Collection<JobWithTask> jobsWithTasksByContractId(@PathVariable String contractId, @PathVariable String envId) {

        return jobService.getJobWithTaskFromContract(contractId, envId);
    }

    @PostMapping(value = "/jobsByTenant/syncToTenants")
    public String syncToTenants(@PathVariable String envId) {
        jobService.syncJobsAndTenants(envId);
        return "SUCCESS";
    }

    @PutMapping(value = "/{jobId}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Job updateJobStatus(@PathVariable String jobId, @RequestBody UpdateJobStatusRequest request, @PathVariable String envId) {
        if (!Objects.equals(request.getJobId(), jobId)) {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), "jobId parameters do not match.");
        }
        return jobService.updateJobStatus(envId, jobId, request.getStatus());
    }

    @PutMapping(value = "/{jobId}/tasks/{taskId}/_execute")
    public TaskInstance executeTask(@PathVariable String jobId, @PathVariable String taskId, @PathVariable String envId) {
        return genericTaskExecutionService.executeTask(envId, jobId, taskId);
    }

    @PutMapping(value = "/{jobId}/tasks/{taskId}/_reExecute")
    public TaskInstance reExecuteTask(@PathVariable String jobId, @PathVariable String taskId, @PathVariable String envId) {
        TaskInstance deleteTenantTaskInstance = reExecuteDeleteTenantTask(envId, jobId);
        if(deleteTenantTaskInstance != null) {
            return deleteTenantTaskInstance;
        }
        return genericTaskExecutionService.reExecuteTask(envId, jobId, taskId, jobService);
    }

    @GetMapping(value = "/{jobId}/tasks/{taskId}")
    @PreAuthorize("@pmsPermissionsManager.hasReadAccessToEnvironment(#envId)")
    public TaskInstance getTask(@PathVariable String jobId, @PathVariable String taskId, @PathVariable String envId) {
        return taskService.getTask(envId, jobId, taskId);
    }

    @PostMapping(value = "/{jobId}")
    public void mapJobParams(@RequestParam(required = false) String tenantId,
                             @RequestParam(required = false) String contractId,
                             @PathVariable String jobId,
                             @PathVariable String envId) {
        jobMappingService.mapJobParamsService(tenantId, contractId, jobId, envId);
    }

    @DeleteMapping(value = "/{jobId}")
    public Job deleteJobAndTask(@PathVariable String envId, @PathVariable String jobId) {
        return jobService.deleteJobAndTask(envId, jobId);
    }

    @PostMapping(value = "/deProvisionTenant", consumes = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("@pmsPermissionsManager.hasWriteAccessToEnvironment(#envId)")
    public Job deProvisionTenant(@PathVariable String envId, @RequestBody DeProvisionTenantJobRequest createJobRequest) {
        return deProvisionTenantJobService.createDeProvisionTenantJob(envId, createJobRequest, JobStatus.DRAFT);
    }

    private Pipeline createPipeline(QATenantCreationRequest qaTenant, String envId, String productEdition) {
        LcaConfig lcaConfig = new LcaConfig();
        lcaConfig.setDvfTimeOut(1000);
        lcaConfig.setLambdaTimeOut(100);
        lcaConfig.setNativeTimeOut(100);
        String currentTime = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        MdmTaskConfig mdmTaskConfig = new MdmTaskConfig("MDM Task", null, Boolean.TRUE,
                productEdition, null, null,
                MatchingStrategy.valueOf(qaTenant.getMatchingStrategy().toString()), "IndustryTTL",
                Collections.emptyList(), qaTenant.getStoragePriorityList(), lcaConfig, null);
        Pipeline pipeline = new Pipeline("auto" + currentTime + "_systemTest", PipelineType.GENERIC_PIPELINE, "pipeline with MDM task only for QA api", Collections.singletonList(mdmTaskConfig));

        return pipelineService.createPipeline(envId, pipeline);
    }

    @PostMapping(value = "/provisionBulkTenants", consumes = MediaType.APPLICATION_JSON_VALUE)
    @PreAuthorize("@pmsPermissionsManager.hasWriteAccessToEnvironment(#envId)")
    public BulkTenantCreationJob createBulkTenantsJob(@PathVariable String envId, @RequestBody BulkTenantCreationRequest createJobRequest, @RequestParam(required = false, defaultValue = "false") boolean skipValidations) throws InvalidDocumentIdException {
        return bulkTenantsJobService.createBulkTenantJobs(envId, createJobRequest, skipValidations);

    }

    @GetMapping(value = "/withParentJobId/{parentJobId}")
    @PreAuthorize("@pmsPermissionsManager.hasReadAccessToEnvironment(#envId)")
    public BulkTenantCreationJob getJobsWithParentJobId(@PathVariable String parentJobId,
                                                        @RequestParam(required = false, defaultValue = "false") boolean withTasks,
                                                        @PathVariable String envId) throws InvalidDocumentIdException {
        return bulkTenantsJobService.getJobsWithParentJobId(envId, parentJobId, withTasks);
    }

    @PutMapping(value = "/updateBulkTenantsJobsStatus/{parentJobId}")
    @PreAuthorize("@pmsPermissionsManager.hasWriteAccessToEnvironment(#envId)")
    public BulkTenantCreationJob updateBulkTenantsJobsStatus(@PathVariable String parentJobId, @PathVariable String envId) throws InvalidDocumentIdException {
        return bulkTenantsJobService.updateJobsStatusWithParentJobId(envId, parentJobId);
    }

    @DeleteMapping(value = "/withParentJobId/{parentJobId}")
    @PreAuthorize("@pmsPermissionsManager.hasWriteAccessToEnvironment(#envId)")
    public BulkTenantCreationJob deleteAllJobsWithParentJobId(@PathVariable String parentJobId, @PathVariable String envId) throws InvalidDocumentIdException {
        return bulkTenantsJobService.deleteAllJobs(envId, parentJobId);
    }

    @GetMapping(value = "/getAllParentJobs")
    @PreAuthorize("@pmsPermissionsManager.hasReadAccessToEnvironment(#envId)")
    public List<BulkTenantCreationJob> getAllParentJobs(@PathVariable String envId,
                                                        @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                                        @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
                                                        @RequestParam(value = "createdBy", required = false) String createdBy,
                                                        @RequestParam(value = "owner", required = false) String owner,
                                                        @RequestParam(value = "sortOrder", required = false, defaultValue = "DESCENDING") SortOrder sortOrder) throws InvalidDocumentIdException {
        return bulkTenantsJobService.getAllParentJobs(envId, size, offset, createdBy, owner, sortOrder.name());
    }

    private TaskInstance reExecuteDeleteTenantTask(String envId, String jobId) {
        Set<String> deleteTenantTaskId = jobService.getTasksByType(envId,jobId, TaskType.DELETE_MDM_TENANT_TASK);
        if(deleteTenantTaskId!=null && !deleteTenantTaskId.isEmpty()){
            TaskInstance deleteTenantTask = taskService.getTask(envId,jobId,deleteTenantTaskId.iterator().next());
            if(deleteTenantTask!=null && deleteTenantTask.getStatus() == TaskStatus.FAILED){
                Set<String> maintenanceModeTaskId = jobService.getTasksByType(envId,jobId, TaskType.MAINTENANCE_MODE_TASK);
                if(maintenanceModeTaskId!=null && !maintenanceModeTaskId.isEmpty()){
                    MaintenanceModeTaskInstance maintenanceTask = (MaintenanceModeTaskInstance)taskService.getTask(envId,jobId,maintenanceModeTaskId.iterator().next());
                    maintenanceTask.setEvents(new ArrayList<>());
                    maintenanceTask.setStatus(TaskStatus.SCHEDULED);
                    taskService.updateTask(envId,jobId,maintenanceTask);
                    genericTaskExecutionService.rescheduleDependentTasks(envId, jobId, deleteTenantTaskId);
                    return genericTaskExecutionService.reExecuteTask(envId, jobId, maintenanceModeTaskId.iterator().next(), jobService);
                }
                return genericTaskExecutionService.reExecuteTask(envId, jobId,deleteTenantTaskId.iterator().next(), jobService);

            }
        }
        return null;
    }

}
