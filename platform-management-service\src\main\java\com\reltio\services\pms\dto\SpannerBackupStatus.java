package com.reltio.services.pms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.reltio.cloud.spanner.BackupInfo;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class SpannerBackupStatus {

    @JsonProperty("status")
    private BackupInfo.State status;

    @JsonProperty("message")
    private String message;

    public SpannerBackupStatus(BackupInfo.State status){
        this.status = status;
    }

    public SpannerBackupStatus(BackupInfo.State status, String message) {
        this.status = status;
        this.message = message;
    }

    public BackupInfo.State getStatus() { return status; }
    public void setStatus(BackupInfo.State status) { this.status = status; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
}