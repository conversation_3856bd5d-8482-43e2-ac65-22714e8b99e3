package com.reltio.services.pms.common.model.jobs.tasks.matchiq;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

public class RIQConfig {
    @JsonProperty(value = "riqServiceUrl")
    private final String riqServiceUrl;

    @JsonCreator
    public RIQConfig(@JsonProperty(value = "riqServiceUrl") String riqServiceUrl) {
        this.riqServiceUrl = riqServiceUrl;
    }

    public String getRiqServiceUrl() {
        return riqServiceUrl;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof RIQConfig)) {
            return false;
        }

        RIQConfig riqConfig = (RIQConfig) o;
        return Objects.equals(riqServiceUrl, riqConfig.riqServiceUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(riqServiceUrl);
    }
}
