package com.reltio.services.pms.common.model.compliance.response.cpusage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The type Cp usage by tenant model.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class CpUsageByTenantModel {

    /**
     * The Date.
     */
    @JsonProperty("date")
    String date;

    /**
     * The Tenant id.
     */
    @JsonProperty("tenantId")
    String tenantId;

    /**
     * The Cp usage.
     */
    @JsonProperty("cpUsage")
    String cpUsage;

    /**
     * The Customer.
     */
    @JsonProperty("customer")
    String customer;

    /**
     * The Purpose.
     */
    @JsonProperty("purpose")
    String purpose;
}
