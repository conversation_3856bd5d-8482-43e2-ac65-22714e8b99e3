package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class ContractsSyncPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonCreator
    public ContractsSyncPipelineTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.CONTRACTS_SYNC_TASK);
    }

}
