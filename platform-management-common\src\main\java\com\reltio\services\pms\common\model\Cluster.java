package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.NullNode;
import com.reltio.devops.common.environment.model.InstanceModel;
import com.reltio.devops.common.environment.model.ServiceModel;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Cluster extends BaseFirestoreEntity {

    @JsonProperty("name")
    private String name;

    @JsonProperty("cloud")
    private String cloud;

    @JsonProperty("region")
    private String region;

    @JsonProperty("type")
    private String type; //c, es, dl

    @JsonProperty("services")
    private List<String> services;   //data, match, api, queueprocess

    @JsonProperty("environments")
    private Set<String> environments;

    @JsonProperty("url")
    private List<String> urlList;

    @JsonProperty("tenants")
    private Set<String> tenants;

    @JsonProperty("customers")
    private Set<String> customers;

    @JsonProperty("newAllocationsPossible")
    private Boolean newAllocationsPossible;

    @JsonProperty("properties")
    private JsonNode properties;

    @JsonCreator
    public Cluster(@JsonProperty("name") String name,
                   @JsonProperty("cloud") String cloud,
                   @JsonProperty("region") String region,
                   @JsonProperty("type") String type,
                   @JsonProperty("services") List<String> services,
                   @JsonProperty("environments") Set<String> environments,
                   @JsonProperty("url") List<String> urlList,
                   @JsonProperty("tenants") Set<String> tenants,
                   @JsonProperty("customers") Set<String> customers,
                   @JsonProperty("newAllocationsPossible") Boolean newAllocationsPossible,
                   @JsonProperty("properties") JsonNode properties) {
        this.name = name;
        this.cloud = cloud;
        this.region = region;
        this.type = type;
        this.services = services;
        this.environments = environments;
        this.urlList = urlList;
        this.tenants = tenants;
        this.customers = customers;
        this.newAllocationsPossible = newAllocationsPossible == null ? Boolean.TRUE : newAllocationsPossible;
        this.properties = properties != null ? properties : NullNode.getInstance();
    }

    public Cluster(ServiceModel service, String env) {
        this.name = service.getName();
        this.cloud = service.getCloudProvider().name().toLowerCase();
        this.customers = new HashSet<>(service.getCustomers());
        this.environments = new HashSet<>(Collections.singleton(env));
        this.newAllocationsPossible = service.getNewAllocationsPossible();
        this.services = service.getPurposes().stream().map(Enum::name).collect(Collectors.toList());
        this.properties = service.getProperties();
        this.type = service.getType().toLowerCase();
        this.region = service.getInstances() != null && service.getInstances().size() > 0 && service.getInstances().get(0).getRegion() != null ? service.getInstances().get(0).getRegion().toLowerCase() : null;
        this.urlList = !service.getUrl().isEmpty() && service.getUrl().stream().iterator().next() != null ?
                new ArrayList<>(service.getUrl()) :
                service.getInstances().stream().map(InstanceModel::getUrl).collect(Collectors.toList());
    }

    public Cluster merge(ServiceModel service, String env) {
        this.name = service.getName();
        this.cloud = service.getCloudProvider().name().toLowerCase();
        Set<String> customers = new HashSet<>(service.getCustomers());
        if (this.getCustomers() != null) {
            customers.addAll(this.getCustomers());
        }
        this.customers = customers;
        Set<String> environments = new HashSet<>(this.getEnvironments());
        environments.add(env);
        this.environments = environments;
        this.newAllocationsPossible = service.getNewAllocationsPossible();
        this.services = service.getPurposes().stream().map(Enum::name).collect(Collectors.toList());
        this.properties = service.getProperties();
        this.type = service.getType().toLowerCase();
        this.region = service.getInstances() != null && service.getInstances().size() > 0 && service.getInstances().get(0).getRegion() != null ?
                service.getInstances().get(0).getRegion().toLowerCase() : null;
        this.urlList =
                service.getUrl() != null && service.getUrl().size() > 0 && service.getUrl().stream().iterator().next() != null ?
                        new ArrayList<>(service.getUrl()) : service.getInstances().stream().map(InstanceModel::getUrl).collect(Collectors.toList());
        return this;
    }

    @Override
    public String getID() {
        if (type.equals("elasticsearch") && urlList != null && urlList.size() == 1) {
            return urlList.get(0).replace("http://", "").replace("https://", "").replace("/", "");
        } else if (type.equals("dynamodb") && urlList != null && urlList.size() == 1) {
            int lastIndex = urlList.get(0).split("/").length - 1;
            return name.concat("@").concat(urlList.get(0).split("/")[lastIndex]);
        } else if (type.equals("spannerdb") && properties != null) {
            String id = name;
            id = environments.size() == 1 ? id.concat("@").concat(environments.iterator().next()) : name;
            id = properties.has("instance") ? id.concat("@").concat(properties.get("instance").asText()) : id;
            return id;
        } else {
            if (region != null) {
                return name.concat("@").concat(cloud).concat("-").concat(region);
            } else if (cloud != null) {
                return name.concat("@").concat(cloud);
            } else {
                return name;
            }
        }
    }

    public String getName() {
        return name;
    }

    public Set<String> getEnvironments() {
        return environments;
    }

    public void setEnvironments(Set<String> environments) {
        this.environments = environments;
    }

    public Set<String> getTenants() {
        return tenants;
    }

    public void setTenants(Set<String> tenants) {
        this.tenants = tenants;
    }

    public Set<String> getCustomers() {
        return customers;
    }

    public void setCustomers(Set<String> customers) {
        this.customers = customers;
    }

    public String getCloud() {
        return cloud;
    }

    public void setCloud(String cloud) {
        this.cloud = cloud;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getType() {
        return type;
    }

    public List<String> getServices() {
        return services;
    }

    public void setServices(List<String> services) {
        this.services = services;
    }

    public List<String> getUrlList() {
        return urlList;
    }

    public void setUrlList(List<String> urlList) {
        this.urlList = urlList;
    }

    public Boolean getNewAllocationsPossible() {
        return newAllocationsPossible;
    }

    public void setNewAllocationsPossible(Boolean newAllocationsPossible) {
        this.newAllocationsPossible = newAllocationsPossible;
    }

    public JsonNode getProperties() {
        return properties;
    }

    public void setProperties(JsonNode properties) {
        this.properties = properties;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Cluster cluster = (Cluster) o;
        return Objects.equals(getName(), cluster.getName()) &&
                Objects.equals(getCloud(), cluster.getCloud()) &&
                Objects.equals(getRegion(), cluster.getRegion()) &&
                Objects.equals(getType(), cluster.getType()) &&
                Objects.equals(getServices(), cluster.getServices()) &&
                Objects.equals(getEnvironments(), cluster.getEnvironments()) &&
                Objects.equals(getUrlList(), cluster.getUrlList()) &&
                Objects.equals(getTenants(), cluster.getTenants()) &&
                Objects.equals(getCustomers(), cluster.getCustomers()) &&
                Objects.equals(getNewAllocationsPossible(), cluster.getNewAllocationsPossible()) &&
                Objects.equals(getProperties(), cluster.getProperties());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getName(), getCloud(), getRegion(), getType(), getServices(), getEnvironments(), getUrlList(), getTenants(), getCustomers(), getNewAllocationsPossible(), getProperties());
    }

    @Override
    public String toString() {
        return "Cluster{" +
                "name='" + name + '\'' +
                ", cloud='" + cloud + '\'' +
                ", region='" + region + '\'' +
                ", type='" + type + '\'' +
                ", services=" + services +
                ", environments=" + environments +
                ", urlList=" + urlList +
                ", tenants=" + tenants +
                ", customers=" + customers +
                ", newAllocationsPossible=" + newAllocationsPossible +
                ", properties=" + properties +
                "} " + super.toString();
    }

    public Cluster toLowerCase() {
        String lowerName = this.getName() == null ? null : this.getName().toLowerCase();
        String lowerCloud = this.getCloud() == null ? null : this.getCloud().toLowerCase();
        String lowerRegion = this.getRegion() == null ? null : this.getRegion().toLowerCase();
        String lowerType = this.getType() == null ? null : this.getType().toLowerCase();
        List<String> lowerService = this.getServices() == null ? Collections.emptyList() : this.getServices().stream().map(String::toLowerCase).collect(Collectors.toList());
        Set<String> lowerEnvs = new HashSet<>();
        Set<String> lowerCust = new HashSet<>();
        List<String> lowerUrls = new ArrayList<>();
        if (this.getEnvironments() != null) {
            for (String env : this.getEnvironments()) {
                lowerEnvs.add(env.toLowerCase());
            }
        }
        if (this.getCustomers() != null) {
            for (String cust : this.getCustomers()) {
                lowerCust.add(cust.toLowerCase());
            }
        }
        if (this.getUrlList() != null) {
            for (String url : this.getUrlList()) {
                if (url != null) {
                    lowerUrls.add(url.toLowerCase());
                }
            }
        }
        return new Cluster(lowerName, lowerCloud, lowerRegion, lowerType, lowerService, lowerEnvs, lowerUrls, this.getTenants(), lowerCust, newAllocationsPossible, properties);
    }
}
