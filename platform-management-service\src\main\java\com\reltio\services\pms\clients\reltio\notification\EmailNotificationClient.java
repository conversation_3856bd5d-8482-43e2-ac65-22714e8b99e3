package com.reltio.services.pms.clients.reltio.notification;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.reltio.io.IOUtils;
import com.reltio.services.pms.common.PMSRestTemplate;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Set;

@Service
public class EmailNotificationClient {

    private static final Logger LOGGER = Logger.getLogger(EmailNotificationClient.class);
    private static final String BASE_NOTIFICATION_URL = "%s/api/tenants/%s";
    private static final String SEND_NOTIFICATION_URL_PATTERN = BASE_NOTIFICATION_URL + "/notifications";
    private static final String GET_NOTIFICATION_TYPE_URL_PATTERN = BASE_NOTIFICATION_URL + "/notificationTypes";
    private static final String CONFIGURE_NOTIFICATION_TYPE_URL_PATTERN = GET_NOTIFICATION_TYPE_URL_PATTERN + "/%s";
    private final PMSRestTemplate rest;
    private final ObjectMapper mapper;

    @Autowired
    public EmailNotificationClient(PMSRestTemplate rest) {
        this.rest = rest;
        this.mapper = new ObjectMapper();
    }

    public void sendNotification(String notificationUrl, String tenantId, String subject, String body, Set<String> emailIds, String notificationType) {
        try {
            String emailIdList = String.join("\",\"", emailIds);
            String file = IOUtils.readResource(EmailNotificationClient.class, "notification_requests/send_notification_request.json")
                    .replace("{PMS_NOTIFICATION}", notificationType)
                    .replace("{body}", body)
                    .replace("{subject}", subject)
                    .replace("{emailIdList}", emailIdList);
            JsonNode request = mapper.readTree(file);
            sendEmail(notificationUrl, tenantId, request);
        } catch (IOException ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    public void sendEmail(String baseUrl, String tenantId, JsonNode body) {

        String url = String.format(SEND_NOTIFICATION_URL_PATTERN, baseUrl, tenantId);
        try {
            rest.postForObject(url, body, JsonNode.class);
        } catch (Exception ex) {
            LOGGER.error(String.format("Error in sending Email message for tenant %s", tenantId));
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_NOTIFICATION_SERVICE_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    public String getTenantNotificationTypes(String notificationUrl, String tenantId) {
        String url = String.format(GET_NOTIFICATION_TYPE_URL_PATTERN, notificationUrl, tenantId);
        try {
            return rest.getForObject(url, String.class);
        } catch (Exception ex) {
            LOGGER.error(String.format("Error in retrieving notification type for tenant %s", tenantId));
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_NOTIFICATION_SERVICE_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

    public void configureNotificationType(String notificationUrl, String tenantId, Set<String> owners, String notificationType) {
        try {
            String url = String.format(CONFIGURE_NOTIFICATION_TYPE_URL_PATTERN, notificationUrl, tenantId, notificationType);
            String ownersList = "\"".concat(String.join("\",\"", owners)).concat("\"");
            String file = IOUtils.readResource(EmailNotificationClient.class, "notification_requests/configure_notification_type_request.json")
                    .replace("\"{emailList}\"", "[".concat(ownersList).concat("]"));
            JsonNode request = mapper.readTree(file);
            rest.postForObject(url, request, String.class);
        } catch (IOException ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        } catch (Exception ex) {
            LOGGER.error(String.format("Error in configuring notification type for tenant %s", tenantId));
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_NOTIFICATION_SERVICE_CANNOT_BE_PROCESSED, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex, ex.getMessage());
        }
    }

}
