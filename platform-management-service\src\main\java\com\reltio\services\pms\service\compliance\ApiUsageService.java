package com.reltio.services.pms.service.compliance;

import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByEndpointResponse;
import com.reltio.services.pms.common.model.compliance.response.apiusage.ApiUsageByTenantResponse;
import com.reltio.services.pms.common.sales.model.ReltioTenant;

import java.util.Collection;

/**
 * The interface Api usage service.
 */
public interface ApiUsageService {
    /**
     * Gets api usage by tenant.
     *
     * @param tenantCollection the tenant collection
     * @param response         the response
     * @return the api usage by tenant
     */
    ApiUsageByTenantResponse getApiUsageByTenant(Collection<ReltioTenant> tenantCollection, ApiUsageByTenantResponse response);

    /**
     * Gets api usage by tenant.
     *
     * @param tenantCollection the tenant collection
     * @param response         the response
     * @return the api usage by tenant
     */
    ApiUsageByEndpointResponse getApiUsageByEndpoint(Collection<ReltioTenant> tenantCollection, ApiUsageByEndpointResponse response);
}
