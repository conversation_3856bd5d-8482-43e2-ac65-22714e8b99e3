package com.reltio.services.pms.common.model.supportability;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * The type Requests.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class Processors {


    /**
     * The Request.
     */
    @JsonProperty("type")
    private String type;
    /**
     * The Count.
     */
    @JsonProperty("totalCount")
    private Long totalCount;
    /**
     * The Failed request.
     */
    @JsonProperty("failedCount")
    private Long failedCount;

    /**
     * The Success request.
     */
    @JsonProperty("successCount")
    private Long successCount;
    /**
     * The Avg.
     */
    @JsonProperty("avg")
    private Long avg;
    /**
     * The Group Type.
     */
    @JsonProperty("groupType")
    private String groupType;
}
