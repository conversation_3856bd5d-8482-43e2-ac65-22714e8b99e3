package com.reltio.services.pms.service.jobs.tasks.deprovision.auth;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.reltio.common.config.RDMTenant;
import com.reltio.common.config.TenantConfiguration;
import com.reltio.services.pms.clients.reltio.auth.ReltioAuthClient;
import com.reltio.services.pms.clients.reltio.mdm.MDMClient;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.RdmTenant;
import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.common.model.User;
import com.reltio.services.pms.common.model.auth.AuthCustomer;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.authDeprovision.AuthDeProvisionTaskInstance;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.AccountStatus;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.dao.AuthCustomerDao;
import com.reltio.services.pms.dao.RdmTenantsDao;
import com.reltio.services.pms.service.PmsLockService;
import com.reltio.services.pms.service.environment.EnvironmentService;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.AbstractMultiStepTaskExecutionService;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.reltio.services.pms.service.reltiotenant.TenantsService;
import org.apache.log4j.Logger;
import org.springframework.http.HttpStatus;
import static com.reltio.auth.provider.service.security.SecurityConstants.ROLE_SECURITY_ADMIN;

public class AuthDeProvisionTaskExecutionService extends AbstractMultiStepTaskExecutionService<AuthDeProvisionTaskInstance> {
    private static final Logger LOG = Logger.getLogger(AuthDeProvisionTaskExecutionService.class);

    public static final String TENANT_REMOVED_FROM_USERS_EVENT = "Tenant removed from auth users";
    public static final String TENANT_REMOVED_FROM_AUTH_CUSTOMER_EVENT = "Tenant removed from auth customers";
    public static final String TENANT_REMOVED_FROM_GROUPS_EVENT = "Tenant removed from groups if applicable";
    public static final String FULL_CUSTOMER_DEPROVISION_EVENT = "Full customer deprovision completed";
    public static final String USERS_WITHOUT_TENANTS_DELETED_EVENT = "Users without any tenants deleted";
    public static final String SPECIFIC_USERS_DELETED_EVENT = "Specific users deleted if applicable";
    public static final String ASSOCIATED_RDM_REMOVED_EVENT = "Associated RDM tenants removed";
    private final ReltioAuthClient authService;
    private final MDMClient mdmClient;
    private final EnvironmentService environmentService;
    private final RdmTenantsDao rdmTenantDao;
    private final AuthCustomerDao authCustomerDao;
    private List<User> authUsers;

    public AuthDeProvisionTaskExecutionService(String envId,
                                               AuthDeProvisionTaskInstance authDeProvisionTaskInstance,
                                               GrafanaDashboardGBQService grafanaDashboardGBQService,
                                               ReltioAuthClient authService,
                                               MDMClient mdmClient,
                                               EnvironmentService environmentService,
                                               TenantsService tenantsService,
                                               PmsLockService pmsLockService,
                                               RdmTenantsDao rdmTenantDao,
                                               AuthCustomerDao  authCustomerDao) {
        super(envId, authDeProvisionTaskInstance, grafanaDashboardGBQService, tenantsService, pmsLockService);
        this.authService = authService;
        this.mdmClient = mdmClient;
        this.environmentService = environmentService;
        this.rdmTenantDao = rdmTenantDao;
        this.authCustomerDao = authCustomerDao;
    }

    @Override
    public void executeTask() {

        AuthDeProvisionTaskInstance task = getTaskDetail();
        boolean failOnError = task.isFailOnError();
        List<String> protectedUsers = task.getProtectedUsers();

        if (task.isFullCustomerDeprovision() && task.getAuthCustomerId() != null) {
            boolean result = runOneStep(() -> deprovisionFullCustomer(task.getAuthCustomerId()), failOnError, FULL_CUSTOMER_DEPROVISION_EVENT);
            if (!result) {
                completeTask();
                return;
            }
            completeTask();
            return;
        }

        authUsers = getAuthUsers();
        if (taskDetail.getTenantId() != null && handleTenantRemovalFlow(task, protectedUsers, failOnError)) {
            return;
        }

        if (task.getAuthCustomerId() != null && handleSpecificDeletions(task, protectedUsers, failOnError)) {
            return;
        }
        completeTask();
    }

    private boolean handleSpecificDeletions(AuthDeProvisionTaskInstance task, List<String> protectedUsers, boolean failOnError) {
        if (!task.getUsersToDelete().isEmpty()) {
            boolean result = runOneStep(() -> deleteSpecificUsers(task.getAuthCustomerId(), task.getUsersToDelete(), protectedUsers), failOnError, SPECIFIC_USERS_DELETED_EVENT);
            if (!result && failOnError) {
                completeTask();
                return true;
            }
        }
        return false;
    }

    private boolean handleTenantRemovalFlow(AuthDeProvisionTaskInstance task,
                                            List<String> protectedUsers,
                                            boolean failOnError) {
        boolean shortCircuited = false;

        if ( task.isRemoveGroupAssignment()) {
            boolean result = runOneStep(() -> removeTenantFromGroups(task.getTenantId()),
                    failOnError,
                    TENANT_REMOVED_FROM_GROUPS_EVENT);
            shortCircuited = isShortCircuited(failOnError, result);
        }

        if (!shortCircuited) {
            boolean result = runOneStep(() -> {
                try {
                    removeTenantAssignmentsFromUsersAndCustomers(task.getTenantId(), protectedUsers);
                } catch (InvalidDocumentIdException e) {
                    LOG.error("Tenant is not present in database ", e);
                    throw new PlatformManagementException(PlatformManagementErrorCode.TENANT_IS_NOT_PRESENT_IN_DATABASE, HttpStatus.NOT_FOUND.value());
                }
            }, failOnError, TENANT_REMOVED_FROM_AUTH_CUSTOMER_EVENT);

            shortCircuited = isShortCircuited(failOnError, result);
        }

        if (!shortCircuited && task.isRemoveAssociatedRdms()) {
            boolean result = runOneStep(() -> removeAssociatedRdmTenants(task.getTenantId(), protectedUsers),
                    failOnError,
                    ASSOCIATED_RDM_REMOVED_EVENT);
            shortCircuited = isShortCircuited(failOnError, result);
        }

        if (!shortCircuited && task.isDeleteAssociatedUsers()) {
            boolean result = runOneStep(() -> deleteUsersWithoutTenants( protectedUsers),
                    failOnError,
                    USERS_WITHOUT_TENANTS_DELETED_EVENT);
            shortCircuited = isShortCircuited(failOnError, result);
        }

        if (shortCircuited) {
            completeTask();
        }

        return shortCircuited;
    }

    private static boolean isShortCircuited(boolean failOnError, boolean result) {
        return !result && failOnError;
    }

    public void deprovisionFullCustomer(String authCustomerId) {

        List<String> protectedCustomers = getTaskDetail().getProtectedCustomers();
        if (protectedCustomers.contains(authCustomerId)) {
            LOG.info("Skipping full deprovision for protected customer: " + authCustomerId);
            return;
        }

        AuthCustomer customer = authService.getCustomer(authCustomerId);
        if (customer == null) {
            LOG.warn("Auth customer not found: " + authCustomerId);
            return;
        }
        Set<String> clients = customer.getApplicationClients();
        authService.dissociateApplicationClientsFromAuthCustomer(authCustomerId);
        if (clients != null) {
            for (String clientId : clients) {
                try {
                    authService.deleteClient(clientId);
                    taskDetail.addDeletedClients(clientId);
                } catch (RuntimeException e) {
                    LOG.error("Failed to delete client: " + clientId, e);
                    throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.NOT_FOUND.value());
                }
            }
        }

        Set<String> tenantIds = customer.getTenants();
        if (tenantIds != null) {
            for (String tenantId : tenantIds) {
                removeAuthAccessFromTenantIfExists(tenantId);
            }
        }

        authService.deleteCustomer(authCustomerId);
        makeCustomerInactive(authCustomerId);
    }

    public void removeTenantAssignmentsFromUsersAndCustomers(String tenantId, List<String> protectedUsers) throws InvalidDocumentIdException {

        ReltioTenant reltioTenant = tenantsService.getTenant(tenantId);
        if (reltioTenant == null) {
            return;
        }

        Set<String> customerIds = authService.getCustomersForTenant(tenantId);
        deleteFromUserAndCustomer(tenantId, protectedUsers, authUsers, customerIds);
    }

    public void removeRDMAssignmentsFromUsersAndCustomers(String tenantId, List<String> protectedUsers) throws InvalidDocumentIdException {

        RdmTenant rdmTenant = rdmTenantDao.get(tenantId);
        if(rdmTenant == null) {
            return;
        }

        Set<String> customerIds = authService.getCustomersForTenant(tenantId);
        deleteFromUserAndCustomer(tenantId, protectedUsers, authUsers, customerIds);
    }

    public void deleteFromUserAndCustomer(String tenantId, List<String> protectedUsers, List<User> authUsers, Set<String> customerIds) {
        for (User user : authUsers) {
            if (protectedUsers.contains(user.getEmail())) {
                LOG.info("Skipping tenant removal for protected user: " + user.getEmail());
                continue;
            }
            Map<String, Set<String>> roles = user.getUserPermissions().getRoles();
            if (roles != null && roles.values().stream().anyMatch(roleSet -> roleSet.contains(ROLE_SECURITY_ADMIN))) {
                LOG.info("Skipping tenant removal for user with ROLE_SECURITY_ADMIN: " + user.getEmail());
                taskDetail.addSkippedUser(user.getEmail());
                continue;
            }

            if (roles != null) {
                for (Map.Entry<String, Set<String>> entry : roles.entrySet()) {
                    entry.getValue().remove(tenantId);
                }
                authService.updateUserRoles(user, true);
                taskDetail.addDeprovisionedUsers(user.getEmail());
            }
        }
        addEventToTask(TENANT_REMOVED_FROM_USERS_EVENT);

        String samlConfigUrl = environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.SAML_CONFIG);
        for (String customerId : customerIds) {
            AuthCustomer customer = authService.getCustomer(customerId);
            if (customer != null && customer.getTenants().contains(tenantId)) {
                authService.removeTenantFromCustomer(customerId, tenantId, samlConfigUrl);
                taskDetail.addDeprovisionedCustomer(customerId);
                removeTenantFromAuthDao(tenantId, customerId);
            }
        }

        removeAuthAccessFromTenantIfExists(tenantId);
    }

    public void removeAuthAccessFromTenantIfExists(String tenantId) {
            getOrAddProductProvisioningConfig(tenantId,getServiceName());
            LOG.info("Set provisioningStatus=REMOVED for tenantId: " + tenantId);

    }

    public void removeTenantFromAuthDao(String tenantId, String authCustomerId) {
        try {
            AuthCustomer authCustomer = authCustomerDao.get(authCustomerId);
            if (authCustomer != null) {
                Set<String> tenants = authCustomer.getTenants();
                tenants.remove(tenantId);
                authCustomerDao.update(authCustomer);
            }
        } catch (InvalidDocumentIdException e) {
            LOG.warn("Tenant doc not found in Firestore for id: " + authCustomerId
                    + " - skipping AUTH_ACCESS removal.", e);
        }
    }

    public void makeCustomerInactive(String authCustomerId) {
        try {
            AuthCustomer authCustomer = authCustomerDao.get(authCustomerId);
            if (authCustomer != null) {
                authCustomer.setAccountStatus(AccountStatus.INACTIVE);
                authCustomerDao.update(authCustomer);
            }
        } catch (InvalidDocumentIdException e) {
            LOG.warn("Tenant doc not found in Firestore for id: " + authCustomerId
                    + " - skipping AUTH_ACCESS removal.", e);
        }
    }

    public void removeTenantFromGroups(String tenantId) {
        Set<String> customerIds = authService.getCustomersForTenant(tenantId);

        boolean removeGroupAssignment = getTaskDetail().isRemoveGroupAssignment();

        if (removeGroupAssignment ||
                mdmClient.getTenantConfigurationReturnsNullIfTenantNotFound(envId, tenantId) == null) {

            for (String customerId : customerIds) {
                JsonNode groups = authService.getGroupForCustomer(customerId);

                if (groups == null || !groups.isArray()) {
                    LOG.warn("No groups found for customer");
                    continue;
                }

                for (JsonNode group : groups) {
                    String groupName = group.has("groupName") ? group.get("groupName").asText() : null;
                    if (groupName == null) {
                        LOG.warn("Skipping a group without a valid groupName under customer ");
                        continue;
                    }

                    boolean tenantRemoved = isTenantRemoved(tenantId, group);

                    if (tenantRemoved) {
                        authService.removeTenantFromGroups(customerId, groupName, tenantId);
                        taskDetail.addDeprovisionedGroup(groupName);
                        LOG.info("Removed tenant from group under customer ");
                    }
                }
            }
        }
    }

    public static boolean isTenantRemoved(String tenantId, JsonNode group) {
        JsonNode rolesNode = group.get("roles");
        boolean tenantRemoved = false;
        if (rolesNode != null && rolesNode.isObject()) {
            ObjectNode rolesObject = (ObjectNode) rolesNode;
            Iterator<String> roleNames = rolesObject.fieldNames();

            while (roleNames.hasNext()) {
                String role = roleNames.next();
                ArrayNode tenantsArray = (ArrayNode) rolesObject.get(role);

                if (tenantsArray != null && tenantsArray.isArray()) {
                    for (int i = tenantsArray.size() - 1; i >= 0; i--) {
                        if (tenantId.equals(tenantsArray.get(i).asText())) {
                            tenantsArray.remove(i);
                            tenantRemoved = true;
                        }
                    }
                }
            }
        }
        return tenantRemoved;
    }


    public void removeAssociatedRdmTenants(String mdmTenantId, List<String> protectedUsers) {
        LOG.info("Initiating removal of associated RDM tenant for MDM tenant: " + mdmTenantId);
        try {
            TenantConfiguration mdmTenantConfig = mdmClient.getTenantConfiguration(envId, mdmTenantId);
            if (mdmTenantConfig == null || mdmTenantConfig.getRdmConfig() == null) {
                LOG.warn("No RDM configuration found for MDM tenant: " + mdmTenantId);
                return;
            }
            List<RDMTenant> rdmTenants = mdmTenantConfig.getRdmConfig().getRdmTenants();
            if (rdmTenants == null || rdmTenants.isEmpty()) {
                LOG.info("No associated RDM tenant found for MDM tenant: " + mdmTenantId);
                return;
            }
            handleRdmTenantRemoval(protectedUsers, rdmTenants);
        } catch (Exception e) {
            LOG.error("Error occurred while removing associated RDM tenant for MDM tenant: " + mdmTenantId, e);
        }
    }

    public void handleRdmTenantRemoval(List<String> protectedUsers, List<RDMTenant> rdmTenants) {
        for(RDMTenant rdmTenant : rdmTenants) {
            try {
                if(taskDetail.isRemoveGroupAssignment()){
                    removeTenantFromGroups(rdmTenant.getRdmTenantId());
                }
                removeRDMAssignmentsFromUsersAndCustomers(rdmTenant.getRdmTenantId(), protectedUsers);
                LOG.info("Successfully processed RDM Tenant: " + rdmTenant.getRdmTenantId());
                addEventToTask("Processed RDM Tenant: " + rdmTenant.getRdmTenantId());
            } catch (Exception e) {
                LOG.error("Failed to process RDM Tenant: " + rdmTenant.getRdmTenantId(), e);
            }
        }
    }

    public void deleteUsersWithoutTenants(List<String> protectedUsers) {

        List<String> protectedCustomers = getTaskDetail().getProtectedCustomers();

        for (User user : getAuthUsers()) {
            if (protectedUsers.contains(user.getEmail())) {
                taskDetail.addSkippedUser(user.getEmail());
                LOG.info("Skipping deletion for protected user: " + user.getEmail());
                continue;
            }
            if (protectedCustomers.contains(user.getCustomer())) {
                taskDetail.addSkippedUser(user.getEmail());
                LOG.info("Skipping deletion for user belonging to protected customer: " + user.getCustomer());
                continue;
            }
            Map<String, Set<String>> roles = user.getUserPermissions().getRoles();
            boolean hasTenantsLeft = false;
            if (roles != null) {
                for (Set<String> ts : roles.values()) {
                    if (!ts.isEmpty()) {
                        hasTenantsLeft = true;
                        break;
                    }
                }
            }
            if (!hasTenantsLeft) {
                authService.deleteUser(user.getEmail());
                taskDetail.addDeletedUsers(user.getEmail());
            }
        }
    }

    private void deleteSpecificUsers(String authCustomerId, List<String> usersToDelete, List<String> protectedUsers) {

        AuthCustomer customer = authService.getCustomer(authCustomerId);
        if (customer == null) {
            LOG.warn("Auth customer not found: " + authCustomerId);
            return;
        }

        List<String> protectedCustomers = getTaskDetail().getProtectedCustomers();

        for (String userName : usersToDelete) {
            User user = authService.getUser(userName);
            if (user == null) {
                LOG.warn("User not found: " + userName);
                continue;
            }

            if (protectedUsers.contains(user.getEmail())) {
                taskDetail.addSkippedUser(user.getEmail());
                LOG.info("Skipping delete for protected user: " + user.getEmail());
                continue;
            }

            if (!authCustomerId.equals(user.getCustomer())) {
                taskDetail.addSkippedUser(user.getEmail());
                LOG.warn("Skipping delete for user " + userName
                        + " because they do not belong to authCustomer " + authCustomerId);
                continue;
            }

            if (protectedCustomers.contains(authCustomerId)) {
                taskDetail.addSkippedUser(user.getEmail());
                LOG.info("Skipping delete for user " + userName
                        + " as parent customer is protected: " + authCustomerId);
                continue;
            }

            authService.deleteUser(userName);
            taskDetail.addDeletedUsers(user.getEmail());
            LOG.info("Deleted user " + userName + " from authCustomer " + authCustomerId);
        }
    }

    public List<User> getAuthUsers() {
        if (this.authUsers == null) {
            String tenantId = getTaskDetail().getTenantId();
            this.authUsers = authService.getAuthUsersListFromTenant(tenantId);
        }
        return this.authUsers;
    }

    private void completeTask() {
        if (getTaskDetail().getStatus() == TaskStatus.PROCESSING) {
            getTaskDetail().setStatus(TaskStatus.COMPLETED);
            updateTask();
        }
    }

    @Override
    protected void addEventToTask(String event) {
        taskDetail.addEvent(event);
        updateTask();
    }

    @Override
    protected boolean isProcessed(String event) {
        return getTaskDetail().getEvents().contains(event);
    }

    protected PMSProductName getServiceName() {
        return PMSProductName.AUTH_ACCESS;
    }
}
