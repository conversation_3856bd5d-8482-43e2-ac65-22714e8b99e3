package com.reltio.services.pms.common.model.compliance.response.cpusage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * The type Cp usage by date response.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class CpUsageByDateResponse {

    /**
     * The Cp usage by date model list.
     */
    @JsonProperty("cpUsageByDateModel")
    List<CpUsageByDateModel> cpUsageByDateModelList;

    /**
     * The Full count.
     */
    @JsonProperty("totalCount")
    Long totalCount;


}
