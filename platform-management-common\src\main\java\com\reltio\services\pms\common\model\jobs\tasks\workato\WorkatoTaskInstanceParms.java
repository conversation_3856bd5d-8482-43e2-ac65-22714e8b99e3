package com.reltio.services.pms.common.model.jobs.tasks.workato;

import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServicesEnablementBaseTaskInstanceParams;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;

public interface WorkatoTaskInstanceParms extends ServicesEnablementBaseTaskInstanceParams {
    String getProductEdition();
    String getDnbApiSecret();
    String getDnbApiKey();
    ReltioPackageType getReltioPackageType();
}
