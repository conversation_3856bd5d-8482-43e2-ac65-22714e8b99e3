package com.reltio.services.pms.interceptor;

import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.service.gbq.LoggerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.reflect.Type;

@ControllerAdvice
public class DARequestBodyAdviceAdapter extends RequestBodyAdviceAdapter {

    private final LoggerService loggerService;
    private final HttpServletRequest httpServletRequest;

    @Autowired
    public DARequestBodyAdviceAdapter(LoggerService loggerService, HttpServletRequest httpServletRequest) {
        this.loggerService = loggerService;
        this.httpServletRequest = httpServletRequest;
    }

    @Override
    public boolean supports(MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return true;
    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        try {
            loggerService.logRequest(httpServletRequest, body);
        } catch (IOException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, e.getMessage());
        }

        return super.afterBodyRead(body, inputMessage, parameter, targetType, converterType);
    }
}
