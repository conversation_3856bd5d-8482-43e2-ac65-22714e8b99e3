package com.reltio.services.pms.clients.reltio.rdm;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.ImmutableSet;
import com.reltio.services.pms.common.PMSRestTemplate;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.function.Supplier;

@Service
public class RDMClient {
    private static final String CREATE_RDM_TENANT_URL = "%s/configuration";
    private static final String ASSOCIATE_RDM_AL_TENANT_URL = "%s/properties";
    private static final String CREATE_AL_TENANT_URL = "%s/tenants";
    private static final String RDM_WORKFLOW_REGISTRATION_URL = "%s/workflow-adapter/workflow/registrations";
    private static final String RDM_CREATE_LOOKUPS_URL = "%s/lookups/%s";
    private static final String GET_RDM_TENANT_URL = "%s/configuration/%s";
    private static final String CREATE_RDM_TASK = "%s/tasks/%s";
    private static final String DELETE_RDM_TENANT_TASK_URL = "%s/tasks/%s/%s";
    private static final String TASK_FAILED = "FAILED";
    private static final String TASK_FINISHED = "FINISHED";
    private final PMSRestTemplate rest;
    private final int sleepDurationSeconds;
    private final int maxAttempts;


    @Autowired
    public RDMClient(PMSRestTemplate rest,
                     @Value("${rdm.sleepDurationSeconds:10}") int sleepDurationSeconds,
                     @Value("${rdm.maxAttempts:30}") int maxAttempts) {
        this.rest = rest;
        this.sleepDurationSeconds = sleepDurationSeconds;
        this.maxAttempts = maxAttempts;
    }

    public void createRdmTenant(JSONObject request, String rdmServiceUrl) {
        try {

            try {
                rest.getForObject(String.format(GET_RDM_TENANT_URL, rdmServiceUrl, request.get("tenantId")), String.class);
            } catch (Exception ex) {
                rest.postForObject(String.format(CREATE_RDM_TENANT_URL, rdmServiceUrl), request, JSONObject.class);
            }
        } catch (Exception ex) {
            throw new RdmClientException("Can not Create Rdm Tenant", ex);
        }
    }

    public void createActivityLogTenant(JSONObject request, String activityLogServiceUrl) {
        try {
            rest.postForObject(String.format(CREATE_AL_TENANT_URL, activityLogServiceUrl), request, JSONObject.class);
        } catch (Exception ex) {
            throw new RdmClientException("Can not Create Activity Log Tenant", ex);
        }
    }

    public void associateRdmToActivityLogTenant(JSONObject request, String rdmServiceUrl) {
        try {
            if (!checkIfPropertyPresent(rdmServiceUrl, request.get("tenantId").toString())) {
                rest.postForObject(String.format(ASSOCIATE_RDM_AL_TENANT_URL, rdmServiceUrl), request, JSONObject.class);
            }
        } catch (Exception ex) {
            throw new RdmClientException("Can not Associate  Activity Log Tenant with Rdm Tenant", ex);
        }
    }

    private boolean checkIfPropertyPresent(String rdmServiceUrl, String rdmTenantId) {
        String propertyUrl = String.format(ASSOCIATE_RDM_AL_TENANT_URL, rdmServiceUrl) + "/" + rdmTenantId;
        try {
            rest.getForEntity(propertyUrl, String.class);
            return true;
        } catch (Exception ex) {
            return false;
        }
    }

    public void registerWorkflow(JSONObject request, String rdmWorkFlowServiceUrl, String rdmServiceUrl) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("EnvironmentURL", rdmServiceUrl);
            HttpEntity<JSONObject> entity = new HttpEntity<>(request, headers);
            rest.exchange(String.format(RDM_WORKFLOW_REGISTRATION_URL, rdmWorkFlowServiceUrl), HttpMethod.POST, entity, JSONObject.class);
        } catch (Exception ex) {
            throw new RdmClientException("Can not Register Workflow for Rdm Tenant", ex);
        }
    }

    public void createRdmLookups(String rdmTenantID,
                                 List<JsonNode> rdmLookUpsAndSourceDetails,
                                 String rdmServiceUrl) {
        List<String> errors = new ArrayList<>();

        try {

            JSONParser parser = new JSONParser();
            JSONArray allLookups = new JSONArray();
            for (JsonNode node : rdmLookUpsAndSourceDetails) {
                String replaced = node.toString().replace("{{rdmTenant}}", rdmTenantID);
                JSONArray arrayOfLookups = (JSONArray) parser.parse(replaced);
                allLookups.addAll(arrayOfLookups);
            }

            final int BATCH_SIZE = 100;
            for (int start = 0; start < allLookups.size(); start += BATCH_SIZE) {
                int end = Math.min(start + BATCH_SIZE, allLookups.size());
                JSONArray batch = new JSONArray();

                for (int i = start; i < end; i++) {
                    batch.add(allLookups.get(i));
                }

                try {
                    rest.postForObject(
                            String.format(RDM_CREATE_LOOKUPS_URL, rdmServiceUrl, rdmTenantID),
                            batch,
                            JSONArray.class
                    );
                } catch (Exception batchEx) {
                    for (Object obj : batch) {
                        JSONArray singleItemArray = new JSONArray();
                        singleItemArray.add(obj);
                        try {
                            rest.postForObject(
                                    String.format(RDM_CREATE_LOOKUPS_URL, rdmServiceUrl, rdmTenantID),
                                    singleItemArray,
                                    JSONArray.class
                            );
                        } catch (Exception singleEx) {
                            errors.add(String.format(
                                    "Failed to post single lookup: %s", singleEx.getMessage()
                            ));
                        }
                    }
                }
            }
        } catch (Exception ex) {
            throw new RdmClientException("Cannot create lookups for RDM Tenant", ex);
        }

        if (!errors.isEmpty()) {
            String errorSummary = String.join("; ", errors);
            throw new RdmClientException(
                    "Some lookups failed to post. Partial success occurred. Errors: " + errorSummary
            );
        }
    }


    public List<String> getRDMTenants(String rdmServiceUrl) {
        try {
            return rest.getForObject(String.format(CREATE_RDM_TENANT_URL, rdmServiceUrl), List.class);
        } catch (Exception e) {
            throw new RdmClientException("Cannot get Rdm Tenants", e);
        }
    }

    /**
     * Start delete RDM tenant task
     *
     * @param rdmServiceUrl RDM service url
     * @param rdmTenantId   tenant id to delete
     * @return task object
     */
    public JSONObject deleteRDMTenant(String rdmServiceUrl, String rdmTenantId) {
        String deleteEndpoint = String.format(CREATE_RDM_TASK, rdmServiceUrl, rdmTenantId);
        JSONObject body = new JSONObject();
        body.put("type", "deleteTenant");
        return rest.postForObject(deleteEndpoint, body, JSONObject.class);
    }

    public JSONObject deleteIndexes(String rdmServiceUrl, String rdmTenantId) {
        String deleteEndpoint = String.format(CREATE_RDM_TASK, rdmServiceUrl, rdmTenantId);
        JSONObject body = new JSONObject();
        body.put("type", "cleanupIndex");
        return rest.postForObject(deleteEndpoint, body, JSONObject.class);
    }

    /**
     * Run delete RDM task and wait till complete
     *
     * @param rdmServiceUrl rdm service url
     * @param rdmTenantId   rdm tenant id to delete
     * @return task details if completed successfully, throw exception in other case.
     */
    public JSONObject deleteRDMTenantAndWait(String rdmServiceUrl, String rdmTenantId) {
        JSONObject task = deleteRDMTenant(rdmServiceUrl, rdmTenantId);
        JSONObject taskDetails = waitForTask(rdmServiceUrl, rdmTenantId, (String) task.get("id"), sleepDurationSeconds, maxAttempts);
        if (taskDetails.get("status").equals(TASK_FINISHED)) {
            return taskDetails;
        } else {
            throw new RdmClientException(String.format("Delete task failed. Task details %s", taskDetails.toJSONString()));
        }
    }

    /**
     * Get tenant task by task id
     *
     * @param rdmServiceUrl RDM service url
     * @param rdmTenantId   tenant id to get tasks for
     * @return task
     */
    public JSONObject getTenantTask(String rdmServiceUrl, String rdmTenantId, String taskId) {
        String tenantTasksEndpoint = String.format(DELETE_RDM_TENANT_TASK_URL, rdmServiceUrl, rdmTenantId, taskId);
        return rest.getForObject(tenantTasksEndpoint, JSONObject.class);
    }

    /**
     * Wait for the RDM task
     *
     * @param rdmServiceUrl        rdm service url
     * @param rdmTenantId          rdm tenant id
     * @param sleepDurationSeconds sleep duration between attempts
     * @param maxAttempts          max attempts to exec
     * @return task response
     */
    private JSONObject waitForTask(String rdmServiceUrl, String rdmTenantId, String taskId, int sleepDurationSeconds, int maxAttempts) {
        Set<String> statuses = ImmutableSet.of(TASK_FINISHED, TASK_FAILED);
        Predicate<JSONObject> completeCondition = result -> statuses.contains((String) result.get("status"));
        Supplier<JSONObject> taskGet = () -> {
            try {
                return getTenantTask(rdmServiceUrl, rdmTenantId, taskId);
            } catch (HttpClientErrorException.NotFound notFound) {
                JSONObject response = new JSONObject();
                response.put("status", TASK_FINISHED);
                return response;
            }
        };
        return wait(completeCondition, taskGet, sleepDurationSeconds, maxAttempts);
    }

    /**
     * Wait for supplier completion using predicate condition
     *
     * @param condition            condition to finish wait
     * @param responseSupplier     response supplier
     * @param sleepDurationSeconds sleep seconds between attempts
     * @param maxAttempts          max number of attempts
     * @return response
     */
    private JSONObject wait(Predicate<JSONObject> condition, Supplier<JSONObject> responseSupplier, int sleepDurationSeconds, int maxAttempts) {
        int currentAttempt = 0;
        while (currentAttempt < maxAttempts) {
            JSONObject response = responseSupplier.get();
            if (condition.test(response)) {
                return response;
            }
            // Sleep for a while before the next attempt
            try {
                TimeUnit.SECONDS.sleep(sleepDurationSeconds);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            currentAttempt++;
        }
        throw new RdmClientException("Task not completed within the specified attempts");
    }
}