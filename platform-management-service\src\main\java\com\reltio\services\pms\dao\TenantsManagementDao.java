package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.CollectionGroup;
import com.google.cloud.firestore.Query;
import com.google.cloud.firestore.QuerySnapshot;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.sales.model.TenantsManagementLogs;
import com.reltio.services.pms.common.sales.model.TenantsManagementLogsFilters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutionException;
@Service
public class TenantsManagementDao extends AbstractRootCollectionDao<TenantsManagementLogs> {
    private static final String CREATED_TIME = "createdTime";
    private static final String TENANTS_MANAGEMENT_COLLECTION = "PMS_TENANTS_MANAGEMENT_SS_LOGS";

    @Autowired
    public TenantsManagementDao(CredentialsProvider provider, @Value("${firestore.env.name}") String deployedEnv, ReltioUserHolder reltioUserHolder) {
        super(provider, TENANTS_MANAGEMENT_COLLECTION, deployedEnv, reltioUserHolder);
    }

    @Override
    protected TypeReference<TenantsManagementLogs> getTypeReference() {
        return new TypeReference<TenantsManagementLogs>() {};
    }

    public QuerySnapshot getLogsByCriteria(TenantsManagementLogsFilters filters) throws ExecutionException, InterruptedException {
        CollectionGroup collection = getCurrentCollectionGroup();

        Query query = collection;

        if (filters.getEnvId() != null) {
            query = query.whereEqualTo("envId", filters.getEnvId());
        }
        if (filters.getTenantId() != null) {
            query = query.whereEqualTo("tenantId", filters.getTenantId());
        }
        if (filters.getRequesterEmail() != null) {
            query = query.whereEqualTo("requesterEmail", filters.getRequesterEmail());
        }
        if (filters.getCreatedTime() != null) {
            query = query.whereGreaterThanOrEqualTo(CREATED_TIME, filters.getCreatedTime());
        }

        QuerySnapshot querySnapshot = query.get().get();

        return querySnapshot;

    }
}