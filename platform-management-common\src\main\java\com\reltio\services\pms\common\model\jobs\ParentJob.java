package com.reltio.services.pms.common.model.jobs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import com.reltio.services.pms.common.model.FirestoreEntity;
import lombok.Getter;

import java.util.List;
import java.util.Map;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ParentJob extends BaseFirestoreEntity {

    @JsonProperty(value = "parentJobId")
    private String parentJobId;

    @JsonProperty(value = "envId")
    private String envId;

    @JsonProperty(value = "pipelineId")
    private String pipelineId;

    @JsonProperty(value = "productEdition")
    private String productEdition;

    @JsonProperty(value = "jobs")
    private List<Map<String,String>> jobIdsAndOwnersList;

    public ParentJob(@JsonProperty(value = "parentJobId")String parentJobId,
                     @JsonProperty(value = "envId")String envId,
                     @JsonProperty(value = "pipelineId")String pipelineId,
                     @JsonProperty(value = "productEdition")String productEdition,
                     @JsonProperty(value = "jobs")List<Map<String,String>> jobIdsAndOwnersList){
        this.parentJobId=parentJobId;
        this.envId=envId;
        this.pipelineId=pipelineId;
        this.productEdition=productEdition;
        this.jobIdsAndOwnersList=jobIdsAndOwnersList;

    }

    @Override
    public String getID() {
        return getParentJobId();
    }
}
