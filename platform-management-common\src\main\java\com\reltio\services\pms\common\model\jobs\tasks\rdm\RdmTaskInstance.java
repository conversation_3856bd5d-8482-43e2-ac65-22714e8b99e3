package com.reltio.services.pms.common.model.jobs.tasks.rdm;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.RdmCache;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public class RdmTaskInstance extends TaskInstance {

    @JsonProperty(value = "productEdition")
    private final String productEdition;

    @JsonProperty(value = "customerId")
    private final String customerId;

    @JsonProperty(value = "customerName")
    private final String customerName;

    @JsonProperty(value = "rdmServiceUrl")
    private  String rdmServiceUrl;

    @JsonProperty(value = "activityLogServiceUrl")
    private  String activityLogServiceUrl;

    @JsonProperty(value = "rdmWorkFlowServiceUrl")
    private  String rdmWorkFlowServiceUrl;

    @JsonProperty(value = "rdmTenantID")
    private  String rdmTenantID;

    @JsonProperty(value = "mdmTenantID")
    private  List<String> mdmTenantID;

    @JsonProperty(value = "reuseRdmTenant")
    private final Boolean reuseRdmTenant;

    @JsonProperty(value = "industry")
    private final String industry;

    @JsonProperty(value = "cache")
    private final RdmCache rdmCache;

    @JsonProperty(value = "publishRDMErrors")
    private final Boolean publishRDMErrors ;

    @JsonProperty(value = "validateSameCustomerId")
    private final Boolean validateSameCustomerId;

    @JsonProperty(value = "trackTranscodeErrors")
    private final Boolean trackTranscodeErrors;

    @JsonProperty(value = "events")
    private final List<String> events;


    @JsonCreator
    public RdmTaskInstance(@JsonProperty(value = "id") String id,
                           @JsonProperty(value = "name") String name,
                           @JsonProperty(value = "envId") String envId,
                           @JsonProperty(value = "jobId") String jobId,
                           @JsonProperty(value = "startTime") Long startTime,
                           @JsonProperty(value = "finishTime") Long finishTime,
                           @JsonProperty(value = "status") TaskStatus status,
                           @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                           @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                           @JsonProperty(value = "executingNodeName") String executingNodeName,
                           @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                           @JsonProperty(value = "productEdition") String productEdition ,
                           @JsonProperty(value = "customerId") String customerId,
                           @JsonProperty(value = "customerName") String customerName,
                           @JsonProperty(value = "rdmServiceUrl") String rdmServiceUrl,
                           @JsonProperty(value = "activityLogServiceUrl") String activityLogServiceUrl,
                           @JsonProperty(value = "rdmWorkFlowServiceUrl") String rdmWorkFlowServiceUrl,
                           @JsonProperty(value = "rdmTenantID") String rdmTenantID,
                           @JsonProperty(value = "mdmTenantID") List<String> mdmTenantID,
                           @JsonProperty(value = "reuseRdmTenant") Boolean reuseRdmTenant,
                           @JsonProperty(value = "industry") String industry,
                           @JsonProperty(value = "cache") RdmCache rdmCache,
                           @JsonProperty(value = "publishRDMErrors") Boolean publishRDMErrors,
                           @JsonProperty(value = "validateSameCustomerId") Boolean validateSameCustomerId,
                           @JsonProperty(value = "trackTranscodeErrors") Boolean trackTranscodeErrors,
                           @JsonProperty(value = "events") List<String> events) {
        super(id, name, jobId, startTime, finishTime, TaskType.RDM_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.productEdition = productEdition;
        this.customerId = customerId;
        this.customerName = customerName;
        this.rdmServiceUrl = rdmServiceUrl;
        this.activityLogServiceUrl = activityLogServiceUrl;
        this.rdmWorkFlowServiceUrl = rdmWorkFlowServiceUrl;
        this.rdmTenantID = rdmTenantID;
        this.mdmTenantID = mdmTenantID;
        this.reuseRdmTenant = reuseRdmTenant;
        this.industry = industry;
        this.rdmCache = rdmCache;
        this.publishRDMErrors = publishRDMErrors;
        this.validateSameCustomerId = validateSameCustomerId;
        this.trackTranscodeErrors = trackTranscodeErrors;
        this.events = events == null ? new ArrayList<>() : new ArrayList<>(events);
    }

    public Boolean validateSameCustomerId() {
        return validateSameCustomerId;
    }

    public void addEvent(String event) {
        events.add(event);
    }
}
