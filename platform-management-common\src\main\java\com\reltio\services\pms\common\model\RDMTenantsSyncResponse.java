package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class RDMTenantsSyncResponse {
    @JsonProperty("syncedRdmTenantsList")
    private final List<String> syncedRdmTenantsList;

    @JsonProperty("message")
    private String message ;

    @JsonCreator
    public RDMTenantsSyncResponse(@JsonProperty List<String> syncedRdmTenantsList){
        this.syncedRdmTenantsList=syncedRdmTenantsList;
    }
    public void setMessage(String message) {
        this.message = message;
    }
    public String getMessage() {
        return message;
    }

}
