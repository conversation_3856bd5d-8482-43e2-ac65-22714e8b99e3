package com.reltio.services.pms.controller.api;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.model.secrets.KeyDto;
import com.reltio.services.pms.common.model.secrets.SecretDto;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * SecretsResources
 * Created by apy<PERSON><PERSON>
 */
@RequestMapping(value = "/api/v1/environments/{environment}/secrets/*", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Secrets Manager")
@ReltioSecured(resourceClass = Pms.Compliance.class)
@Validated
public interface SecretsResources {

    @PostMapping("/{service}")
    ResponseEntity<KeyDto> createKey(@PathVariable("environment") String environment,
                                     @PathVariable("service") String service,
                                     @RequestBody SecretDto secretDto);

    @PutMapping("/{service}")
    ResponseEntity<KeyDto> updateKey(@PathVariable("environment") String environment,
                                     @PathVariable("service") String service,
                                     @RequestBody SecretDto secretDto);

    @DeleteMapping("/{service}")
    ResponseEntity<String> deleteKey(@PathVariable("environment") String environment,
                                     @PathVariable("service") String service);

    @GetMapping("/{service}/list")
    ResponseEntity<List<String>> listKeys(@PathVariable("environment") String environment, @PathVariable("service") String service);
}
