package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.Query;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.tenant.access.AccessRequest;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.Set;

@Service
public class AccessRequestDao extends AbstractRootCollectionDao<AccessRequest> {
    private static final String PMS_ACCESS_REQUESTS_COLLECTION_NAME = "PMS_ACCESS_REQUESTS";
    private static final Logger LOGGER = Logger.getLogger(AccessRequestDao.class);

    public AccessRequestDao(CredentialsProvider provider,
                            @Value("${firestore.env.name}") String deployedEnv,
                            ReltioUserHolder reltioUserHolder) {
        super(provider, PMS_ACCESS_REQUESTS_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    protected TypeReference<AccessRequest> getTypeReference() {
        return new TypeReference<AccessRequest>() {
        };
    }

    public Collection<AccessRequest> getRequests(Map<String, Object> inputs, Integer size, Integer offset) {
        Query query = getBaseCollection();
        for(Map.Entry<String, Object> input : inputs.entrySet()) {
            if (input.getValue() != null) {
                query = query.whereEqualTo(input.getKey(), input.getValue());
            }
        }
        return getResultFromQuery(query.limit(size).offset(offset).orderBy("createdTime", Query.Direction.DESCENDING));
    }

    public Collection<AccessRequest> getAccessGrantedRequestsByZendeskId(String zendeskId) {
        Query query = getBaseCollection().whereEqualTo("zendeskId", zendeskId).whereEqualTo("status", "ACCESS_GRANTED");
        return getResultFromQuery(query);
    }
}
