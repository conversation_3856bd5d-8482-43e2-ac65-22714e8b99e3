package com.reltio.services.pms.convertors.sales;


import com.reltio.services.pms.common.sales.QuantityType;
import com.reltio.services.pms.common.sales.QuotaPeriod;
import com.reltio.services.pms.common.sales.QuotaType;
import com.reltio.services.pms.common.sales.SalesPackageType;
import com.reltio.services.pms.common.sales.model.*;
import com.reltio.services.pms.common.sales.model.quotas.BaseQuotasConfig;
import com.reltio.services.pms.convertors.sales.quotas.QuotasConfigProvider;
import com.reltio.services.pms.service.SalesPackageService;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SalesPackageProviderService {

    private final SalesTenantProviderService salesTenantProviderService;

    private final SalesPackageService salesPackageService;

    private static final Long MILLIS_IN_MONTH = 2_629_800_000L;
    private static final int MONTHS_IN_YEAR = 12; // Number of months in a year
    private static final long MILLIS_IN_YEAR = MILLIS_IN_MONTH * MONTHS_IN_YEAR; // Milliseconds in a year

    private final Collection<QuotasConfigProvider<? extends BaseQuotasConfig>> quotasConfigProviders;

    public SalesPackageProviderService(SalesTenantProviderService salesTenantProviderService, Collection<QuotasConfigProvider<? extends BaseQuotasConfig>> quotasConfigProviders, SalesPackageService salesPackageService) {
        this.salesTenantProviderService = salesTenantProviderService;
        this.quotasConfigProviders = quotasConfigProviders;
        this.salesPackageService = salesPackageService;

    }

public void populatePackages(ReltioContract reltioContract, List<SalesConfig> subscriptions) {
    Set<String> basePackageCodes = salesPackageService.getSalesForceBasePackages();
    Set<String> additionalPackageCodes = salesPackageService.getSalesForceAdditionalPackages();
    Set<String> rihPackageCodes = salesPackageService.getSalesForceRihPackages();
    Map<SalesConfig, Map<String, Set<SalesConfig>>> packageSubscriptions = getSubscriptionsByPackage(subscriptions, basePackageCodes, additionalPackageCodes, rihPackageCodes);
    List<SalesPackageConfig> packages = new ArrayList<>();

    for (Map.Entry<SalesConfig, Map<String, Set<SalesConfig>>> salesConfigSetEntry : packageSubscriptions.entrySet()) {
        SalesConfig salesConfig = salesConfigSetEntry.getKey();
        Map<String, Set<SalesConfig>> valueMap = salesConfigSetEntry.getValue();

        if (salesConfig.getProductCode().equals("R-IH2-PKG")) {
            Map<String, String> rihAddOnsMap = salesPackageService.getSalesForceRihAddOns();
            Map<String, Set<SalesConfig>> updatedValueMap = updateProductCodesInMap(valueMap, rihAddOnsMap);

            RihSalesPackageConfig rihSalesPackageConfig = new RihSalesPackageConfig(salesConfig, updatedValueMap);
            rihSalesPackageConfig.setPackageType(SalesPackageType.RIH);
            packages.add(rihSalesPackageConfig);
            continue;
        }

        SalesPackageConfig packageConfig = new SalesPackageConfig(salesConfig);
        if (packageConfig.isActive() || packageConfig.getStartDate().before(Date.from(Instant.now().plusMillis(MILLIS_IN_YEAR)))) {
            if (basePackageCodes.contains(packageConfig.getProductCode())) {
                packageConfig.setPackageType(SalesPackageType.BASE);
                reltioContract.setReltioPackageType(salesPackageService.getReltioPackageType(packageConfig.getProductCode()));
            } else {
                packageConfig.setPackageType(SalesPackageType.ADDITIONAL);
            }

            // Get Package level Quotas
            for (QuotasConfigProvider<? extends BaseQuotasConfig> quotasConfigProvider : quotasConfigProviders) {
                BaseQuotasConfig quotasConfig = quotasConfigProvider.getQuotasConfig(valueMap);
                if (quotasConfig != null) {
                    packageConfig.getProductQuotas().put(quotasConfigProvider.getQuotaName(), quotasConfig);
                }
            }

            // Compute separate RIH_TASKS sums for BASE and ADDITIONAL
            Map<SalesPackageType, Long> rihTasksSumMap = computeRihTasksSum(valueMap);

            long baseRihTasksSum = rihTasksSumMap.getOrDefault(SalesPackageType.BASE, 0L);
            long additionalRihTasksSum = rihTasksSumMap.getOrDefault(SalesPackageType.ADDITIONAL, 0L);

            // Handle RIH_TASKS quota separately for BASE
            if (packageConfig.getPackageType() == SalesPackageType.BASE && baseRihTasksSum > 0) {
                BaseQuotasConfig rihTasksQuotaConfig = createRihTasksQuotaConfig(baseRihTasksSum);
                packageConfig.getProductQuotas().put(PMSQuotaName.RIH_TASKS, rihTasksQuotaConfig);
            }

            // Handle RIH_TASKS quota separately for ADDITIONAL
            if (packageConfig.getPackageType() == SalesPackageType.ADDITIONAL && additionalRihTasksSum > 0) {
                BaseQuotasConfig rihTasksQuotaConfig = createRihTasksQuotaConfig(additionalRihTasksSum);
                packageConfig.getProductQuotas().put(PMSQuotaName.RIH_TASKS, rihTasksQuotaConfig);
            }

            packageConfig.setMdmTenants(salesTenantProviderService.getSalesTenants(reltioContract, valueMap, salesConfig.getSubscriptionId()));
            packageConfig.setConfigurations(prepareConfigurations(valueMap));
            packages.add(packageConfig);
        }
    }
    reltioContract.setPackages(packages);
}
    protected Map<SalesPackageType, Long> computeRihTasksSum(Map<String, Set<SalesConfig>> valueMap) {
        long baseRihTasksSum = 0;
        long additionalRihTasksSum = 0;

        for (SalesConfig subscription : valueMap.values().stream().flatMap(Set::stream).toList()) {
            if (subscription.getProductCode().equals("R-IH") || subscription.getProductCode().equals("R-IH2")) {
                baseRihTasksSum += subscription.getQuantity();
            } else if (subscription.getProductCode().equals("RIH-TASK-NT")) {
                additionalRihTasksSum += subscription.getQuantity();
            }
        }

        Map<SalesPackageType, Long> rihTasksMap = new HashMap<>();
        rihTasksMap.put(SalesPackageType.BASE, baseRihTasksSum);
        rihTasksMap.put(SalesPackageType.ADDITIONAL, additionalRihTasksSum);

        return rihTasksMap;
    }



    protected BaseQuotasConfig createRihTasksQuotaConfig(long rihTasksSum) {
        BaseQuotasConfig rihTasksQuotaConfig = new BaseQuotasConfig();
        rihTasksQuotaConfig.setQuotaPeriod(QuotaPeriod.MONTHLY);
        rihTasksQuotaConfig.setQuantityType(QuantityType.COUNT);
        rihTasksQuotaConfig.setQuotaType(QuotaType.PACKAGE_LEVEL);
        rihTasksQuotaConfig.setQuantity((float) rihTasksSum);
        return rihTasksQuotaConfig;
    }

    private Map<SalesForceProductName, Set<SalesConfig>> prepareConfigurations(Map<String, Set<SalesConfig>> salesConfigSetEntry) {
        Map<SalesForceProductName, Set<SalesConfig>> configurations = new EnumMap<>(SalesForceProductName.class);
        configurations.put(SalesForceProductName.DATA_DOMAIN, salesConfigSetEntry.entrySet().stream().filter(entry -> salesPackageService.getSalesForceDataDomains().contains(entry.getKey()))
                .flatMap(entry -> entry.getValue().stream()).collect(Collectors.toSet()));
        configurations.put(SalesForceProductName.VELOCITY_PACK, salesConfigSetEntry.entrySet().stream().filter(entry -> salesPackageService.getSalesForceVelocityPacks().contains(entry.getKey()))
                .flatMap(entry -> entry.getValue().stream()).collect(Collectors.toSet()));
        configurations.put(SalesForceProductName.MDM_DEPLOYMENT_CLOUD, salesConfigSetEntry.entrySet().stream().filter(entry -> salesPackageService.getSalesForceMDMDeploymentCloud().contains(entry.getKey()))
                .flatMap(entry -> entry.getValue().stream()).collect(Collectors.toSet()));
        configurations.put(SalesForceProductName.RDM_DEPLOYMENT_CLOUD, salesConfigSetEntry.entrySet().stream().filter(entry -> salesPackageService.getSalesForceMDMDeploymentCloud().contains(entry.getKey()))
                .flatMap(entry -> entry.getValue().stream()).collect(Collectors.toSet()));
        configurations.put(SalesForceProductName.DEPLOYMENT_REGION, salesConfigSetEntry.entrySet().stream().filter(entry -> salesPackageService.getSalesForceDeploymentRegion().contains(entry.getKey()))
                .flatMap(entry -> entry.getValue().stream()).collect(Collectors.toSet()));
        configurations.put(SalesForceProductName.COMPLIANCE, salesConfigSetEntry.entrySet().stream().filter(entry -> salesPackageService.getSalesForceComplianceValues().contains(entry.getKey()))
                .flatMap(entry -> entry.getValue().stream()).collect(Collectors.toSet()));
        return configurations;
    }


    private Map<SalesConfig, Map<String, Set<SalesConfig>>> getSubscriptionsByPackage(List<SalesConfig> subscriptions, Set<String> basePackageCodes,
                                                                                      Set<String> additionalPackageCodes, Set<String> rihPackageCodes) {
        Map<SalesConfig, Map<String, Set<SalesConfig>>> packageSubscriptions = new HashMap<>();
        Set<SalesConfig> globalSubscriptions = new HashSet<>();
        Set<SalesConfig> nonPackageSubscriptions = new HashSet<>();
        List<SalesConfig> filteredSubscriptions = filterSubscriptions(subscriptions);
        for (SalesConfig salesConfig : filteredSubscriptions) {
            String productCode = salesConfig.getProductCode();
            if (basePackageCodes.contains(productCode) || additionalPackageCodes.contains(productCode) || rihPackageCodes.contains(productCode)) {
                packageSubscriptions.put(salesConfig, new HashMap<>());
            } else if (salesConfig.getParentSubscriptionId() == null) {
                globalSubscriptions.add(salesConfig);
            } else {
                nonPackageSubscriptions.add(salesConfig);
            }
        }

        moveSubscriptionsToPackages(packageSubscriptions, nonPackageSubscriptions);

        packageSubscriptions.forEach((key, value) -> {
            if (!key.getProductCode().equals("R-IH2-PKG")) {
                addProductByCode(value, globalSubscriptions);
            }
        });

        return packageSubscriptions;
    }

    private List<SalesConfig> filterSubscriptions(List<SalesConfig> subscriptions) {
        Map<String, SalesConfig> idMap = subscriptions.stream()
                .collect(Collectors.toMap(SalesConfig::getSubscriptionId, o -> o, (o1, o2) -> o1));

        // Set of Ids to remove
        Set<String> toRemove = new HashSet<>();

        for (SalesConfig config : subscriptions) {
            if (idMap.containsKey(config.getRevisedSub())) {
                SalesConfig correctedConfig = idMap.get(config.getRevisedSub());
                if ((config.getQuantity() != null) && (correctedConfig.getQuantity() != null)
                        && config.getQuantity() + correctedConfig.getQuantity() == 0) {
                    toRemove.add(config.getSubscriptionId());
                    toRemove.add(config.getRevisedSub());
                }
            }
        }

        // Filter out objects where Id is in toRemove set
        return subscriptions.stream()
                .filter(obj -> !toRemove.contains(obj.getSubscriptionId()))
                .collect(Collectors.toList());
    }

    private void moveSubscriptionsToPackages(Map<SalesConfig, Map<String, Set<SalesConfig>>> packageSubscriptions, Set<SalesConfig> nonPackageSubscriptions) {

        Set<SalesConfig> missedPackageSubscriptions = new HashSet<>();
        boolean isFound;
        for (SalesConfig salesConfig : nonPackageSubscriptions) {
            isFound = false;
            for (Map.Entry<SalesConfig, Map<String, Set<SalesConfig>>> packageEntrySet : packageSubscriptions.entrySet()) {
                Map<String, Set<SalesConfig>> existingProducts = packageEntrySet.getValue();
                if (packageEntrySet.getKey().getSubscriptionId().equals(salesConfig.getParentSubscriptionId()) || existingProducts.entrySet().stream().anyMatch(product ->
                        product.getValue().stream().anyMatch(sub -> sub.getSubscriptionId().equals(salesConfig.getParentSubscriptionId())))) {
                    isFound = true;
                    addProductByCode(existingProducts, salesConfig);
                    break;
                }
            }
            if (!isFound) {
                missedPackageSubscriptions.add(salesConfig);
            }
        }

        if (!missedPackageSubscriptions.isEmpty() && nonPackageSubscriptions.size() > missedPackageSubscriptions.size()) {
            moveSubscriptionsToPackages(packageSubscriptions, missedPackageSubscriptions);
        }
    }

    private void addProductByCode(Map<String, Set<SalesConfig>> existingProducts, SalesConfig salesConfig) {
        Set<SalesConfig> productsByCode = existingProducts.get(salesConfig.getProductCode());
        if (productsByCode == null) {
            productsByCode = new HashSet<>();
        }
        productsByCode.add(salesConfig);
        existingProducts.put(salesConfig.getProductCode(), productsByCode);
    }

    private void addProductByCode(Map<String, Set<SalesConfig>> existingProducts, Set<SalesConfig> salesConfigs) {
        salesConfigs.forEach(salesConfig -> addProductByCode(existingProducts, salesConfig));
    }

    public Map<String, Set<SalesConfig>> updateProductCodesInMap(Map<String, Set<SalesConfig>> originalMap, Map<String, String> rihAddOnsMap) {
        Map<String, Set<SalesConfig>> updatedValueMap = new HashMap<>();

        for (Map.Entry<String, Set<SalesConfig>> entry : originalMap.entrySet()) {
            String newProductCode = rihAddOnsMap.getOrDefault(entry.getKey(), entry.getKey());
            updatedValueMap.put(newProductCode, entry.getValue());
        }

        return updatedValueMap;
    }

}
