package com.reltio.services.pms.common.model.jobs.tasks.imagehosting;

import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServicesEnablementBaseTaskInstanceParams;
import com.reltio.services.pms.common.sales.TenantPurpose;

public interface ImageHostingTaskInstanceParams extends ServicesEnablementBaseTaskInstanceParams {
    String getCustomerId();

    String getTenantId();

    TenantPurpose getTenantPurpose();

}

