package com.reltio.services.pms.common.model.tenant;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class TenantStreamingInfo {

    @JsonProperty(value = "queueArn")
    private String queueArn;

    @JsonProperty(value = "subscriptionName")
    private String subscriptionName;

    @JsonProperty(value = "keyName")
    private String keyName;

    @JsonProperty(value = "configuredByJob")
    private String configuredByJob;

    @JsonProperty(value = "serviceBusNamespaceName")
    private String serviceBusNamespaceName;

    @JsonProperty(value = "queueName")
    private String queueName;

}
