package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Set;

public class ApprovalPipelineTaskConfig extends AbstractPipelineTaskConfig {

    private final Set<String> approverEmails;

    @JsonCreator
    public ApprovalPipelineTaskConfig(
            @JsonProperty(value = "name") String name,
            @JsonProperty(value = "approverEmails") Set<String> approverEmails) {
        super(name, TaskType.APPROVAL_TASK);
        this.approverEmails = approverEmails;
    }

    public Set<String> getApproverEmails() {
        return approverEmails;
    }

    @Override
    public String toString() {
        return "ApprovalPipelineTaskConfig{" +
                "approverEmails=" + approverEmails +
                '}';
    }
}
