package com.reltio.services.pms.convertors.sales;


import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.reltio.services.pms.common.sales.model.SalesConfig;

import java.io.IOException;

import static com.reltio.services.pms.convertors.UtilDeserializer.*;

public class SalesConfigDeserializer extends StdDeserializer<SalesConfig> {

    public SalesConfigDeserializer(Class<?> vc) {
        super(vc);
    }

    @Override
    public SalesConfig deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        JsonNode node = jsonParser.getCodec().readTree(jsonParser);
        SalesConfig salesConfig = new SalesConfig();
        salesConfig.setSubscriptionId(getField("Id", node));
        salesConfig.setSubscriptionName(getField("Name", node));
        salesConfig.setProductCode(getField("Product_Code__c", node));
        salesConfig.setProductName(getField("SBQQ__ProductName__c", node));
        salesConfig.setParentSubscriptionId(getField("SBQQ__RequiredById__c", node) != null ?
                getField("SBQQ__RequiredById__c", node) : getField("SBQQ__RootId__c", node));
        salesConfig.setStartDate(getDateField("SBQQ__StartDate__c", node));
        salesConfig.setEndDate(getDateField("SBQQ__EndDate__c", node));
        salesConfig.setActive(getBooleanField("Active__c", node));
        salesConfig.setQuantity(getLongField("SBQQ__Quantity__c", node));
        salesConfig.setSalesTenantId(getField("Tenant_ID2__c", node));
        //optionalFields
        if (getField("Tenant_Deployment_Region__c", node) != null) {
            salesConfig.setDeploymentRegion(getField("Tenant_Deployment_Region__c", node));
        }
        if (getField("HIPAA_Finance__c", node) != null) {
            salesConfig.setHipaaFinance(getBooleanField("HIPAA_Finance__c", node));
        }
        if (getField("MDM_Cloud_Provider__c", node) != null) {
            salesConfig.setMdmCloudProvider(getField("MDM_Cloud_Provider__c", node));
        }
        if (getField("RDM_Cloud_Provider__c", node) != null) {
            salesConfig.setRdmCloudProvider(getField("RDM_Cloud_Provider__c", node));
        }
        if (getField("Velocity_Pack__c", node) != null) {
            salesConfig.setVelocityPack(getField("Velocity_Pack__c", node));
        }
        if (getField("Consolidated_Profiles__c", node) != null) {
            salesConfig.setConsolidatedProfiles(getLongField("Consolidated_Profiles__c", node));
        }
        if (getField("Package_Mapping__c", node) != null) {
            salesConfig.setPackageLink(getField("Package_Mapping__c", node));
        }
        if (getField("SBQQ__RevisedSubscription__c", node) != null) {
            salesConfig.setRevisedSub(getField("SBQQ__RevisedSubscription__c", node));
        }
        return salesConfig;
    }


}
