package com.reltio.services.pms.common.model.compliance.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.msgpack.annotation.Optional;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class ComplianceRequest {

    /**
     * The Start date.
     */
    @Pattern(regexp = "\\d{8}", message = "startDate should be in yyyyMMdd format")
    @NotEmpty(message = "startDate is required")
    String startDate;
    /**
     * The End date.
     */
    @Pattern(regexp = "\\d{8}", message = "endDate should be in yyyyMMdd format")
    @NotEmpty(message = "endDate is required")
    String endDate;

    /**
     * The Tenant ids.
     */
    @NotEmpty(message = "tenantIds are required")
    List<String> tenantIds;

    /**
     * The Page Size.
     */
    @Optional
    Integer size;

    /**
     * The Page Number.
     */
    @Optional
    Integer offset;

    /**
     * The Sort Order.
     */
    @Optional
    String sortOrder;

    /**
     * The Sort Field.
     */
    @Optional
    String sortField;


}
