package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import com.reltio.services.pms.common.sales.AccountType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class SalesAccount extends BaseFirestoreEntity {

    @JsonProperty("accountId")
    private String accountId;

    @JsonProperty("name")
    private String name;
    @JsonProperty("accountType")
    private AccountType accountType;
    @JsonProperty("industry")
    private String industry;
    @JsonProperty("subIndustry")
    private String subIndustry;
    @JsonProperty("csm")
    private BusinessContact csm; //Customer Success Manager
    @JsonProperty("totalACV")
    private String totalACV;
    @JsonProperty("contractSummaries")
    private List<ContractSummary> contractSummaries;

    @JsonProperty("support")
    private CustomerSupport support;

    @JsonProperty("contracts")
    private Set<ReltioContract> contracts = new HashSet<>();

    @JsonProperty("cleanableFlag")
    private boolean cleanableFlag;

    @JsonProperty("authCustomerId")
    private Set<String> authCustomerId = new HashSet<>();

    @JsonProperty("reltioPackageType")
    private ReltioPackageType reltioPackageType;

    @JsonProperty("accountStatus")
    private AccountStatus accountStatus;

    @Override
    public String getID() {
        return this.accountId;
    }
}
