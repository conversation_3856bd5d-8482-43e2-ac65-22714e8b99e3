package com.reltio.services.pms.clients.reltio.mdm;

public class MdmClientException extends RuntimeException {

    public MdmClientException() {
    }

    public MdmClientException(String message) {
        super(message);
    }

    public MdmClientException(String message, Throwable cause) {
        super(message, cause);
    }

    public MdmClientException(Throwable cause) {
        super(cause);
    }

    public MdmClientException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    static class TenantCreationException extends MdmClientException{
        public TenantCreationException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    static class BusinessConfigUpdateFailureException extends MdmClientException{
        public BusinessConfigUpdateFailureException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    static public class LookupsUpdateFailureException extends MdmClientException {
        public LookupsUpdateFailureException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
