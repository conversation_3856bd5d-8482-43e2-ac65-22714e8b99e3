package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.Query;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.jobs.ParentJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;

@Service
public class ParentJobDao extends AbstractLevel1CollectionDao<ParentJob> {
    private static final String PARENT_JOBS_COLLECTION_NAME = "PARENT_JOBS";

    private static final String CREATED_TIME = "createdTime";

    private static final String CREATED_BY = "createdBy";

    @Autowired
    public ParentJobDao(CredentialsProvider provider,
                  EnvironmentDao environmentDao, ReltioUserHolder reltioUserHolder) {
        super(provider, environmentDao, PARENT_JOBS_COLLECTION_NAME, reltioUserHolder);
    }

    @Override
    protected TypeReference<ParentJob> getTypeReference() {
        return new TypeReference<ParentJob>() {
        };
    }

    public Collection<ParentJob> getAllJobs(String envId, Integer size, Integer offset,String createdBy ,String sortOrder) {
        Query query;
        if(createdBy == null) {
             query = getBaseCollection(envId).limit(size).offset(offset).orderBy(CREATED_TIME, Query.Direction.valueOf(sortOrder));
        }else{
             query = getBaseCollection(envId).whereEqualTo(CREATED_BY, createdBy).limit(size).offset(offset).orderBy(CREATED_TIME, Query.Direction.valueOf(sortOrder));
        }
        return getResultFromQuery(query);
    }
}
