apiVersion: autoscaling/v1
kind: HorizontalPodAutoscaler
metadata:
  name: {{.Values.serviceName}}
  namespace: {{ .Release.Namespace }}
  labels:
    name: {{.Values.serviceName}}
    type: autoscaler
    environment: {{ .Release.Namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{.Values.serviceName}}
  minReplicas: {{ .Values.minReplicas }}
  maxReplicas: {{ .Values.maxReplicas }}
  targetCPUUtilizationPercentage: {{ .Values.targetCPUUtilizationPercentage }}
