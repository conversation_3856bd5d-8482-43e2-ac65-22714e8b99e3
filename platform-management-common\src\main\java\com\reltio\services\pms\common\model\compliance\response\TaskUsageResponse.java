package com.reltio.services.pms.common.model.compliance.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The type Task usage response.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class TaskUsageResponse {

    /**
     * The Report date.
     */
    @JsonProperty("reportDate")
    private String reportDate;
    /**
     * The Billing start date.
     */
    @JsonProperty("billingStartDate")
    private String billingStartDate;
    /**
     * The Billing end date.
     */
    @JsonProperty("billingEndDate")
    private String billingEndDate;

    /**
     * The Environment.
     */
    @JsonProperty("environment")
    private String environment;

    /**
     * The Tenant id.
     */
    @JsonProperty("tenantId")
    private String tenantId;
    /**
     * The Task count.
     */
    @JsonProperty("taskCount")
    private String taskCount;
}
