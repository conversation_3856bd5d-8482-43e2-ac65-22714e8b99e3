package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.Query;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.ProductEdition;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.Set;

@Service
public class ProductEditionDao extends AbstractRootCollectionDao<ProductEdition> {

    private static final String PRODUCT_EDITIONS_COLLECTION_NAME = "PMS_PRODUCT_EDITIONS";
    private static final Logger LOGGER = Logger.getLogger(ProductEditionDao.class);

    @Autowired
    public ProductEditionDao(CredentialsProvider provider, @Value("${firestore.env.name}") String deployedEnv, ReltioUserHolder reltioUserHolder) {
        super(provider, PRODUCT_EDITIONS_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties = Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties, LOGGER);
    }

    @Override
    protected TypeReference<ProductEdition> getTypeReference() {
        return new TypeReference<ProductEdition>() {
        };
    }

    public Collection<ProductEdition> getProductEditionsByFieldId(String fieldName, String fieldValue) {
        Query query = getCurrentCollectionGroup().whereEqualTo(fieldName, fieldValue);
        return getResultFromQuery(query);
    }

    @Override
    public ProductEdition update(ProductEdition productEdition) {
        return override(productEdition);
    }
}
