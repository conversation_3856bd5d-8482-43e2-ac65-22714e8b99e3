package com.reltio.services.pms.common.model.jobs.tasks.auth;

public class AuthTaskConstants {
    public static final String PIPELINE_NAME = "name";
    public static final String CUSTOMER_SPECIFIC_ROLE_BODY = "roleBody";
    public static final String CLIENTS_COUNT = "clientsCount";
    public static final String PREASSIGNED_ROLE_NAME = "requesterSpecificRoleName";
    public static final String ACCESS_LEVEL = "accessLevel";
    public static final String MAPPING_NAME = "mappingName";
    public static final String ROLE_MAPPING_NAME = "roleMappingName";
    public static final String USER_CONFIG = "userConfig";
    public static final String CUSTOMER_CONFIG = "customerConfig";
    public static final String CLIENT_CONFIG = "clientConfig";
    public static final String REQUESTER_EMAIL = "requesterEmail";
    public static final String CLIENTS = "applicationClients";
    public static final String CUSTOMER_ID = "customerId";
    public static final String TENANT_OWNERS = "owners";
    public static final String RDM_TENANT_ID = "rdmTenantId";
    public static final String REUSE_RDM_TENANT = "reuseRdmTenant";
    public static final String CUSTOMER_SETTINGS = "customSettings";
    public static final String CASE_SENSITIVE_LOGIN_ENABLED= "caseSensitiveLoginEnabled";
    public static final String RESET_PASSWORD_EMAIL_TEMPLATE_ID= "resetPasswordEmailTemplateId";
    public static final String GROUP_ID= "groupId";
    public static final String Customer= "customer";
    public static final String ROLES="roles";
    public static final String GROUP_CONFIG="groupConfig";
    public static final String ADDITIONAL_USERS="additionalUsers";
    public static final String USER_NAME_PREFIX="userNamePrefix";
    public static final String DEFAULT_PASSWORD ="defaultPassword";
    public static final String CUSTOMER_ID_PREFIX ="customerIdPrefix";

}
