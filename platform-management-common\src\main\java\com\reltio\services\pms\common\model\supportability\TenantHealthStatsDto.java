package com.reltio.services.pms.common.model.supportability;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;
import java.util.Objects;

/**
 * EventModel
 * Created by aravuru
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class TenantHealthStatsDto implements StatsDto {
    @JsonProperty("startTime")
    private Long startTime;
    @JsonProperty("endTime")
    private Long endTime;
    @JsonProperty("totalCount")
    private Long totalCount;
    @JsonProperty("errorCount")
    private Long errorCount;
    @JsonProperty("warningCount")
    private Long warningCount;
    @JsonProperty("top5ErrorsByCount")
    private Map<String, Long> top5ErrorsByCount;
    @JsonProperty("top5WarningsByCount")
    private Map<String, Long> top5WarningsByCount;

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TenantHealthStatsDto tenantHealthStatsDto = (TenantHealthStatsDto) o;
        return Objects.equals(getStartTime(), tenantHealthStatsDto.getStartTime()) &&
                Objects.equals(getEndTime(), tenantHealthStatsDto.getEndTime()) &&
                Objects.equals(getTotalCount(), tenantHealthStatsDto.getTotalCount()) &&
                Objects.equals(getErrorCount(), tenantHealthStatsDto.getErrorCount()) &&
                Objects.equals(getWarningCount(), tenantHealthStatsDto.getWarningCount()) &&
                Objects.equals(getTop5ErrorsByCount().keySet(), tenantHealthStatsDto.getTop5ErrorsByCount().keySet()) &&
                Objects.equals(getTop5WarningsByCount().keySet(), tenantHealthStatsDto.getTop5WarningsByCount().keySet());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getStartTime(), getEndTime(), getTotalCount(), getErrorCount(), getWarningCount(), getTop5ErrorsByCount().keySet(),
                getTop5WarningsByCount().keySet());
    }
}
