package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class SpannerBackupInfo extends BaseFirestoreEntity {

    @JsonProperty(value = "backupId")
    private String backupId;

    @JsonProperty(value = "tenantId")
    private String tenantId;

    @JsonProperty(value = "envId")
    private String envId;

    @JsonProperty(value = "ticketId")
    private String ticketId;

    @JsonProperty(value = "creationTime")
    private Timestamp creationTime;

    @JsonProperty(value = "expirationTime")
    private Timestamp expirationTime;

    @JsonProperty(value = "listOfErrors")
    private List<String> listOfErrors;

    @Override
    public String getID() {
        return backupId;
    }

}