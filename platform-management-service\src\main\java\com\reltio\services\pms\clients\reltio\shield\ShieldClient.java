package com.reltio.services.pms.clients.reltio.shield;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.reltio.services.pms.common.PMSRestTemplate;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.common.model.ShieldStorageTemplate;
import com.reltio.services.pms.common.model.shield.ShieldSecrets;
import com.reltio.services.pms.dao.ShieldNotificationDao;
import com.reltio.services.pms.clients.external.gcp.secret.SecretsManagerService;
import com.reltio.services.pms.service.environment.EnvironmentService;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.apache.log4j.Logger;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

@Service
public class ShieldClient {
    private static final String ENABLE_SHIELD_FOR_CUSTOMER = "%s/customers/%s/shieldConfig";
    private static final String SHIELD_URL = "api/v1/shield";
    private static final String CREATE_SHIELD_KEY_URL = "%s/%s/keys";
    private static final String CREATE_SHIELD_POLICY_URL = "%s/%s/policies";
    private final PMSRestTemplate rest;
    private final EnvironmentService environmentService;
    private final String authUrl;
    private final ShieldNotificationDao shieldNotificationDao;
    private final SecretsManagerService secretsManagerService;
    private static final Logger LOGGER = Logger.getLogger(ShieldClient.class);


    @Autowired
    public ShieldClient(PMSRestTemplate rest,
                        ShieldNotificationDao shieldNotificationDao,
                         @Value("${reltio.auth.server.url}") String authUrl,
                        EnvironmentService environmentService,
                        SecretsManagerService secretsManagerService) {
        this.rest = rest;
        this.authUrl = authUrl;
        this.environmentService=environmentService;
        this.shieldNotificationDao = shieldNotificationDao;
        this.secretsManagerService = secretsManagerService;
        Set<String> properties=new HashSet<>();
        properties.add(authUrl);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }


    public void enableShieldConfig(String customerId, String taskId) {
        String url = String.format(ENABLE_SHIELD_FOR_CUSTOMER, authUrl, customerId);
        JSONObject body = getShieldEnablementBody(taskId);
        try {
            rest.postForObject(url, body, JSONObject.class);
        } catch (Exception ex) {
            throw new ShieldClientException("Can not enable shield config for customer : " + customerId, ex);
        }

    }

    private JSONObject getShieldEnablementBody(String taskId) {
        ShieldStorageTemplate template = shieldNotificationDao.getShieldStorageTemplate(taskId);
        ShieldSecrets secrets = getShieldSecrets(taskId);
        JSONObject body = new JSONObject();
        body.put("domain", template.getDomain());
        body.put("domainId", template.getDomainId());
        body.put("userName", secrets.getUserName());
        body.put("password", secrets.getPassword());
        return body;
    }

    private ShieldSecrets getShieldSecrets(String taskId) {
        ShieldSecrets secrets = null;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String secret = secretsManagerService.getSecret(taskId);
            secrets = objectMapper.readValue(secret, ShieldSecrets.class);
        } catch (IOException ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "Can not get Shield Secrets");
        }

        return secrets;
    }


    public String createShieldKey(String Key,String envId) {
        String shieldServiceUrl=environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.RELTIO_SHIELD_SERVICE);
        String key = null;
        JSONObject body = getCreateShieldKeyBody(Key);
        String url = String.format(CREATE_SHIELD_KEY_URL, shieldServiceUrl, SHIELD_URL);
        try {
            JSONObject response = rest.postForObject(url, body, JSONObject.class);
            Objects.requireNonNull(response, "Invalid response from server");
            key = response.get("id").toString();
        } catch (Exception ex) {
            throw new ShieldClientException("Can not create encryption key", ex);
        }
        return key;

    }

    private JSONObject getCreateShieldKeyBody(String key) {
        JSONObject body = new JSONObject();
        body.put("algorithm", "AES256");
        body.put("description", "Shield Key " + key);
        body.put("name", "key_" + key);
        body.put("rotationPeriodinDays", 365);
        return body;
    }

    public String createShieldPolicy(String policyName, String encryptionKey,String envId) {
        String shieldServiceUrl=environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.RELTIO_SHIELD_SERVICE);
        String policy = null;
        JSONObject body = getCreateShieldPolicyBody(policyName, encryptionKey);
        String url = String.format(CREATE_SHIELD_POLICY_URL, shieldServiceUrl, SHIELD_URL);
        try {
            JSONObject response = rest.postForObject(url, body, JSONObject.class);
            Objects.requireNonNull(response, "Invalid response from server");
            policy = response.get("id").toString();
        } catch (Exception ex) {
            throw new ShieldClientException("Can not create shield policy", ex);
        }
        return policy;

    }

    private JSONObject getCreateShieldPolicyBody(String policyName, String encryptionKey) {
        JSONObject body = new JSONObject();
        body.put("name", "shield_policy_" + policyName);
        body.put("targetKeyId", encryptionKey);
        return body;
    }
}
