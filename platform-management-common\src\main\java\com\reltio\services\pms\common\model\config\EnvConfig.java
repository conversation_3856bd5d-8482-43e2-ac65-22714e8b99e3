package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class EnvConfig extends PMSConfig {
    /**
     * Customer type config
     */
    @JsonProperty("env")
    private Map<String, EnvConfigEntry> envConfigEntryMap;

    @JsonCreator
    public EnvConfig(@JsonProperty("configName") String configName,
                     @JsonProperty("env") Map<String, EnvConfigEntry> envConfigEntryMap) {
        super(configName);
        this.envConfigEntryMap = envConfigEntryMap;
    }
}
