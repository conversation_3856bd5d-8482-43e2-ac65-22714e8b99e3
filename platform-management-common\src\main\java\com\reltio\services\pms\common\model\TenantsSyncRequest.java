package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.TenantPurpose;

import jakarta.validation.constraints.NotNull;
import java.util.List;

public class TenantsSyncRequest {

    @JsonProperty("pipelineId")
    private final String pipelineId;

    @JsonProperty("tenantsList")
    private final List<String> tenantsList;


    @JsonProperty("tenantPurpose")
    @NotNull
    private final TenantPurpose tenantPurpose;

    @JsonProperty(value = "processAllTenants")
    private final boolean processAllTenants;


    @JsonCreator
    public TenantsSyncRequest(@JsonProperty("pipelineId") String pipelineId,
                              @JsonProperty("tenantsList") List<String> tenantsList,
                              @JsonProperty("tenantPurpose") TenantPurpose tenantPurpose,
                              @JsonProperty(value = "processAllTenants") boolean processAllTenants) {
        this.pipelineId=pipelineId;
        this.tenantsList = tenantsList;
        this.tenantPurpose = tenantPurpose;
        this.processAllTenants = processAllTenants;
    }

    public String getPipelineId(){return pipelineId;}


    public List<String> getTenantsList() {
        return tenantsList;
    }


    public TenantPurpose getTenantPurpose() {
        return tenantPurpose;
    }

    public boolean isProcessAllTenants() {
        return processAllTenants;
    }
}
