package com.reltio.services.pms.common;

import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.circuitbreaker.resilience4j.Resilience4JCircuitBreaker;
import org.springframework.cloud.circuitbreaker.resilience4j.Resilience4JCircuitBreakerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;

import java.net.URI;
import java.util.Map;
import java.util.Objects;

import static com.reltio.services.pms.clients.reltio.mdm.MDMCleanTenantClient.GBQ_CONFIG_IS_NOT_DEFINED_FOR_TENANT;

/**
 * The type Pms rest template.
 */
@Component
public class PMSRestTemplate {

    /**
     * The constant LOG.
     */
    private static final Logger LOG = Logger.getLogger(PMSRestTemplate.class);
    /**
     * The Rest template.
     */
    private final IPMSRestTemplate restTemplate;

    /**
     * The Circuit breaker.
     */
    private final Resilience4JCircuitBreaker circuitBreaker;

    /**
     * Instantiates a new Pms rest template.
     *
     * @param circuitBreakerFactory the circuit breaker factory
     * @param restTemplate          the rest template
     */
    @Autowired
    public PMSRestTemplate(Resilience4JCircuitBreakerFactory circuitBreakerFactory, IPMSRestTemplate restTemplate) {
        this.circuitBreaker =(Resilience4JCircuitBreaker) circuitBreakerFactory.create("platform-management");
        this.restTemplate = restTemplate;
    }

    /**
     * Gets for object.
     *
     * @param <T>          the type parameter
     * @param url          the url
     * @param responseType the response type
     * @param uriVariables the uri variables
     * @return the for object
     */
    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 3000, multiplier = 5))
    public <T> T getForObject(String url, Class<T> responseType, Object... uriVariables) {
        return circuitBreaker.run(() -> restTemplate.getForObject(url, responseType, uriVariables),
                PMSRestTemplate::handleFallback);
    }

    /**
     * Gets for object with reduced backoff.
     *
     * @param <T>          the type parameter
     * @param url          the url
     * @param responseType the response type
     * @param uriVariables the uri variables
     * @return the for object
     */
    @Retryable(maxAttempts = 2, backoff = @Backoff(delay = 1000, multiplier = 2))
    public <T> T getObject(String url, Class<T> responseType, Object... uriVariables) {
        return circuitBreaker.run(() -> restTemplate.getForObject(url, responseType, uriVariables),
                PMSRestTemplate::handleFallback);
    }

    /**
     * Gets for object with no retries.
     *
     * @param <T>          the type parameter
     * @param url          the url
     * @param responseType the response type
     * @param uriVariables the uri variables
     * @return the for object
     */
    public <T> T getObjectWithNoRetries(String url, Class<T> responseType, Object... uriVariables) {
        return circuitBreaker.run(() -> restTemplate.getForObject(url, responseType, uriVariables),
                PMSRestTemplate::handleFallback);
    }

    /**
     * Gets for object.
     *
     * @param <T>          the type parameter
     * @param url          the url
     * @param responseType the response type
     * @return the for object
     */
    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 3000, multiplier = 5))
    public <T> T getForObject(URI url, Class<T> responseType) {
        return circuitBreaker.run(() -> restTemplate.getForObject(url, responseType),
                PMSRestTemplate::handleFallback);
    }

    /**
     * Post for object t.
     *
     * @param <T>          the type parameter
     * @param url          the url
     * @param request      the request
     * @param responseType the response type
     * @param uriVariables the uri variables
     * @return the t
     */
    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 3000, multiplier = 5))
    public <T> T postForObject(String url, @Nullable Object request, Class<T> responseType,
                               Object... uriVariables) {
        return circuitBreaker.run(() -> restTemplate.postForObject(url, request, responseType, uriVariables),
                PMSRestTemplate::handleFallback);
    }

    /**
     * Post for object t.
     *
     * @param <T>          the type parameter
     * @param url          the url
     * @param request      the request
     * @param responseType the response type
     * @return the t
     */
    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 3000, multiplier = 5))
    public <T> T postForObject(URI url, @Nullable Object request, Class<T> responseType) {
        return circuitBreaker.run(() -> restTemplate.postForObject(url, request, responseType),
                PMSRestTemplate::handleFallback);
    }

    /**
     * Gets for entity.
     *
     * @param <T>          the type parameter
     * @param url          the url
     * @param responseType the response type
     * @param uriVariables the uri variables
     * @return the for entity
     */
    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 3000, multiplier = 5))
    public <T> ResponseEntity<T> getForEntity(String url, Class<T> responseType, Object... uriVariables) {
        return circuitBreaker.run(() -> restTemplate.getForEntity(url, responseType, uriVariables),
                PMSRestTemplate::handleFallback);
    }

    /**
     * Gets for entity with reduced backoff.
     *
     * @param <T>          the type parameter
     * @param url          the url
     * @param responseType the response type
     * @param uriVariables the uri variables
     * @return the for entity
     */
    @Retryable(maxAttempts = 2, backoff = @Backoff(delay = 1000, multiplier = 2))
    public <T> ResponseEntity<T> getEntity(String url, Class<T> responseType, Object... uriVariables) {
        return circuitBreaker.run(() -> restTemplate.getForEntity(url, responseType, uriVariables),
                PMSRestTemplate::handleFallback);
    }

    /**
     * Post for entity response entity.
     *
     * @param <T>          the type parameter
     * @param url          the url
     * @param request      the request
     * @param responseType the response type
     * @param uriVariables the uri variables
     * @return the response entity
     */
    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 3000, multiplier = 5))
    public <T> ResponseEntity<T> postForEntity(String url, @Nullable Object request, Class<T> responseType,
                                               Object... uriVariables) {
        return circuitBreaker.run(() -> restTemplate.postForEntity(url, request, responseType, uriVariables),
                PMSRestTemplate::handleFallback);
    }


    /**
     * Exchange t.
     *
     * @param <T>           the type parameter
     * @param url           the url
     * @param method        the method
     * @param requestEntity the request entity
     * @param responseType  the response type
     * @param uriVariables  the uri variables
     * @return the t
     */
    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 3000, multiplier = 5))
    public <T> T exchange(String url, HttpMethod method, @Nullable HttpEntity<?> requestEntity,
                          Class<T> responseType, Object... uriVariables) {
        ResponseEntity<T> responseEntity = circuitBreaker.run(() ->
                        restTemplate.exchange(url, method, requestEntity, responseType, uriVariables),
                PMSRestTemplate::handleFallback);

        return extractResponseBody(responseEntity);
    }

    /**
     * Exchange t.
     *
     * @param <T>           the type parameter
     * @param url           the url
     * @param method        the method
     * @param requestEntity the request entity
     * @param responseType  the response type
     * @return the t
     */
    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 3000, multiplier = 5))
    public <T> T exchange(URI url, HttpMethod method, @Nullable HttpEntity<?> requestEntity, Class<T> responseType) {
        ResponseEntity<T> responseEntity = circuitBreaker.run(() ->
                        restTemplate.exchange(url, method, requestEntity, responseType),
                PMSRestTemplate::handleFallback);
        return extractResponseBody(responseEntity);
    }


    /**
     * Extract response body t.
     *
     * @param <T>            the type parameter
     * @param responseEntity the response entity
     * @return the t
     */
    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 3000, multiplier = 5))
    private static <T> T extractResponseBody(ResponseEntity<T> responseEntity) {
        if (responseEntity != null && responseEntity.getBody() != null) {
            return responseEntity.getBody();
        } else {
            throw new PlatformManagementException(PlatformManagementErrorCode.RESPONSE_FROM_AUTH_SERVER_CANNOT_BE_PROCESSED,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), "Empty or null response body");
        }
    }

    /**
     * Put.
     *
     * @param url          the url
     * @param request      the request
     * @param uriVariables the uri variables
     */
    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 3000, multiplier = 5))
    public void put(String url, @Nullable Object request, Object... uriVariables) {
        circuitBreaker.run(() -> {
            restTemplate.put(url, request, uriVariables);
            return null; // Return null because it's a void method
        }, PMSRestTemplate::handleFallback);
    }

    /**
     * Delete.
     *
     * @param url the url
     */
    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 3000, multiplier = 5))
    public void delete(String url) {
        circuitBreaker.run(() -> {
            restTemplate.delete(url);
            return null; // Return null because it's a void method
        }, PMSRestTemplate::handleFallback);
    }

    /**
     * Delete.
     *
     * @param url          the url
     * @param uriVariables the uri variables
     */
    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 3000, multiplier = 5))
    public void delete(String url, Object... uriVariables) {
        circuitBreaker.run(() -> {
            restTemplate.delete(url, uriVariables);
            return null; // Return null because it's a void method
        }, PMSRestTemplate::handleFallback);
    }

    /**
     * Delete.
     *
     * @param url          the url
     * @param uriVariables the uri variables
     */
    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 3000, multiplier = 5))
    public void delete(String url, Map<String, ?> uriVariables) {
        circuitBreaker.run(() -> {
            restTemplate.delete(url, uriVariables);
            return null; // Return null because it's a void method
        }, PMSRestTemplate::handleFallback);
    }


    /**
     * Handle fallback t.
     *
     * @param <T>       the type parameter
     * @param throwable the throwable
     * @return the t
     */
    private static <T> T handleFallback(Throwable throwable) {
        if (throwable instanceof HttpClientErrorException) {
            HttpClientErrorException clientErrorException = (HttpClientErrorException) throwable;
            LOG.error("Http Client Error exception", throwable);
            throw HttpClientErrorException.create(HttpStatus.BAD_REQUEST, HttpStatus.BAD_REQUEST.getReasonPhrase(), Objects.requireNonNull(clientErrorException.getResponseHeaders()), clientErrorException.getResponseBodyAsByteArray(), null);
        } else if (throwable instanceof HttpServerErrorException) {
            HttpServerErrorException serverErrorException = (HttpServerErrorException) throwable;
            LOG.error("Http Server Error exception", throwable);
            String message = serverErrorException.getMessage();
            if (message != null && message.contains(GBQ_CONFIG_IS_NOT_DEFINED_FOR_TENANT)) {
                throw serverErrorException;
            }
            throw HttpServerErrorException.create(HttpStatus.BAD_REQUEST, HttpStatus.BAD_REQUEST.getReasonPhrase(), Objects.requireNonNull(serverErrorException.getResponseHeaders()), serverErrorException.getResponseBodyAsByteArray(), null);
        } else {
            LOG.error("Unable to fetch response due to fall back", throwable.getCause());
            throw new PlatformManagementException(PlatformManagementErrorCode.UNABLE_TO_FETCH_RESPONSE, HttpStatus.INTERNAL_SERVER_ERROR.value(), throwable.getCause());
        }
    }
}
