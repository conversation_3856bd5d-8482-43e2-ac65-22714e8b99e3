package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.approve.ApprovalStatus;
import com.reltio.services.pms.service.jobs.TaskService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/v1/environments/{envId}/jobs", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name= "Approval Tasks Management")
@ReltioSecured(resourceClass = Pms.Environment.Jobs.class)
public class ApprovalTaskController {
    private final TaskService taskService;

    @Autowired
    public ApprovalTaskController(TaskService taskService) {
        this.taskService = taskService;
    }

    @PutMapping(value = "/{jobId}/tasks/{taskId}/_approve")
    public TaskInstance approve(@PathVariable String jobId, @PathVariable String taskId, @PathVariable String envId) {
        TaskInstance taskInstance = taskService.setResolution(envId, jobId, taskId, ApprovalStatus.APPROVED);
        taskService.updateTask(envId, jobId, taskInstance);
        return taskInstance;
    }

    @PutMapping(value = "/{jobId}/tasks/{taskId}/_reject")
    public TaskInstance reject(@PathVariable String jobId, @PathVariable String taskId, @PathVariable String envId) {
        TaskInstance taskInstance = taskService.setResolution(envId, jobId, taskId, ApprovalStatus.REJECTED);
        taskService.updateTask(envId, jobId, taskInstance);
        //TODO: Update the below logic to directly update the Task Object in database
        return taskInstance;
    }
}
