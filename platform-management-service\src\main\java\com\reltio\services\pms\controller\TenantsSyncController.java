package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.auth.domain.ReltioPrivileges;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.model.RDMTenantsSyncResponse;
import com.reltio.services.pms.service.sync.RdmSyncService;
import com.reltio.services.pms.service.sync.TenantsSyncService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/api/v1/synctopms", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "TenantsSyncToPMS")
public class TenantsSyncController {
    private static final Logger LOG = Logger.getLogger(TenantsSyncController.class);

    private final TenantsSyncService tenantsSyncService;
    private final RdmSyncService rdmSyncService;

    @Autowired
    public TenantsSyncController(TenantsSyncService tenantsSyncService, RdmSyncService rdmSyncService) {
        this.tenantsSyncService = tenantsSyncService;
        this.rdmSyncService = rdmSyncService;
    }

    @PostMapping(value = "/{environmentName}/status", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ReltioSecured(resourceClass = Pms.Tenant.class, privileges = ReltioPrivileges.CREATE)
    public Map<String, String> syncTenantStatus(@PathVariable String environmentName) {
        LOG.warn("Attempt to call sync status for environment " + environmentName);
        return new HashMap<>();
    }

    @DeleteMapping(value = "/{environmentName}")
    @ReltioSecured(resourceClass = Pms.Tenant.class, privileges = ReltioPrivileges.READ)
    public Map<String, List<String>> getDeleteTenantList(@PathVariable String environmentName) {
        return tenantsSyncService.getDeleteTenantList(environmentName);
    }

    @PutMapping(value = "/rdmTenants")
    @ReltioSecured(resourceClass = Pms.Tenant.class, privileges = ReltioPrivileges.CREATE)
    public RDMTenantsSyncResponse syncRDMTenants() {
        return rdmSyncService.syncRDM();
    }
}
