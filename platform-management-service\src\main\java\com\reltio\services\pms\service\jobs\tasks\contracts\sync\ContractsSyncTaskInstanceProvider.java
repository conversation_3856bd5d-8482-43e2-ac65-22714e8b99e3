package com.reltio.services.pms.service.jobs.tasks.contracts.sync;

import com.reltio.services.pms.common.model.jobs.tasks.contracts.sync.ContractsSyncTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.contracts.sync.ContractsSyncTaskInstanceParams;
import com.reltio.services.pms.common.model.pipeline.tasks.ContractsSyncPipelineTaskConfig;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.jobs.tasks.TaskInstanceProvider;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class ContractsSyncTaskInstanceProvider implements TaskInstanceProvider<ContractsSyncTaskInstance, ContractsSyncPipelineTaskConfig, ContractsSyncTaskInstanceParams> {

    @Override
    public TaskType getTaskType() {
        return TaskType.CONTRACTS_SYNC_TASK;
    }


    @Override
    public ContractsSyncTaskInstance getTaskDetail(String envId, String jobId, ContractsSyncPipelineTaskConfig
            taskConfig, ContractsSyncTaskInstanceParams params) {
        return new ContractsSyncTaskInstance(
                UUID.randomUUID().toString(),
                taskConfig.getName(),
                jobId,
                null,
                null,
                null,
                null,
                null,
                envId,
                params.getActivatedTime());

    }
}
