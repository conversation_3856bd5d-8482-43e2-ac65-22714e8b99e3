package com.reltio.services.pms.common.model;

import java.beans.PropertyEditorSupport;

public class ServiceTypePropertyEditor extends PropertyEditorSupport {

    @Override
    public String getAsText() {
        ServiceType serviceType = (ServiceType) getValue();
        return serviceType == null ? null : serviceType.getValue();
    }

    @Override
    public void setAsText(String text){
        setValue(ServiceType.convertFromString(text));
    }

}
