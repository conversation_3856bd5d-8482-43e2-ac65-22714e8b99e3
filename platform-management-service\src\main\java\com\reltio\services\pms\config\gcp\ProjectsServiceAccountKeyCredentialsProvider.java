package com.reltio.services.pms.config.gcp;

import com.google.api.gax.core.CredentialsProvider;
import com.google.api.gax.core.FixedCredentialsProvider;
import com.google.auth.Credentials;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Base64InputStream;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component("projectsCredentialsProvider")
public class ProjectsServiceAccountKeyCredentialsProvider implements CredentialsProvider {

    private static final Logger LOGGER = Logger.getLogger(ProjectsServiceAccountKeyCredentialsProvider.class);

    private final String credentials;
    private final List<String> scopes = Arrays.asList("https://www.googleapis.com/auth/cloud-platform","https://www.googleapis.com/auth/bigtable.admin.table",
            "https://www.googleapis.com/auth/bigtable.data");

    public ProjectsServiceAccountKeyCredentialsProvider(@Value("${projects.service.account.credentials}") String credentials) {
        this.credentials = credentials;
        Set<String> properties = new HashSet<>();
        properties.add(credentials);
        PropertiesValidator.validateProperties(properties, LOGGER);
    }

    @Override
    public Credentials getCredentials() throws IOException {
        try (InputStream credentialsStream = getCredentialsStream(credentials)) {
            return FixedCredentialsProvider.create(ServiceAccountCredentials.fromStream(credentialsStream).createScoped(scopes)).getCredentials();
        }
    }

    public GoogleCredentials getCredentialsGBT() throws IOException {
        try (InputStream credentialsStream = getCredentialsStream(credentials)) {
            return ServiceAccountCredentials.fromStream(credentialsStream).createScoped(scopes);
        }
    }

    /**
     * This method gets Base64 input Stream or Regular Byte Array stream based on the input String.
     *
     * @param credentials
     * @return
     */
    private static InputStream getCredentialsStream(String credentials) {

        if (Base64.isBase64(credentials)) {
            return new Base64InputStream(new ByteArrayInputStream(credentials.getBytes(StandardCharsets.UTF_8)));
        } else {
            return new ByteArrayInputStream(credentials.getBytes(StandardCharsets.UTF_8));
        }
    }

}
