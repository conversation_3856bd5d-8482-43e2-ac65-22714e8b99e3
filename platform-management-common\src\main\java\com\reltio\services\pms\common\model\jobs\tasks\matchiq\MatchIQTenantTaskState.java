package com.reltio.services.pms.common.model.jobs.tasks.matchiq;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

public class MatchIQTenantTaskState {
    @JsonProperty(value = "matchIQTaskStatus")
    private MatchIQTaskStatus matchIQTaskStatus;

    @JsonProperty(value = "exportJobId")
    private String exportJobId;

    @JsonProperty(value = "gCSExportJobID")
    private String gCSExportJobID;

    @JsonProperty(value = "destinationFlowId")
    private String destinationFlowId;

    @JsonProperty(value = "downloadAttempted")
    private int downloadAttempted;

    @JsonCreator
    public MatchIQTenantTaskState(@JsonProperty(value = "matchIQTaskStatus") MatchIQTaskStatus matchIQTaskStatus,
                                  @JsonProperty(value = "exportJobId") String exportJobId,
                                  @JsonProperty(value = "gCSExportJobID") String gCSExportJobID,
                                  @JsonProperty(value = "destinationFlowId") String destinationFlowId,
                                  @JsonProperty(value = "downloadAttempted", defaultValue = "0") int downloadAttempted) {
        this.matchIQTaskStatus = matchIQTaskStatus;
        this.exportJobId = exportJobId;
        this.gCSExportJobID = gCSExportJobID;
        this.destinationFlowId = destinationFlowId;
        this.downloadAttempted = downloadAttempted;
    }

    public MatchIQTaskStatus getMatchIQTaskStatus() {
        return matchIQTaskStatus;
    }

    public void setMatchIQTaskStatus(MatchIQTaskStatus matchIQTaskStatus) {
        this.matchIQTaskStatus = matchIQTaskStatus;
    }

    public String getExportJobId() {
        return exportJobId;
    }

    public void setExportJobId(String exportJobId) {
        this.exportJobId = exportJobId;
    }

    public String getGCSExportJobID() {
        return gCSExportJobID;
    }

    public void setGCSExportJobID(String gCSExportJobID) {
        this.gCSExportJobID = gCSExportJobID;
    }

    public String getDestinationFlowId() {
        return destinationFlowId;
    }

    public void setDestinationFlowId(String destinationFlowId) {
        this.destinationFlowId = destinationFlowId;
    }

    public int getDownloadAttempted() {
        return downloadAttempted;
    }

    public void incrementDownloadAttempted() {
        this.downloadAttempted++;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MatchIQTenantTaskState)) {
            return false;
        }

        MatchIQTenantTaskState that = (MatchIQTenantTaskState) o;
        return getMatchIQTaskStatus() == that.getMatchIQTaskStatus() &&
                Objects.equals(getGCSExportJobID(), that.getGCSExportJobID()) &&
                Objects.equals(getExportJobId(), that.getExportJobId()) &&
                Objects.equals(getDestinationFlowId(), that.getDestinationFlowId()) &&
                Objects.equals(getDownloadAttempted(), that.getDownloadAttempted());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getMatchIQTaskStatus(),getGCSExportJobID(), getExportJobId(),getDestinationFlowId(), getDownloadAttempted());
    }
}
