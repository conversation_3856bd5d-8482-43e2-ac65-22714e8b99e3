package com.reltio.services.pms.common.model.jobs.tasks.workato;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.Requester;
import com.reltio.services.pms.common.model.Rih;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServiceEnablementBaseTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

import java.util.Map;
import java.util.Objects;
import java.util.Set;

public class WorkatoEnablementTaskInstance extends ServiceEnablementBaseTaskInstance {

    @JsonProperty(value = "productEdition")
    private final String productEdition;

    @JsonProperty(value = "rih")
    private final Rih rih;

    @JsonProperty(value = "dnb_api_secret")
    private final String dnbApiSecret;

    @JsonProperty(value = "dnb_api_key")
    private final String dnbApiKey;

    @JsonProperty(value = "requester")
    private final Requester requester;

    @JsonCreator
    public WorkatoEnablementTaskInstance(@JsonProperty(value = "id") String id,
                                         @JsonProperty(value = "name") String name,
                                         @JsonProperty(value = "envId") String envId,
                                         @JsonProperty(value = "jobId") String jobId,
                                         @JsonProperty(value = "startTime") Long startTime,
                                         @JsonProperty(value = "finishTime") Long finishTime,
                                         @JsonProperty(value = "status") TaskStatus status,
                                         @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                         @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                         @JsonProperty(value = "executingNodeName") String executingNodeName,
                                         @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                         @JsonProperty(value = "failedTenants") Set<String> failedTenants,
                                         @JsonProperty(value = "totalProcessedTenants", defaultValue = "0") int totalProcessedTenants,
                                         @JsonProperty(value = "tenantsForServiceEnablement") Set<String> tenantsForServiceEnablement,
                                         @JsonProperty(value="events") Map<String,Set<String>> events,
                                         @JsonProperty(value = "rih") Rih rih,
                                         @JsonProperty(value = "productEdition") String productEdition,
                                         @JsonProperty(value = "dnb_api_secret") String dnbApiSecret,
                                         @JsonProperty(value = "dnb_api_key") String dnbApiKey,
                                         @JsonProperty(value = "requester") Requester requester) {
        super(id, name, jobId, startTime, finishTime, TaskType.WORKATO_ENABLEMENT_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId, failedTenants, totalProcessedTenants,
                tenantsForServiceEnablement, events);
        this.rih = rih;
        this.productEdition = productEdition;
        this.dnbApiSecret = dnbApiSecret;
        this.dnbApiKey = dnbApiKey;
        this.requester = requester;
    }

    public String getProductEdition() {
        return productEdition;
    }

    public Rih getRih() {
        return rih;
    }

    public String getDnbApiSecret() {
        return dnbApiSecret;
    }

    public String getDnbApiKey() {
        return dnbApiKey;
    }

    public Requester getRequester() {
        return requester;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || this.getClass() != o.getClass()) {
            return false;
        }
        WorkatoEnablementTaskInstance that = (WorkatoEnablementTaskInstance) o;
        return super.equals(that) && Objects.equals(getProductEdition(), that.getProductEdition())
                && Objects.equals(getRih(), that.getRih()) && Objects.equals(getDnbApiSecret(), that.getDnbApiSecret())
                && Objects.equals(getDnbApiKey(), that.getDnbApiKey()) && Objects.equals(getRequester(), that.getRequester());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getProductEdition(), getRih(), getDnbApiSecret(), getDnbApiKey(), getRequester());
    }


}
