package com.reltio.services.pms.common.sales;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.tenant.ProductUrl;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.Map;
import java.util.Set;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TenantConfigurationValues {

    @JsonProperty("awsMessagingQueue")
    private transient Set<String> awsMessagingQueue;

    @JsonProperty("keySpaceType")
    private transient Map<String, String> keySpaceType;

    @JsonProperty("clusters")
    private transient Set<String> clusters;

    @JsonProperty("productUrl")
    private ProductUrl productUrl;

    @JsonProperty("throttlingConfigValues")
    private Map<String, Object> throttlingConfigValues;

    @JsonProperty("dynamoDbTableProprties")
    private Map<String, Object> dynamoDbTableProperties;

    @JsonProperty("dataloadCluster")
    private String dataloadCluster;

    @JsonProperty("esClusterName")
    private String esClusterName;

    @JsonProperty("dataStorage")
    private TenantStorageInfo dataStorage;

    @JsonProperty("historyStorage")
    private TenantStorageInfo historyStorage;

    @JsonProperty("matchStorage")
    private TenantStorageInfo matchStorage;
}
