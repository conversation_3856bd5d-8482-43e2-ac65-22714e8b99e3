package com.reltio.services.pms.common.model.jobs.tasks;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.approve.ApprovalTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.approve.storage.ApprovalOfStorageTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.auth.AuthTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.authDeprovision.AuthDeProvisionTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.azureBlobStorage.AzureBlobStorageTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.combined.CleanTenantTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.separated.execution.CleanTenantExecutionTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.separated.validation.CleanTenantValidationTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.contracts.sync.ContractsSyncTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.dnb.connector.DNBConnectorTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.dnbDeprovision.DnbDeProvisionTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.dtss.DTSSTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.dtssDeprovision.DeProvisionDtssTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.email.notification.EmailNotificationTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.email.verification.EmailVerificationTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.fern.FernTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.fernDeprovision.FernDeProvisionTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.matchIqDeprovision.DeProvisionMatchIqTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.rdm.RDMServiceDeploymentTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.rih.RihGenericTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.riqDeprovision.RiqDeProvisionTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.configs.CloneTenantConfigsTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.configs.RetrieveAndStoreTenantConfigsTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.data.clone.ConfigureStreamingAndAnalyticsTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.data.clone.DataBackupTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.data.clone.DataRestoreTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.data.clone.DeleteDynamoDbBackupTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.history.clone.HistoryBackupTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.history.clone.HistoryRestoreTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.workatoDeprovision.WorkatoDeProvisionTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.imageHostingDeprovision.ImageHostingDeProvisionTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.imagehosting.ImageHostingTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.maintenance.MaintenanceModeTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.matchiq.MatchIQTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.mdm.MdmTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.notification.EnterpriseNotificationTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.notification.FreeTierEmailNotificationTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.r360task.R360DataSyncTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.rdm.RdmTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServiceEnablementBaseTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.sfdcDeprovision.SfdcDeProvisionTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.sfdcconnector.SFDCConnectorEnablementTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.shield.ShieldEnablementTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.streaming.deprovision.StreamingDeProvisionTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.streaming.provision.StreamingTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.deprovision.DeleteMDMTenantExecutionTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.deprovision.rdm.DeprovisionRDMTenantExecutionTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.sync.TenantsSyncTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.update.tenant.UpdateTenantTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.updateR360Tenant.UpdateR360TenantTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.workato.WorkatoEnablementTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.workflow.WorkflowTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.workflowDeprovision.WorkflowDeProvisionTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;
import java.util.Objects;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, visible = true, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type", defaultImpl = ServiceEnablementBaseTaskInstance.class)
@JsonSubTypes({
        @JsonSubTypes.Type(value = AuthTaskInstance.class, name = TaskType.AUTH_TASK_NAME),
        @JsonSubTypes.Type(value = SleepTaskInstance.class, name = TaskType.SLEEP_TASK_NAME),
        @JsonSubTypes.Type(value = MultiSleepTaskInstance.class, name = TaskType.MULTI_SLEEP_TASK_NAME),
        @JsonSubTypes.Type(value = ApprovalTaskInstance.class, name = TaskType.APPROVAL_TASK_NAME),
        @JsonSubTypes.Type(value = MdmTaskInstance.class, name = TaskType.MDM_TASK_NAME),
        @JsonSubTypes.Type(value = FreeTierEmailNotificationTaskInstance.class, name = TaskType.FREE_TIER_EMAIL_NOTIFICATION_TASK_NAME),
        @JsonSubTypes.Type(value = EmailVerificationTaskInstance.class, name = TaskType.EMAIL_VERIFICATION_TASK_NAME),
        @JsonSubTypes.Type(value = R360DataSyncTaskInstance.class, name = TaskType.R360_DATA_SYNC_TASK_NAME),
        @JsonSubTypes.Type(value = WorkflowTaskInstance.class, name = TaskType.WORKFLOW_TASK_NAME),
        @JsonSubTypes.Type(value = MatchIQTaskInstance.class, name = TaskType.MATCH_IQ_TASK_NAME),
        @JsonSubTypes.Type(value = StreamingTaskInstance.class, name = TaskType.STREAMING_TASK_NAME),
        @JsonSubTypes.Type(value = RdmTaskInstance.class, name = TaskType.RDM_TASK_NAME),
        @JsonSubTypes.Type(value = RDMServiceDeploymentTaskInstance.class, name = TaskType.RDM_SERVICE_DEPLOYMENT_NAME),
        @JsonSubTypes.Type(value = WorkatoEnablementTaskInstance.class, name = TaskType.WORKATO_ENABLEMENT_TASK_NAME),
        @JsonSubTypes.Type(value = ApprovalOfStorageTaskInstance.class, name = TaskType.APPROVAL_OF_STORAGE_TASK_NAME),
        @JsonSubTypes.Type(value = ImageHostingTaskInstance.class, name = TaskType.IMAGE_HOSTING_ENABLEMENT_TASK_NAME),
        @JsonSubTypes.Type(value = EnterpriseNotificationTaskInstance.class, name = TaskType.ENTERPRISE_NOTIFICATION_TASK_NAME),
        @JsonSubTypes.Type(value = DNBConnectorTaskInstance.class, name = TaskType.DNB_CONNECTOR_TASK_NAME),
        @JsonSubTypes.Type(value = SFDCConnectorEnablementTaskInstance.class, name = TaskType.SFDC_CONNECTOR_ENABLEMENT_TASK_NAME),
        @JsonSubTypes.Type(value = DTSSTaskInstance.class, name = TaskType.DTSS_TASK_NAME),
        @JsonSubTypes.Type(value = ShieldEnablementTaskInstance.class, name = TaskType.SHIELD_ENABLEMENT_TASK_NAME),
        @JsonSubTypes.Type(value = UpdateTenantTaskInstance.class, name = TaskType.UPDATE_TENANT_TASK_NAME),
        @JsonSubTypes.Type(value = ContractsSyncTaskInstance.class, name = TaskType.CONTRACTS_SYNC_TASK_NAME),
        @JsonSubTypes.Type(value = UpdateR360TenantTaskInstance.class, name = TaskType.UPDATE_R360_TENANT_TASK_NAME),
        @JsonSubTypes.Type(value = CleanTenantTaskInstance.class, name = TaskType.CLEAN_TENANT_TASK_NAME),
        @JsonSubTypes.Type(value = FernTaskInstance.class, name = TaskType.FERN_TASK_NAME),
        @JsonSubTypes.Type(value = FernDeProvisionTaskInstance.class, name = TaskType.FERN_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = CleanTenantValidationTaskInstance.class, name =
                TaskType.CLEAN_TENANT_VALIDATION_TASK_NAME),
        @JsonSubTypes.Type(value = CleanTenantExecutionTaskInstance.class, name =
                TaskType.CLEAN_TENANT_EXECUTION_TASK_NAME),
        @JsonSubTypes.Type(value = MaintenanceModeTaskInstance.class, name =
                TaskType.MAINTENANCE_MODE_TASK_NAME),
        @JsonSubTypes.Type(value = TenantsSyncTaskInstance.class, name =
                TaskType.TENANTS_SYNC_TASK_NAME),
        @JsonSubTypes.Type(value = AuthDeProvisionTaskInstance.class, name =
                TaskType.AUTH_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = WorkflowDeProvisionTaskInstance.class, name =
                TaskType.WORKFLOW_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = DeleteMDMTenantExecutionTaskInstance.class, name =
                TaskType.DELETE_MDM_TENANT_TASK_NAME),
        @JsonSubTypes.Type(value = EmailNotificationTaskInstance.class, name =
                TaskType.EMAIL_NOTIFICATION_TASK_NAME),
        @JsonSubTypes.Type(value = DnbDeProvisionTaskInstance.class, name =
                TaskType.DNB_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = SfdcDeProvisionTaskInstance.class, name =
                TaskType.SFDC_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = DeprovisionRDMTenantExecutionTaskInstance.class, name =
                TaskType.DELETE_RDM_TENANT_TASK_NAME),
        @JsonSubTypes.Type(value = StreamingDeProvisionTaskInstance.class, name =
                TaskType.STREAMING_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = ImageHostingDeProvisionTaskInstance.class, name =
                TaskType.IMAGE_HOSTING_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = RiqDeProvisionTaskInstance.class, name =
                TaskType.RIQ_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = WorkatoDeProvisionTaskInstance.class, name =
                TaskType.WORKATO_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = AzureBlobStorageTaskInstance.class , name = TaskType.AZURE_BLOB_STORAGE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = DeProvisionDtssTaskInstance.class , name = TaskType.DTSS_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = DeProvisionMatchIqTaskInstance.class, name = TaskType.MATCH_IQ_DE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = AzureBlobStorageTaskInstance.class , name = TaskType.AZURE_BLOB_STORAGE_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = RihGenericTaskInstance.class , name = TaskType.RIH_GENERIC_PROVISION_TASK_NAME),
        @JsonSubTypes.Type(value = RetrieveAndStoreTenantConfigsTaskInstance.class , name = TaskType.RETRIEVE_AND_STORE_TENANT_CONFIGS_TASK_NAME),
        @JsonSubTypes.Type(value = CloneTenantConfigsTaskInstance.class , name = TaskType.CLONE_TENANTS_CONFIGS_TASK_NAME),
        @JsonSubTypes.Type(value = DataBackupTaskInstance.class, name = TaskType.DATA_BACKUP_TASK_NAME),
        @JsonSubTypes.Type(value = DataRestoreTaskInstance.class, name = TaskType.DATA_RESTORE_TASK_NAME),
        @JsonSubTypes.Type(value = DeleteDynamoDbBackupTaskInstance.class, name = TaskType.DELETE_DYNAMO_DB_BACKUP_TASK_NAME),
        @JsonSubTypes.Type(value = ConfigureStreamingAndAnalyticsTaskInstance.class, name = TaskType.CONFIGURE_STREAMING_AND_ANALYTICS_TASK_NAME),
        @JsonSubTypes.Type(value = HistoryBackupTaskInstance.class, name = TaskType.HISTORY_BACKUP_TASK_NAME),
        @JsonSubTypes.Type(value = HistoryRestoreTaskInstance.class, name = TaskType.HISTORY_RESTORE_TASK_NAME)
})
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
public class TaskInstance extends BaseFirestoreEntity {
    private String taskId;
    private String name;
    private String jobId;
    private Long startTime;
    private Long finishTime;
    private TaskType type;
    private TaskStatus status;
    private Long lastUpdatedTime;
    private TaskFailureContext taskFailureContext;
    private String executingNodeName;
    private ServiceNodeStatus serviceNodeStatus;
    private String envId;

    public TaskInstance(String taskId,
                        String name,
                        String jobId,
                        Long startTime,
                        Long finishTime,
                        TaskType type,
                        TaskStatus status,
                        Long lastUpdatedTime,
                        TaskFailureContext taskFailureContext,
                        String executingNodeName,
                        ServiceNodeStatus serviceNodeStatus,
                        String envId) {
        this.taskId = taskId;
        this.name = name;
        this.jobId = jobId;
        this.startTime = startTime;
        this.finishTime = finishTime;
        this.type = type;
        this.status = status;
        this.lastUpdatedTime = lastUpdatedTime;
        this.taskFailureContext = taskFailureContext;
        this.executingNodeName = executingNodeName;
        this.serviceNodeStatus = serviceNodeStatus;
        this.envId = envId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }


    public void setName(String name) {
        this.name = name;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public void setFinishTime(Long finishTime) {
        this.finishTime = finishTime;
    }

    public Long taskDuration() {
        if (startTime != null && finishTime != null) {
            return finishTime - startTime;
        } else {
            return startTime != null && startTime > 0L ? System.currentTimeMillis() - startTime : 0L;
        }
    }


    public void setType(TaskType type) {
        this.type = type;
    }

    public void setStatus(TaskStatus status) {
        this.status = status;
    }

    public void setLastUpdatedTime(Long lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    public void setTaskFailureContext(TaskFailureContext taskFailureContext) {
        this.taskFailureContext = taskFailureContext;
    }


    public void setExecutingNodeName(String executingNodeName) {
        this.executingNodeName = executingNodeName;
    }

    public void setServiceNodeStatus(ServiceNodeStatus serviceNodeStatus) {
        this.serviceNodeStatus = serviceNodeStatus;
    }


    public void setEnvId(String envId) {
        this.envId = envId;
    }

    @Override
    @JsonIgnore
    public String getID() {
        return taskId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TaskInstance that = (TaskInstance) o;
        return Objects.equals(getTaskId(), that.getTaskId()) &&
                Objects.equals(getName(), that.getName()) &&
                Objects.equals(getJobId(), that.getJobId()) &&
                Objects.equals(getStartTime(), that.getStartTime()) &&
                Objects.equals(getFinishTime(), that.getFinishTime()) &&
                getType() == that.getType() &&
                getStatus() == that.getStatus() &&
                Objects.equals(getLastUpdatedTime(), that.getLastUpdatedTime()) &&
                Objects.equals(getTaskFailureContext(), that.getTaskFailureContext()) &&
                Objects.equals(getExecutingNodeName(), that.getExecutingNodeName()) &&
                getServiceNodeStatus() == that.getServiceNodeStatus() &&
                Objects.equals(getEnvId(), that.getEnvId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getTaskId(), getName(), getJobId(), getCreatedBy(), getCreatedTime(), getUpdatedBy(), getUpdatedTime(), getStartTime(), getFinishTime(), getType(), getStatus(), getLastUpdatedTime(), getTaskFailureContext(), getExecutingNodeName(), getServiceNodeStatus());
    }

    @Override
    public String toString() {
        return "TaskDetail{" +
                "taskId='" + taskId + '\'' +
                ", name='" + name + '\'' +
                ", jobId='" + jobId + '\'' +
                ", createdBy='" + createdBy + '\'' +
                ", createdTime=" + createdTime +
                ", updatedBy='" + updatedBy + '\'' +
                ", updatedTime=" + updatedTime +
                ", startTime=" + startTime +
                ", finishTime=" + finishTime +
                ", type=" + type +
                ", status=" + status +
                ", lastUpdatedTime=" + lastUpdatedTime +
                ", failureReason='" + taskFailureContext + '\'' +
                ", executingNodeName='" + executingNodeName + '\'' +
                ", serviceNodeStatus=" + serviceNodeStatus +
                '}';
    }
}
