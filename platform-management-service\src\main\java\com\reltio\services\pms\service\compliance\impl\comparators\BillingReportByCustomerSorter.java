package com.reltio.services.pms.service.compliance.impl.comparators;

import com.reltio.services.pms.common.model.compliance.SortField;
import com.reltio.services.pms.common.model.compliance.SortOrder;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingReportByCustomerModel;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;

public class BillingReportByCustomerSorter {
    public static List<BillingReportByCustomerModel> sort(List<BillingReportByCustomerModel> reports,
                                                          String sortField, String sortOrder, Boolean export) {
        if (Objects.isNull(sortField)|| export) {
            sortField = SortField.BillingStartDate.name();
        }

        if (Objects.isNull(sortOrder)|| export) {
            sortOrder = SortOrder.DESC.name();
        }

        SortField sortFieldEnum = Enum.valueOf(SortField.class, sortField);
        SortOrder sortOrderEnum = Enum.valueOf(SortOrder.class, sortOrder);
        // Create a custom comparator based on the sortField and sortOrder
        Comparator<BillingReportByCustomerModel> comparator = (report1, report2) -> {
            switch (sortFieldEnum) {
                case Customer:
                    return report1.getCustomer().compareTo(report2.getCustomer());
                case CpQuota:
                    return report1.getCpQuota().compareTo(report2.getCpQuota());
                case TaskQuota:
                    return report1.getTaskQuota().compareTo(report2.getTaskQuota());
                case BillingEndDate:
                    return report1.getBillingEndDate().compareTo(report2.getBillingEndDate());
                case TaskUsage:
                    return Long.compare(report1.getTaskUsage(), report2.getTaskUsage());
                case TaskPercentage:
                    return Double.compare(report1.getTaskPercentage(), report2.getTaskPercentage());
                default:
                    return report1.getBillingStartDate().compareTo(report2.getBillingStartDate());
            }
        };

        // Sort the list based on the comparator and sortOrder
        if (SortOrder.DESC == sortOrderEnum) {
            reports.sort(comparator.reversed());
        } else if (SortOrder.ASC == sortOrderEnum) {
            reports.sort(comparator);
        }

        return reports;
    }
}

