package com.reltio.services.pms.common.model.supportability;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Objects;

/**
 * RESTModel
 * Created by apylkov
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class RestStatsDto implements StatsDto {
    @JsonProperty("startTime")
    private Long startTime;
    @JsonProperty("endTime")
    private Long endTime;
    @JsonProperty("totalCount")
    private Long totalCount;
    @JsonProperty("failedRequests")
    private Long failedRequests;
    @JsonProperty("avgTime")
    private Long avgTime;
    @JsonProperty("p50")
    private Long p50;
    @JsonProperty("p90")
    private Long p90;
    @JsonProperty("p95")
    private Long p95;
    @JsonProperty("p99")
    private Long p99;

    @JsonProperty("clusterType")
    private String clusterType;
    @JsonProperty("clusterName")
    private String clusterName;

    @JsonProperty("latencyByTime")
    private LatencyByTime latencyByTime;

    @JsonProperty("top5requestsByCount")
    private List<Requests> requestsByCount;

    @JsonProperty("top5requestsByLatency")
    private List<Requests> requestsByLatency;

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RestStatsDto restStatsDto = (RestStatsDto) o;
        return Objects.equals(getStartTime(), restStatsDto.getStartTime()) &&
                Objects.equals(getEndTime(), restStatsDto.getEndTime()) &&
                Objects.equals(getAvgTime(), restStatsDto.getAvgTime()) &&
                Objects.equals(getFailedRequests(), restStatsDto.getFailedRequests()) &&
                Objects.equals(getTotalCount(), restStatsDto.getTotalCount()) &&
                Objects.equals(getP50(), restStatsDto.getP50()) &&
                Objects.equals(getP90(), restStatsDto.getP90()) &&
                Objects.equals(getP95(), restStatsDto.getP95()) &&
                Objects.equals(getP99(), restStatsDto.getP99()) && Objects.equals(getClusterType(),
                restStatsDto.getClusterType()) && Objects.equals(getClusterName(), restStatsDto.getClusterName()) && Objects.equals(getLatencyByTime(), restStatsDto.getLatencyByTime());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getStartTime(), getEndTime(), getAvgTime(), getFailedRequests(), getTotalCount(),
                getP50(), getP90(), getP95(), getP99(), getClusterType(), getClusterName(), getLatencyByTime());
    }
}
