package com.reltio.services.pms.common.model.jobs.tasks.authDeprovision;

import com.reltio.services.pms.common.model.jobs.tasks.TaskInstanceParams;

import java.util.List;

public interface AuthDeProvisionTaskInstanceParams extends TaskInstanceParams {
    String getTenantId();
    boolean isDeleteAssociatedUsers();
    boolean isRemoveAssociatedRdms();
    boolean isRemoveGroupAssignment();
    String getAuthCustomerId();
    boolean isFullCustomerDeprovision();
    List<String> getUsersToDelete();
    boolean isFailOnError();

}
