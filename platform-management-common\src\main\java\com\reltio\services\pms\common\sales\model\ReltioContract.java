package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import com.reltio.services.pms.common.model.compliance.SubscriptionType;
import com.reltio.services.pms.common.sales.ProvisioningStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Builder
@AllArgsConstructor
@Getter
@Setter
public class ReltioContract extends BaseFirestoreEntity {

    @JsonProperty("contractId")
    private String contractId;

    @JsonProperty("accountId")
    private String accountId;

    @JsonProperty("contractNumber")
    private String contractNumber;

    @JsonProperty("status")
    private String status;

    @JsonProperty("opportunity")
    private SalesOpportunity opportunity;

    @JsonProperty("startDate")
    private Date startDate;

    @JsonProperty("endDate")
    private Date endDate;

    @JsonProperty("procurementChannel")
    private String procurementChannel;

    @JsonProperty("contractType")
    private String contractType;

    @JsonProperty("activatedDate")
    private Date activatedDate;

    @JsonProperty("industry")
    private String industry;

    @JsonProperty("platformPreference")
    private String platformPreference;

    @JsonProperty("deploymentRegion")
    private String deploymentRegion;

    @JsonProperty("productEdition")
    private String productEdition;

    @JsonProperty("customerContact")
    private BusinessContact customerContact;
    @JsonProperty("billingContact")
    private BusinessContact billingContact;

    @JsonProperty("provisioningStatus")
    private ProvisioningStatus provisioningStatus;

    @JsonProperty("authCustomerId")
    private String authCustomerId;

    @JsonProperty("isHipaa")
    private boolean isHipaa;

    @JsonProperty("subscriptionType")
    private SubscriptionType subscriptionType;

    @JsonProperty("packages")
    private List<SalesPackageConfig> packages = new ArrayList<>();

    private DeltaStatus deltaStatus;

    @JsonProperty("reltioPackageType")
    private ReltioPackageType reltioPackageType;

    public ReltioContract() {
    }

    public ReltioContract(String contractId, String accountId, String contractNumber, String status, SalesOpportunity opportunity, Date startDate, Date endDate, String procurementChannel, String contractType, Date activatedDate, String industry, String platformPreference, String deploymentRegion, String productEdition,
                          BusinessContact customerContact, BusinessContact billingContact,
                          ProvisioningStatus provisioningStatus,
                          String authCustomerId, List<SalesPackageConfig> packages, boolean isHipaa, SubscriptionType subscriptionType) {
        this.contractId = contractId;
        this.accountId = accountId;
        this.contractNumber = contractNumber;
        this.status = status;
        this.opportunity = opportunity;
        this.startDate = startDate;
        this.endDate = endDate;
        this.procurementChannel = procurementChannel;
        this.contractType = contractType;
        this.activatedDate = activatedDate;
        this.industry = industry;
        this.platformPreference = platformPreference;
        this.deploymentRegion = deploymentRegion;
        this.productEdition = productEdition;
        this.customerContact = customerContact;
        this.billingContact = billingContact;
        this.provisioningStatus = provisioningStatus;
        this.authCustomerId = authCustomerId;
        this.packages = packages;
        this.isHipaa = isHipaa;
        this.subscriptionType = subscriptionType != null ? subscriptionType : SubscriptionType.SUBSCRIPTION;
    }

    public void setOpportunity(SalesOpportunity opportunity) {
        this.opportunity = opportunity == null ? null : new SalesOpportunity(opportunity.getId(), opportunity.getName(),
                opportunity.getDescription(), opportunity.getType(), opportunity.getBusinessNeed(),
                opportunity.getMarketSegment(), opportunity.getDataDomain(), opportunity.getDeploymentRegion());
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate == null ? null : Date.from(startDate.toInstant());
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate == null ? null : Date.from(endDate.toInstant());
    }

    public void setActivatedDate(Date activatedDate) {
        this.activatedDate = activatedDate == null ? null : Date.from(activatedDate.toInstant());
    }

    public void setCustomerContact(BusinessContact customerContact) {
        this.customerContact = customerContact == null ? null : new BusinessContact(customerContact.getId(),
                customerContact.getName(),
                customerContact.getEmail(), customerContact.getPhone());
    }

    public void setBillingContact(BusinessContact billingContact) {
        this.billingContact = billingContact == null ? null : new BusinessContact(billingContact.getId(),
                billingContact.getName(),
                billingContact.getEmail(), billingContact.getPhone());
    }

    public void setPackages(List<SalesPackageConfig> packages) {
        this.packages = packages == null ? new ArrayList<>() : new ArrayList<>(packages);
    }

    @Override
    public String getID() {
        return this.contractId;
    }
}
