package com.reltio.services.pms.service.jobs.tasks.clean.tenant.separated.execution;

import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.AnalyticsCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.DataCleanUpOptions;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.ExternalServicesCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.MetaDataCleanUp;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.combined.CleanTenantTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.separated.execution.CleanTenantExecutionTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.CleanTenantExecutionTaskConfig;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.environment.EnvironmentService;
import com.reltio.services.pms.service.jobs.tasks.TaskInstanceProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Service
public class CleanTenantExecutionTaskInstanceProvider implements TaskInstanceProvider<CleanTenantExecutionTaskInstance, CleanTenantExecutionTaskConfig, CleanTenantTaskInstanceParams> {
    private final EnvironmentService environmentService;

    @Autowired
    public CleanTenantExecutionTaskInstanceProvider(EnvironmentService environmentService) {
        this.environmentService = environmentService;
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.DELETE_TENANT_DATA_EXECUTION_TASK;
    }

    @Override
    public CleanTenantExecutionTaskInstance getTaskDetail(String envId, String jobId,
                                                          CleanTenantExecutionTaskConfig pipelineTaskConfig,
                                                          CleanTenantTaskInstanceParams taskInstanceParams) {

        DataCleanUpOptions dataCleanUpOptions = pipelineTaskConfig.getDataCleanUpOptions();
        dataCleanUpOptions.setGraphClean(dataCleanUpOptions.isGraphClean() && taskInstanceParams.getParameters().isTenantData());
        dataCleanUpOptions.setActivities(dataCleanUpOptions.isActivities() && taskInstanceParams.getParameters().isHistoryData());
        dataCleanUpOptions.setHistory(dataCleanUpOptions.isHistory() && taskInstanceParams.getParameters().isHistoryData());
        dataCleanUpOptions.setPrimary(dataCleanUpOptions.isPrimary() && taskInstanceParams.getParameters().isTenantData());
        dataCleanUpOptions.setSecondary(dataCleanUpOptions.isSecondary() && taskInstanceParams.getParameters().isTenantData());
        dataCleanUpOptions.setInternalQueue(dataCleanUpOptions.isInternalQueue() && taskInstanceParams.getParameters().isTenantData());

        AnalyticsCleanUp analyticsCleanUp = pipelineTaskConfig.getAnalyticsCleanUp();
        analyticsCleanUp.setRiq(analyticsCleanUp.isRiq() && taskInstanceParams.getParameters().isTenantData());
        analyticsCleanUp.setGbQClean(analyticsCleanUp.isGbQClean() && taskInstanceParams.getParameters().isTenantData());
        analyticsCleanUp.setMatchIQ(analyticsCleanUp.isMatchIQ() && taskInstanceParams.getParameters().isTenantData());

        MetaDataCleanUp metaDataCleanUp = pipelineTaskConfig.getMetaDataCleanUp();
        metaDataCleanUp.setLookUpsClean(metaDataCleanUp.isLookUpsClean() && taskInstanceParams.getParameters().isTenantData());

        boolean externalQueue = (pipelineTaskConfig.isExternalQueue() && taskInstanceParams.getParameters().isTenantData());

        ExternalServicesCleanUp externalServicesCleanUp = pipelineTaskConfig.getExternalServicesCleanUp();
        externalServicesCleanUp.setWorkflow(externalServicesCleanUp.isWorkflow() && taskInstanceParams.getParameters().isTenantData());
        validateServiceUrl(envId, pipelineTaskConfig);

        return new CleanTenantExecutionTaskInstance(
                UUID.randomUUID().toString(),
                TaskType.CLEAN_TENANT_EXECUTION_TASK_NAME, jobId, 0L, null, null, null, null, null, null,
                envId,
                dataCleanUpOptions,
                externalServicesCleanUp,
                externalQueue,
                metaDataCleanUp,
                analyticsCleanUp,
                taskInstanceParams.getTenantName(),
                Collections.emptyList(),
                Collections.emptyList(),
                taskInstanceParams.getParameters(),
                taskInstanceParams.isQaAutomation()
        );
    }


    private void validateServiceUrl(String environmentName, CleanTenantExecutionTaskConfig cleanTenantTaskConfig) {
        List<ServiceType> serviceTypes = new ArrayList<>();
        if (cleanTenantTaskConfig.getAnalyticsCleanUp().isMatchIQ()) {
            serviceTypes.add(ServiceType.ML);
        }
        if (cleanTenantTaskConfig.getAnalyticsCleanUp().isRiq()) {
            serviceTypes.add(ServiceType.RI_API);
        }
        if (cleanTenantTaskConfig.getDataCleanUpOptions().isInternalQueue()) {
            serviceTypes.add(ServiceType.IRS);
        }
        environmentService.validateServiceUrl(serviceTypes, environmentName);
    }

}
