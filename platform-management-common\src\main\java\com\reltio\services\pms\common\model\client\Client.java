package com.reltio.services.pms.common.model.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@ToString
public class Client extends BaseFirestoreEntity {

    @JsonProperty("clientId")
    private String clientId;

    //TODO need to remove in next phase once all the clientSecret will be move in GCP
    @Setter
    @JsonProperty("clientSecret")
    private String clientSecret;

    @Setter
    @JsonProperty("authClientCloudKey")
    private String authClientCloudKey;

    @JsonProperty("scope")
    private List<String> scope = Collections.emptyList();

    @JsonProperty("authorities")
    private List<String> authorities = Collections.emptyList();

    @JsonProperty("resourceIds")
    private List<String> resourceIds = Arrays.asList("reltio_api", "token_validate");

    @JsonProperty("authorizedGrantTypes")
    private List<String> authorizedGrantTypes = Arrays.asList("refresh_token",
            "password",
            "external_token",
            "client_credentials");

    @JsonProperty("redirectUri")
    private List<String> redirectUri = List.of("http://localhost");

    @JsonProperty("defaultRolesAssignmentEnabled")
    private boolean defaultRolesAssignmentEnabled = true;

    @Setter
    @JsonProperty("consumer")
    private String consumer;

    @JsonCreator
    public Client(@JsonProperty("clientId") String clientId, @JsonProperty("clientSecret") String clientSecret,
                  @JsonProperty("authClientCloudKey") String authClientCloudKey) {
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.authClientCloudKey = authClientCloudKey;
    }

    @Override
    public String getID() {
        return getClientId();
    }

    @Override
    public int hashCode() {
        return Objects.hash(getClientId());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Client client = (Client) o;
        return Objects.equals(getClientId(), client.getClientId());
    }
}
