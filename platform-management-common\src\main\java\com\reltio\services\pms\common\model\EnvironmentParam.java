package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import lombok.Getter;
import java.time.Period;
import java.util.Objects;


@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EnvironmentParam {
    @JsonProperty("environmentPurpose")
    private final EnvironmentPurpose environmentPurpose;

    @JsonProperty("defaultTtl")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private final Period defaultTtl;

    @JsonProperty("maxTenantSizeSupported")
    private final TenantSize tenantSize;

    @JsonProperty("reltioRegion")
    private final String reltioRegion;

    @JsonProperty("geographicRegion")
    private final String geographicRegion;

    @JsonProperty("rihEnvironment")
    private String rihEnvironment;

    @JsonProperty("defaultPipelineId")
    private String defaultPipelineId;

    @JsonProperty("defaultPackageType")
    private ReltioPackageType defaultPackageType;

    @JsonProperty("defaultProductEdition")
    private String defaultProductEdition;

    @JsonProperty("allowedOptions")
    private final AllowedOptions allowedOptions;


    public EnvironmentParam(@JsonProperty("environmentPurpose") EnvironmentPurpose environmentPurpose, @JsonProperty("defaultTtl") Period defaultTtl,
                            @JsonProperty("maxTenantSizeSupported") TenantSize tenantSize, @JsonProperty("reltioRegion") String reltioRegion,
                            @JsonProperty("geographicRegion") String geographicRegion, @JsonProperty("rihEnvironment") String rihEnvironment,
                            @JsonProperty("defaultPipelineId") String defaultPipelineId, @JsonProperty("defaultPackageType") ReltioPackageType defaultPackageType,
                            @JsonProperty("defaultProductEdition") String defaultProductEdition, @JsonProperty("allowedOptions") AllowedOptions allowedOptions) {
        this.environmentPurpose = environmentPurpose;
        this.defaultTtl = defaultTtl;
        this.tenantSize=tenantSize;
        this.reltioRegion = reltioRegion;
        this.geographicRegion = geographicRegion;
        this.rihEnvironment = rihEnvironment;
        this.defaultPipelineId = defaultPipelineId;
        this.defaultPackageType = defaultPackageType;
        this.defaultProductEdition = defaultProductEdition;
        this.allowedOptions = allowedOptions;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        EnvironmentParam that = (EnvironmentParam) o;
        return environmentPurpose == that.environmentPurpose &&
                Objects.equals(defaultTtl, that.defaultTtl);
    }

    public void setRihEnvironment(String rihEnvironment) {
        this.rihEnvironment = rihEnvironment;
    }

    @Override
    public int hashCode() {
        return Objects.hash(environmentPurpose, defaultTtl);
    }
}
