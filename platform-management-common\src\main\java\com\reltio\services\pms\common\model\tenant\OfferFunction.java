package com.reltio.services.pms.common.model.tenant;

import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.util.StdConverter;

import java.util.Arrays;

@JsonDeserialize(converter = OfferFunction.OfferFunctionConverter.class)
public enum OfferFunction {
    LIFESCIENCES("Life Sciences"),
    MARKETING("Marketing"),
    DEFAULT("default");

    private final String value;

    OfferFunction(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return value;
    }

    public static OfferFunction convertFromString(String value) {
        return Arrays.stream(OfferFunction.values())
                .filter(e -> e.value.equals(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid value '" + value + "'"));

    }

    public static final class OfferFunctionConverter extends StdConverter<String, OfferFunction> {

        @Override
        public OfferFunction convert(String value) {
            return OfferFunction.convertFromString(value);
        }
    }
}
