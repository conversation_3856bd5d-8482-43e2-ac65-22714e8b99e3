package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.tenant.OfferFunction;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;
import java.util.regex.Pattern;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class TenantRequest {
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[\\w.%+-]+@[\\w.-]+\\.[a-zA-Z]{2,}$");
    private static final Pattern TAG_PART_PATTERN = Pattern.compile("\\+.*$");
    private static final String FULL_NAME_FORMAT = "%s %s";
    private static final int MAX_EMAIL_LENGTH = 254;

    @JsonProperty("firstName")
    private String firstName;

    @JsonProperty("lastName")
    private String lastName;

    @JsonProperty("company")
    private String company;

    @JsonProperty("email")
    private String email;

    @JsonProperty("country")
    private String country;

    @JsonProperty("purpose")
    private TenantPurpose purpose;

    @JsonProperty("offerType")
    private String offerType;

    @JsonProperty("envId")
    private String envId;

    @JsonProperty("emailVerified")
    private Boolean emailVerified;

    @JsonProperty("termsAccepted")
    private Boolean termsAccepted;

    @JsonProperty("termsFilePath")
    private String termsFilePath;

    @JsonProperty("offerFunction")
    private OfferFunction offerFunction;

    @JsonProperty("reltioPackageType")
    private ReltioPackageType reltioPackageType;

    @JsonCreator
    public TenantRequest(@JsonProperty(value = "firstName") String firstName,
                         @JsonProperty(value = "lastName") String lastName,
                         @JsonProperty("company") String company,
                         @JsonProperty(value = "email", required = true) String email,
                         @JsonProperty("country") String country,
                         @JsonProperty("purpose") TenantPurpose purpose,
                         @JsonProperty("offerType") String offerType,
                         @JsonProperty("envId") String envId,
                         @JsonProperty("emailVerified") Boolean emailVerified,
                         @JsonProperty("termsAccepted") Boolean termsAccepted,
                         @JsonProperty("termsFilePath") String termsFilePath,
                         @JsonProperty("offerFunction") OfferFunction offerFunction,
                         @JsonProperty("reltioPackageType") ReltioPackageType reltioPackageType) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.company = company;
        this.email = normalizeEmail(email);
        this.country = country;
        this.purpose = purpose == null ? TenantPurpose.FREE : purpose;
        this.offerType = offerType;
        this.envId = envId;
        this.emailVerified = emailVerified;
        this.termsAccepted = termsAccepted;
        this.offerFunction = offerFunction;
        this.termsFilePath = termsFilePath;
        this.reltioPackageType= reltioPackageType == null ? ReltioPackageType.MDM :reltioPackageType ;
    }

    public void setEmail(String email) {
        this.email = normalizeEmail(email);
    }

    private String normalizeEmail(String email) {
        if (email == null || email.length() > MAX_EMAIL_LENGTH) {
            throw new IllegalArgumentException("Email provided is invalid");
        }

        if (!EMAIL_PATTERN.matcher(email).matches()) {
            throw new IllegalArgumentException("Email provided is invalid");
        }

        String[] emailParts = email.split("@");
        String userPart = TAG_PART_PATTERN.matcher(emailParts[0]).replaceAll("");
        String domainPart = emailParts[1].toLowerCase();
        return String.format("%s@%s", userPart, domainPart);
    }

    public String getFullName() {
        String firstName = nullToEmpty(getFirstName());
        String lastName = nullToEmpty(getLastName());
        return String.format(FULL_NAME_FORMAT, firstName, lastName).trim();
    }

    private String nullToEmpty(String string) {
        return string != null ? string : "";
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TenantRequest that = (TenantRequest) o;
        return firstName.equals(that.firstName) && lastName.equals(that.lastName) && Objects.equals(company, that.company) && email.equals(that.email) && Objects.equals(country, that.country) && purpose == that.purpose && offerType.equals(that.offerType) && Objects.equals(envId, that.envId) && Objects.equals(emailVerified, that.emailVerified) && Objects.equals(termsAccepted, that.termsAccepted) && Objects.equals(termsFilePath, that.termsFilePath) && offerFunction == that.offerFunction && reltioPackageType == that.reltioPackageType ;

    }

    @Override
    public int hashCode() {
        return Objects.hash(firstName, lastName, company, email, country, purpose, offerType, envId, emailVerified, termsAccepted, termsFilePath, offerFunction ,reltioPackageType);
    }
}
