package com.reltio.services.pms.common.sales.model.addon;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@Getter
@Setter
public class CleanseProductConfig extends BaseProductConfig {

    @JsonProperty("regions")
    private Set<String> regions;

    @JsonProperty("processTypes")
    private Set<CleanseProcessType> processTypes;

    @JsonProperty("allowAllRegions")
    private boolean allowAllRegions;

    public CleanseProductConfig() {
        this.pmsProductName = PMSProductName.CLEANSE;
    }
}
