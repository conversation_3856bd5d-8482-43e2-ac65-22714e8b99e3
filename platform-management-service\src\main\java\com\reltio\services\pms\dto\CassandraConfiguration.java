package com.reltio.services.pms.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CassandraConfiguration {

    private String clusterName;
    private String keyspaceName;
    private String hosts;
    private Integer replicationFactor;
    private String ttl;

    @JsonCreator
    public CassandraConfiguration(@JsonProperty(value = "clusterName", required = true)  String clusterName,
                                  @JsonProperty(value = "keyspaceName", required = true)  String keyspaceName,
                                  @JsonProperty(value = "hosts", required = true) String hosts,
                                  @JsonProperty(value = "replicationFactor", required = true) Integer replicationFactor,
                                  @JsonProperty(value = "ttl", required = true) String ttl) {
        this.clusterName = clusterName;
        this.keyspaceName = keyspaceName;
        this.hosts = hosts;
        this.replicationFactor = replicationFactor;
        this.ttl = ttl;
    }
}
