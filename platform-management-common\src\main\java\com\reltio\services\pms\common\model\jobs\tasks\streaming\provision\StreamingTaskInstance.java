package com.reltio.services.pms.common.model.jobs.tasks.streaming.provision;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.config.DestinationConfig;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServiceEnablementBaseTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Getter
public class StreamingTaskInstance extends ServiceEnablementBaseTaskInstance {

    @JsonProperty(value = "configureDefaultQueue")
    private final boolean configureDefaultQueue;

    @JsonProperty(value = "gcpProjectName")
    private final String gcpProjectName;

    @JsonProperty(value = "createQueueIfExisting")
    private final boolean createQueueIfExisting;

    @JsonProperty(value = "streamingCloud")
    private final String streamingCloud;

    @JsonProperty(value = "destinationConfig")
    private final DestinationConfig destinationConfig;

    @JsonProperty(value = "enabledByDefault")
    private final boolean enabledByDefault;

    @Setter
    @JsonProperty(value = "queueNames")
    private Set<String> queueNames;

    @JsonCreator
    public StreamingTaskInstance(@JsonProperty(value = "taskId") String taskId,
                                 @JsonProperty(value = "name") String name,
                                 @JsonProperty(value = "jobId") String jobId,
                                 @JsonProperty(value = "startTime") Long startTime,
                                 @JsonProperty(value = "finishTime") Long finishTime,
                                 @JsonProperty(value = "status") TaskStatus status,
                                 @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                 @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                 @JsonProperty(value = "executingNodeName") String executingNodeName,
                                 @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                 @JsonProperty(value = "envId") String envId,
                                 @JsonProperty(value = "configureDefaultQueue") boolean configureDefaultQueue,
                                 @JsonProperty(value = "gcpProjectName") String gcpProjectName,
                                 @JsonProperty(value = "createQueueIfExisting") boolean createQueueIfExisting,
                                 @JsonProperty(value = "failedTenants") Set<String> failedTenants,
                                 @JsonProperty(value = "totalProcessedTenants", defaultValue = "0") int totalProcessedTenants,
                                 @JsonProperty(value = "tenantsForServiceEnablement") Set<String> tenantsForServiceEnablement,
                                 @JsonProperty(value="events") Map<String,Set<String>> events,
                                 @JsonProperty(value = "streamingCloud") String streamingCloud,
                                 @JsonProperty(value = "destinationConfig") DestinationConfig destinationConfig,
                                 @JsonProperty(value = "enabledByDefault") boolean enabledByDefault,
                                 @JsonProperty(value = "queueNames") Set<String> queueNames) {
        super(taskId, name, jobId, startTime, finishTime, TaskType.STREAMING_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId, failedTenants, totalProcessedTenants,
                tenantsForServiceEnablement,events);
        this.configureDefaultQueue = configureDefaultQueue;
        this.gcpProjectName = gcpProjectName;
        this.createQueueIfExisting = createQueueIfExisting;
        this.streamingCloud = streamingCloud;
        this.destinationConfig = destinationConfig;
        this.enabledByDefault = enabledByDefault;
        this.queueNames =  queueNames !=null ? queueNames : new HashSet<>();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }

        StreamingTaskInstance that = (StreamingTaskInstance) o;
        return isConfigureDefaultQueue() == that.isConfigureDefaultQueue() &&
                isCreateQueueIfExisting() == that.isCreateQueueIfExisting() &&
                isEnabledByDefault() == that.isEnabledByDefault() &&
                Objects.equals(getGcpProjectName(), that.getGcpProjectName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), isConfigureDefaultQueue(), getGcpProjectName(), isCreateQueueIfExisting(),getDestinationConfig(),isEnabledByDefault());
    }

    @Override
    public String toString() {
        return "StreamingTaskInstance{" +
                ", configureDefaultQueue=" + configureDefaultQueue +
                ", gcpProjectName='" + gcpProjectName + '\'' +
                ", createQueueIfExisting=" + createQueueIfExisting +
                ",destinationConfig=" + destinationConfig +
                "} " + super.toString();
    }
}
