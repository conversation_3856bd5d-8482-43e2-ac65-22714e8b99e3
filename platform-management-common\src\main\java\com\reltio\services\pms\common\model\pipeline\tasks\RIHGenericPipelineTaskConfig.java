package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.RihProducts;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

import static com.reltio.services.pms.common.model.pipeline.tasks.TaskType.*;

@Getter
public class RIHGenericPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonProperty
    private final String manifestId;

    @JsonProperty
    private final RihProducts rihProduct;

    @JsonProperty
    private final Map<String, String> properties;

    @JsonCreator
    public RIHGenericPipelineTaskConfig(@JsonProperty(value = "name") String name,
                                       @JsonProperty(value = "manifestId") String manifestId,
                                       @JsonProperty(value = "rihProduct", required = true) String rihProduct,
                                        @JsonProperty(value = "properties") Map<String, String> properties) {
        super(name, RIH_GENERIC_PROVISION_TASK);
        this.manifestId = manifestId;
        this.rihProduct = RihProducts.convertFromString(rihProduct);
        this.properties = properties== null ? new HashMap<>() : properties;
    }

    @Override
    public PMSProductName getProductName() {
        return switch (rihProduct) {
            case ZOOM_INFO -> PMSProductName.ZOOM_INFO;
            case PURVIEW -> PMSProductName.PURVIEW;
            case SFDC_RIH -> PMSProductName.SFDC_RIH;
            case COLLIBRA -> PMSProductName.COLLIBRA;
            case DNB_DATABLOCKS -> PMSProductName.DNB_DATABLOCKS;
            case DATABRICKS -> PMSProductName.DATABRICKS;
            case MEDPRO -> PMSProductName.MEDPRO;
            case BVD -> PMSProductName.BVD_CONNECTOR;
            case ALATION -> PMSProductName.ALATION;
        };
    }
    @Override
    public boolean visibleInContract() {
        return true;
    }

    @Override
    public String toString() {
        return RIH_GENERIC_PROVISION_TASK_NAME + "{}";
    }


}
