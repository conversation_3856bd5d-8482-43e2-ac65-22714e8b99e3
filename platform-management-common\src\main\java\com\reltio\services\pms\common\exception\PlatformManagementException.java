package com.reltio.services.pms.common.exception;

import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;

import java.io.InputStream;
import java.text.MessageFormat;
import java.util.MissingResourceException;
import java.util.PropertyResourceBundle;
import java.util.ResourceBundle;

/**
 * Created by ${user.name} on 2017/12/14 08:56:40.
 * Exception for the tenant-management-service Service
 */
public class PlatformManagementException extends RuntimeException {

    private static final Logger LOGGER = LogManager.getLogger(PlatformManagementException.class);
    private static final String MESSAGES_FILE = "errors.properties";
    private static final ResourceBundle BUNDLE = loadResources();

    private static final String MESSAGE_KEY_PREFIX = "error.";
    private static final String DETAILED_MESSAGE_KEY_SUFFIX = ".details";

    private final PlatformManagementErrorCode code;
    private final int httpStatus;
    private final String[] messageParameters;
    private final String[] detailMessageParameters;

    public PlatformManagementException(PlatformManagementErrorCode code, int httpStatus) {
        this(code, httpStatus, null, null, null);
    }


    public PlatformManagementException(PlatformManagementErrorCode code, int httpStatus, Throwable cause) {
        this(code, httpStatus, null, null, cause);
    }

    public PlatformManagementException(PlatformManagementErrorCode code, int httpStatus, String[] messageParameters, String[] detailMessageParameters, Throwable cause) {
        super(cause);
        this.code = code;
        this.httpStatus = httpStatus;
        this.messageParameters = messageParameters;
        this.detailMessageParameters = detailMessageParameters;
    }

    public PlatformManagementException(PlatformManagementErrorCode code, int httpStatus, String... parameters) {
        this(code, httpStatus, parameters, parameters, null);
    }

    public PlatformManagementException(PlatformManagementErrorCode code, int httpStatus, Throwable exception, String... parameters) {
        this(code, httpStatus, parameters, parameters, exception);
    }

    private static String getResource(String key) {
        if (BUNDLE == null) {
            return key;
        }
        try {
            return BUNDLE.getString(key);
        } catch (MissingResourceException ex) {
            LOGGER.error("Unable to get message description", ex);
        }
        return key;
    }

    private static String formatString(String pattern, String[] args) {
        if (args == null || args.length == 0) {
            return pattern;
        }
        return MessageFormat.format(pattern, args);
    }

    private static final ResourceBundle loadResources() {
        try (InputStream is = PlatformManagementException.class.getClassLoader()
                .getResourceAsStream(MESSAGES_FILE)) {
            return new PropertyResourceBundle(is);
        } catch (Exception e) {
            LOGGER.error("An unexpected error occurred while loading error messages for the tenant-management-serviceServiceClient: " + e.getMessage(), e);
        }
        return null;
    }

    @Override
    public String getMessage() {
        StringBuilder keyBuilder = new StringBuilder(MESSAGE_KEY_PREFIX).append(code.name());
        String messageKey = keyBuilder.toString();
        String messagePattern = getResource(messageKey);

        return formatString(messagePattern, messageParameters);
    }

    public String getDetailMessage() {
        StringBuilder keyBuilder = new StringBuilder(MESSAGE_KEY_PREFIX).append(code.name())
                .append(DETAILED_MESSAGE_KEY_SUFFIX);
        String detailedMessageKey = keyBuilder.toString();
        String detailedMessagePattern = getResource(detailedMessageKey);

        return formatString(detailedMessagePattern, detailMessageParameters);
    }

    public int getHttpStatus() {
        return httpStatus;
    }

    public PlatformManagementErrorCode getCode() {
        return code;
    }

}
