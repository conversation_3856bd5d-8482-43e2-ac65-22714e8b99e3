package com.reltio.services.pms.convertors.sales;

import com.reltio.devops.common.environment.CloudProvider;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import com.reltio.services.pms.common.sales.model.ReltioContract;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.dao.ReltioTenantDao;
import com.reltio.services.pms.service.SalesPackageService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

@Service
public class SalesTenantProviderService {
    private static final Logger LOG = Logger.getLogger(SalesTenantProviderService.class);

    private final Collection<TenantAddOnProductProvider<? extends BaseProductConfig>> tenantAddOnProductProviders;
    private final ReltioTenantDao reltioTenantDao;
    private final SalesPackageService salesPackageService;


    @Autowired
    public SalesTenantProviderService(Collection<TenantAddOnProductProvider<? extends BaseProductConfig>> tenantAddOnProductProviders,
                                      ReltioTenantDao reltioTenantDao, SalesPackageService salesPackageService) {
        this.tenantAddOnProductProviders = tenantAddOnProductProviders;
        this.reltioTenantDao = reltioTenantDao;
        this.salesPackageService = salesPackageService;
    }

    public List<ReltioTenant> getSalesTenants(ReltioContract contract, Map<String, Set<SalesConfig>> packageSubscriptions, String packageId) {

        Map<String, Map<PMSProductName, BaseProductConfig>> addonConfigByTenantProduct = new HashMap<>();
        List<ReltioTenant> reltioTenants = new ArrayList<>();

        for (TenantAddOnProductProvider<? extends BaseProductConfig> productProvider : tenantAddOnProductProviders) {
            copyProductConfig(productProvider.getProductConfigByTenantCode(packageSubscriptions), addonConfigByTenantProduct, productProvider.getProductName());
        }

        for (Map.Entry<String, Map<PMSProductName, BaseProductConfig>> addonEntry : addonConfigByTenantProduct.entrySet()) {
            Set<SalesConfig> tenantConfigs = packageSubscriptions.get(addonEntry.getKey());
            reltioTenants.addAll(getTenant(contract, addonEntry.getKey(), tenantConfigs, addonEntry.getValue(), packageId));
        }

        return reltioTenants;
    }

    private List<ReltioTenant> getTenant(ReltioContract contract, String tenantProductCode, Set<SalesConfig> salesConfigs, Map<PMSProductName, BaseProductConfig> additionalProducts, String packageId) {
        Map<String, TenantPurpose> tenantPurposeMap = salesPackageService.getProductCodesByTenantPurpose();
        List<ReltioTenant> reltioTenants = new ArrayList<>();

        for (SalesConfig salesConfig : salesConfigs) {
            ReltioTenant reltioTenant = new ReltioTenant();
            //add company name
            reltioTenant.setSalesConfig(salesConfig);
            String tenantId = salesConfig.getSalesTenantId();
            reltioTenant.setTenantId(tenantId);
            reltioTenant.setContractId(contract.getContractId());
            reltioTenant.setPackageId(packageId);
            reltioTenant.getAdditionalProducts().putAll(additionalProducts);
            reltioTenant.setSubscriptionId(salesConfig.getSubscriptionId());
            reltioTenant.setAccountId(contract.getAccountId());
            reltioTenant.setReltioPackageType(contract.getReltioPackageType());
            reltioTenant.setHipaa(salesConfig.isHipaaFinance());
            reltioTenant.setIndustry(contract.getIndustry());
            if (salesConfig.getMdmCloudProvider() != null) {
                reltioTenant.setDeploymentCloud(CloudProvider.valueOf(salesConfig.getMdmCloudProvider()
                        .toUpperCase(Locale.ROOT)).name());
            }
            reltioTenant.setDeploymentRegion(salesConfig.getDeploymentRegion());
            reltioTenant.setProductEdition(salesConfig.getVelocityPack());
            reltioTenant.setTenantPurpose(tenantPurposeMap.get(tenantProductCode));
            String env = null;
            if (tenantId == null) {
                LOG.warn("tenant is null for contract " + contract.getContractId());
            } else {
                try {
                    ReltioTenant tenant = reltioTenantDao.get(tenantId);
                    env = tenant.getReltioEnv();
                } catch (InvalidDocumentIdException e) {
                    LOG.warn("tenant not found for contract " + contract.getContractId() + " " + e.getMessage());
                }
            }
            reltioTenant.setReltioEnv(env);
            reltioTenants.add(reltioTenant);
        }

        return reltioTenants;
    }


    private void copyProductConfig(Map<String, ? extends BaseProductConfig> additionalProductsByTenant, Map<String, Map<PMSProductName, BaseProductConfig>> addonConfigByTenantProduct,
                                   PMSProductName productName) {

        if (additionalProductsByTenant != null) {

            for (EnumMap.Entry<String, ? extends BaseProductConfig> addonProductEntry : additionalProductsByTenant.entrySet()) {
                Map<PMSProductName, BaseProductConfig> productConfigByName = addonConfigByTenantProduct.get(addonProductEntry.getKey());
                if (productConfigByName == null) {
                    productConfigByName = new EnumMap<>(PMSProductName.class);
                }
                productConfigByName.put(productName, addonProductEntry.getValue());
                addonConfigByTenantProduct.put(addonProductEntry.getKey(), productConfigByName);
            }
        }
    }
}
