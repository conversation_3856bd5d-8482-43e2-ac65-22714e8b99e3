package com.reltio.services.pms.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.reltio.services.pms.enums.ResolveConflictStrategyEnum;
import com.reltio.services.pms.exception.ConflictsFoundException;
import com.reltio.services.pms.service.TenantUpdateService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/api/v1/dataDomains")
@Tag(name= "Data Domain")
public class DataDomainController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataDomainController.class);
    private static final String CONFLICTS = "conflicts";
    private static final String PARTIAL_MERGED_CONFIG = "partialMergedConfig";
    private static final String ERROR = "errors";
    private final TenantUpdateService tenantUpdateService;
    @Autowired
    public DataDomainController(TenantUpdateService tenantUpdateService) {
        this.tenantUpdateService = tenantUpdateService;
    }
    @PostMapping("/{tenantId}/updateTenant")
    public ResponseEntity<JsonNode> updateTenant(
            @PathVariable String tenantId,
            @RequestParam String env,
            @RequestParam(defaultValue = "false") boolean updateConfig,
            @RequestParam(defaultValue = "WARN") String conflictStrategy,
            @RequestParam(defaultValue = "true") boolean forceRefresh,
            @RequestParam String path) {
        try {
            ResolveConflictStrategyEnum resolveConflictStrategy = ResolveConflictStrategyEnum.fromValue(conflictStrategy);
            LOGGER.info("Resolve conflicts set to: {}", resolveConflictStrategy);
            JsonNode result = tenantUpdateService.updateTenantConfiguration(
                    tenantId, path, env, updateConfig, forceRefresh, resolveConflictStrategy);
            return ResponseEntity.ok(result);
        } catch (ConflictsFoundException e) {
            LOGGER.warn("Conflict found updating tenant: {} in env: {}", tenantId, env, e);
            ObjectNode response = JsonNodeFactory.instance.objectNode();
            response.set(CONFLICTS, e.getConflicts());
            response.set(PARTIAL_MERGED_CONFIG, e.getPartialMergedConfig());
            return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
        } catch (Exception e) {
            LOGGER.error("Error updating tenant: {} in env: {}", tenantId, env, e);
            ObjectNode errorResponse = JsonNodeFactory.instance.objectNode();
            errorResponse.put(ERROR, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
}