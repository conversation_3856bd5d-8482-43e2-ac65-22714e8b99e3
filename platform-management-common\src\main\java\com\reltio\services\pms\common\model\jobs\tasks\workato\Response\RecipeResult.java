package com.reltio.services.pms.common.model.jobs.tasks.workato.Response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

public class RecipeResult {
    @JsonProperty("customer")
    Map<String, String> customer;

    @JsonProperty("activationResult")
    ActivationResult activationResult;

    @JsonProperty("manifest")
    String manifest;

    public RecipeResult(@JsonProperty("customer") Map<String, String> customer,
                        @JsonProperty("activationResult") ActivationResult activationResult,
                        @JsonProperty("manifest") String manifest) {
        this.customer = customer;
        this.activationResult = activationResult;
        this.manifest = manifest;
    }

    public Map<String, String> getCustomer() {
        return customer;
    }

    public ActivationResult getActivationResult() {
        return activationResult;
    }

    public String getManifest() {
        return manifest;
    }
}
