package com.reltio.services.pms.common.model.jobs;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

import java.util.List;
import java.util.Map;

@Getter
public class BulkTenantCreationJob {
    @JsonProperty(value="parentJobId")
    private final String parentJobId;

    @JsonProperty(value = "envId")
    private String envId;

    @JsonProperty(value = "pipelineId")
    private String pipelineId;

    @JsonProperty(value = "productEdition")
    private String productEdition;

    @JsonProperty(value = "jobs")
    private final List<Job> jobs;

    @JsonProperty(value = "createdBy")
    private final String createdBy;

    @JsonProperty(value = "createdTime")
    private final Long createdTime;

    public BulkTenantCreationJob(@JsonProperty(value = "parentJobId")String parentJobId,
                                 @JsonProperty(value = "envId")String envId,
                                 @JsonProperty(value = "pipelineId")String pipelineId,
                                 @JsonProperty(value = "productEdition")String productEdition,
                                 @JsonProperty(value = "jobs")List<Job> jobs,
                                 @JsonProperty(value = "createdBy")String createdBy,
                                 @JsonProperty(value = "createdTime")Long createdTime){
        this.parentJobId=parentJobId;
        this.envId=envId;
        this.pipelineId=pipelineId;
        this.productEdition=productEdition;
        this.jobs=jobs;
        this.createdBy=createdBy;
        this.createdTime=createdTime;
    }
}
