package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import lombok.Getter;

import java.util.Collections;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Getter
public class EnterpriseTenantCreationRequest extends TenantCreationRequest {

    @JsonProperty("customerName")
    private final String customerName;

    @JsonProperty("customerId")
    private final String customerId;

    @JsonProperty("industry")
    private final String industry;

    @JsonProperty("cleanseRegions")
    private final List<String> cleanseRegions;

    @JsonProperty("owners")
    private  List<String> owners;

    @JsonProperty("configureDefaultQueue")
    private final boolean configureDefaultQueue;

    @JsonProperty("rdmTenantToReuse")
    private final String rdmTenantToReuse;

    @JsonProperty("tenantPurpose")
    private final TenantPurpose tenantPurpose;

    @JsonProperty("clientNames")
    private final Set<String> clientNames;

    @JsonProperty("productEdition")
    private final String productEdition;

    @JsonProperty("enableRiq")
    private final boolean enableRiq;

    @JsonProperty("streamingCloud")
    private final String streamingCloud;

    @JsonProperty("loqateProcesses")
    private final String loqateProcesses;

    @JsonProperty(value = "dataStorageArn")
    private final String dataStorageArn;

    @JsonProperty(value = "matchStorageArn")
    private final String matchStorageArn;

    @JsonProperty(value = "contractId")
    private final String contractId;

    @JsonProperty("rdmTenantId")
    private final String rdmTenantId;

    @JsonProperty(value = "subscriptionId")
    private final String subscriptionId;

    @JsonProperty(value = "shortDescription")
    private final String shortDescription;

    @JsonProperty(value = "tenantRecordType")
    private final String tenantRecordType;

    @JsonProperty(value = "ownedByReltioDept")
    private final String ownedByReltioDept;

    @JsonProperty(value = "rih")
    private final Map<String, String> rihKey;

    @JsonProperty(value = "rdmHostNameUrl")
    private final String rdmHostNameUrl;

    @JsonProperty(value = "queueNames")
    private final Set<String> queueNames;

    @JsonProperty("customerType")
    private CustomerType customerType;

    @JsonProperty("division")
    private String division;

    @JsonProperty("department")
    private String department;

    @JsonProperty("costCenter")
    private String costCenter;

    @JsonProperty("endDate")
    private String endDate;

    @JsonProperty("checkDuplicates")
    private Boolean checkDuplicates;

    @JsonProperty(value = "opportunityId")
    private final String opportunityId;

    @JsonProperty(value = "vaultKey")
    private String vaultKey;

    @JsonProperty(value = "keyRequired")
    private boolean keyRequired;

    @JsonProperty(value = "useServiceBusFunction")
    private boolean useServiceBusFunction;

    @JsonProperty(value = "BCETenant")
    private boolean bceTenant;

    @JsonProperty(value = "awsAccountId")
    private String awsAccountId;

    @JsonProperty(value = "gitBranchName")
    private String gitBranchName;
    @JsonProperty(value = "predicateRegions")
    private Set<String> predicateRegions;

    @JsonCreator
    public EnterpriseTenantCreationRequest(@JsonProperty(value = "pipelineId", required = true) String pipelineId,
                                           @JsonProperty(value = "customerName", required = true) String customerName,
                                           @JsonProperty(value = "customerId") String customerId,
                                           @JsonProperty(value = "tenantSize", required = true) TenantSize tenantSize,
                                           @JsonProperty(value = "industry") String industry,
                                           @JsonProperty(value = "cleanseRegions") List<String> cleanseRegions,
                                           @JsonProperty(value = "owners") List<String> owners,
                                           @JsonProperty(value = "configureDefaultQueue") Boolean configureDefaultQueue,
                                           @JsonProperty(value = "rdmTenantToReuse") String rdmTenantToReuse,
                                           @JsonProperty(value = "tenantPurpose", required = true) TenantPurpose tenantPurpose,
                                           @JsonProperty(value = "clientNames") Set<String> clientNames,
                                           @JsonProperty(value = "productEdition", required = true) String productEdition,
                                           @JsonProperty(value = "enableRiq") Boolean enableRiq,
                                           @JsonProperty(value = "streamingCloud") String streamingCloud,
                                           @JsonProperty(value = "loqateProcesses") String loqateProcesses,
                                           @JsonProperty(value = "mdmTenantId") String mdmTenantId,
                                           @JsonProperty(value = "dataStorageArn") String dataStorageArn,
                                           @JsonProperty(value = "matchStorageArn") String matchStorageArn,
                                           @JsonProperty(value = "contractId") String contractId,
                                           @JsonProperty(value = "subscriptionId") String subscriptionId,
                                           @JsonProperty(value = "shortDescription") String shortDescription,
                                           @JsonProperty(value = "tenantRecordType", defaultValue = "Customer Tenant") String tenantRecordType,
                                           @JsonProperty(value = "ownedByReltioDept") String ownedByReltioDept,
                                           @JsonProperty(value = "rdmTenantId") String rdmTenantId,
                                           @JsonProperty(value = "rih") Map<String, String> rihKey,
                                           @JsonProperty(value = "rdmHostNameUrl") String rdmHostNameUrl,
                                           @JsonProperty(value = "isQaAutomation") Boolean isQaAutomation,
                                           @JsonProperty(value = "newInstanceId") String newInstanceId,
                                           @JsonProperty(value = "queueNames") Set<String> queueNames,
                                           @JsonProperty(value = "customerType") CustomerType customerType,
                                           @JsonProperty(value = "division") String division,
                                           @JsonProperty(value = "department") String department,
                                           @JsonProperty(value = "costCenter") String costCenter,
                                           @JsonProperty(value = "endDate") String endDate,
                                           @JsonProperty(value = "reltioPackageType") ReltioPackageType reltioPackageType,
                                           @JsonProperty(value = "skipTasks") EnumSet<PMSProductName> skipTasks,
                                           @JsonProperty("useSpannerCloudFunction") Boolean useSpannerCloudFunction,
                                           @JsonProperty(value = "checkDuplicates") Boolean checkDuplicates,
                                           @JsonProperty(value = "opportunityIdId") String opportunityIdId) {
        super(pipelineId, tenantSize, mdmTenantId, isQaAutomation, newInstanceId, useSpannerCloudFunction,
                reltioPackageType, skipTasks);
        this.customerName = customerName;
        this.customerId = customerId;
        this.industry = industry;
        this.cleanseRegions = cleanseRegions == null ? Collections.singletonList("LOQ-NA-NT") : cleanseRegions;
        this.owners = owners == null ? Collections.emptyList() : owners;
        this.configureDefaultQueue = configureDefaultQueue == null ? Boolean.TRUE : configureDefaultQueue;
        this.rdmTenantToReuse = rdmTenantToReuse;
        this.tenantPurpose = tenantPurpose;
        this.clientNames = clientNames;
        this.productEdition = productEdition;
        this.enableRiq = enableRiq == null ? Boolean.FALSE : enableRiq;
        this.streamingCloud = streamingCloud;
        this.loqateProcesses = loqateProcesses;
        this.dataStorageArn = dataStorageArn;
        this.matchStorageArn = matchStorageArn;
        this.contractId = contractId;
        this.rdmTenantId = rdmTenantId;
        this.subscriptionId = subscriptionId;
        this.shortDescription = shortDescription;
        this.tenantRecordType = tenantRecordType;
        this.ownedByReltioDept = ownedByReltioDept;
        this.rihKey = rihKey == null ? Collections.emptyMap() : rihKey;
        this.rdmHostNameUrl = rdmHostNameUrl;
        this.queueNames = queueNames;
        this.customerType = customerType;
        this.division = division;
        this.department = department;
        this.costCenter = costCenter;
        this.endDate = endDate;
        this.checkDuplicates = checkDuplicates != null && checkDuplicates;
        this.opportunityId = opportunityIdId;
    }

    public void setOwners(List<String> owners){
        this.owners=owners;
    }

}