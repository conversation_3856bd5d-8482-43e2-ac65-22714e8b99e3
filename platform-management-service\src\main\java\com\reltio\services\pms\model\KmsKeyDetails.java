package com.reltio.services.pms.model;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KmsKeyDetails extends BaseFirestoreEntity {
    @JsonProperty("tenantId")
    private String tenantId;
    @JsonProperty("projectId")
    private String projectId;
    @JsonProperty("locationId")
    private String locationId;
    @JsonProperty("keyRingId")
    private String keyRingId;
    @JsonProperty("keyId")
    private String keyId;

    @JsonProperty("gcsBucketKey")
    private GcsBucketKey gcsBucketKey;

    @Override
    public String getID() {
        return this.tenantId;
    }
}