package com.reltio.services.pms.convertors.sales;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import com.reltio.services.pms.common.sales.model.SalesConfig;

import java.util.Map;
import java.util.Set;

public interface TenantAddOnProductProvider<P extends BaseProductConfig> {


    PMSProductName getProductName();

    Map<String, Set<String>> getProductCodesByTenant();

    Map<String,P> getProductConfigByTenantCode(Map<String,Set<SalesConfig>> salesConfigs);

    Set<String> getTenantProductCodes(Map<String,Set<SalesConfig>> salesConfigs);

    P getProductConfig(Set<SalesConfig> salesConfigs,  Set<String> tenantCodes, String currentTenantCode);
}
