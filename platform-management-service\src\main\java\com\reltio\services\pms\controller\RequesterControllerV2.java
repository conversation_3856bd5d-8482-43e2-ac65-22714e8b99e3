package com.reltio.services.pms.controller;

import com.reltio.services.pms.clients.external.captcha.CaptchaService;
import com.reltio.services.pms.common.model.Requester;
import com.reltio.services.pms.common.model.TenantRequest;
import com.reltio.services.pms.common.model.tenant.Subscription;
import com.reltio.services.pms.service.RequesterService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value={"/api/v2/pms/requesters", "/api/v2/requesters"}, produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name= "Requesters V2")
public class RequesterControllerV2 {

    private static final Logger LOG = Logger.getLogger(RequesterControllerV2.class);

    private final RequesterService requesterService;
    private final CaptchaService captchaService;

    @Autowired
    public RequesterControllerV2(RequesterService requesterService, CaptchaService captchaService) {
        this.requesterService = requesterService;
        this.captchaService = captchaService;
    }

    @PostMapping(value = "", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public Requester createOrUpdateRequester(TenantRequest tenantRequest) {
        captchaService.validateRequest();
        Requester requesterWithOfferTypes = new Requester(tenantRequest);
        Subscription offer = new Subscription(tenantRequest);
        return requesterService.handleNewTenantRequest(requesterWithOfferTypes, offer);
    }

}
