package com.reltio.services.pms.common.model.jobs.tasks.azureBlobStorage;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServiceEnablementBaseTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

import java.util.Map;
import java.util.Set;

public class AzureBlobStorageTaskInstance extends ServiceEnablementBaseTaskInstance {

    @JsonCreator
    public AzureBlobStorageTaskInstance(@JsonProperty(value = "taskId") String taskId,
                            @JsonProperty(value = "name") String name,
                            @JsonProperty(value = "jobId") String jobId,
                            @JsonProperty(value = "startTime") Long startTime,
                            @JsonProperty(value = "finishTime") Long finishTime,
                            @JsonProperty(value = "status") TaskStatus status,
                            @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                            @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                            @JsonProperty(value = "executingNodeName") String executingNodeName,
                            @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                            @JsonProperty(value = "failedTenants") Set<String> failedTenants,
                            @JsonProperty(value = "totalProcessedTenants", defaultValue = "0") int totalProcessedTenants,
                            @JsonProperty(value = "tenantsForServiceEnablement") Set<String> tenantsForServiceEnablement,
                            @JsonProperty(value = "events") Map<String, Set<String>> events,
                            @JsonProperty(value = "envId") String envId)
    {
        super(taskId, name, jobId, startTime, finishTime, TaskType.AZURE_BLOB_STORAGE_PROVISION_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId, failedTenants, totalProcessedTenants,
                tenantsForServiceEnablement, events);
    }
}
