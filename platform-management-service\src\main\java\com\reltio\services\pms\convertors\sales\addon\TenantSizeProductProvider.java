package com.reltio.services.pms.convertors.sales.addon;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.common.sales.model.addon.TenantSizeConfig;
import com.reltio.services.pms.convertors.UtilDeserializer;
import com.reltio.services.pms.convertors.sales.AbstractTenantAddOnProductProvider;
import com.reltio.services.pms.service.SalesPackageService;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

@Service
public class TenantSizeProductProvider extends AbstractTenantAddOnProductProvider<TenantSizeConfig> {

    public TenantSizeProductProvider(SalesPackageService salesPackageService) {
        super(salesPackageService);
    }


    @Override
    public PMSProductName getProductName() {
        return PMSProductName.TENANT_SIZE;
    }

    @Override
    public Map<String, Set<String>> getProductCodesByTenant() {
        return salesPackageService.getSalesAddOnsByProductCodes(PMSProductName.TENANT_SIZE);

    }

    @Override
    public TenantSizeConfig getProductConfig(Set<SalesConfig> salesConfigs, Set<String> tenantCodes, String currentTenantCode) {
        TenantSizeConfig tenantSizeConfig = new TenantSizeConfig();
        tenantSizeConfig.setPmsProductName(getProductName());
        tenantSizeConfig.addAllSalesConfigs(salesConfigs);

        Integer totalCP = getQuantity(tenantSizeConfig);
        // get sales product code for DEV tenant purpose
        String productTenantCode = salesPackageService.getProductCodeByTenantPurpose(TenantPurpose.DEV);
        if (totalCP == 0 && productTenantCode.equals(currentTenantCode)) {
            totalCP = 200000;
        }

        tenantSizeConfig.setQuantity(totalCP);
        tenantSizeConfig.setTenantSize(UtilDeserializer.getTenantSize(totalCP));
        return tenantSizeConfig;
    }

}
