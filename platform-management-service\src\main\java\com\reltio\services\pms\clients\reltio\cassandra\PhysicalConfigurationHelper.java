package com.reltio.services.pms.clients.reltio.cassandra;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.reltio.common.config.CassandraConfigMapping;
import com.reltio.common.config.CassandraConfigWithDSMapping;
import com.reltio.common.config.DataStorageMapping;
import com.reltio.common.config.DataStorageType;
import com.reltio.common.config.TenantConfiguration;
import com.reltio.common.config.TenantDatabaseConfiguration;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * PhysicalConfigurationHelper
 */
public class PhysicalConfigurationHelper {

    private PhysicalConfigurationHelper() {
    }

    public static Map<String, Set<String>> getKeyspaces(TenantConfiguration tenantConfiguration) {
        Map<String, Set<String>> clusterNameKeyspacesMap = new HashMap<>();

        putClusterKeyspaceToMap(tenantConfiguration.getDataStorageConfig().getDataKeyspaceConfig(), clusterNameKeyspacesMap);
        putClusterKeyspaceToMap(tenantConfiguration.getDataStorageConfig().getMatchKeyspaceConfig(), clusterNameKeyspacesMap);

        if (tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig() != null && tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig().getBigTableProjectId() == null) {
            putClusterKeyspaceToMap(tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig(), clusterNameKeyspacesMap);
        }
        putClusterKeyspaceToMap(tenantConfiguration.getDataStorageConfig().getInteractionKeyspaceConfig(), clusterNameKeyspacesMap);
        putClusterKeyspaceToMap(tenantConfiguration.getDataStorageConfig().getActivityLogV2KeyspaceConfig(), clusterNameKeyspacesMap);
        putClusterKeyspaceToMap(tenantConfiguration.getDataStorageConfig().getActivityLogKeyspaceConfig(), clusterNameKeyspacesMap);
        putClusterKeyspaceToMap(tenantConfiguration.getDataStorageConfig().getPotentialMatchesKeyspaceConfig(), clusterNameKeyspacesMap);

        return clusterNameKeyspacesMap;
    }

    private static Map<String, Set<String>> putClusterKeyspaceToMap(CassandraConfigMapping cassandraConfigMapping, Map<String, Set<String>> map) {
        if (cassandraConfigMapping != null) {
            String clusterName = cassandraConfigMapping.getClusterName();
            String keyspaceName = cassandraConfigMapping.getKeyspaceName();
            if (clusterName != null && keyspaceName != null) {
                map.computeIfAbsent(clusterName, k -> new HashSet<>());
                map.get(clusterName).add(keyspaceName);
            }
        }
        return map;
    }

    public static Map<String, Set<String>> getHostMap(TenantConfiguration tenantConfiguration) {
        Map<String, Set<String>> collectedHosts = new HashMap<>();

        putClusterHostToMap(tenantConfiguration.getDataStorageConfig().getDataKeyspaceConfig(), collectedHosts);
        putClusterHostToMap(tenantConfiguration.getDataStorageConfig().getMatchKeyspaceConfig(), collectedHosts);
        if (tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig() != null && tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig().getBigTableProjectId() == null) {
            putClusterHostToMap(tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig(), collectedHosts);
        }
        putClusterHostToMap(tenantConfiguration.getDataStorageConfig().getInteractionKeyspaceConfig(), collectedHosts);
        putClusterHostToMap(tenantConfiguration.getDataStorageConfig().getActivityLogV2KeyspaceConfig(), collectedHosts);
        putClusterHostToMap(tenantConfiguration.getDataStorageConfig().getActivityLogKeyspaceConfig(), collectedHosts);
        putClusterHostToMap(tenantConfiguration.getDataStorageConfig().getPotentialMatchesKeyspaceConfig(), collectedHosts);

        return collectedHosts;
    }

    private static Map<String, Set<String>> putClusterHostToMap(CassandraConfigMapping cassandraConfigMapping, Map<String, Set<String>> map) {
        if (cassandraConfigMapping != null) {
            String clusterName = cassandraConfigMapping.getClusterName();
            String host = cassandraConfigMapping.getHost();
            if (clusterName != null && host != null) {
                map.computeIfAbsent(clusterName, k -> new HashSet<>());
                map.get(clusterName).addAll(getHostSet(host));
            }
        }
        return map;
    }

    private static Set<String> getHostSet(String host) {
        return Stream.of(host.split(",")).collect(Collectors.toSet());
    }

    public static List<String> getHostList(String hosts, String delimiter) {
        return Arrays.stream(hosts.split(delimiter)).map(String::trim).collect(Collectors.toList());
    }

    public static Map<String, List<String>> getMappings(TenantConfiguration tenantConfiguration) {
        try {
            Map<String, List<String>> mapping = new HashMap<>();
            String dataKeyspaceName = tenantConfiguration.getDataStorageConfig().getDataKeyspaceConfig() != null ?
                    tenantConfiguration.getDataStorageConfig().getDataKeyspaceConfig().getKeyspaceName() : null;
            List<String> dataHosts = getKeyspaceHosts(tenantConfiguration.getDataStorageConfig().getDataKeyspaceConfig());
            addHosts(mapping, dataKeyspaceName, dataHosts);

            String historyKeyspaceName = tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig() != null ?
                    tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig().getKeyspaceName() : null;
            List<String> historyHosts = getKeyspaceHosts(tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig());
            addHosts(mapping, historyKeyspaceName, historyHosts);

            String matchKeyspaceName = tenantConfiguration.getDataStorageConfig().getMatchKeyspaceConfig() != null ?
                    tenantConfiguration.getDataStorageConfig().getMatchKeyspaceConfig().getKeyspaceName() : null;
            List<String> matchHosts = getKeyspaceHosts(tenantConfiguration.getDataStorageConfig().getMatchKeyspaceConfig());
            addHosts(mapping, matchKeyspaceName, matchHosts);

            String interactionKeyspaceName = tenantConfiguration.getDataStorageConfig().getInteractionKeyspaceConfig() != null ?
                    tenantConfiguration.getDataStorageConfig().getInteractionKeyspaceConfig().getKeyspaceName() : null;
            List<String> interactionHosts = getKeyspaceHosts(tenantConfiguration.getDataStorageConfig().getInteractionKeyspaceConfig());
            addHosts(mapping, interactionKeyspaceName, interactionHosts);

            String activityLogKeyspaceName = tenantConfiguration.getDataStorageConfig().getActivityLogKeyspaceConfig() != null ?
                    tenantConfiguration.getDataStorageConfig().getActivityLogKeyspaceConfig().getKeyspaceName() : null;
            List<String> activityLogHosts = getKeyspaceHosts(tenantConfiguration.getDataStorageConfig().getActivityLogKeyspaceConfig());
            addHosts(mapping, activityLogKeyspaceName, activityLogHosts);

            String activityLogV2KeyspaceName = tenantConfiguration.getDataStorageConfig().getActivityLogV2KeyspaceConfig() != null ?
                    tenantConfiguration.getDataStorageConfig().getActivityLogV2KeyspaceConfig().getKeyspaceName() : null;
            List<String> activityV2LogHosts = getKeyspaceHosts(tenantConfiguration.getDataStorageConfig().getActivityLogV2KeyspaceConfig());
            addHosts(mapping, activityLogV2KeyspaceName, activityV2LogHosts);

            String pmKeyspaceName = tenantConfiguration.getDataStorageConfig().getPotentialMatchesKeyspaceConfig() != null ?
                    tenantConfiguration.getDataStorageConfig().getPotentialMatchesKeyspaceConfig().getKeyspaceName() : null;
            List<String> pmHosts = getKeyspaceHosts(tenantConfiguration.getDataStorageConfig().getPotentialMatchesKeyspaceConfig());
            addHosts(mapping, pmKeyspaceName, pmHosts);
            return mapping;
        } catch (IllegalStateException e) {
            throw new IllegalStateException(PlatformManagementErrorCode.CANNOT_PARSE_PHYSICAL_CONFIGURATION.toString(), e);
        }
    }

    private static Map<String, List<String>> addHosts(Map<String, List<String>> map, String key, List<String> hosts) {
        if (hosts != null && hosts.size() > 0) {
            List<String> curHost = map.getOrDefault(key, null);
            if (curHost != null) {
                Set<String> actualHosts = new HashSet<>(curHost);
                actualHosts.addAll(hosts);
                map.put(key, new ArrayList<>(actualHosts));
            } else {
                map.put(key, hosts);
            }
        }
        return map;
    }

    private static List<String> getKeyspaceHosts(CassandraConfigMapping keyspaceConfig) {
        return keyspaceConfig != null && keyspaceConfig.getHost() != null ? Arrays.asList(keyspaceConfig.getHost().split(",")) : null;

    }

    public static JsonObject getDataStorageConfigJson(String configuration) {
        JsonObject conf = JsonParser.parseString(configuration).getAsJsonObject();
        return conf.get("dataStorageConfig").getAsJsonObject();
    }

    public static Set<String> getAllHosts(TenantConfiguration tenantConfiguration) {
        Set<String> result = new HashSet<>();
        for (Set<String> s : getHostMap(tenantConfiguration).values()) {
            result.addAll(s);
        }
        return result;
    }

    public static boolean isCassandraHistory(TenantConfiguration tenantConfiguration) {
        try {
            return tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig() != null &&
                    tenantConfiguration.getDataStorageConfig().getHistoryBigTableConfig() == null;
        } catch (Exception e) {
            //Ignore
        }
        return false;
    }

    /**
     * Get Cassandra Mapping depending on the storage type
     *
     * @param tenantConfiguration          tenant configuration
     * @param cassandraConfigWithDSMapping mapping
     * @return cassandra mapping or null in case type is no CASSANDRA_EXECUTOR
     */
    public static CassandraConfigMapping getCassandraConfigMapping(TenantConfiguration tenantConfiguration, CassandraConfigWithDSMapping cassandraConfigWithDSMapping) {
        String dataStorageId = cassandraConfigWithDSMapping.getDataStorageId();
        if (StringUtils.isNotEmpty(dataStorageId)) {
            DataStorageMapping dataStorageMapping = tenantConfiguration.getDataStorageConfig().getDataStoragesMap().get(dataStorageId);
            if (dataStorageMapping.getType() == DataStorageType.CASSANDRA_EXECUTOR) {
                return dataStorageMapping.getCassandraConfig();
            }
        }
        return cassandraConfigWithDSMapping;
    }

    /**
     * Get DataStorage type. By default we will use DataStorageType.CASSANDRA_EXECUTOR
     *
     * @param tenantDatabaseConfiguration tenant database configuration
     * @param cassandraConfigMapping      mapping from pConf
     * @return Type of DataStorage
     */
    public static DataStorageType getDatasDataStorageType(TenantDatabaseConfiguration tenantDatabaseConfiguration, CassandraConfigMapping cassandraConfigMapping) {
        if (cassandraConfigMapping instanceof CassandraConfigWithDSMapping) {
            String dataStorageId = ((CassandraConfigWithDSMapping) cassandraConfigMapping).getDataStorageId();
            Map<String, DataStorageMapping> dsMap = tenantDatabaseConfiguration.getDataStoragesMap();
            if (StringUtils.isNotEmpty(dataStorageId) && MapUtils.isNotEmpty(dsMap)) {
                DataStorageMapping dataStorageMapping = dsMap.get(dataStorageId);
                return dataStorageMapping.getType();
            }
        }
        return DataStorageType.CASSANDRA_EXECUTOR;
    }

    /**
     * Check if DataStorage type is DataStorageType.CASSANDRA_EXECUTOR
     *
     * @param tenantDatabaseConfiguration tenant database configuration
     * @param cassandraConfigMapping      mapping from pConf
     * @return true if config use Cassandra
     */
    public static boolean isDataStorageCassandra(TenantDatabaseConfiguration tenantDatabaseConfiguration, CassandraConfigMapping cassandraConfigMapping) {
        return getDatasDataStorageType(tenantDatabaseConfiguration, cassandraConfigMapping) == DataStorageType.CASSANDRA_EXECUTOR;
    }

    /**
     * Check if DataStorage type is DataStorageType.DYNAMO_DB
     *
     * @param tenantDatabaseConfiguration tenant database configuration
     * @param cassandraConfigMapping      mapping from pConf
     * @return true if config use DynamoDB
     */
    public static boolean isDataStorageDynamoDB(TenantDatabaseConfiguration tenantDatabaseConfiguration, CassandraConfigMapping cassandraConfigMapping) {
        return getDatasDataStorageType(tenantDatabaseConfiguration, cassandraConfigMapping) == DataStorageType.DYNAMO_DB;
    }

    /**
     * Check if DataStorage type is DataStorageType.SPANNER_DB
     *
     * @param tenantDatabaseConfiguration the tenant database configuration
     * @param cassandraConfigMapping      the cassandra config mapping
     * @return true if config use SpannerDb
     */
    public static boolean isDataStorageSpannerDB(TenantDatabaseConfiguration tenantDatabaseConfiguration,
                                                 CassandraConfigMapping cassandraConfigMapping) {
        return getDatasDataStorageType(tenantDatabaseConfiguration, cassandraConfigMapping) == DataStorageType.SPANNER_DB;
    }

    public static boolean isDataStorageCosmosDB(TenantDatabaseConfiguration tenantDatabaseConfiguration, CassandraConfigMapping cassandraConfigMapping) {
        return getDatasDataStorageType(tenantDatabaseConfiguration, cassandraConfigMapping) == DataStorageType.COSMOS_DB;
    }

}
