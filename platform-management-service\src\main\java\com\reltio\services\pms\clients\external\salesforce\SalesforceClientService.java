package com.reltio.services.pms.clients.external.salesforce;

import com.fasterxml.jackson.databind.JsonNode;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;

import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Component
public final class SalesforceClientService {
    private static final String OBJECT_URL_PATTERN = "%s/services/data/%s/sobjects/%s";
    private static final String QUERY_PATTERN = "%s/services/data/%s/query/?q=%s";
    private static final String GET_SUB_QUERY_PATTERN = "SELECT Id,Name,SBQQ__StartDate__c,SBQQ__EndDate__c,Product_Code__c,SBQQ__ProductName__c," +
            "SBQQ__Quantity__c,SBQQ__RequiredById__c,SBQQ__RootId__c, SBQQ__RevisedSubscription__c, Package_Mapping__c, Tenant_ID2__c, Active__c, Velocity_Pack__c, Tenant_Deployment_Region__c, HIPAA_Finance__c," +
            "MDM_Cloud_Provider__c, RDM_Cloud_Provider__c, Consolidated_Profiles__c from SBQQ__Subscription__c WHERE SBQQ__Contract__c = '%s'";
    private static final String GET_ALL_ACTIVE_CONTRACTS = "SELECT Id, AccountId from Contract WHERE status='Activated' and RecordType.Name = 'Order Form'";
    private final static String INSTANCE_URL = "instance_url";
    private final static String ACCESS_TOKEN = "access_token";
    private final static Logger LOGGER = Logger.getLogger(SalesforceClientService.class);
    @Resource
    private final RestTemplate restTemplate = new RestTemplate();
    @Value("${salesforce.server.url}")
    private String salesforceUrl;
    @Value("${salesforce.username}")
    private String salesforceUsername;
    @Value("${salesforce.password}")
    private String salesforcePassword;
    @Value("${salesforce.client.id}")
    private String salesforceClientId;
    @Value("${salesforce.client.secret}")
    private String salesforceSecret;
    @Value("${salesforce.api.version:v56.0}")
    private String apiVersion;
    private String sfUrl = null;
    private String sfAccessToken = null;

    @EventListener
    public void onApplicationEvent(ContextRefreshedEvent event) {
        getNewToken();
    }

    public String getSfUrl() {
        if (this.sfUrl == null) {
            return salesforceUrl;
        }
        return this.sfUrl;
    }

    private void getNewToken() {
        Set<String> properties=new HashSet<>(Arrays.asList(salesforceSecret,salesforceClientId,salesforcePassword,salesforceUrl,salesforceUsername));
        PropertiesValidator.validateProperties(properties,LOGGER);
        try {
            synchronized (this) {
                Map<String, String> token = restTemplate.postForObject(salesforceUrl + "/services/oauth2/token" + "?grant_type=password&client_id=" + salesforceClientId
                        + "&client_secret=" + salesforceSecret + "&username=" + salesforceUsername + "&password=" + salesforcePassword, null, Map.class);
                if (token != null) {
                    if (token.get(INSTANCE_URL) != null) {
                        sfUrl = token.get(INSTANCE_URL);
                    }
                    if (token.get(ACCESS_TOKEN) != null) {
                        sfAccessToken = token.get(ACCESS_TOKEN);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("Salesforce services is not working or it key got refresh " + e.getMessage());
        }

    }

    private HttpEntity<String> getHttpEntityWithAuthorization() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + sfAccessToken);
        return new HttpEntity<>(headers);
    }

    public JsonNode getData(String url) {
        //TODO some way to check URL for NULL or any other case
        return getDataWithRetry(url, true);
    }

    public JsonNode getDataWithRetry(String url, boolean retry) {

        try {
            ResponseEntity<JsonNode> response = restTemplate.exchange(url, HttpMethod.GET, getHttpEntityWithAuthorization(), JsonNode.class);
            if (response.getStatusCode() != HttpStatus.OK) {
                response = restTemplate.exchange(url, HttpMethod.GET, getHttpEntityWithAuthorization(), JsonNode.class);
            }
            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            } else {
                throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), "Server returned an error: " + response.getStatusCode() + ". Message: " + response.getBody());
            }
        } catch (Exception ex) {
            LOGGER.info("Error has occurred while getting data.", ex);
            if (retry) {
                getNewToken();
                return getDataWithRetry(url, false);
            } else {
                throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), ex.getMessage());
            }
        }
    }

    public JsonNode getContract(String id) {
        return getData(String.format(OBJECT_URL_PATTERN, getSfUrl(), apiVersion, "Contract/" + id));
    }

    public JsonNode getOpportunity(String id) {
        return getData(String.format(OBJECT_URL_PATTERN, getSfUrl(), apiVersion, "Opportunity/" + id));
    }

    public JsonNode getSubscriptions(String contractId) {
        String subscriptionQuery = String.format(GET_SUB_QUERY_PATTERN, contractId);
        return getData(String.format(QUERY_PATTERN, getSfUrl(), apiVersion, subscriptionQuery));
    }

    public JsonNode getAccount(String id) {
        return getData(String.format(OBJECT_URL_PATTERN, getSfUrl(), apiVersion, "Account/" + id));
    }

    public JsonNode getContact(String id) {
        return getData(String.format(OBJECT_URL_PATTERN, getSfUrl(), apiVersion, "Contact/" + id));
    }

    public JsonNode getUser(String id) {

        return getData(String.format(OBJECT_URL_PATTERN, getSfUrl(), apiVersion, "User/" + id));
    }

    public JsonNode getQueryResult(String query) {
        return getData(String.format(QUERY_PATTERN, getSfUrl(), apiVersion, query));
    }

    public JsonNode getQueryById(String queryId) {
        return getData(getSfUrl().concat(queryId));
    }

    public JsonNode getAllActiveContracts() {
        return getData(String.format(QUERY_PATTERN, getSfUrl(), apiVersion, GET_ALL_ACTIVE_CONTRACTS));
    }
}
