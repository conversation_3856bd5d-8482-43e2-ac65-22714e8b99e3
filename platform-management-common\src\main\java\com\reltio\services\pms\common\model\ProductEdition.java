package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.enterprise.contracts.DTSSType;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@Getter
@Setter
public class ProductEdition extends BaseFirestoreEntity {

    public static final String BUSINESS_CONFIG_DEFAULT_PATH_FORMAT = "%s/BusinessConfig.json";
    public static final String UI_CONFIG_DEFAULT_PATH_FORMAT = "%s/UI Config";
    public static final String PERMISSIONS_CONFIG_DEFAULT_PATH_FORMAT = "%s/Permissions.json";

    @JsonProperty("name")
    private String name;

    @JsonProperty("businessConfigPath")
    private String businessConfigPath;

    @JsonProperty("lookUpsPath")
    private String lookUpsPath;

    @JsonProperty("rdmLookUpsPath")
    private String rdmLookUpsPath;

    @JsonProperty("uiConfigFolderPath")
    private String uiConfigFolderPath;

    @JsonProperty("sampleData")
    private SampleDataInfo sampleData;

    @JsonProperty("permissionsPath")
    private String permissionsPath;

    @JsonProperty("cleansersPath")
    private String cleansersPath;

    @JsonProperty("generatorsPath")
    private String generatorsPath;

    @JsonProperty("dtssConfigPath")
    private Map<DTSSType, String> dtssConfigPath;

    @JsonProperty("dvfConfigPath")
    private String dvfConfigPath;

    @JsonProperty("rih")
    private final Rih rih;

    @JsonProperty("dnb")
    private Map<String, Map<String, String>> dnbConfigs;

    @JsonProperty("uiConfig")
    private final Map<String, String> uiConfig;

    @JsonProperty("repositoryBranch")
    private String repositoryBranch;

    @JsonProperty("mlPretrainedEnable")
    private boolean mlPretrainedEnable;

    @JsonProperty("reltioPackageType")
    private ReltioPackageType reltioPackageType;

    @JsonCreator
    public ProductEdition(@JsonProperty(value = "name", required = true) String name,
                          @JsonProperty("businessConfigPath") String businessConfigPath,
                          @JsonProperty("lookUpsPath") String lookUpsPath,
                          @JsonProperty("uiConfigFolderPath") String uiConfigFolderPath,
                          @JsonProperty("sampleData") SampleDataInfo sampleData,
                          @JsonProperty("permissionsPath") String permissionsPath,
                          @JsonProperty("rdmLookUpsPath") String rdmLookUpsPath,
                          @JsonProperty("cleansersPath") String cleansersPath,
                          @JsonProperty("generatorsPath") String generatorsPath,
                          @JsonProperty("dtssConfigPath") Map<DTSSType, String> dtssConfigPath,
                          @JsonProperty("dvfConfigPath") String dvfConfigPath,
                          @JsonProperty("dnb") Map<String, Map<String, String>> dnbConfigs,
                          @JsonProperty("uiConfig") Map<String, String> uiConfig,
                          @JsonProperty("rih") Rih rih,
                          @JsonProperty("repositoryBranch") String repositoryBranch,
                          @JsonProperty("mlPretrainedEnable") boolean mlPretrainedEnable) {
        this.name = name;
        this.businessConfigPath = businessConfigPath == null ? String.format(BUSINESS_CONFIG_DEFAULT_PATH_FORMAT, name) : businessConfigPath;
        this.lookUpsPath = lookUpsPath;
        this.uiConfigFolderPath = uiConfigFolderPath == null ? String.format(UI_CONFIG_DEFAULT_PATH_FORMAT, name) : uiConfigFolderPath;
        this.permissionsPath = permissionsPath == null ? String.format(PERMISSIONS_CONFIG_DEFAULT_PATH_FORMAT, name) : permissionsPath;
        this.sampleData = sampleData == null ? new SampleDataInfo(Collections.emptyList(), Collections.emptyList(), Collections.emptyList()) : sampleData;
        this.rdmLookUpsPath = rdmLookUpsPath;
        this.cleansersPath = cleansersPath;
        this.generatorsPath = generatorsPath;
        this.dtssConfigPath = dtssConfigPath == null ? new EnumMap<>(DTSSType.class) : dtssConfigPath;
        this.dvfConfigPath = dvfConfigPath;
        this.dnbConfigs = dnbConfigs == null ? new HashMap<>() : dnbConfigs;
        this.uiConfig = uiConfig == null ? new HashMap<>() : uiConfig;
        this.rih = rih;
        this.repositoryBranch = repositoryBranch;
        this.mlPretrainedEnable = mlPretrainedEnable;
    }

    public ProductEdition(@JsonProperty(value = "name", required = true) String name,
                          @JsonProperty("businessConfigPath") String businessConfigPath,
                          @JsonProperty("lookUpsPath") String lookUpsPath,
                          @JsonProperty("uiConfigFolderPath") String uiConfigFolderPath,
                          @JsonProperty("sampleData") SampleDataInfo sampleData,
                          @JsonProperty("permissionsPath") String permissionsPath,
                          @JsonProperty("rdmLookUpsPath") String rdmLookUpsPath,
                          @JsonProperty("cleansersPath") String cleansersPath,
                          @JsonProperty("generatorsPath") String generatorsPath,
                          @JsonProperty("dtssConfigPath") Map<DTSSType, String> dtssConfigPath,
                          @JsonProperty("dvfConfigPath") String dvfConfigPath,
                          @JsonProperty("dnb") Map<String, Map<String, String>> dnbConfigs,
                          @JsonProperty("uiConfig") Map<String, String> uiConfig,
                          @JsonProperty("rih") Rih rih,
                          @JsonProperty("mlPretrainedEnable") boolean mlPretrainedEnable) {
        this(name, businessConfigPath, lookUpsPath, uiConfigFolderPath, sampleData, permissionsPath, rdmLookUpsPath, cleansersPath, generatorsPath, dtssConfigPath, dvfConfigPath, dnbConfigs, uiConfig, rih, null, mlPretrainedEnable);
    }

    public Map<String, String> getDnbMappings(String mapping) {
        return getDnbConfigs().get(mapping);
    }


    @Override
    @JsonIgnore
    public String getID() {
        ReltioPackageType packageType=getReltioPackageType();
        if(packageType!=null){
            return String.format("%s@%s", getName(), packageType.name());
        }
        return String.format("%s@%s", getName(), ReltioPackageType.MDM.name());
    }

    public boolean getMlPretrainedEnable() {
        return mlPretrainedEnable;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ProductEdition)) {
            return false;
        }
        ProductEdition productEdition = (ProductEdition) o;
        return Objects.equals(getName(), productEdition.getName()) &&
                Objects.equals(getBusinessConfigPath(), productEdition.getBusinessConfigPath()) &&
                Objects.equals(getLookUpsPath(), productEdition.getLookUpsPath()) &&
                Objects.equals(getUiConfigFolderPath(), productEdition.getUiConfigFolderPath()) &&
                Objects.equals(getSampleData(), productEdition.getSampleData()) &&
                Objects.equals(getPermissionsPath(), productEdition.getPermissionsPath()) &&
                Objects.equals(getRdmLookUpsPath(), productEdition.getRdmLookUpsPath()) &&
                Objects.equals(getCleansersPath(), productEdition.getCleansersPath()) &&
                Objects.equals(getGeneratorsPath(), productEdition.getGeneratorsPath()) &&
                Objects.equals(getDtssConfigPath(), productEdition.getDtssConfigPath()) &&
                Objects.equals(getDvfConfigPath(), productEdition.getDvfConfigPath()) &&
                Objects.equals(getUiConfig(), productEdition.getUiConfig()) &&
                Objects.equals(getRih(), productEdition.getRih()) &&
                Objects.equals(getRepositoryBranch(), productEdition.getRepositoryBranch()) &&
                Objects.equals(getDnbConfigs(), productEdition.getDnbConfigs()) &&
                Objects.equals(getMlPretrainedEnable(), productEdition.getMlPretrainedEnable());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getName(), getBusinessConfigPath(), getLookUpsPath(), getUiConfigFolderPath(), getSampleData(), getPermissionsPath(), getRdmLookUpsPath(), getCleansersPath(), getGeneratorsPath(), getDtssConfigPath(), getDvfConfigPath(), getDnbConfigs(), getRih(), getUiConfig(), getRepositoryBranch(), getMlPretrainedEnable());
    }

    @Override
    public String toString() {
        return "ProductEdition{" +
                "name='" + name + '\'' +
                ", businessConfigPath='" + businessConfigPath + '\'' +
                ", lookUpsPath='" + lookUpsPath + '\'' +
                ", rdmLookUpsPath='" + rdmLookUpsPath + '\'' +
                ", uiConfigFolderPath='" + uiConfigFolderPath + '\'' +
                ", sampleData=" + sampleData +
                ", permissionsPath='" + permissionsPath + '\'' +
                ", cleansersPath='" + cleansersPath + '\'' +
                ", generatorsPath='" + generatorsPath + '\'' +
                ", dtssConfigPath='" + dtssConfigPath + '\'' +
                ", dvfConfigPath='" + dvfConfigPath + '\'' +
                ", rih='" + rih + '\'' +
                ", dnb'" + dnbConfigs + '\'' +
                ", uiConfig'" + uiConfig + '\'' +
                ", repositoryBranch'" + repositoryBranch + '\'' +
                ", mlPretrainedEnable'" + mlPretrainedEnable + '\'' +
                '}';
    }
}
