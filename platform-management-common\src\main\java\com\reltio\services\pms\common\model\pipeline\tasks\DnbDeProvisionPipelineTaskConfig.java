package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = false)
public class DnbDeProvisionPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonCreator
    public DnbDeProvisionPipelineTaskConfig(@JsonProperty(value="name") String name){
        super(name,TaskType.DNB_DE_PROVISION_TASK);

    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.DNB_CONNECTOR;
    }

    @Override
    public String toString() {
        return TaskType.DNB_DE_PROVISION_TASK + "{}";
    }
}
