package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.Getter;
import java.util.List;

@Getter
public class AuthDeProvisionPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonProperty(value = "protectedUsers")
    private final List<String> protectedUsers;

    @JsonProperty("protectedCustomers")
    private List<String> protectedCustomers;

    @JsonCreator
    public AuthDeProvisionPipelineTaskConfig(@JsonProperty(value="name") String name,
                                             @JsonProperty(value = "protectedUsers") List<String> protectedUsers,
                                             @JsonProperty(value = "protectedCustomers") List<String> protectedCustomers){
        super(name,TaskType.AUTH_DE_PROVISION_TASK);
        this.protectedUsers = protectedUsers != null ? protectedUsers : List.of();
        this.protectedCustomers = protectedCustomers != null ? protectedCustomers : List.of();
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.AUTH_ACCESS;
    }
}
