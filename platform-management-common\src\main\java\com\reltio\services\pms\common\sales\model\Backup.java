package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class Backup extends BaseFirestoreEntity {

    @JsonProperty(value = "id")
    private String id;

    @JsonProperty(value = "sourceTenantId")
    private String sourceTenantId;

    @JsonProperty(value = "sourceEnvId")
    private String sourceEnvId;

    @JsonProperty(value = "ticketId")
    private String ticketId;

    @JsonProperty(value = "jobId")
    private String jobId;

    @JsonProperty(value = "taskId")
    private String taskId;

    @JsonProperty(value = "retentionPeriod")
    private long retentionPeriod;

    @JsonProperty(value = "sourceTenantBackupVaultName")
    private String sourceTenantBackupVaultName;

    @JsonProperty(value = "status")
    private String status;

    @JsonProperty(value = "skipTables")
    private List<String> skipTables;

    @JsonProperty(value = "dataBackupArnsWithTableNames")
    private Map<String, String> dataBackupArnsWithTableNames;

    @JsonProperty(value = "dataTableToBackupFailureReason")
    private Map<String,String> dataTableToBackupFailureReason;

    @JsonProperty(value = "matchBackupArnsWithTableNames")
    private Map<String, BackupJobDetails> matchBackupArnsWithTableNames;

    @JsonProperty(value = "matchTableToBackupFailureReason")
    private Map<String,String> matchTableToBackupFailureReason;

    @Override
    public String getID() {
        return id;
    }


    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    public static class BackupJobDetails {

        @JsonProperty(value = "backupJobId")
        private String backupJobId;

        @JsonProperty(value = "recoveryPointArn")
        private String recoveryPointArn;
    }

}


