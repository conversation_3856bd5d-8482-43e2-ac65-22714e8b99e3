package com.reltio.services.pms.common.model.compliance.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class EnvironmentValidateResponse {
    @JsonProperty("warning")
    private Warning warning;
    @JsonProperty("error")
    private Error error;
}
