package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;

public class ImageHostingPipelineTaskConfig extends AbstractPipelineTaskConfig{

    @JsonCreator
    public ImageHostingPipelineTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.IMAGE_HOSTING_ENABLEMENT_TASK);
    }
    @Override
    public PMSProductName getProductName() {
        return PMSProductName.IMAGE_HOSTING;
    }

    @Override
    public String toString() {
        return "IMAGE_HOSTING_ENABLEMENT_TASK{}";
    }
}