package com.reltio.services.pms.clients.reltio.dtss;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.reltio.io.IOUtils;
import com.reltio.services.pms.clients.reltio.IReltioClient;
import com.reltio.services.pms.common.IPMSRestTemplate;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.Environment;
import com.reltio.services.pms.common.model.enterprise.contracts.DTSSType;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import com.reltio.services.pms.service.ProductEditionsService;
import com.reltio.services.pms.service.environment.EnvironmentService;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Set;


@Service
@Retryable(exceptionExpression = "${retry.shouldRetry:true}", maxAttempts = 5, backoff = @Backoff(delay = 2000, multiplier = 5))
public class DTSSClient implements IReltioClient {

    @Value("${reltio.auth.server.url}/oauth/token")
    private String oAuthEndpointUrl;

    private final IPMSRestTemplate restTemplate;
    private final EnvironmentService environmentService;
    private final ProductEditionsService productEditionsService;
    private final ObjectMapper mapper;
    private static final String TENANTS = "tenants";
    private static final String CUSTOMER_TENANTS = "customerTenants";
    private static final String DATA_TENANTS = "dataTenants";
    private static final String DTSS = "dtss";
    private static final Logger LOGGER = Logger.getLogger(DTSSClient.class);

    @Autowired
    public DTSSClient(@Qualifier("console") IPMSRestTemplate restTemplate,
                      EnvironmentService environmentService,
                      ProductEditionsService productEditionsService) {
        this.restTemplate = restTemplate;
        this.environmentService = environmentService;
        this.productEditionsService = productEditionsService;
        mapper = new ObjectMapper();
        Set<String> properties = Collections.singleton(oAuthEndpointUrl);
        PropertiesValidator.validateProperties(properties, LOGGER);
    }

    public boolean registerCustomerTenant(String envId, String tenantID) throws IOException {
        Environment environment = environmentService.getEnvironment(envId);
        String environmentUrl = environment.getUrl();
        String dtssUrl = String.format("%s/%s/%s/%s", environmentUrl, "dtss", TENANTS, CUSTOMER_TENANTS);
        String file = IOUtils.readResource(DTSSClient.class, "dtss_requests/register_tenant_request.json")
                .replace("{tenant_id}", tenantID)
                .replace("{oauth_end_point_url}", oAuthEndpointUrl)
                .replace("{reltio_api_url}", environmentUrl + "/reltio/api")
                .replace("{email}", "");
        JsonNode request = mapper.readTree(file);
        try {
            restTemplate.postForObject(dtssUrl, request, JsonNode.class);
            return true;
        } catch (HttpClientErrorException.NotFound ex) { // In case the tenant already registered
            return false;
        }
    }

    public JsonNode subscribeTenant(String envId, String customerTenantId, String dataTenantId, String productEdition, DTSSType dtssSubscriptionType, ReltioPackageType reltioPackageType) throws IOException {
        String productEditionNameWithReltioPackageType =
                productEditionsService.getProductEditionNameWithReltioPackageType(productEdition, reltioPackageType);
        String file = productEditionsService.getDtssFileContent(productEditionNameWithReltioPackageType, dtssSubscriptionType)
                .replaceAll("((\\{\\{)\\bdataTenantId.*\\b(\\}\\}))", dataTenantId)
                .replace("{{tenantId}}", customerTenantId);
        Environment environment = environmentService.getEnvironment(envId);
        String environmentUrl = environment.getUrl();
        String dtssUrl = String.format("%s/%s/%s", environmentUrl, "dtss", "subscriptions");
        JsonNode request = mapper.readTree(file);
        try {
            return restTemplate.postForObject(dtssUrl, request, JsonNode.class);
        } catch (HttpClientErrorException.NotFound ex) {
            throw new DTSSClientException("Can not subscribe tenant: ", ex);
        }
    }

    public boolean checkIfCustomerTenantRegistered(String envId, String customerTenantId) {
        Environment environment = environmentService.getEnvironment(envId);
        String environmentUrl = environment.getUrl();
        String dtssUrl = String.format("%s/%s/%s/%s/%s", environmentUrl, "dtss", TENANTS, CUSTOMER_TENANTS, customerTenantId);
        try {
            restTemplate.getForObject(dtssUrl, JsonNode.class);
            return true;
        } catch (HttpClientErrorException.NotFound ex) {
            return false;
        }
    }

    public List<String> isTenantSubscribed(String env, String customerTenant, boolean useCache, boolean validate) {
        Environment environment = environmentService.getEnvironment(env);
        String subcriptionUrl = String.format("%s/dtss/subscriptions?useCache=%s&validate=%s", environment.getUrl(), useCache, validate);
        HttpHeaders headers = new HttpHeaders();
        headers.put("customerTenant", Collections.singletonList(customerTenant));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        try {
            ResponseEntity<JsonNode> exchange = restTemplate.exchange(subcriptionUrl, HttpMethod.GET, entity, JsonNode.class);
            JsonNode body = exchange.getBody();
            if (body == null) {
                throw new PlatformManagementException(PlatformManagementErrorCode.TENANT_NOT_SUBSCRIBED, HttpStatus.BAD_REQUEST.value(), customerTenant);
            }
            List<String> dataTenantIdList = new ArrayList<>();
            for (int i = 0; i < body.size(); ++i) {
                dataTenantIdList.add(body.get(i).get("dataTenantId").get("id").asText());
            }
            return dataTenantIdList;
        } catch (HttpClientErrorException.NotFound ex) {
            return Collections.emptyList();
        }
    }

    public boolean isEnabledForTenant(String tenantId, String env) {
        Environment environment = environmentService.getEnvironment(env);
        String subcriptionUrl = String.format("%s/dtss/%s/%s/%s", environment.getUrl(), TENANTS, CUSTOMER_TENANTS, tenantId);
        try {
            restTemplate.getForObject(subcriptionUrl, JsonNode.class);
            return true;
        } catch (HttpClientErrorException.NotFound ex) {
            return false;
        }
    }

    public boolean correctSubscriptionType(String tenantId, String env, DTSSType dtssSubscriptionType) {
        Environment environment = environmentService.getEnvironment(env);
        String dataTenantsUrl = String.format("%s/dtss/%s/%s/%s", environment.getUrl(), TENANTS, DATA_TENANTS, tenantId);
        try {
            JsonNode response = restTemplate.getForObject(dataTenantsUrl, JsonNode.class);
            if (response == null) {
                return false;
            } else if (response.get("name") != null && response.get("name").asText().toLowerCase(Locale.ROOT)
                    .replace("_", "").replace(" ", "")
                    .contains(dtssSubscriptionType.name().toLowerCase(Locale.ROOT))) {
                return true;
            } else {
                return response.get("label") != null && response.get("label").asText().toLowerCase(Locale.ROOT)
                        .replace(":", "").replace(" ", "")
                        .contains(dtssSubscriptionType.name().toLowerCase(Locale.ROOT));
            }
        } catch (HttpClientErrorException.NotFound ex) {
            return false;
        }
    }

    public JsonNode getCustomerTenantRegistration(String envId, String customerTenantId) {
        Environment environment = environmentService.getEnvironment(envId);
        String environmentUrl = environment.getUrl();
        String url = String.format("%s/%s/%s/%s/%s", environmentUrl, DTSS, TENANTS, CUSTOMER_TENANTS, customerTenantId);
        try {
            return restTemplate.getForObject(url, JsonNode.class);
        } catch (HttpClientErrorException.NotFound ex) {
            LOGGER.warn(String.format("Tenant %s is not registered as customer tenant %s", customerTenantId, ex.getMessage()));
            return null;
        }
    }

    public void deleteCustomerTenantAndDataTenantRegistration(String envId, String customerTenantId, String dataTenantId) {
        Environment environment = environmentService.getEnvironment(envId);
        String environmentUrl = environment.getUrl();
        String url = String.format("%s/%s/subscriptions",environmentUrl,DTSS);
        HttpHeaders headers = new HttpHeaders();
        headers.put("CustomerTenant", Collections.singletonList(customerTenantId));
        headers.put("DataTenant", Collections.singletonList(dataTenantId));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try{
            restTemplate.exchange(url, HttpMethod.DELETE, entity, JsonNode.class);
        }catch (Exception ex) {
            throw new DTSSClientException("Error occurred while deleting customer tenant registration: ", ex);
        }
    }

    public void removeCustomerTenantRegistrationFromDtssService(String envId, String customerTenantId) {
        Environment environment = environmentService.getEnvironment(envId);
        String environmentUrl = environment.getUrl();
        String url = String.format("%s/%s/%s/%s", environmentUrl, DTSS, TENANTS, CUSTOMER_TENANTS);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String requestBody = String.format("[\"%s\"]", customerTenantId);
        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
        try {
            restTemplate.exchange(url, HttpMethod.DELETE, requestEntity, JsonNode.class);
        }catch (Exception ex) {
            throw new DTSSClientException("Error occurred while removing customer tenant registration from dtss service: ", ex);

        }


    }

}
