package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.annotation.Nullable;
import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomSettings {

    @JsonProperty("resetPasswordEmailTemplateId")
    private String resetPasswordEmailTemplateId;

    @JsonCreator
    public CustomSettings(@JsonProperty(value = "resetPasswordEmailTemplateId") String resetPasswordEmailTemplateId) {
        this.resetPasswordEmailTemplateId = resetPasswordEmailTemplateId;
    }
    @JsonProperty(value = "resetPasswordEmailTemplateId")
    @Nullable
    public String getResetPasswordEmailTemplate() {
        return resetPasswordEmailTemplateId;
    }

    public CustomSettings() {

    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CustomSettings)) return false;
        CustomSettings that = (CustomSettings) o;
        return getResetPasswordEmailTemplate().equals(that.getResetPasswordEmailTemplate());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getResetPasswordEmailTemplate());
    }


    @Override
    public String toString() {
        return "customSettings{" +
                "resetPasswordEmailTemplateId='" + resetPasswordEmailTemplateId + '\'' +
                '}';
    }
}
