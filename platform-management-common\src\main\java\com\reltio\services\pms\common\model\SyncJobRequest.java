package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;

import java.util.EnumSet;

public class SyncJobRequest extends CreateJobRequest {

    @JsonProperty("activatedTime")
    private Long activatedTime;

    public SyncJobRequest(@JsonProperty("pipelineId") String pipelineId,
                          @JsonProperty("activatedTime") Long activatedTime,
                          @JsonProperty("skipTasks") EnumSet<PMSProductName> skipTasks) {
        super(pipelineId, skipTasks);
        this.activatedTime = activatedTime;
    }

    public Long getActivatedTime() {
        return activatedTime;
    }
}
