package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.CustomerType;
import com.reltio.services.pms.common.model.auth.AuthCustomer;
import com.reltio.services.pms.common.model.request.AccountToCustomer;
import com.reltio.services.pms.common.model.request.TenantsToContract;
import com.reltio.services.pms.common.sales.model.ReltioContract;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.SalesAccount;
import com.reltio.services.pms.service.AuthCustomerService;
import com.reltio.services.pms.service.SalesAccountService;
import com.reltio.services.pms.service.reltiotenant.TenantsService;
import com.reltio.services.pms.service.sales.ContractService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@RestController
@RequestMapping(value = "/api/v1/relations", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name= "Tenant Relations")
@ReltioSecured(resourceClass = Pms.Environment.class)
public class RelationsController {
    private final AuthCustomerService authCustomerService;
    private final ContractService contractService;
    private final TenantsService tenantsService;
    private final SalesAccountService salesAccountService;

    @Autowired
    public RelationsController(AuthCustomerService authCustomerService, ContractService contractService,
                               TenantsService tenantsService, SalesAccountService salesAccountService) {
        this.authCustomerService = authCustomerService;
        this.contractService = contractService;
        this.tenantsService = tenantsService;
        this.salesAccountService = salesAccountService;
    }

    @PostMapping(value = "/accountToCustomer", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ReltioContract accountToCustomer(@RequestBody AccountToCustomer accountToCustomer) {
        //check input
        String validationOnAccountToCustomerRequestBody = "Request you to please give all 3 AuthCustomerId, SalesContractId and SalesAccountId";
        Objects.requireNonNull(accountToCustomer.getAuthCustomerId(), validationOnAccountToCustomerRequestBody);
        Objects.requireNonNull(accountToCustomer.getSalesContractId(), validationOnAccountToCustomerRequestBody);
        Objects.requireNonNull(accountToCustomer.getSalesAccountId(), validationOnAccountToCustomerRequestBody);
        //do validation
        AuthCustomer authCustomer = authCustomerService.getCustomerFromPMS(accountToCustomer.getAuthCustomerId());
        ReltioContract reltioContract = contractService.getContract(accountToCustomer.getSalesContractId());
        SalesAccount salesAccount = salesAccountService.getSalesAccount(accountToCustomer.getSalesAccountId());
        if (reltioContract.getAccountId() == null || !reltioContract.getAccountId().equals(accountToCustomer.getSalesAccountId())) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CONTRACT_WITH_THIS_ACCOUNTID_NOT_FOUND, HttpStatus.INTERNAL_SERVER_ERROR.value(), reltioContract.getAccountId());
        }
        //update PMS_AUTH_CUSTOMERS

        CustomerType customerType = CustomerType.getCustomerType(authCustomer.getSalesAccountId(), authCustomer.getID());
        authCustomer.setCustomerType(customerType);
        authCustomer.setSalesAccountId(reltioContract.getAccountId());
        authCustomer.setSalesContractId(accountToCustomer.getSalesContractId());
        //update PMS_SALES_CONTRACTS
        reltioContract.setAuthCustomerId(accountToCustomer.getAuthCustomerId());
        authCustomerService.updatePMSCustomer(authCustomer);
        salesAccount.getAuthCustomerId().add(accountToCustomer.getAuthCustomerId());
        salesAccountService.createOrUpdate(salesAccount);
        return contractService.update(reltioContract);
    }

    @PostMapping(value = "/tenantsToContract", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ReltioContract tenantsToContract(@RequestBody TenantsToContract tenantsToContract) {
        //check input
        String validationOnTenantsToContractRequestBody = "Request you to please give all 4 TenantId, SalesContractId, SalesAccountId and SalesSubscriptionId";
        Objects.requireNonNull(tenantsToContract.getTenantId(), validationOnTenantsToContractRequestBody);
        Objects.requireNonNull(tenantsToContract.getSalesContractId(), validationOnTenantsToContractRequestBody);
        Objects.requireNonNull(tenantsToContract.getSalesAccountId(), validationOnTenantsToContractRequestBody);
        Objects.requireNonNull(tenantsToContract.getSalesSubscriptionId(), validationOnTenantsToContractRequestBody);
        //do validation
        ReltioTenant reltioTenant = tenantsService.getTenant(tenantsToContract.getTenantId());
        ReltioContract reltioContract = contractService.getContract(tenantsToContract.getSalesContractId());
        if (!tenantsToContract.getSalesAccountId().equals(reltioContract.getAccountId())) {
            throw new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(),
                    "SalesAccountId : " + tenantsToContract.getSalesAccountId() + " is not part of Contract for ContractId " + tenantsToContract.getSalesContractId());
        }
        ReltioTenant tenantPurpose = reltioContract.getPackages().stream().flatMap(s -> s.getMdmTenants().stream()).filter(t -> t.getSalesConfig() != null && t.getSalesConfig().getSubscriptionId().equals(tenantsToContract.getSalesSubscriptionId())).findFirst()
                .orElseThrow(() -> new PlatformManagementException(PlatformManagementErrorCode.GENERIC_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(),
                        "SubscriptionId : " + tenantsToContract.getSalesSubscriptionId() + " not present in Contract for ContractId " + tenantsToContract.getSalesContractId()));
        //update PMS_MDM_TENANTS
        reltioTenant.setAccountId(tenantsToContract.getSalesAccountId());
        reltioTenant.setContractId(tenantsToContract.getSalesContractId());
        reltioTenant.setSubscriptionId(tenantsToContract.getSalesSubscriptionId());
        reltioTenant.setTenantPurpose(tenantPurpose.getTenantPurpose());

        //update PMS_SALES_CONTRACTS
        tenantPurpose.setAccountId(tenantsToContract.getSalesAccountId());
        tenantPurpose.setTenantId(tenantsToContract.getTenantId());

        tenantsService.createOrUpdateTenant(reltioTenant);
        return contractService.update(reltioContract);
    }

}
