package com.reltio.services.pms.service.jobs.tasks.contracts.sync;

import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.contracts.sync.ContractsSyncTaskInstance;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.AbstractTaskExecutionService;
import com.reltio.services.pms.service.sales.ContractService;

public class ContractsSyncTaskExecutionService extends AbstractTaskExecutionService<ContractsSyncTaskInstance> {

    private final ContractService contractService;

    public ContractsSyncTaskExecutionService(String envId, ContractsSyncTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService, ContractService contractService) {
        super(envId, taskDetail, grafanaDashboardGBQService);
        this.contractService = contractService;
    }

    @Override
    protected void executeTask() throws Exception {
        contractService.syncAllActiveContracts();
        taskDetail.setStatus(TaskStatus.COMPLETED);
    }
}
