package com.reltio.services.pms.common.model.tenant.access;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ZendeskRevokedResponse {

    @JsonProperty("status")
    private ZendeskRevokedStatus status;

    @JsonProperty("requestIds")
    private List<String> requestIds;

    @JsonProperty("failedRequestIds")
    private List<String> failedRequestIds;

    @JsonCreator
    public ZendeskRevokedResponse(@JsonProperty("status") ZendeskRevokedStatus status,
                                @JsonProperty("requestIds") List<String> requestIds,
                                @JsonProperty("failedRequestIds") List<String> failedRequestIds) {
        this.status = status;
        this.requestIds = requestIds;
        this.failedRequestIds = failedRequestIds;
    }

    public ZendeskRevokedStatus getStatus() {
        return status;
    }

    public void setStatus(ZendeskRevokedStatus status) {
        this.status = status;
    }

    public List<String> getRequestIds() {
        return requestIds;
    }

    public void setRequestIds(List<String> requestIds) {
        this.requestIds = requestIds;
    }

    public List<String> getFailedRequestIds() {
        return failedRequestIds;
    }

    public void setFailedRequestIds(List<String> failedRequestIds) {
        this.failedRequestIds = failedRequestIds;
    }
}
