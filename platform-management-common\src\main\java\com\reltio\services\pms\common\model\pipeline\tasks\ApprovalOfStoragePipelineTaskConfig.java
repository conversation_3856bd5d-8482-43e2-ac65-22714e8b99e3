package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.common.config.LcaConfig;
import com.reltio.devops.common.environment.ServicePurpose;
import com.reltio.devops.common.environment.ServiceType;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

public class ApprovalOfStoragePipelineTaskConfig extends AbstractPipelineTaskConfig {

    public static final int LCA_NATIVE_TIME_OUT = 100;
    public static final int LCA_LAMBDA_TIME_OUT = 100;
    public static final int LCA_DVF_TIME_OUT = 1000;

    private final Set<String> approverEmails;

    @JsonProperty("storagePriorityList")
    private final Map<ServicePurpose, List<ServiceType>> storagePriorityList ;

    @JsonProperty("lcaConfig")
    private final LcaConfig lcaConfig;

    @JsonCreator
    public ApprovalOfStoragePipelineTaskConfig(
            @JsonProperty(value = "name") String name,
            @JsonProperty(value = "approverEmails") Set<String> approverEmails,
            @JsonProperty(value = "storagePriorityList")Map<ServicePurpose, List<ServiceType>> storagePriorityList,
            @JsonProperty(value = "lcaConfig") LcaConfig lcaConfig) {
        super(name, TaskType.APPROVAL_OF_STORAGE);
        this.approverEmails = approverEmails;
        this.storagePriorityList = storagePriorityList;
        this.lcaConfig = Objects.isNull(lcaConfig) ? lcaConfig : getLcaConfigCopy(lcaConfig);
    }

    private LcaConfig getLcaConfigCopy(LcaConfig lcaConfig){
        LcaConfig config = new LcaConfig();
        if(Objects.isNull(lcaConfig)) {
            config.setDvfTimeOut(LCA_DVF_TIME_OUT);
            config.setLambdaTimeOut(LCA_LAMBDA_TIME_OUT);
            config.setNativeTimeOut(LCA_NATIVE_TIME_OUT);
            return config;
        }
        config.setLambdaTimeOut(lcaConfig.getLambdaTimeOut());
        config.setNativeTimeOut(lcaConfig.getNativeTimeOut());
        config.setDvfTimeOut(lcaConfig.getDvfTimeOut());
        return config;
    }

    public Set<String> getApproverEmails() {
        return approverEmails;
    }

    public LcaConfig getLcaConfig() {
        return getLcaConfigCopy(lcaConfig);
    }

    public Map<ServicePurpose, List<ServiceType>> getStoragePriorityList() {
        return storagePriorityList;
    }

    @Override
    public String toString() {
        return "ApprovalOfStoragePipelineTaskConfig{" +
                "approverEmails=" + approverEmails +
                "lcaConfig=" + lcaConfig +
                '}';
    }
}
