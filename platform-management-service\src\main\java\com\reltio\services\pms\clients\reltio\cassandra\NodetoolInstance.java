package com.reltio.services.pms.clients.reltio.cassandra;

import org.apache.cassandra.tools.NodeProbe;
import org.apache.log4j.Logger;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

public class NodetoolInstance{
    private static final int MAX_RETRIES = 5;
    private static final int WAIT = 1000;
    private NodeProbe nodeProbe;
    private String host;
    private static final Logger log = Logger.getLogger(NodetoolInstance.class);


    public NodetoolInstance(String host, int port, String username, String password) throws IOException {
        this.host = host;
        nodeProbe = new NodeProbe(host, port, username, password);
    }

    public void invalidateKeyCache(int retry) throws Exception {
        if (retry >= MAX_RETRIES) {
            throw new Exception("Max retries exceeded");
        }
        try {
            nodeProbe.invalidateKeyCache();
        } catch (Throwable e) {
            log.error("", e);
            Thread.sleep(WAIT);
            ++retry;
            invalidateKeyCache(retry);
        }
    }

    public void flushKeyspace(String keyspaceName, int retry) throws Exception {
        if (retry >= MAX_RETRIES) {
            log.error(String.format("Max retries exceeded for %s@%s", keyspaceName, host));
            throw new Exception("Max retries exceeded");
        }
        try {
            nodeProbe.forceKeyspaceFlush(keyspaceName);
        } catch (Throwable e) {
            if (e instanceof IOException) {
                if (e.getMessage().contains("does not exist")) {
                    return;
                }
            }
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            log.error("", e);
            Thread.sleep(WAIT);
            ++retry;
            flushKeyspace(keyspaceName, retry);
        }
    }

    public void disableAutoCompaction(String keyspaceName) throws IOException {
        nodeProbe.disableAutoCompaction(keyspaceName);
        nodeProbe.stop("Compaction");
    }

    public void enableAutoCompaction(String keyspaceName) throws IOException {
        nodeProbe.enableAutoCompaction(keyspaceName);
    }



    public String getInternalip() {
        String hostID = nodeProbe.getLocalHostId();
        Map<String, String> ids = nodeProbe.getHostIdMap();
        for (String hostIP : ids.keySet()) {
            if (ids.get(hostIP).equals(hostID)) {
                return hostIP;
            }
        }
        return null;
    }

    /**
     * Get ip for specified host
     *
     * @param host - host name
     * @return - IP of specified host
     * @throws UnknownHostException - in case DNS record is not found
     */
    public String getInetAddress(String host) throws UnknownHostException {
        return InetAddress.getByName(host).getHostAddress();
    }

    public void close() {
        try {
            nodeProbe.close();
        } catch (IOException e) {
            log.error("Cant close nodetool", e);
        }
    }
}
