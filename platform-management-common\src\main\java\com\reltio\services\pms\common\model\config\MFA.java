package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MFA {

    @JsonProperty
    private String status;

    @JsonProperty
    private List<String> availables;

    @JsonCreator
    public MFA(@JsonProperty("status") String status,
               @JsonProperty("availables") List<String> availables) {
        this.status = status;
        this.availables = availables;
    }
}
