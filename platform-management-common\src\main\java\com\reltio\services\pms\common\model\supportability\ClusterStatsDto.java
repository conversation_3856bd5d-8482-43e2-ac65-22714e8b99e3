package com.reltio.services.pms.common.model.supportability;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

/**
 * ClusterModel
 * Created by aravuru
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ClusterStatsDto implements StatsDto {
    @JsonProperty("startTime")
    private Long startTime;
    @JsonProperty("endTime")
    private Long endTime;
    @JsonProperty("cpu")
    private CpuUsage cpu;
    @JsonProperty("memory")
    private Memory memory;
    @JsonProperty("scaling")
    private Scaling scaling;


    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ClusterStatsDto clusterStatsDto = (ClusterStatsDto) o;
        return Objects.equals(getStartTime(), clusterStatsDto.getStartTime()) &&
                Objects.equals(getEndTime(), clusterStatsDto.getEndTime()) &&
                Objects.equals(getCpu().getAverage(), clusterStatsDto.getCpu().getAverage()) &&
                Objects.equals(getCpu().getMax(), clusterStatsDto.getCpu().getMax()) &&
                Objects.equals(getCpu().getP90(), clusterStatsDto.getCpu().getP90()) &&
                Objects.equals(getCpu().getP95(), clusterStatsDto.getCpu().getP95()) &&
                Objects.equals(getCpu().getCpu80().getCount(), clusterStatsDto.getCpu().getCpu80().getCount()) &&
                Objects.equals(getMemory().getPod().getAverage(), clusterStatsDto.getMemory().getPod().getAverage()) &&
                Objects.equals(getMemory().getPod().getMax(), clusterStatsDto.getMemory().getPod().getMax()) &&
                Objects.equals(getMemory().getPod().getP90(), clusterStatsDto.getMemory().getPod().getP90()) &&
                Objects.equals(getMemory().getPod().getP95(), clusterStatsDto.getMemory().getPod().getP95()) &&
                Objects.equals(getMemory().getPod().getMemory80().getCount(), clusterStatsDto.getMemory().getPod().getMemory80().getCount()) &&
                Objects.equals(getMemory().getApp().getAverage(), clusterStatsDto.getMemory().getApp().getAverage()) &&
                Objects.equals(getMemory().getApp().getMax(), clusterStatsDto.getMemory().getApp().getMax()) &&
                Objects.equals(getMemory().getApp().getP90(), clusterStatsDto.getMemory().getApp().getP90()) &&
                Objects.equals(getMemory().getApp().getP95(), clusterStatsDto.getMemory().getApp().getP95()) &&
                Objects.equals(getMemory().getApp().getMemory80().getCount(), clusterStatsDto.getMemory().getApp().getMemory80().getCount()) &&
                Objects.equals(getScaling().getAverage(), clusterStatsDto.getScaling().getAverage()) &&
                Objects.equals(getScaling().getMax(), clusterStatsDto.getScaling().getMax()) &&
                Objects.equals(getScaling().getP90(), clusterStatsDto.getScaling().getP90()) &&
                Objects.equals(getScaling().getP95(), clusterStatsDto.getScaling().getP95()) &&
                Objects.equals(getScaling().getMinReplicas(), clusterStatsDto.getScaling().getMinReplicas()) &&
                Objects.equals(getScaling().getMaxReplicas(), clusterStatsDto.getScaling().getMaxReplicas());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getStartTime(), getEndTime(), getCpu(), getMemory(), getScaling());
    }
}
