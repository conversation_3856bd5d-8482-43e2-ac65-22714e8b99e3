gcloud functions deploy PMS_TRIGGER_JOB_RP_88157 --entry-point com.reltio.pms.trail.TriggerJobsFunction --runtime java11  --trigger-event providers/cloud.firestore/eventTypes/document.create --trigger-resource "projects/data-integration-hub-internal/databases/(default)/documents/PMS_REQUESTERS_tst-01/{message}/OFFER_TYPES/{offer}" --service-account <EMAIL> --set-env-vars "RELTIO_GCP_PROJECT_ID=data-integration-hub-internal,RELTIO_SECRET_NAME=auth_stg,RELTIO_SECRET_VERSION=2,RELTIO_AUTH_URL=https://auth-stg.reltio.com/oauth/token,PMS_JOBS_URL=https://etalon-tst-01-platform-management.reltio.com/api/v1/environments/tst-01/jobs/provisionFreeTierTenant,TRIAL_PIPELINE_ID=verification,GCS_TERMS_PATH=gs://trial-tenant-provis/Terms-Conditions-1.0-********.pdf"
gcloud functions deploy PMS_TRIGGER_JOB_OLGA --entry-point com.reltio.pms.trail.TriggerJobsFunction --project data-integration-hub-internal --runtime java11  --trigger-event providers/cloud.firestore/eventTypes/document.create --trigger-resource "projects/data-integration-hub-internal/databases/(default)/documents/PMS_REQUESTERS_olga-new/{message}/OFFER_TYPES/{offer}" --service-account <EMAIL> --set-env-vars "RELTIO_GCP_PROJECT_ID=data-integration-hub-internal,RELTIO_SECRET_NAME=auth_stg,RELTIO_SECRET_VERSION=2,RELTIO_AUTH_URL=https://auth-stg.reltio.com/oauth/token,PMS_JOBS_URL=https://etalon-tst-01-platform-management.reltio.com/api/v1/environments/envId/jobs/provisionFreeTierTenant,TRIAL_PIPELINE_ID=invite-user-flow,TESTDRIVE_PIPELINE_ID=notif,DEFAULT_ENVIRONMENT=gus-trial,GCS_TERMS_PATH=gs://trial-tenant-provis/Terms-Conditions-1.0-********.pdf"
