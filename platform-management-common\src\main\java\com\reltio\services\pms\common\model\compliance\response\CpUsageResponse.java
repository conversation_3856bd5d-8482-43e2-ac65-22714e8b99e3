package com.reltio.services.pms.common.model.compliance.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class CpUsageResponse {

    @JsonProperty("reportDate")
    private String reportDate;

    /**
     * The Environment.
     */
    @JsonProperty("environment")
    private String environment;

    /**
     * The Tenant id.
     */
    @JsonProperty("tenantId")
    private String tenantId;

}
