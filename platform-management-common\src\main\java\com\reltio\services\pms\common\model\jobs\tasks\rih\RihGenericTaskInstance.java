package com.reltio.services.pms.common.model.jobs.tasks.rih;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServiceEnablementBaseTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.common.sales.RihProducts;
import lombok.Getter;

import java.util.Map;
import java.util.Set;

@Getter
public class RihGenericTaskInstance extends ServiceEnablementBaseTaskInstance {

    @JsonProperty(value = "manifestId")
    private final String manifestId;

    @JsonProperty(value = "rihProduct")
    private final RihProducts rihProduct;

    @JsonProperty(value = "properties")
    private final Map<String, String> properties;


    @JsonCreator
    public RihGenericTaskInstance(@JsonProperty(value = "id") String id,
                                  @JsonProperty(value = "name") String name,
                                  @JsonProperty(value = "envId") String envId,
                                  @JsonProperty(value = "jobId") String jobId,
                                  @JsonProperty(value = "startTime") Long startTime,
                                  @JsonProperty(value = "finishTime") Long finishTime,
                                  @JsonProperty(value = "status") TaskStatus status,
                                  @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                  @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                  @JsonProperty(value = "executingNodeName") String executingNodeName,
                                  @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                  @JsonProperty(value = "failedTenants") Set<String> failedTenants,
                                  @JsonProperty(value = "totalProcessedTenants", defaultValue = "0") int totalProcessedTenants,
                                  @JsonProperty(value = "tenantsForServiceEnablement") Set<String> tenantsForServiceEnablement,
                                  @JsonProperty(value="events") Map<String,Set<String>> events,
                                  @JsonProperty(value = "rihProduct") RihProducts rihProduct,
                                  @JsonProperty(value = "manifestId") String manifestId,
                                  @JsonProperty(value = "properties") Map<String, String> properties
 ) {
        super(id, name, jobId, startTime, finishTime, TaskType.RIH_GENERIC_PROVISION_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId, failedTenants, totalProcessedTenants,
                tenantsForServiceEnablement, events);
        this.manifestId = manifestId;
        this.rihProduct = rihProduct;
        this.properties = properties;
    }

}
