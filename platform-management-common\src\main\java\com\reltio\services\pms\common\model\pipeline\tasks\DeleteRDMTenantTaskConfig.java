package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = false)
public class DeleteRDMTenantTaskConfig extends AbstractPipelineTaskConfig {

    @JsonCreator
    public DeleteRDMTenantTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.DELETE_RDM_TENANT_TASK);
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.RDM;
    }

    @Override
    public String toString() {
        return TaskType.DELETE_RDM_TENANT_TASK + "{}";
    }
}
