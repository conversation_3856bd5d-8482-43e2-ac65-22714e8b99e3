package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.Getter;
import lombok.ToString;

import java.util.Set;

@Getter
@ToString
public class EmailNotificationPipelineTaskConfig extends AbstractPipelineTaskConfig {

    private final String subject;
    private final String body;
    private final Set<String> additionalRecipients;

    @JsonCreator
    public EmailNotificationPipelineTaskConfig(@JsonProperty(value = "name") String name,
                                               @JsonProperty(value = "subject") String subject,
                                               @JsonProperty(value = "body") String body,
                                               @JsonProperty(value = "additionalRecipients") Set<String> additionalRecipients) {
        super(name, TaskType.EMAIL_NOTIFICATION_TASK);
        this.subject = subject;
        this.body = body;
        this.additionalRecipients = additionalRecipients;
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.EMAIL_NOTIFICATION;
    }

}
