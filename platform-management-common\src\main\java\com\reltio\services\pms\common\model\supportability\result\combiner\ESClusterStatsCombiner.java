package com.reltio.services.pms.common.model.supportability.result.combiner;

import com.reltio.services.pms.common.model.supportability.CpuUsage;
import com.reltio.services.pms.common.model.supportability.ESClusterStatsDto;
import com.reltio.services.pms.common.model.supportability.Indexation;
import com.reltio.services.pms.common.model.supportability.IndexationStats;
import com.reltio.services.pms.common.model.supportability.MemoryUsage;
import com.reltio.services.pms.common.model.supportability.result.StatsCombiner;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;

/**
 * The type Event stats combiner.
 */
public class ESClusterStatsCombiner implements StatsCombiner<ESClusterStatsDto> {
    /**
     * mergeAllRestRows method is used to merge all the provided rows from GBQ into single record by sum/avg based on the column in GBQ.
     *
     * @param dtoList the dto list
     * @return the list
     */
    @Override
    public ESClusterStatsDto mergeAllRows(List<ESClusterStatsDto> dtoList) {
        ESClusterStatsDto response = dtoList.get(NumberUtils.INTEGER_ZERO);
        ESClusterStatsDto lastResponse = dtoList.get(dtoList.size() - NumberUtils.INTEGER_ONE);
        long startTime = response.getStartTime();
        long endTime = lastResponse.getEndTime();
        int totalRows = dtoList.size();
        float cpuSum = NumberUtils.FLOAT_ZERO;
        float cpuMax = NumberUtils.FLOAT_ZERO;
        float cpuP90Sum = NumberUtils.FLOAT_ZERO;
        float cpuP95Sum = NumberUtils.FLOAT_ZERO;
        float memorySum = NumberUtils.FLOAT_ZERO;
        float memoryMax = NumberUtils.FLOAT_ZERO;
        float memoryP90Sum = NumberUtils.FLOAT_ZERO;
        float memoryP95Sum = NumberUtils.FLOAT_ZERO;

        long clusterSuccess = NumberUtils.LONG_ZERO;
        long clusterFail = NumberUtils.LONG_ZERO;
        long clusterThr = NumberUtils.LONG_ZERO;
        long tenantSuccess = NumberUtils.LONG_ZERO;
        long tenantFail = NumberUtils.LONG_ZERO;
        long tenantThr = NumberUtils.LONG_ZERO;


        for (ESClusterStatsDto dto : dtoList) {
            cpuSum += dto.getCpu().getAverage();
            cpuMax = Math.max(dto.getCpu().getMax(), cpuMax);
            cpuP90Sum += dto.getCpu().getP90();
            cpuP95Sum += dto.getCpu().getP95();
            memorySum += dto.getMemoryUsage().getAverage();
            memoryMax = Math.max(dto.getMemoryUsage().getMax(), memoryMax);
            memoryP90Sum += dto.getMemoryUsage().getP90();
            memoryP95Sum += dto.getMemoryUsage().getP95();
            clusterSuccess += dto.getIndexation().getCluster().getSuccess();
            clusterFail += dto.getIndexation().getCluster().getFailed();
            clusterThr += dto.getIndexation().getCluster().getThrottled();
            tenantSuccess += dto.getIndexation().getTenant().getSuccess();
            tenantFail += dto.getIndexation().getTenant().getFailed();
            tenantThr += dto.getIndexation().getTenant().getThrottled();
        }
        Float cpuAvg = roundOff2Digits(cpuSum / totalRows);
        Float cpuP90Avg = roundOff2Digits(cpuP90Sum / totalRows);
        Float cpuP95Avg = roundOff2Digits(cpuP95Sum / totalRows);
        CpuUsage cpuUsage = new CpuUsage(cpuAvg, roundOff2Digits(cpuMax), cpuP90Avg, cpuP95Avg, null);

        Float memoryAvg = roundOff2Digits(memorySum / totalRows);
        Float podMemoryP90Avg = roundOff2Digits(memoryP90Sum / totalRows);
        Float podMemoryP95Avg = roundOff2Digits(memoryP95Sum / totalRows);

        MemoryUsage memoryUsage = new MemoryUsage(memoryAvg, roundOff2Digits(memoryMax), podMemoryP90Avg, podMemoryP95Avg, null);

        ESClusterStatsDto dto = new ESClusterStatsDto();
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setCpu(cpuUsage);
        dto.setMemoryUsage(memoryUsage);
        dto.setIndexation(Indexation.builder()
                .cluster(IndexationStats.builder()
                        .success(clusterSuccess)
                        .throttled(clusterThr)
                        .failed(clusterFail)
                        .build())
                .tenant(IndexationStats.builder()
                        .success(tenantSuccess)
                        .failed(tenantFail)
                        .throttled(tenantThr)
                        .build())
                .build()
        );

        return dto;
    }
}
