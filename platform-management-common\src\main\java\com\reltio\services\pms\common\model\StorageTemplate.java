package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

import java.util.Objects;

@Getter
public class StorageTemplate extends BaseFirestoreEntity {

    @JsonProperty("environment")
    private String environment;

    @JsonProperty("name")
    private String name;

    @JsonProperty("matchHost")
    private String matchHost;

    @JsonProperty("matchCluster")
    private String matchCluster;

    @JsonProperty("dataHost")
    private String dataHost;

    @JsonProperty("dataCluster")
    private String dataCluster;

    @JsonProperty("historyProject")
    private String historyProject;

    @JsonProperty("historyInstance")
    private String historyInstance;

    @JsonProperty("historyTableName")
    private String historyTableName;

    @JsonProperty("matchStorageId")
    private String matchStorageId;

    @JsonProperty("esHost")
    private String esHost;

    @JsonProperty("esCluster")
    private String esCluster;

    @JsonProperty("indexOvStrategy")
    private String indexOvStrategy;

    @JsonProperty("matchingStrategy")
    private String matchingStrategy;

    @JsonProperty("dataloadUrl")
    private String dataLoadUrl;

    @JsonCreator
    public StorageTemplate(@JsonProperty("environment") String environment,
                           @JsonProperty("name") String name,
                           @JsonProperty("matchHost") String matchHost,
                           @JsonProperty("matchCluster") String matchCluster,
                           @JsonProperty("dataHost") String dataHost,
                           @JsonProperty("dataCluster") String dataCluster,
                           @JsonProperty("historyProject") String historyProject,
                           @JsonProperty("historyInstance") String historyInstance,
                           @JsonProperty("historyTableName") String historyTableName,
                           @JsonProperty("esHost") String esHost,
                           @JsonProperty("esCluster") String esCluster,
                           @JsonProperty("indexOvStrategy") String indexOvStrategy,
                           @JsonProperty("matchingStrategy") String matchingStrategy,
                           @JsonProperty("dataloadUrl") String dataLoadUrl) {
        this.environment = environment;
        this.name = name;
        this.matchHost = matchHost;
        this.matchCluster = matchCluster;
        this.dataHost = dataHost;
        this.dataCluster = dataCluster;
        this.historyProject = historyProject;
        this.historyInstance = historyInstance;
        this.historyTableName = historyTableName;
        this.esHost = esHost;
        this.esCluster = esCluster;
        this.indexOvStrategy = indexOvStrategy;
        this.matchingStrategy = matchingStrategy;
        this.dataLoadUrl = dataLoadUrl;
    }

    @Override
    public String getID() {
        return name;
    }

    public void setMatchHost(String matchHost) {
        this.matchHost = matchHost;
    }

    public void setMatchCluster(String matchCluster) {
        this.matchCluster = matchCluster;
    }

    public void setDataHost(String dataHost) {
        this.dataHost = dataHost;
    }

    public void setDataCluster(String dataCluster) {
        this.dataCluster = dataCluster;
    }

    public void setEsHost(String esHost) {
        this.esHost = esHost;
    }

    public void setEsCluster(String esCluster) {
        this.esCluster = esCluster;
    }

    public void setDataLoadUrl(String dataLoadUrl) {
        this.dataLoadUrl = dataLoadUrl;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        StorageTemplate template = (StorageTemplate) o;

        if (!Objects.equals(environment, template.environment)) {
            return false;
        }
        return Objects.equals(name, template.name);
    }

    @Override
    public int hashCode() {
        int result = environment != null ? environment.hashCode() : 0;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        return result;
    }
}
