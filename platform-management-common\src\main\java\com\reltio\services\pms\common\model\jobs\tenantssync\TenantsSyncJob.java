package com.reltio.services.pms.common.model.jobs.tenantssync;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.jobs.Job;
import com.reltio.services.pms.common.model.jobs.JobStatus;
import com.reltio.services.pms.common.model.pipeline.SkipReason;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantPurpose;

import java.util.EnumMap;
import java.util.EnumSet;
import java.util.List;
import java.util.Objects;


public class TenantsSyncJob extends Job {
    @JsonProperty(value = "tenantPurpose")
    private TenantPurpose tenantPurpose;

    @JsonProperty(value = "tenantsList")
    private List<String> tenantsList;

    public TenantsSyncJob(String parentJobId,String jobId, String pipelineId, Long startTime, Long finishTime, JobStatus status, List<String> tasks,
                          String envId, TenantPurpose tenantPurpose, List<String> tenantsList,
                          EnumMap<SkipReason, EnumSet<PMSProductName>> skippedTasks,List<String> owners) {
        super(parentJobId,jobId, pipelineId, startTime, finishTime, status, tasks, envId, skippedTasks,owners,false);
        this.tenantPurpose = tenantPurpose;
        this.tenantsList = tenantsList;

    }

    public TenantPurpose getTenantPurpose() {
        return tenantPurpose;
    }

    public List<String> getTenantsList() {
        return tenantsList;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        TenantsSyncJob tenantsSyncJob = (TenantsSyncJob) o;
        return tenantPurpose == tenantsSyncJob.tenantPurpose &&
                tenantsList == tenantsSyncJob.tenantsList &&
                Objects.equals(tenantPurpose, ((TenantsSyncJob) o).getTenantPurpose()) &&
                Objects.equals(tenantsList, ((TenantsSyncJob) o).getTenantsList());

    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), tenantPurpose, tenantsList);
    }

    @Override
    public String toString() {
        return "TenantsSyncJob{" +
                "tenantPurpose='" + tenantPurpose + '\'' +
                ", tenantsList=" + tenantsList +
                '}';
    }
}
