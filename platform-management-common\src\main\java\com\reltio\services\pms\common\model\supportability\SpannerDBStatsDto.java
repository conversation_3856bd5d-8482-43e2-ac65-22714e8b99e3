package com.reltio.services.pms.common.model.supportability;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * SpannerDBStatsDto
 * Created by apylkov
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
@Builder
public class SpannerDBStatsDto implements StatsDto {
    private Long startTime;
    private Long endTime;
    private String projectId;
    private String databaseId;
    private Float cpu;
    private Integer nodesCount;
    private Integer sessionCount;
    private Float storageUtilization;
    private Float lockWaitTime;
    private Float cpuTime;
    private Long queryCount;
    private Long apiRequestCount;
    private Long failedExecutionCount;
    private Float queryLatencies;
}
