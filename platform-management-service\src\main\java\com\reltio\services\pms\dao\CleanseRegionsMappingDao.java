package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.CollectionReference;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.config.CleanseRegionsMapping;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import java.util.Collections;
import java.util.Map;
import java.util.Set;

@Service
public class CleanseRegionsMappingDao extends AbstractRootCollectionDao<CleanseRegionsMapping> {

    private static final String CONFIG_COLLECTION_NAME = "PMS_CONFIG";
    private static final Logger LOGGER = Logger.getLogger(CleanseRegionsMappingDao.class);

    @Autowired
    public CleanseRegionsMappingDao(CredentialsProvider provider,
                                    @Value("${firestore.env.name}") String deployedEnv,
                                    ReltioUserHolder reltioUserHolder) {
        super(provider, CONFIG_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    protected TypeReference<CleanseRegionsMapping> getTypeReference() {
        return new TypeReference<CleanseRegionsMapping>() {
        };
    }

    @Override
    protected CleanseRegionsMapping update(CollectionReference collectionReference, CleanseRegionsMapping entity) {
        String username = getUsername();
        Long time = System.currentTimeMillis();
        entity.setUpdatedBy(username);
        entity.setUpdatedTime(time);
        return createOrUpdate(collectionReference, entity);

    }

    private CleanseRegionsMapping createOrUpdate(CollectionReference collectionReference, CleanseRegionsMapping entity) {
        try {
            String id = entity.getID();
            Map<String, Object> objectMap = convertToMap(entity);
            collectionReference.document(id).set(objectMap);
            return entity;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }

}
