package com.reltio.services.pms.common.model.compliance.response.rsuusage;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class RsuBreakdownModel {

    /**
     * The Report date.
     */
    @JsonProperty("reportDate")
    private String date;

    /**
     * The Tenant ID.
     */
    @JsonProperty("tenantId")
    private String tenantId;

    /**
     * The Tenant Purpose.
     */
    @JsonProperty("tenantPurpose")
    private String tenantPurpose;

    /**
     * The total rsu.
     */
    @JsonProperty("totalSize")
    String totalSize;

    /**
     * The primary data.
     */
    @JsonProperty("primaryData")
    String primaryData;

    /**
     * The history data.
     */
    @JsonProperty("historyData")
    String historyData;

    /**
     * The activity log.
     */
    @JsonProperty("activityLog")
    String activityLog;

    /**
     * The match data.
     */
    @JsonProperty("matchData")
    String matchData;

    /**
     * The index data.
     */
    @JsonProperty("indexData")
    String indexData;

    /**
     * The interaction size.
     */
    @JsonProperty("interactionSize")
    String interactionSize;

}
