package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import lombok.Getter;

import java.util.Collections;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class BulkServicesEnablementJobRequest extends TenantCreationRequest {

    private final Set<String> mdmTenants;
    private final String rdmTenantId;

    @JsonProperty("configureDefaultQueue")
    private final boolean configureDefaultQueue;

    @JsonProperty("streamingCloud")
    private final String streamingCloud;
    @JsonProperty("tenantPurpose")
    private final TenantPurpose tenantPurpose;
    @JsonProperty(value = "cleanseRegions")
    private final List<String> cleanseRegions;
    @JsonProperty(value = "tenantSize")
    private final TenantSize tenantSize;
    @JsonProperty(value = "rih")
    private final Map<String, String> rihKey;
    @JsonProperty(value = "rdmHostNameUrl")
    private final String rdmHostNameUrl;
    @JsonProperty("offerType")
    private final String offerType;
    @JsonProperty(value = "loqateProcesses")
    private final String loqateProcesses;

    @JsonProperty(value = "queueNames")
    private final Set<String> queueNames;

    @JsonProperty(value = "awsAccountId")
    @Getter
    private String awsAccountId;

    @JsonProperty(value = "gitBranchName")
    @Getter
    private String gitBranchName;

    public BulkServicesEnablementJobRequest(@JsonProperty("pipelineId") String pipelineId,
                                            @JsonProperty("mdmTenants") Set<String> mdmTenants,
                                            @JsonProperty("rdmTenantId") String rdmTenantId,
                                            @JsonProperty("configureDefaultQueue") boolean configureDefaultQueue,
                                            @JsonProperty("streamingCloud") String streamingCloud,
                                            @JsonProperty("offerType") String offerType,
                                            @JsonProperty("tenantPurpose") TenantPurpose tenantPurpose,
                                            @JsonProperty("loqateProcesses") String loqateProcesses,
                                            @JsonProperty("cleanseRegions") List<String> cleanseRegions,
                                            @JsonProperty("tenantSize") TenantSize tenantSize,
                                            @JsonProperty(value = "rih") Map<String, String> rihKey,
                                            @JsonProperty(value = "rdmHostNameUrl") String rdmHostNameUrl,
                                            @JsonProperty(value = "queueNames") Set<String> queueNames,
                                            @JsonProperty(value = "reltioPackageType") ReltioPackageType reltioPackageType,
                                            @JsonProperty(value = "skipTasks") EnumSet<PMSProductName> skipTasks) {
        super(pipelineId, null, null, false, null, null, reltioPackageType, skipTasks);
        this.mdmTenants = mdmTenants;
        this.rdmTenantId = rdmTenantId;
        this.configureDefaultQueue = configureDefaultQueue;
        this.streamingCloud = streamingCloud;
        this.offerType = offerType;
        this.tenantPurpose = tenantPurpose;
        this.loqateProcesses = loqateProcesses;
        this.cleanseRegions = cleanseRegions;
        this.tenantSize = tenantSize;
        this.rihKey = rihKey == null ? Collections.emptyMap() : rihKey;
        this.rdmHostNameUrl = rdmHostNameUrl;
        this.queueNames = queueNames;
    }

    public Set<String> getMdmTenants() {
        return mdmTenants;
    }

    public String getRdmTenantId() {
        return rdmTenantId;
    }

    public boolean isConfigureDefaultQueue() {
        return configureDefaultQueue;
    }


    public String getStreamingCloud() {
        return streamingCloud;
    }

    public String getOfferType() {
        return offerType;
    }

    public TenantPurpose getTenantPurpose() {
        return tenantPurpose;
    }

    public String getLoqateProcesses() {
        return loqateProcesses;
    }

    public List<String> getCleanseRegions() {
        return cleanseRegions;
    }

    public TenantSize getTenantSize() {
        return tenantSize;
    }

    public String getRdmHostNameUrl() {
        return rdmHostNameUrl;
    }

    public Set<String> getQueueNames() {
        return queueNames;
    }

    public Map<String, String> getRihKey() {
        return rihKey;
    }

}
