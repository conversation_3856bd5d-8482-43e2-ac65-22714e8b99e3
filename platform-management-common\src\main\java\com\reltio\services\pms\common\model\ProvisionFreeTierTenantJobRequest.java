package com.reltio.services.pms.common.model;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import lombok.Getter;

import java.util.EnumSet;

@Getter
public class ProvisionFreeTierTenantJobRequest extends TenantCreationRequest {

    private final String requesterEmail;

    private final String linkTermsFile;

    private final String offerType;

    private final TenantPurpose tenantPurpose;

    private final CustomerType customerType;

    private final String division;

    private final String department;

    private final String costCenter;

    private final String endDate;

    public ProvisionFreeTierTenantJobRequest(@JsonProperty(value = "pipelineId", required = true) String pipelineId,
                                             @JsonProperty(value = "requesterEmail", required = true) String requesterEmail,
                                             @JsonProperty("linkTermsFile") String linkTermsFile,
                                             @JsonProperty("offerType") String offerType,
                                             @JsonProperty("tenantPurpose") TenantPurpose tenantPurpose,
                                             @JsonProperty(value = "customerType") CustomerType customerType,
                                             @JsonProperty(value = "division") String division,
                                             @JsonProperty(value = "department") String department,
                                             @JsonProperty(value = "costCenter") String costCenter,
                                             @JsonProperty(value = "endDate") String endDate,
                                             @JsonProperty(value = "reltioPackageType") ReltioPackageType reltioPackageType,
                                             @JsonProperty("skipTasks") EnumSet<PMSProductName> skipTasks,
                                             @JsonProperty("newInstanceId") String newInstanceId,
                                             @JsonProperty("useSpannerCloudFunction") Boolean useSpannerCloudFunction) {
        super(pipelineId, null, null, null, newInstanceId, newInstanceId != null ? Boolean.FALSE : useSpannerCloudFunction, reltioPackageType, skipTasks);
        this.requesterEmail = requesterEmail;
        this.linkTermsFile = linkTermsFile;
        this.offerType = offerType;
        this.tenantPurpose = tenantPurpose == null ? TenantPurpose.FREE : tenantPurpose;
        this.customerType = customerType == null ? CustomerType.customerTypeByPurpose(this.tenantPurpose) : customerType;
        this.division = division;
        this.department = department;
        this.costCenter = costCenter;
        this.endDate = endDate;
    }
}
