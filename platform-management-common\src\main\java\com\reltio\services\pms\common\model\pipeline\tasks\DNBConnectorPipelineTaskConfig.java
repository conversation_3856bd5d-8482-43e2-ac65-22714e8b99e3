package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.enterprise.contracts.DTSSType;
import com.reltio.services.pms.common.sales.PMSProductName;

import static com.reltio.services.pms.common.model.pipeline.tasks.TaskType.DNB_CONNECTOR_TASK;
import static com.reltio.services.pms.common.model.pipeline.tasks.TaskType.DNB_CONNECTOR_TASK_NAME;

public class DNBConnectorPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonProperty
    private final String dataTenantId;

    @JsonProperty
    private final DTSSType dtssSubscriptionType;

    @JsonCreator
    public DNBConnectorPipelineTaskConfig(@JsonProperty(value = "name") String name,
                                          @JsonProperty(value = "dataTenantId") String dataTenantId,
                                          @JsonProperty(value = "dtssSubscriptionType") DTSSType dtssSubscriptionType
                                          ) {
        super(name, DNB_CONNECTOR_TASK);
        this.dataTenantId = dataTenantId;
        this.dtssSubscriptionType = dtssSubscriptionType;
    }

    @Override
    public String toString() {
        return DNB_CONNECTOR_TASK_NAME + "{}";
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.DNB_CONNECTOR;
    }
    @Override
    public boolean visibleInContract() {
        return true;
    }

    public String getDataTenantId() {
        return dataTenantId;
    }

    public DTSSType getDtssSubscriptionType() {
        return dtssSubscriptionType;
    }
}
