package com.reltio.services.pms.common.model.jobs.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.ServiceNodeStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

import java.util.Objects;

public class MultiSleepTaskInstance extends TaskInstance {
    private final Long sleepTime;
    private final Long noOfSleep;
    private Long noOfSleepAlreadySleep;

    @JsonCreator
    public MultiSleepTaskInstance(@JsonProperty(value = "id") String id,
                                  @JsonProperty(value = "name") String name,
                                  @JsonProperty(value = "jobId") String jobId,
                                  @JsonProperty(value = "startTime") Long startTime,
                                  @JsonProperty(value = "finishTime") Long finishTime,
                                  @JsonProperty(value = "status") TaskStatus status,
                                  @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                  @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                  @JsonProperty(value = "executingNodeName") String executingNodeName,
                                  @JsonProperty(value = "serviceNodeStatus") ServiceNodeStatus serviceNodeStatus,
                                  @JsonProperty(value = "sleepTime") Long sleepTime,
                                  @JsonProperty(value = "noOfSleep") Long noOfSleep,
                                  @JsonProperty(value = "noOfSleepAlreadySleep") Long noOfSleepAlreadySleep,
                                  @JsonProperty(value = "envId") String envId) {
        super(id, name, jobId, startTime, finishTime, TaskType.MULTI_SLEEP_TASK, status, lastUpdatedTime, taskFailureContext,
                executingNodeName, serviceNodeStatus, envId);
        this.sleepTime = sleepTime;
        this.noOfSleep = noOfSleep;
        this.noOfSleepAlreadySleep = noOfSleepAlreadySleep;
    }

    public Long getSleepTime() {
        return sleepTime;
    }

    public Long getNoOfSleep() {
        return noOfSleep;
    }

    public Long getNoOfSleepAlreadySleep() {
        return noOfSleepAlreadySleep;
    }

    public void setNoOfSleepAlreadySleep(Long noOfSleepAlreadySleep) {
        this.noOfSleepAlreadySleep = noOfSleepAlreadySleep;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MultiSleepTaskInstance)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        MultiSleepTaskInstance that = (MultiSleepTaskInstance) o;
        return Objects.equals(getSleepTime(), that.getSleepTime()) &&
                Objects.equals(getNoOfSleep(), that.getNoOfSleep()) &&
                Objects.equals(getNoOfSleepAlreadySleep(), that.getNoOfSleepAlreadySleep());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getSleepTime(), getNoOfSleep(), getNoOfSleepAlreadySleep());
    }

    @Override
    public String toString() {
        return "MultiSleepTaskDetail{" +
                "sleepTime=" + sleepTime +
                ", noOfSleep=" + noOfSleep +
                ", noOfSleepAlreadySleep=" + noOfSleepAlreadySleep +
                "} " + super.toString();
    }
}
