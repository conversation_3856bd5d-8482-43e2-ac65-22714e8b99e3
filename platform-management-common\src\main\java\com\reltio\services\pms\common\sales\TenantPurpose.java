package com.reltio.services.pms.common.sales;

import java.util.Arrays;
import java.util.Locale;

public enum TenantPurpose {
    DEV("Dev"),
    TEST("Test"),
    PROD("Prod"),

    FREE("Free"),
    POC("Poc"),
    TRAINING("Training"),
    INTERNAL("Internal"),
    UNKNOWN("Unknown"),
    TESTDRIVE("Testdrive");

    private final String cloudUserMarker;

    TenantPurpose(String cloudUserMarker) {
        this.cloudUserMarker = cloudUserMarker;
    }

    public String getCloudUserMarker() {
        return cloudUserMarker;
    }

    public static boolean isEnterprise(TenantPurpose tenantPurpose) {
        return DEV == tenantPurpose || TEST == tenantPurpose || PROD == tenantPurpose;
    }

    public String getStreamingPrefix() {
        if (this == TEST || this == DEV) {
            return "devtest";
        } else {
            return cloudUserMarker;
        }
    }

    public static TenantPurpose fromString(String purpose) {
        if (purpose == null) {
            return null;
        }
        return Arrays.stream(TenantPurpose.values())
                .filter(v -> purpose.toLowerCase(Locale.ROOT)
                        .contains(v.name().toLowerCase(Locale.ROOT))).findFirst().orElse(null);
    }
}
