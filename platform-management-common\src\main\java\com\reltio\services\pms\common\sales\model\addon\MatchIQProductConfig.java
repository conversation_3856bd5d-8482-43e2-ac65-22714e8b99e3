package com.reltio.services.pms.common.sales.model.addon;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.jobs.tasks.matchiq.MatchIQTenantTaskState;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class MatchIQProductConfig extends BaseProductConfig {

    @JsonProperty("matchIQTenantTaskState")
    private MatchIQTenantTaskState matchIQTenantTaskState;

    public MatchIQProductConfig() {
        this.pmsProductName = PMSProductName.MATCHIQ;
    }
}