package com.reltio.services.pms.common.model.supportability;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class MemoryUsage {
    @JsonProperty("average")
    private Float average;
    @JsonProperty("max")
    private Float max;
    @JsonProperty("p90")
    private Float p90;
    @JsonProperty("p95")
    private Float p95;
    @JsonProperty("memory80")
    private PodNamesAbove80 memory80;
}
