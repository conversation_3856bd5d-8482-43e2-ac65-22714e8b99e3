package com.reltio.services.pms.convertors;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import org.apache.log4j.Logger;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

public final class UtilDeserializer {

    public static final ObjectMapper objectMapper = new CustomObjectMapper();
    private static final Logger LOG = Logger.getLogger(UtilDeserializer.class);
    private static Map<TenantSize, Integer> upperBorderSizeMap = ImmutableMap.<TenantSize, Integer>builder()
            .put(TenantSize.XX_SMALL, 10_000)
            .put(TenantSize.X_SMALL, 200_000)
            .put(TenantSize.SMALL, 1_000_000)
            .put(TenantSize.PRE_MEDIUM, 5_000_000)
            .put(TenantSize.MEDIUM, 10_000_000)
            .put(TenantSize.LARGE, 100_000_000)
            .put(TenantSize.X_LARGE, 300_000_000)
            .put(TenantSize.XX_LARGE, 700_000_000)
            .build();

    public static String getField(String fieldName, JsonNode node) {
        return node != null && node.hasNonNull(fieldName) ? node.get(fieldName).asText() : null;
    }

    public static Date getDateField(String fieldName, JsonNode node) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
        String fieldValue = getField(fieldName, node);
        try {
            return fieldValue != null ? sdf.parse(fieldValue) : null;
        } catch (ParseException parseException) {
            LOG.info("Error has occurred while parsing date field.", parseException);
        }
        return null;
    }

    public static Boolean getBooleanField(String fieldName, JsonNode node) {
        String fieldValue = getField(fieldName, node);
        return Boolean.valueOf(fieldValue);
    }

    public static Long getLongField(String fieldName, JsonNode node) {
        return node != null && node.hasNonNull(fieldName) ? node.get(fieldName).asLong() : null;
    }

    public static final <T> Set<T> newHashSet(T... objects) {
        Set<T> set = new HashSet<T>();
        Collections.addAll(set, objects);
        return set;
    }

    public static TenantSize getTenantSize(Integer count) {
        TenantSize tenantSize;
        if (count < upperBorderSizeMap.get(TenantSize.XX_SMALL)) {
            tenantSize = TenantSize.XX_SMALL;
        } else if (count < upperBorderSizeMap.get(TenantSize.X_SMALL)) {
            tenantSize = TenantSize.X_SMALL;
        } else if (count < upperBorderSizeMap.get(TenantSize.SMALL)) {
            tenantSize = TenantSize.SMALL;
        } else if (count < upperBorderSizeMap.get(TenantSize.PRE_MEDIUM)) {
            tenantSize = TenantSize.PRE_MEDIUM;
        } else if (count < upperBorderSizeMap.get(TenantSize.MEDIUM)) {
            tenantSize = TenantSize.MEDIUM;
        } else if (count < upperBorderSizeMap.get(TenantSize.LARGE)) {
            tenantSize = TenantSize.LARGE;
        } else if (count < upperBorderSizeMap.get(TenantSize.X_LARGE)) {
            tenantSize = TenantSize.X_LARGE;
        } else if (count < upperBorderSizeMap.get(TenantSize.XX_LARGE)) {
            tenantSize = TenantSize.XX_LARGE;
        } else {
            tenantSize = TenantSize.XXX_LARGE;
        }
        return tenantSize;
    }
}
