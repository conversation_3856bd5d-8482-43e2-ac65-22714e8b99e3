package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;

public class FernDeProvisionPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonCreator
    public FernDeProvisionPipelineTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.FERN_DE_PROVISION_TASK);
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.FERN;
    }
}
