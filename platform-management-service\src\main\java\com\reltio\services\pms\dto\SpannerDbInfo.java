package com.reltio.services.pms.dto;

public class SpannerDbInfo {
    private String envId;
    private String tenantId;
    private String projectId;
    private String instanceId;
    private String databaseId;

    public SpannerDbInfo() {

    }

    public SpannerDbInfo(String envId, String tenantId, String projectId, String instanceId, String databaseId) {
        this.envId = envId;
        this.tenantId = tenantId;
        this.projectId = projectId;
        this.instanceId = instanceId;
        this.databaseId = databaseId;
    }

    public String getEnvId() {
        return envId;
    }

    public void setEnvId(String envId) {
        this.envId = envId;
    }

    public String getDatabaseId() {
        return databaseId;
    }

    public void setDatabaseId(String databaseId) {
        this.databaseId = databaseId;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}