package com.reltio.services.pms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.reltio.cloud.spanner.DatabaseInfo;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class SpannerDatabaseStatus {

    @JsonProperty("status")
    private DatabaseInfo.State status;

    @JsonProperty("message")
    private String message;

    public SpannerDatabaseStatus(DatabaseInfo.State status){
        this.status = status;
    }

    public SpannerDatabaseStatus(DatabaseInfo.State status, String message) {
        this.status = status;
        this.message = message;
    }

    public DatabaseInfo.State getStatus() { return status; }
    public void setStatus(DatabaseInfo.State status) { this.status = status; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
}