package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.StorageTemplate;
import org.springframework.stereotype.Service;

@Service
public class StorageTemplateDao extends AbstractLevel1CollectionDao<StorageTemplate> {

    private static final String STORAGE_TEMPLATE_COLLECTION_NAME = "STORAGE_TEMPLATES";

    public StorageTemplateDao(CredentialsProvider provider, EnvironmentDao environmentDao, ReltioUserHolder reltioUserHolder) {
        super(provider, environmentDao, STORAGE_TEMPLATE_COLLECTION_NAME, reltioUserHolder);
    }

    @Override
    protected TypeReference<StorageTemplate> getTypeReference() {
        return new TypeReference<StorageTemplate>() {
        };
    }
}
