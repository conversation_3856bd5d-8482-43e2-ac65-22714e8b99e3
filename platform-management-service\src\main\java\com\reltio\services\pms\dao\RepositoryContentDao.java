package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.Query;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.RepositoryContent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * The type Repository content dao.
 */
@Service
public class RepositoryContentDao extends AbstractLevel1CollectionDao<RepositoryContent> {
    private static final String REPOSITORY_CONTENT_NAME = "REPOSITORY_CONTENT";

    /**
     * Instantiates a new Repository content dao.
     *
     * @param provider         the provider
     * @param repositoryDao    the repository dao
     * @param reltioUserHolder the reltio user holder
     */
    @Autowired
    public RepositoryContentDao(CredentialsProvider provider,
                                RepositoryDao repositoryDao, ReltioUserHolder reltioUserHolder) {
        super(provider, repositoryDao, REPOSITORY_CONTENT_NAME, reltioUserHolder);
    }

    @Override
    protected TypeReference<RepositoryContent> getTypeReference() {
        return new TypeReference<RepositoryContent>() {
        };
    }


    /**
     * Gets repo content by file.
     *
     * @param repoName   the repo name
     * @param branchName the branch name
     * @param path       the field name
     * @param fileName   the field value
     * @return the repo content by file
     */
    public Collection<RepositoryContent> getRepoContentByFile(String repoName, String branchName, String path, String fileName) {
        Query query = getBaseCollection(String.format("%s@%s", repoName, branchName)).whereEqualTo("path", path).whereEqualTo("fileName", fileName);
        return getResultFromQuery(query);
    }


    /**
     * Gets repo contents.
     *
     * @param repoName   the repo name
     * @param branchName the branch name
     * @param fieldName  the field name
     * @param fieldValue the field value
     * @return the repo contents
     */
    public Collection<RepositoryContent> getRepoContents(String repoName, String branchName, String fieldName, String fieldValue) {
        String directoryPathWithSlash = fieldValue + "/";
        String directoryPathWithSuffix = fieldValue + "\ufffd";
        Query query = getBaseCollection(String.format("%s@%s", repoName, branchName)).whereGreaterThanOrEqualTo(fieldName, directoryPathWithSlash)
                .whereLessThan(fieldName, directoryPathWithSuffix);
        return getResultFromQuery(query);
    }

    public Collection<RepositoryContent> getRepoContentsPaginated(String repoName,
                                                                  String branchName,
                                                                  String fieldName,
                                                                  String fieldValue,
                                                                  int page,
                                                                  int pageSize) {
        String lowerBound = fieldValue + "/";
        String upperBound = fieldValue + "\ufffd";

        Query query = getBaseCollection(String.format("%s@%s", repoName, branchName))
                .whereGreaterThanOrEqualTo(fieldName, lowerBound)
                .whereLessThan(fieldName, upperBound)
                .limit(pageSize)
                .offset(page * pageSize);

        return getResultFromQuery(query);
    }
}
