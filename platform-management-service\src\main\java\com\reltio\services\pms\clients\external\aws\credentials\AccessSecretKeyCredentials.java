package com.reltio.services.pms.clients.external.aws.credentials;

import com.reltio.services.pms.validator.PropertiesValidator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.apache.log4j.Logger;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Component
@Primary
public class AccessSecretKeyCredentials implements PlatformManagementAwsCredentials {

    private final String accessKey;
    private final String secretKey;
    private final String region;
    private final String awsRoleArn;
    private static final Logger LOGGER = Logger.getLogger(AccessSecretKeyCredentials.class);

    public AccessSecretKeyCredentials(
            @Value("${aws.access.key}") String accessKey,
            @Value("${aws.secret.key}") String secretKey,
            @Value("${aws.region}") String region,
            @Value("${aws.access.awsIAMRole}") String awsRoleArn) {
        this.accessKey = accessKey;
        this.secretKey = secretKey;
        this.region = region;
        this.awsRoleArn = awsRoleArn;
        Set<String> properties=new HashSet<>(Arrays.asList(accessKey,secretKey,region));
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    public String accessKeyId() {
        return accessKey;
    }

    @Override
    public String secretAccessKey() {
        return secretKey;
    }

    @Override
    public String awsRoleArn(){
        return awsRoleArn;
    }

    @Override
    public String getAWSRegion() {
        return region;
    }
}
