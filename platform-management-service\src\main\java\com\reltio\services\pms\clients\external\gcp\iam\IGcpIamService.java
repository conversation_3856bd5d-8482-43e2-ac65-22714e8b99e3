package com.reltio.services.pms.clients.external.gcp.iam;

import com.google.api.services.iam.v1.model.ServiceAccount;
import com.google.api.services.iam.v1.model.ServiceAccountKey;
import com.google.iam.v1.Policy;

import java.io.IOException;
import java.util.Map;
import java.util.Set;

public interface IGcpIamService {
    ServiceAccount createServiceAccount(String projectId, String serviceAccountName);

    ServiceAccountKey createServiceAccountKey(String projectId, String serviceAccountName);

    Boolean isAccountExist(String projectId, String serviceAccountName);

    void assignTopicPermission(String projectId, String resourceName, String member, boolean clean) throws IOException;

    void assignSubscriptionPermission(String projectId, String resourceName, String member, boolean clean) throws IOException;

    Policy getSubscriptionPolicy(String projectId, String resourceName) throws IOException;

    Policy getTopicPolicy(String projectId, String resourceName) throws IOException;

    Map<GCPResourceType, Set<String>> getGCPResourceRoles();

}
