package com.reltio.services.pms.common.model.supportability;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Objects;

/**
 * EventModel
 * Created by aravuru
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ProcessorTypeGroupEventStatsDto implements StatsDto {
    @JsonProperty("startTime")
    private Long startTime;
    @JsonProperty("endTime")
    private Long endTime;
    @JsonProperty("totalCount")
    private Long totalCount;
    @JsonProperty("failedCount")
    private Long failedCount;
    @JsonProperty("successfulCount")
    private Long successfulCount;
    @JsonProperty("avg")
    private Long avg;
    @JsonProperty("top5processorsByCount")
    private List<Processors> processorsByCount;
    @JsonProperty("top5processorsByLatency")
    private List<Processors> processorsByLatency;

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ProcessorTypeGroupEventStatsDto restStatsDto = (ProcessorTypeGroupEventStatsDto) o;
        return Objects.equals(getStartTime(), restStatsDto.getStartTime()) &&
                Objects.equals(getEndTime(), restStatsDto.getEndTime()) &&
                Objects.equals(getTotalCount(), restStatsDto.getTotalCount()) &&
                Objects.equals(getFailedCount(), restStatsDto.getFailedCount()) &&
                Objects.equals(getSuccessfulCount(), restStatsDto.getSuccessfulCount()) &&
                Objects.equals(getAvg(), restStatsDto.getAvg());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getStartTime(), getEndTime(), getTotalCount(), getFailedCount(), getSuccessfulCount(),
                getAvg());
    }
}
