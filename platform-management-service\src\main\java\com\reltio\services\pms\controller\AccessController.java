package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.auth.domain.ReltioPrivileges;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.User;
import com.reltio.services.pms.common.model.tenant.access.AccessRequest;
import com.reltio.services.pms.common.model.tenant.access.AccessStatus;
import com.reltio.services.pms.common.model.tenant.access.AccessSummaryResponse;
import com.reltio.services.pms.common.model.tenant.access.CreateAccessRequest;
import com.reltio.services.pms.common.model.tenant.access.ZendeskRevokedResponse;
import com.reltio.services.pms.service.AuthCustomerService;
import com.reltio.services.pms.service.ZendeskService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/api/v1/access", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name= "Access")
@ReltioSecured(resourceClass = Pms.Environment.Access.class, privileges = ReltioPrivileges.EXECUTE)
public class AccessController {

    private final ZendeskService zendeskService;
    private final AuthCustomerService authCustomerService;

    @Autowired
    public AccessController(ZendeskService zendeskService, AuthCustomerService authCustomerService) {
        this.zendeskService = zendeskService;
        this.authCustomerService = authCustomerService;
    }

    @PostMapping(value = "/zendesk", consumes = MediaType.APPLICATION_JSON_VALUE)
    public AccessSummaryResponse requestAccess(@RequestBody CreateAccessRequest createAccessRequest) {
        return zendeskService.requestAccess(createAccessRequest);
    }

    @GetMapping(value = "/zendesk")
    public Collection<AccessRequest> getRequests(@RequestParam(value = "zendeskId", required = false) String zendeskId,
                                                 @RequestParam(value = "status", required = false) AccessStatus status,
                                                 @RequestParam(value = "tenantId", required = false) String tenantId,
                                                 @RequestParam(value = "userEmail", required = false) String userEmail,
                                                 @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
                                                 @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset) {
        return zendeskService.getRequests(zendeskId, tenantId, userEmail, status, size, offset);
    }

    @DeleteMapping(value = "/zendesk/_byIDs")
    public Map<String, ZendeskRevokedResponse> revokeAccess(@RequestBody List<String> zendeskIds) {
        return zendeskService.revokeAccess(zendeskIds);
    }

    @PostMapping(value = "/replicate")
    public User authAccessReplicate(@RequestParam(value = "from") String from, @RequestParam(value = "to") String to,
                                    @RequestParam(value = "tenantId", required = false) String tenantId) {
        if (Boolean.FALSE.equals(authCustomerService.isUserExistInAuth(from))) {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), "old emailId is absent in Auth.");
        }
        return authCustomerService.createdNewUserInAuth(from, to, tenantId);
    }

}
