package com.reltio.services.pms.common.model.jobs.deprovision;

import com.reltio.services.pms.common.model.jobs.tasks.authDeprovision.AuthDeProvisionTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.dnbDeprovision.DnbDeProvisionTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.dtssDeprovision.DeProvisionDtssTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.imageHostingDeprovision.ImageHostingDeprovisionTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.maintenance.MaintenanceModeTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.matchIqDeprovision.DeProvisionMatchIqTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.sfdcDeprovision.SfdcDeProvisionTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.streaming.deprovision.StreamingDeProvisionTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.configs.RetrieveAndStoreTenantConfigsTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.deprovision.DeleteMDMTenantTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.tenants.deprovision.rdm.DeprovisionRDMTenantTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.fernDeprovision.FernDeProvisionTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.workflowDeprovision.WorkflowDeProvisionTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.riqDeprovision.RiqDeProvisionTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.workatoDeprovision.WorkatoDeProvisionTaskInstanceParams;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * DeprovisionJobInstanceParams
 * Created by apylkov
 */
@Getter
@AllArgsConstructor
public class DeprovisionJobInstanceParams implements AuthDeProvisionTaskInstanceParams, DeleteMDMTenantTaskInstanceParams, MaintenanceModeTaskInstanceParams, DnbDeProvisionTaskInstanceParams, SfdcDeProvisionTaskInstanceParams, DeprovisionRDMTenantTaskInstanceParams, StreamingDeProvisionTaskInstanceParams, FernDeProvisionTaskInstanceParams, ImageHostingDeprovisionTaskInstanceParams, RiqDeProvisionTaskInstanceParams, WorkatoDeProvisionTaskInstanceParams, WorkflowDeProvisionTaskInstanceParams, DeProvisionDtssTaskInstanceParams , DeProvisionMatchIqTaskInstanceParams , RetrieveAndStoreTenantConfigsTaskInstanceParams {
    private final String tenantName;
    private final String envId;
    private final String sourceTenantId;
    private final boolean failOnError;
    private final boolean deleteIhBucket;
    private final boolean deleteGcpServiceAccount;
    private final String streamingCloud;
    private final String rdmTenantId;
    private final boolean detachAllAssociatedRdmTenants;
    private final boolean deleteRdmIfNoAssociatedMdm;
    private final boolean deleteAssociatedUsers;
    private final boolean removeAssociatedRdms;
    private final boolean removeGroupAssignment;
    private final String authCustomerId;
    private final boolean fullCustomerDeprovision;
    private final List<String> usersToDelete;

    @Override
    public String getTenantId() {
        return tenantName;
    }

    @Override
    public boolean deleteIhBucket() {
        return deleteIhBucket;
    }

    @Override
    public boolean deleteGcpServiceAccount(){return deleteGcpServiceAccount;}

    @Override
    public String getRdmTenant(){
        return rdmTenantId;
    }

    @Override
    public boolean detachAllAssociatedRdmTenants() {
        return detachAllAssociatedRdmTenants;
    }

    @Override
    public boolean deleteRdmIfNoAssociatedMdm() {
        return deleteRdmIfNoAssociatedMdm;
    }

    @Override
    public boolean isDeleteAssociatedUsers(){
        return deleteAssociatedUsers;
    }

    @Override
    public boolean isRemoveAssociatedRdms(){
        return removeAssociatedRdms;
    }

    @Override
    public boolean isRemoveGroupAssignment(){
        return removeGroupAssignment;
    }

    @Override
    public String getAuthCustomerId(){
        return authCustomerId;
    }

    @Override
    public boolean isFullCustomerDeprovision(){
        return fullCustomerDeprovision;
    }

    @Override
    public List<String> getUsersToDelete(){
        return usersToDelete;
    }

    @Override
    public String getSourceTenantId() {
        return sourceTenantId;
    }

    @Override
    public String getTargetTenantId() {
        return null;
    }

    @Override
    public String getSourceEnvId() {
        return envId;
    }

    @Override
    public String getTargetEnvId() {
        return null;
    }
}
