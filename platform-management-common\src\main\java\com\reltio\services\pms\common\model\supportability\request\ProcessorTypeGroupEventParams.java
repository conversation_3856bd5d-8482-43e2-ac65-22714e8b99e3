package com.reltio.services.pms.common.model.supportability.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.reltio.services.pms.common.model.supportability.Facet;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class ProcessorTypeGroupEventParams extends EventParams {
    private final String processorTypeGroup;

    public ProcessorTypeGroupEventParams(Long startTime, Long endTime, String tenantId, String queueType, String consumerType, String processorTypeGroup, Facet facet) {
        super(startTime, endTime, tenantId, queueType, consumerType, facet);
        this.processorTypeGroup = processorTypeGroup;
    }
}
