package com.reltio.services.pms.common.model.tenant;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

/**
 * The type Tenant details.
 */
@Getter
@Setter
public class ProductUrl implements Serializable {

    /**
     * The Ui url.
     */
    @JsonProperty(value = "uiUrl")
    private String uiUrl;
    /**
     * The Api url.
     */
    @JsonProperty(value = "apiUrl")
    private String apiUrl;

    /**
     * The Auth url.
     */
    @JsonProperty(value = "authUrl")
    private String authUrl;

    public ProductUrl(@JsonProperty(value = "uiUrl") String uiUrl, @JsonProperty(value = "apiUrl") String apiUrl, @JsonProperty(value = "authUrl") String authUrl) {
        this.uiUrl = uiUrl;
        this.apiUrl = apiUrl;
        this.authUrl = authUrl;
    }

    /**
     * Equals boolean.
     *
     * @param objectInstance the objectInstance
     * @return the boolean
     */
    @Override
    public boolean equals(Object objectInstance) {
        if (this == objectInstance) {
            return true;
        }
        if (objectInstance == null || this.getClass() != objectInstance.getClass()) {
            return false;
        }
        ProductUrl that = (ProductUrl) objectInstance;
        return Objects.equals(getUiUrl(), that.getUiUrl()) && Objects.equals(getApiUrl(), that.getApiUrl()) && Objects.equals(getAuthUrl(), that.getAuthUrl());
    }

    /**
     * Hash code int.
     *
     * @return the int
     */
    @Override
    public int hashCode() {
        return Objects.hash(getUiUrl(), getApiUrl(), getAuthUrl());
    }


}
