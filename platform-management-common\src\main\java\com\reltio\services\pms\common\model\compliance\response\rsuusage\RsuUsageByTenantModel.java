package com.reltio.services.pms.common.model.compliance.response.rsuusage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class RsuUsageByTenantModel {

    /**
     * The Report date.
     */
    @JsonProperty("reportDate")
    String date;


    /**
     * The Tenant id.
     */
    @JsonProperty("tenantId")
    private String tenantId;

    /**
     * The Tenant Purpose.
     */
    @JsonProperty("tenantPurpose")
    private String tenantPurpose;

    /**
     * The Rsu usage.
     */
    @JsonProperty("rsuUsage")
    String rsuUsage;


    /**
     * The Environment.
     */
    @JsonProperty("environment")
    private String environment;
}
