package com.reltio.services.pms.common.model.jobs.free.tier;

import com.reltio.services.pms.common.model.Requester;
import com.reltio.services.pms.common.model.jobs.tasks.approve.ApprovalTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.auth.AuthTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.azureBlobStorage.AzureBlobStorageTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.email.verification.EmailVerificationTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.mdm.MdmTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.notification.FreeTierEmailNotificationTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.r360task.R360SyncTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.rdm.RdmTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.sfdcconnector.SFDCConnectorEnablementTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.workato.WorkatoTaskInstanceParms;
import com.reltio.services.pms.common.model.jobs.tasks.workflow.WorkflowTaskInstanceParams;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import com.reltio.services.pms.common.sales.model.addon.CleanseProcessType;

import java.util.Collections;
import java.util.List;
import java.util.Set;

public class FreeTierTenantJobInstanceParams implements ApprovalTaskInstanceParams, MdmTaskInstanceParams, AuthTaskInstanceParams,
        FreeTierEmailNotificationTaskInstanceParams, EmailVerificationTaskInstanceParams, R360SyncTaskInstanceParams,
        WorkflowTaskInstanceParams, SFDCConnectorEnablementTaskInstanceParams, RdmTaskInstanceParams, WorkatoTaskInstanceParms, AzureBlobStorageTaskInstanceParams {
    private final String customerId;
    private final String linkTermsFile;
    private final boolean requesterExistsOnAuth;
    private final String productEdition;
    private final TenantPurpose tenantPurpose;
    private final String rdmTenantId;
    private final String overwriteToEmail;
    private final List<String> cc;
    private final ReltioPackageType reltioPackageType;
    private Requester requester;
    private String tenantId;
    private String clientId;
    private final Boolean useSpannerCloudFunction;

    public FreeTierTenantJobInstanceParams(Requester requester, String tenantId, String customerId, String linkTermsFile, boolean requesterExistsOnAuth,
                                           String productEdition, TenantPurpose tenantPurpose, String rdmTenantId,
                                           String overwriteToEmail, List<String> cc,
                                           ReltioPackageType reltioPackageType, Boolean useSpannerCloudFunction) {
        this.requester = requester;
        this.tenantId = tenantId;
        this.customerId = customerId;
        this.linkTermsFile = linkTermsFile;
        this.clientId = customerId;
        this.requesterExistsOnAuth = requesterExistsOnAuth;
        this.productEdition = productEdition;
        this.tenantPurpose = tenantPurpose;
        this.rdmTenantId = rdmTenantId;
        this.overwriteToEmail = overwriteToEmail;
        this.cc = cc;
        this.reltioPackageType = reltioPackageType;
        this.useSpannerCloudFunction = useSpannerCloudFunction == null ? Boolean.TRUE : useSpannerCloudFunction;
    }

    @Override
    public Requester getRequester() {
        return requester;
    }

    public void setRequester(Requester requester) {
        this.requester = requester;
    }

    @Override
    public String getCustomerId() {
        return customerId;
    }

    @Override
    public String getLoqateProcesses() {
        return CleanseProcessType.DEFAULT.getValue();
    }

    @Override
    public String getDataStorageArn() {
        return null;
    }

    @Override
    public String getMatchStorageArn() {
        return null;
    }

    @Override
    public Boolean getIsQaAutomation() {
        return Boolean.FALSE;
    }

    @Override
    public String getNewInstanceId() {
        return null;
    }

    @Override
    public String getRequesterEmail() {
        return this.requester.getEmail();
    }

    @Override
    public Set<String> getEmailsToAuthorizeAccess() {
        return Collections.singleton(requester.getEmail());
    }

    @Override
    public boolean isEnableRiq() {
        return false;
    }

    @Override
    public String getCustomerName() {
        return getCustomerId();
    }

    @Override
    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String getRdmTenantId() {
        return rdmTenantId;
    }

    @Override
    public List<String> getMdmTenantId() {
        return Collections.singletonList(tenantId);
    }

    @Override
    public String getProductEdition() {
        return productEdition;
    }

    @Override
    public String getDnbApiSecret() {
        return null;
    }

    @Override
    public String getDnbApiKey() {
        return null;
    }

    @Override
    public Boolean validateSameCustomerId() {
        return false;
    }

    @Override
    public String getRdmHostNameUrl() {
        return null;
    }

    @Override
    public TenantSize getTenantSize() {
        return TenantSize.XX_SMALL;
    }

    @Override
    public String getIndustry() {
        return null;
    }

    @Override
    public List<String> getCleanseRegions() {
        return Collections.emptyList();
    }

    @Override
    public TenantPurpose getTenantPurpose() {
        return tenantPurpose;
    }

    @Override
    public Set<String> getClients() {
        return Collections.singleton(clientId);
    }

    @Override
    public Boolean reuseRdmTenant() {
        return false;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    @Override
    public String getLinkTermsFile() {
        return linkTermsFile;
    }

    @Override
    public boolean isRequesterExistsOnAuth() {
        return requesterExistsOnAuth;
    }

    @Override
    public List<String> getCc() {
        return cc;
    }

    @Override
    public String getOverwriteToEmail() {
        return overwriteToEmail;
    }

    @Override
    public Set<String> getTenantsForServiceEnablement() {
        return Collections.singleton(tenantId);
    }

    @Override
    public String getOfferType() {
        return productEdition;
    }

    @Override
    public ReltioPackageType getReltioPackageType() {
        return reltioPackageType;
    }

    @Override
    public Boolean getUseSpannerCloudFunction() {
        return useSpannerCloudFunction;
    }
}
