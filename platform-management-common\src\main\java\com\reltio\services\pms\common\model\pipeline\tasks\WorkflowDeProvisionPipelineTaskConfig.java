package com.reltio.services.pms.common.model.pipeline.tasks;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;

public class WorkflowDeProvisionPipelineTaskConfig extends AbstractPipelineTaskConfig {
    @JsonCreator
    public WorkflowDeProvisionPipelineTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.WORKFLOW_DE_PROVISION_TASK);
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.WORKFLOW;
    }
}