package com.reltio.services.pms.common.model.supportability.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.reltio.services.pms.common.model.supportability.Facet;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * TableParams
 * Created by apy<PERSON>ov
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class TableParams extends Params {
    private final String tableName;

    public TableParams(Long startTime, Long endTime, String tenantId, String tableName, Facet facet) {
        super(startTime, endTime, tenantId, facet);
        this.tableName = tableName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        TableParams params = (TableParams) o;
        return Objects.equals(getTenantId(), params.getTenantId()) &&
                Objects.equals(getStartTime(), params.getStartTime()) &&
                Objects.equals(getEndTime(), params.getEndTime()) &&
                Objects.equals(getFacet(), params.getFacet()) &&
                Objects.equals(getTableName(), params.getTableName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getTenantId(), getStartTime(), getEndTime(), getTableName(), getFacet());
    }
}
