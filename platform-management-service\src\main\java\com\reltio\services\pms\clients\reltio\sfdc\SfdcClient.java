package com.reltio.services.pms.clients.reltio.sfdc;


import com.reltio.services.pms.common.PMSRestTemplate;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SfdcClient {

    private static final String LIST_SFDC_PROFILES = "%s/configuration/%s/_list";

    private static final String DELETE_SFDC_PROFILES = "%s/registration/%s/%s";

    private final PMSRestTemplate rest;


    @Autowired
    public SfdcClient(PMSRestTemplate rest) {
        this.rest = rest;
    }


    public void deleteProfile(String sfdcConnectorUrl, String profileName, String tenantId) {
        try {
            String apiUrl = String.format(DELETE_SFDC_PROFILES, sfdcConnectorUrl, tenantId, profileName);
            rest.delete(apiUrl);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.TENANT_IS_NOT_REGISTERED_SALESFORCE, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, tenantId + "+" + profileName);
        }
    }

    public List<String> getProfileName(String tenantId, String sfdcConnectorUrl) {
        String apiUrl = String.format(LIST_SFDC_PROFILES, sfdcConnectorUrl, tenantId);
        try {
            return rest.getForObject(apiUrl, List.class);
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.ERROR_GETTING_PROFILES_FOR_THIS_TENANT, HttpStatus.INTERNAL_SERVER_ERROR.value(), e, tenantId);

        }
    }
}
