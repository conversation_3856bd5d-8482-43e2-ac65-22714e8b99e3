package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class ShieldEnablementPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonProperty(value = "emailList")
    List<String> emailList;

    @JsonCreator
    public ShieldEnablementPipelineTaskConfig(@JsonProperty(value = "name") String name,
                                              @JsonProperty(value = "emailList") List<String> emailList) {
        super(name, TaskType.SHIELD_ENABLEMENT_TASK);
        this.emailList = (emailList != null && emailList.size() > 0) ? emailList : null;
    }


    public List<String> getEmailList() {
        return emailList;
    }
}
