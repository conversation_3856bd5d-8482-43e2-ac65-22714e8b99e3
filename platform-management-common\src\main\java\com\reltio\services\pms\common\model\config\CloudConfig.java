package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.devops.common.environment.CloudProvider;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class CloudConfig extends PMSConfig {
    /**
     * Customer type config
     */
    @JsonProperty("cloud")
    private Map<CloudProvider, CloudConfigEntry> cloudConfigEntryMap;

    @JsonCreator
    public CloudConfig(@JsonProperty("configName") String configName,
                              @JsonProperty("cloud") Map<CloudProvider, CloudConfigEntry> cloudConfigEntryMap) {
        super(configName);
        this.cloudConfigEntryMap = cloudConfigEntryMap;
    }
}
