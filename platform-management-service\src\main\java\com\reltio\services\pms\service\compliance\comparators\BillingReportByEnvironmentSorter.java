package com.reltio.services.pms.service.compliance.comparators;

import com.reltio.services.pms.common.model.compliance.SortField;
import com.reltio.services.pms.common.model.compliance.SortOrder;
import com.reltio.services.pms.common.model.compliance.response.billingreport.BillingByEnvironmentResponseModel;

import java.util.Comparator;
import java.util.List;

public class BillingReportByEnvironmentSorter {
    public static List<BillingByEnvironmentResponseModel> sort(List<BillingByEnvironmentResponseModel> reports,
                                                               String sortField, String sortOrder, Boolean export) {
        // If sortField is null, set it to "BillingStartDate"
        if (sortField == null || export) {
            sortField = SortField.BillingStartDate.name();
        }

        // If sortOrder is null, set it to "DESC" as default
        if (sortOrder == null || export) {
            sortOrder = SortOrder.DESC.name();
        }
        SortField sortFieldEnum = Enum.valueOf(SortField.class, sortField);
        SortOrder sortOrderEnum = Enum.valueOf(SortOrder.class, sortOrder);
        // Create a custom comparator based on the sortField and sortOrder
        String finalSortField = sortField;
        Comparator<BillingByEnvironmentResponseModel> comparator = (report1, report2) -> {
            switch (sortFieldEnum) {
                case Customer:
                    return report1.getCustomer().toLowerCase().compareTo(report2.getCustomer().toLowerCase());
                case CpQuota:
                    return Long.compare(report1.getCpQuota(), report2.getCpQuota());
                case TaskQuota:
                    return Long.compare(report1.getTaskQuota(), report2.getTaskQuota());
                case BillingStartDate:
                    return report1.getBillingStartDate().compareTo(report2.getBillingStartDate());
                case BillingEndDate:
                    return report1.getBillingEndDate().compareTo(report2.getBillingEndDate());
                case TaskUsage:
                    return Long.compare(report1.getTaskUsage(), report2.getTaskUsage());
                case TaskPercentage:
                    return report1.getTaskPercentage().compareTo(report2.getTaskPercentage());
                default:
                    throw new IllegalArgumentException("Invalid sortField: " + finalSortField);
            }
        };
        // Sort the list based on the comparator and sortOrder
        if (SortOrder.DESC == sortOrderEnum) {
            reports.sort(comparator.reversed());
        } else if (SortOrder.ASC == sortOrderEnum) {
            reports.sort(comparator);
        }
        return reports;
    }

}
