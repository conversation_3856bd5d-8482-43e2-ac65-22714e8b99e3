package com.reltio.services.pms.service.jobs.tasks.deprovision.dnb;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.reltio.services.pms.clients.reltio.auth.ReltioAuthClient;
import com.reltio.services.pms.clients.reltio.dnb.DNBConnectorClient;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.dnbDeprovision.DnbDeProvisionTaskInstance;
import com.reltio.services.pms.service.PmsLockService;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.AbstractMultiStepTaskExecutionService;
import com.reltio.services.pms.service.reltiotenant.TenantsService;
import org.springframework.http.HttpStatus;

import java.util.HashSet;
import java.util.Set;

public class DnbDeProvisionTaskExecutionService extends AbstractMultiStepTaskExecutionService<DnbDeProvisionTaskInstance> {

    public static final String TENANT_DEREGISTERED_EVENT = "Tenant removed from the profile.";

    public static final String USER_PERMISSION_DELETED_EVENT = "User Permission deleted from User";

    public static final String DNB_CONNECTOR_USER = "dnb.connector";

    private final DNBConnectorClient dnbConnectorClient;

    private final ReltioAuthClient authClient;

    private final Set<String> roles = new HashSet<>();

    public DnbDeProvisionTaskExecutionService(String envId,
                                              DnbDeProvisionTaskInstance dnbDeProvisionTaskInstance,
                                              GrafanaDashboardGBQService grafanaDashboardGBQService,
                                              DNBConnectorClient dnbConnectorClient, ReltioAuthClient authClient, TenantsService tenantsService, PmsLockService pmsLockService) {
        super(envId, dnbDeProvisionTaskInstance, grafanaDashboardGBQService,tenantsService,pmsLockService);
        this.dnbConnectorClient = dnbConnectorClient;
        this.authClient = authClient;
    }

    @Override
    public void executeTask() throws JsonProcessingException{

        String tenantId = taskDetail.getTenantId();
        String envId = taskDetail.getEnvId();
        runOneStep(()-> {
            try {
                deRegisterTenant(tenantId,envId);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        },taskDetail.isFailOnError(),TENANT_DEREGISTERED_EVENT);
        runOneStep(()->removeUserPermission(tenantId), taskDetail.isFailOnError(), USER_PERMISSION_DELETED_EVENT);
        taskDetail.setStatus(TaskStatus.COMPLETED);
    }

    private void deRegisterTenant(String tenantId, String envId) throws JsonProcessingException {
        JsonNode response = dnbConnectorClient.deRegisterTenant(tenantId, envId);
        if (!response.get("success").asText().equals("OK") && !response.get("message").asText().contains("Tenant is not registered")) {
            throw new PlatformManagementException(PlatformManagementErrorCode.ERROR_WHILE_DE_REGISTERING_TENANT_FROM_DNB, HttpStatus.BAD_REQUEST.value(), tenantId);
        }
        getOrAddProductProvisioningConfig(tenantId);

    }


    private void removeUserPermission(String tenantId) {
        roles.add("ROLE_ADMIN_TENANT");
        authClient.removeTenantRolesToUser(DNB_CONNECTOR_USER, roles, tenantId, true);
    }


    @Override
    protected boolean isProcessed(String event) {
        return getTaskDetail().getEvents().contains(event);
    }

    @Override
    protected void addEventToTask(String event) {
        taskDetail.addEvent(event);
        updateTask();
    }


}
