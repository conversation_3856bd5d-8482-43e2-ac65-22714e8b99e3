package com.reltio.services.pms.common.model.jobs.tasks.approve;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.Requester;
import com.reltio.services.pms.common.model.jobs.tasks.TaskFailureContext;
import com.reltio.services.pms.common.model.jobs.tasks.TaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;

import java.util.Set;

public class ApprovalTaskInstance extends TaskInstance {

    private final Set<String> approverEmails;
    private ApprovalStatus approvalStatus;
    private Requester requester;

    @JsonCreator
    public ApprovalTaskInstance(@JsonProperty(value = "id") String id,
                                @JsonProperty(value = "name") String name,
                                @JsonProperty(value = "jobId") String jobId,
                                @JsonProperty(value = "startTime") Long startTime,
                                @JsonProperty(value = "finishTime") Long finishTime,
                                @JsonProperty(value = "status") TaskStatus status,
                                @JsonProperty(value = "lastUpdatedTime") Long lastUpdatedTime,
                                @JsonProperty(value = "taskFailureContext") TaskFailureContext taskFailureContext,
                                @JsonProperty(value = "appoverEmails") Set<String> approverEmails,
                                @JsonProperty(value = "resolution") ApprovalStatus approvalStatus,
                                @JsonProperty(value = "requester") Requester requester,
                                @JsonProperty(value = "envId") String envId) {
        super(id, name, jobId, startTime, finishTime, TaskType.APPROVAL_TASK, status, lastUpdatedTime, taskFailureContext,
                null, null, envId);
        this.approverEmails = approverEmails;
        this.approvalStatus = approvalStatus;
        this.requester = requester;
    }

    public ApprovalStatus getResolution() {
        return approvalStatus;
    }

    public void setResolution(ApprovalStatus approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public Set<String> getApproverEmails() {
        return approverEmails;
    }

    public Requester getRequester() {
        return requester;
    }

    public void setRequester(Requester requester) {
        this.requester = requester;
    }
}
