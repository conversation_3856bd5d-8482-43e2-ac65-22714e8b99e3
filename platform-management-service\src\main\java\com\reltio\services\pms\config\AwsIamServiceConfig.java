package com.reltio.services.pms.config;


import com.reltio.services.pms.clients.external.aws.credentials.AccessSecretKeyCredentialsProviderImageHosting;
import com.reltio.services.pms.clients.external.aws.credentials.PlatformManagementAwsCredentials;
import com.reltio.services.pms.clients.external.aws.iam.AwsIamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class AwsIamServiceConfig {

    @Autowired

    PlatformManagementAwsCredentials mainAccountCredentials;

    @Autowired
    AccessSecretKeyCredentialsProviderImageHosting s3AccountCredentials;

    @Bean(name = "AwsIamServiceInstanceForImageHosting")
    public AwsIamService getAwsIamServiceInstanceForImageHosting() {
        return new AwsIamService(s3AccountCredentials);
    }

    @Bean(name = "AwsIamServiceInstanceForStreaming")
    @Primary
    public AwsIamService getAwsIamServiceInstanceForStreaming() {
        return new AwsIamService(mainAccountCredentials);
    }


}
