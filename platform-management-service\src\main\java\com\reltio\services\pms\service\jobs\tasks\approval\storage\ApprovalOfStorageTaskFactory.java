package com.reltio.services.pms.service.jobs.tasks.approval.storage;

import com.reltio.services.pms.common.model.jobs.tasks.approve.storage.ApprovalOfStorageTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.common.model.proxy.approve.ApprovalSecureAction;
import com.reltio.services.pms.service.StorageTemplateService;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.TaskFactory;
import com.reltio.services.pms.service.jobs.tasks.provisioning.mdm.TenantConfigurationConstructor;
import com.reltio.services.pms.service.notification.EmailNotificationService;
import com.reltio.services.pms.service.proxy.SecureActionProxyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ApprovalOfStorageTaskFactory implements TaskFactory<ApprovalOfStorageTaskInstance, ApprovalOfStorageTaskExecutionService> {

    private final EmailNotificationService emailNotificationService;
    private final SecureActionProxyService<ApprovalSecureAction> secureActionProxyService;
    private final StorageTemplateService storageTemplateService;
    private final TenantConfigurationConstructor tenantConfigurationConstructor;

    @Autowired
    public ApprovalOfStorageTaskFactory(EmailNotificationService emailNotificationService,
                                        SecureActionProxyService<ApprovalSecureAction> secureActionProxyService,
                                        StorageTemplateService storageTemplateService,
                                        TenantConfigurationConstructor tenantConfigurationConstructor) {
        this.emailNotificationService = emailNotificationService;
        this.secureActionProxyService = secureActionProxyService;
        this.storageTemplateService = storageTemplateService;
        this.tenantConfigurationConstructor = tenantConfigurationConstructor;
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.APPROVAL_OF_STORAGE;
    }

    @Override
    public ApprovalOfStorageTaskExecutionService createTask(String jobId, ApprovalOfStorageTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService) {
        return new ApprovalOfStorageTaskExecutionService(jobId, taskDetail, grafanaDashboardGBQService, emailNotificationService, secureActionProxyService, storageTemplateService, tenantConfigurationConstructor);
    }
}
