package com.reltio.services.pms.convertors.sales;

import com.fasterxml.jackson.databind.JsonNode;
import com.reltio.services.pms.clients.external.salesforce.SalesforceClientService;
import com.reltio.services.pms.common.model.compliance.SubscriptionType;
import com.reltio.services.pms.common.sales.AccountType;
import com.reltio.services.pms.common.sales.model.BusinessContact;
import com.reltio.services.pms.common.sales.model.CustomerSupport;
import com.reltio.services.pms.common.sales.model.ReltioContract;
import com.reltio.services.pms.common.sales.model.SalesAccount;
import com.reltio.services.pms.common.sales.model.AccountStatus;
import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.common.sales.model.SalesOpportunity;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.reltio.services.pms.convertors.UtilDeserializer.getBooleanField;
import static com.reltio.services.pms.convertors.UtilDeserializer.getDateField;
import static com.reltio.services.pms.convertors.UtilDeserializer.getField;
import static com.reltio.services.pms.convertors.UtilDeserializer.objectMapper;

@Service
public class SalesforceDataDeserializerImpl implements SalesDataDeserializer {

    private final static Logger LOGGER = Logger.getLogger(SalesforceDataDeserializerImpl.class);
    private final SalesforceClientService salesforceClient;
    private final SalesPackageProviderService salesPackageProviderService;

    @Autowired
    public SalesforceDataDeserializerImpl(SalesforceClientService salesforceClient, SalesPackageProviderService salesPackageProviderService) {
        this.salesforceClient = salesforceClient;
        this.salesPackageProviderService = salesPackageProviderService;
    }

    @Override
    public SalesAccount getSalesAccount(String accountId) {

        JsonNode account = salesforceClient.getAccount(accountId);

        SalesAccount salesAccount = new SalesAccount();

        //Account
        salesAccount.setAccountId(getField("Id", account));
        salesAccount.setName(getField("Name", account));
        salesAccount.setAccountType(AccountType.convertSalesforceString(getField("Type", account)));
        salesAccount.setIndustry(getField("Industry", account));
        salesAccount.setSubIndustry(getField("Sub_Industry__c", account));
        salesAccount.setTotalACV(getField("Total_Subscription_ACV__c", account));

        CustomerSupport support = new CustomerSupport();
        support.setLevel(getField("Support_Level__c", account));
        support.setOrgName(getField("Zendesk_Org_Name__c", account));
        support.setZendeskId(getField("Zendesk_Org_ID__c", account));
        salesAccount.setSupport(support);

        salesAccount.setCsm(getCSM(getField("CSM__c", account)));
        salesAccount.setAccountStatus(AccountStatus.ACTIVE);

        return salesAccount;
    }

    @Override
    public ReltioContract getReltioContract(String contractId) {

        JsonNode contractNode = salesforceClient.getContract(contractId);

        ReltioContract reltioContract = new ReltioContract();
        reltioContract.setContractId(getField("Id", contractNode));
        reltioContract.setAccountId(getField("AccountId", contractNode));
        reltioContract.setContractType(getField("Contract_Type__c", contractNode));
        reltioContract.setContractNumber(getField("ContractNumber", contractNode));
        SalesOpportunity opportunity = getSalesOpportunity(getField("Opportunity__c", contractNode), reltioContract);
        reltioContract.setOpportunity(opportunity);
        reltioContract.setIndustry(getField("Industry__c", contractNode));
        reltioContract.setStatus(getField("Status", contractNode));
        reltioContract.setStartDate(getDateField("StartDate", contractNode));
        reltioContract.setEndDate(getDateField("EndDate", contractNode));
        reltioContract.setActivatedDate(getDateField("ActivatedDate", contractNode));
        reltioContract.setProcurementChannel(getField("Procurement_Channel__c", contractNode));
        reltioContract.setPlatformPreference(getField("Cloud_Platform_Tenant__c", contractNode));
        reltioContract.setDeploymentRegion(getField("Tenant_Deployment_Region__c", contractNode) != null ? getField(
                "Tenant_Deployment_Region__c", contractNode).replace(";", "") : null);
        reltioContract.setProductEdition(opportunity != null ? opportunity.getMarketSegment() : null);
        reltioContract.setHipaa(getBooleanField("HIPAA__c", contractNode));


        reltioContract.setCustomerContact(getCustomerContact(getField("Technical_Contact__c", contractNode)));
        reltioContract.setBillingContact(getCustomerContact(getField("Billing_Contact__c", contractNode)));
        reltioContract.setSubscriptionType(getSubscriptionType(getField("Pricing_Model_FX__c", contractNode)));
        JsonNode subsNode = salesforceClient.getSubscriptions(reltioContract.getContractId());

        try {

            if (subsNode != null && subsNode.get("records") != null) {
                List<SalesConfig> salesConfigs = objectMapper.readValue(objectMapper.writeValueAsString(subsNode.get("records")), new com.fasterxml.jackson.core.type.TypeReference<List<SalesConfig>>() {
                });
                salesPackageProviderService.populatePackages(reltioContract, salesConfigs);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to get the Subscriptions for Contract Id = " + contractId, e);
        }

        return reltioContract;
    }

    /**
     * getSubscriptionType method is used to get whether the contract is Subscription based or Usage Based Pricing model
     *
     * @param pricingModel
     * @return subscriptionType
     */
    protected SubscriptionType getSubscriptionType(String pricingModel) {
        if (StringUtils.isNotBlank(pricingModel) && Objects.equals(pricingModel, SubscriptionType.USAGE.getType())) {
            return SubscriptionType.USAGE;
        }
        return SubscriptionType.SUBSCRIPTION;
    }


    private BusinessContact getCSM(String contactId) {
        try {
            JsonNode csm = salesforceClient.getUser(contactId);
            BusinessContact businessContact = new BusinessContact();
            businessContact.setId(getField("Id", csm));
            businessContact.setName(getField("Name", csm));
            businessContact.setEmail(getField("Email", csm));
            businessContact.setPhone(getField("Phone", csm));
            return businessContact;

        } catch (Exception e) {
            LOGGER.warn("GET CSM Failed for ID = " + contactId, e);
        }
        return null;
    }

    protected SalesOpportunity getSalesOpportunity(String id, ReltioContract reltioContract) {
        if (id == null) {
            return null;
        }
        try {
            JsonNode node = salesforceClient.getOpportunity(id);
            SalesOpportunity salesOpportunity = new SalesOpportunity();

            salesOpportunity.setId(getField("Id", node));
            salesOpportunity.setBusinessNeed(getField("Business_Need__c", node));
            salesOpportunity.setDataDomain(getField("Data_Domain__c", node));
            salesOpportunity.setDescription(getField("Description", node));
            salesOpportunity.setName(getField("Name", node));
            salesOpportunity.setType(getField("Type", node));
            salesOpportunity.setMarketSegment(getField("Market_Segment__c", node));
            salesOpportunity.setDeploymentRegion(getField("Tenant_Deployment_Region__c", node));
            reltioContract.setDeploymentRegion(salesOpportunity.getDeploymentRegion());
            String cloudProvider = getField("Cloud_Platform_Tenant__c", node);
            cloudProvider = cloudProvider == null ? getField("Cloud_Provider__c", node) : cloudProvider;
            reltioContract.setPlatformPreference(cloudProvider);
            return salesOpportunity;
        } catch (Exception e) {
            LOGGER.info("Error has occurred while getting sales opportunity.", e);
        }

        return null;
    }


    protected BusinessContact getCustomerContact(String contactId) {
        if (contactId == null) {
            return null;
        }
        try {
            JsonNode node = salesforceClient.getContact(contactId);
            BusinessContact businessContact = new BusinessContact();
            businessContact.setId(getField("Id", node));
            businessContact.setName(getField("Name", node));
            businessContact.setEmail(getField("Email", node));
            businessContact.setPhone(getField("Phone", node));
            return businessContact;

        } catch (Exception e) {
            LOGGER.info("Error has occurred while getting customer contact.", e);
        }
        return null;
    }
}
