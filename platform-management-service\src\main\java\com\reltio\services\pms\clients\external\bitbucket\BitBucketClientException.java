package com.reltio.services.pms.clients.external.bitbucket;

public class BitBucketClientException extends RuntimeException {

    public BitBucketClientException() {
    }

    public BitBucketClientException(String message, Throwable cause) {
        super(message, cause);
    }

    public BitBucketClientException(Throwable cause) {
        super(cause);
    }

    public BitBucketClientException(String message) {
        super(message);
    }

    public static class PathPointToFolder extends BitBucketClientException {
        public PathPointToFolder() {
        }

        public PathPointToFolder(Throwable cause) {
            super(cause);
        }
    }

    public static class PathPointToFile extends BitBucketClientException {
        public PathPointToFile() {
        }

        public PathPointToFile(Throwable cause) {
            super(cause);
        }
    }

}