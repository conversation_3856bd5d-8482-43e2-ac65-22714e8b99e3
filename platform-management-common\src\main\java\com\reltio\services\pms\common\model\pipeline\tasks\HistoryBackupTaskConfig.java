package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.Getter;

@Getter
public class HistoryBackupTaskConfig extends AbstractPipelineTaskConfig{

    @JsonCreator
    public HistoryBackupTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.HISTORY_BACKUP_TASK);
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.HISTORY_BACKUP;
    }

}
