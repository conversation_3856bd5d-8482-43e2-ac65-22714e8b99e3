package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.SalesPackageType;
import com.reltio.services.pms.common.sales.model.quotas.BaseQuotasConfig;
import lombok.Getter;
import lombok.Setter;
import org.checkerframework.checker.units.qual.C;


import java.util.*;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class SalesPackageConfig extends SalesConfig {

    @JsonProperty("packageType")
    private transient SalesPackageType packageType;

    @JsonProperty("mdmTenants")
    private List<ReltioTenant> mdmTenants;

    @JsonProperty("configurations")
    private Map<SalesForceProductName, Set<SalesConfig>> configurations;

    private Map<PMSQuotaName, BaseQuotasConfig> productQuotas = new EnumMap<>(PMSQuotaName.class);

    public SalesPackageConfig(SalesPackageType packageType, List<ReltioTenant> mdmTenants, Map<SalesForceProductName, Set<SalesConfig>> configurations) {
        this.packageType = packageType;
        this.mdmTenants = mdmTenants;
        this.configurations = configurations;
    }

    public SalesPackageConfig() {
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        SalesPackageConfig that = (SalesPackageConfig) o;
        return getPackageType() == that.getPackageType() && Objects.equals(getMdmTenants(), that.getMdmTenants()) &&
                Objects.equals(getProductQuotas(), that.getProductQuotas());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getPackageType(), getMdmTenants(), getProductQuotas());
    }

    public SalesPackageConfig(SalesConfig salesConfig) {
        super(salesConfig.getSubscriptionId(), salesConfig.getProductCode(), salesConfig.getProductName(), salesConfig.getSubscriptionName(), salesConfig.getStartDate(),
                salesConfig.getEndDate(), salesConfig.getQuantity(), salesConfig.getParentSubscriptionId() , salesConfig.isActive(), salesConfig.getSalesTenantId());
    }

}
