package com.reltio.services.pms.dao;

import com.google.api.core.ApiFuture;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.CollectionGroup;
import com.google.cloud.firestore.CollectionReference;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.FirestoreOptions;
import com.google.cloud.firestore.QueryDocumentSnapshot;
import com.google.cloud.firestore.QuerySnapshot;
import com.google.cloud.firestore.WriteBatch;
import com.google.common.collect.Lists;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import org.apache.log4j.Logger;
import org.springframework.http.HttpStatus;

import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * Provides the generic functions to support the sub collections at level 1.
 *
 * @param <T>
 */
public abstract class AbstractLevel1CollectionDao<T extends BaseFirestoreEntity> extends AbstractDao<T> {


    private static final Logger logger = Logger.getLogger(AbstractLevel1CollectionDao.class);
    private static final int MAX_BATCH_LIMIT = 500;

    protected final Firestore db;
    protected final AbstractRootCollectionDao rootCollectionDao;
    protected final String collectionName;

    private static final String FORMAT_SPECIFIER = "%s_%s";

    public AbstractLevel1CollectionDao(CredentialsProvider provider, AbstractRootCollectionDao rootCollectionDao, String collectionName, ReltioUserHolder reltioUserHolder) {
        super(reltioUserHolder);
        this.db = FirestoreOptions.newBuilder().setCredentialsProvider(provider).build().getService();
        this.rootCollectionDao = rootCollectionDao;
        this.collectionName = collectionName;
    }

    public AbstractLevel1CollectionDao(CredentialsProvider provider, AbstractRootCollectionDao rootCollectionDao, String collectionPrefix, String deployedEnv, ReltioUserHolder reltioUserHolder) {
        super(reltioUserHolder);
        this.db = FirestoreOptions.newBuilder().setCredentialsProvider(provider).build().getService();
        this.rootCollectionDao = rootCollectionDao;
        this.collectionName = String.format(FORMAT_SPECIFIER, collectionPrefix, deployedEnv);
    }

    @PreDestroy
    public void destroy() throws Exception {
        db.close();
    }

    public T create(String rootDocId, T entity) {
        return create(getBaseCollection(rootDocId), entity);
    }

    public T update(String rootDocId, T entity) {
        return update(getBaseCollection(rootDocId), entity);
    }

    public T get(String rootDocId, String docId) throws InvalidDocumentIdException {
        return get(getBaseCollection(rootDocId), docId);
    }

    public boolean isExists(String rootDocId, String docId) {
        return isExists(getBaseCollection(rootDocId), docId);
    }

    public Collection<T> getAll(String rootDocId) {
        return getAll(getBaseCollection(rootDocId));
    }

    public void delete(String rootDocId, String docId) {
        delete(getBaseCollection(rootDocId), docId);
    }

    public final DocumentReference getDocumentReference(String rootDocId, String docId) {
        return getBaseCollection(rootDocId).document(docId);
    }

    protected final CollectionReference getBaseCollection(String rootDocId) {
        return rootCollectionDao.getDocumentReference(rootDocId).collection(collectionName);
    }

    public Collection<T> getAllWithSizeAndOffset(String rootDocId, Integer size, Integer offset) {
        return getAllWithSizeAndOffset(getBaseCollection(rootDocId), size, offset);
    }

    public Collection<T> getFilteredWithSizeAndOffset(String rootDocId, String filterField, String value, Integer size, Integer offset) {
        return getFilteredWithSizeAndOffset(getBaseCollection(rootDocId), filterField, value, size, offset);
    }

    public Collection<T> getAllFiltered(String rootDocId, String filterField, String value) {
        return getAllFiltered(getBaseCollection(rootDocId), filterField, value);
    }

    public Collection<T> getFilteredByBooleanField(String rootDocId, String filterField, boolean value) {

        return getFilteredByBooleanField(getBaseCollection(rootDocId), filterField, value);
    }

    public CollectionGroup getCurrentCollectionGroup() {
        return db.collectionGroup(collectionName);
    }

    public List<T> createBatch(String rootDocId, List<T> entity) {
        return batchCreate(getBaseCollection(rootDocId), getBatch(), entity);
    }

    protected final WriteBatch getBatch() {
        return db.batch();
    }

    protected List<T> batchCreate(CollectionReference collectionReference, WriteBatch batch, List<T> entityList) {
        List<T> createRecordList = new ArrayList<>();
        try {
            if (entityList.size() > MAX_BATCH_LIMIT) {
                List<List<T>> valueBatches = Lists.partition(entityList, MAX_BATCH_LIMIT);
                for (int i = 0; i <= valueBatches.size() - 1; i++) {
                    WriteBatch batchInstance = getBatch();
                    batchExecution(collectionReference, batchInstance, valueBatches.get(i));
                }
            } else {
                batchExecution(collectionReference, batch, entityList);
            }
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
        return createRecordList;
    }

    private void batchExecution(CollectionReference collectionReference, WriteBatch batch, List<T> entityList) {
        for (T entity : entityList) {
            String username = getUsername();
            Long time = System.currentTimeMillis();
            entity.setCreatedBy(username);
            entity.setCreatedTime(time);
            entity.setUpdatedBy(username);
            entity.setUpdatedTime(time);
            batch.set(collectionReference.document(), entity);
        }
        batch.commit();
    }

    public void deleteBatch(DocumentReference docRef,String collectionName) {
        try {
            CollectionReference subCollectionRef = docRef.collection(collectionName);
            ApiFuture<QuerySnapshot> future = subCollectionRef.get();
            List<QueryDocumentSnapshot> subCollectionDocuments = future.get().getDocuments();
            int batchSize = 500;
            int deletionCount = 0;
            WriteBatch batch = getBatch();

            for (QueryDocumentSnapshot subDocRef : subCollectionDocuments) {
                batch.delete(subDocRef.getReference());
                deletionCount++;
                if (deletionCount == batchSize) {
                    batch.commit().get();
                    batch = getBatch();
                    deletionCount = 0;
                }
            }
            if (deletionCount > 0) {
                batch.commit().get();
            }

        } catch (ExecutionException exception) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), exception);
        } catch (InterruptedException interruptedException) {
            Thread.currentThread().interrupt();
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), interruptedException);
        }
    }

}
