package com.reltio.services.pms.common.model.auth;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import com.reltio.services.pms.common.model.CustomerType;
import com.reltio.services.pms.common.model.config.CustomSettings;
import com.reltio.services.pms.common.model.config.MFA;
import com.reltio.services.pms.common.sales.model.AccountStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Set;


@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AuthCustomer extends BaseFirestoreEntity {

    @JsonProperty("id")
    private String id;

    @JsonProperty("tenants")
    private Set<String> tenants;

    @JsonProperty("applicationClients")
    private Set<String> applicationClients;

    @JsonProperty("customSettings")
    private CustomSettings customSettings;

    @JsonProperty("caseSensitiveLoginEnabled")
    private boolean caseSensitiveLoginEnabled;

    @JsonProperty("salesAccountId")
    private String salesAccountId;

    @JsonProperty("customerType")
    private CustomerType customerType;

    @JsonProperty("salesContractId")
    private String salesContractId;

    @JsonProperty("owners")
    private Set<String> owners;

    @JsonProperty("division")
    private String division;

    @JsonProperty("department")
    private String department;

    @JsonProperty("costCenter")
    private String costCenter;

    @JsonProperty("accountStatus")
    private AccountStatus accountStatus;

    @JsonProperty("mfa")
    private MFA mfa;



    public AuthCustomer(@JsonProperty("id") String id,
                        @JsonProperty("tenants") Set<String> tenants,
                        @JsonProperty("applicationClients") Set<String> applicationClients,
                        @JsonProperty("customSettings") CustomSettings customSettings,
                        @JsonProperty("caseSensitiveLoginEnabled") boolean caseSensitiveLoginEnabled,
                        @JsonProperty("salesAccountId") String salesAccountId,
                        @JsonProperty("customerType") CustomerType customerType,
                        @JsonProperty("salesContractId") String salesContractId) {
        this.id = id;
        this.tenants = tenants;
        this.applicationClients = applicationClients;
        this.customSettings = customSettings;
        this.caseSensitiveLoginEnabled = caseSensitiveLoginEnabled;
        this.salesAccountId = salesAccountId;
        this.customerType = customerType;
        this.salesContractId = salesContractId;
        this.accountStatus = AccountStatus.ACTIVE;
    }

    @JsonCreator
    public AuthCustomer(
            @JsonProperty("id") String id,
            @JsonProperty("tenants") Set<String> tenants,
            @JsonProperty("applicationClients") Set<String> applicationClients,
            @JsonProperty("customSettings") CustomSettings customSettings,
            @JsonProperty("caseSensitiveLoginEnabled") boolean caseSensitiveLoginEnabled,
            @JsonProperty("salesAccountId") String salesAccountId,
            @JsonProperty("customerType") CustomerType customerType,
            @JsonProperty("salesContractId") String salesContractId,
            @JsonProperty("owners") Set<String> owners,
            @JsonProperty("division") String division,
            @JsonProperty("department") String department,
            @JsonProperty("costCenter") String costCenter,
            @JsonProperty("accountStatus") AccountStatus accountStatus
    ) {
        this.id = id;
        this.tenants = tenants;
        this.applicationClients = applicationClients;
        this.customSettings = customSettings;
        this.caseSensitiveLoginEnabled = caseSensitiveLoginEnabled;
        this.salesAccountId = salesAccountId;
        this.customerType = customerType;
        this.salesContractId = salesContractId;
        this.owners = owners;
        this.division = division;
        this.department = department;
        this.costCenter = costCenter;
        this.accountStatus = accountStatus;
    }

    @JsonProperty("applicationClients")
    public Set<String> getClients() {
        return applicationClients;
    }

    @Override
    public String getID() {
        return id;
    }

    public void mergeOldNewAuthCustomer(AuthCustomer oldCustomer) {
        AuthCustomerBuilder authCustomerBuilder = AuthCustomer.builder();
        String updatedSalesAccountId = this.salesAccountId == null ? oldCustomer.salesAccountId : this.salesAccountId;
        authCustomerBuilder.id(this.id);
        authCustomerBuilder.tenants(this.tenants);
        authCustomerBuilder.applicationClients(this.applicationClients);
        authCustomerBuilder.customSettings(this.customSettings);
        authCustomerBuilder.caseSensitiveLoginEnabled(this.caseSensitiveLoginEnabled);
        authCustomerBuilder.salesAccountId(updatedSalesAccountId);
        authCustomerBuilder.salesContractId(this.salesContractId == null ? oldCustomer.salesContractId : this.salesContractId);
        authCustomerBuilder.customerType((this.customerType == null || oldCustomer.customerType == null) ? CustomerType.getCustomerType(updatedSalesAccountId, this.id) : this.customerType);
        authCustomerBuilder.owners(this.owners == null ? oldCustomer.owners : this.owners);
        authCustomerBuilder.division(this.division == null ? oldCustomer.division : this.division);
        authCustomerBuilder.department(this.department == null ? oldCustomer.department : this.department);
        authCustomerBuilder.costCenter(this.costCenter == null ? oldCustomer.costCenter : this.costCenter);
        authCustomerBuilder.accountStatus(AccountStatus.ACTIVE);
        authCustomerBuilder.build();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AuthCustomer authCustomer = (AuthCustomer) o;
        if (!id.equals(authCustomer.id)) {
            return false;
        }
        if ((tenants != null) ? (!tenants.equals(authCustomer.tenants)) : (authCustomer.tenants != null)) {
            return false;
        }
        if ((salesAccountId != null) ? (!salesAccountId.equals(authCustomer.salesAccountId)) : (authCustomer.salesAccountId != null)) {
            return false;
        }
        if ((salesContractId != null) ? (!salesContractId.equals(authCustomer.salesContractId)) : (authCustomer.salesContractId != null)) {
            return false;
        }
        if ((customerType != null) ? (customerType != authCustomer.customerType) : (authCustomer.customerType != null)) {
            return false;
        }
        if ((owners != null) ? (!owners.equals(authCustomer.owners)) : (authCustomer.owners != null)) {
            return false;
        }
        if ((division != null) ? (division.equals(authCustomer.division)) : (authCustomer.division != null)) {
            return false;
        }
        if ((department != null) ? (department.equals(authCustomer.department)) : (authCustomer.department != null)) {
            return false;
        }
        if ((costCenter != null) ? (costCenter.equals(authCustomer.costCenter)) : (authCustomer.costCenter != null)) {
            return false;
        }
        return ((applicationClients != null) ? (applicationClients.equals(authCustomer.applicationClients)) : (authCustomer.applicationClients == null));
    }


    @Override
    public int hashCode() {
        int result = id.hashCode();
        result = 31 * result + (tenants != null ? tenants.hashCode() : 0);
        result = 31 * result + (salesAccountId != null ? salesAccountId.hashCode() : 0);
        result = 31 * result + (salesContractId != null ? salesContractId.hashCode() : 0);
        result = 31 * result + (customerType != null ? customerType.hashCode() : 0);
        result = 31 * result + (applicationClients != null ? applicationClients.hashCode() : 0);
        result = 31 * result + (owners != null ? owners.hashCode() : 0);
        result = 31 * result + (division != null ? division.hashCode() : 0);
        result = 31 * result + (department != null ? department.hashCode() : 0);
        result = 31 * result + (costCenter != null ? costCenter.hashCode() : 0);
        return result;
    }
}
