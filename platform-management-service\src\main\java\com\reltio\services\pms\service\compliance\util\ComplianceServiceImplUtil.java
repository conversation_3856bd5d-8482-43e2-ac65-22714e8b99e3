package com.reltio.services.pms.service.compliance.util;

import com.google.auth.Credentials;
import com.google.cloud.bigquery.*;
import com.reltio.services.pms.clients.external.gcp.GoogleCredentialsProvider;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.compliance.SortField;
import com.reltio.services.pms.common.model.compliance.SortOrder;
import com.reltio.services.pms.common.model.compliance.request.*;
import com.reltio.services.pms.service.compliance.impl.gbq.QueryBuilder;
import org.apache.commons.configuration.Configuration;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.http.HttpStatus;

import jakarta.validation.Valid;
import java.io.IOException;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;
import java.util.Map;
import java.util.Locale;
import java.util.stream.Collectors;

import static com.reltio.services.pms.service.compliance.impl.gbq.QueryBuilder.getCpUsageByDateTemplate;

public class ComplianceServiceImplUtil {
    /**
     * The Client.
     */
    private BigQuery client;
    /**
     * The constant SCOPES.
     */
    protected static final List<String> SCOPES = java.util.Collections.singletonList("https://www.googleapis.com/auth/bigquery");

    /**
     * The constant log.
     */
    private static final Logger log = Logger.getLogger(ComplianceServiceImplUtil.class);



    /**
     * The Gbq key.
     */
    private final String gbqKey;

    /**
     * The Project id.
     */
    private final String projectId;

    /**
     * The Task dataset.
     */
    protected final String taskDataset;

    /**
     * The Rsu dataset.
     */
    protected final String rsuDataset;

    /**
     * The Api dataset.
     */
    protected final String apiDataset;

    /**
     * The Task table name.
     */
    protected final String taskTableName;

    /**
     * The Rsu table name.
     */
    protected final String rsuTableName;

    /**
     * The Api table name.
     */
    protected final String apiTableName;


    /**
     * Instantiates a new Gbq service.
     *
     * @param configuration the configuration
     */
    protected ComplianceServiceImplUtil(Configuration configuration) {
        this.projectId = configuration.getString("compliance.gbq.projectId");
        this.taskDataset = configuration.getString("compliance.gbq.task.dataset");
        this.rsuDataset = configuration.getString("compliance.gbq.rsu.dataset");
        this.apiDataset = configuration.getString("compliance.gbq.api.dataset");
        this.taskTableName = configuration.getString("compliance.gbq.taskTableName");
        this.rsuTableName = configuration.getString("compliance.gbq.rsuTableName");
        this.apiTableName = configuration.getString("compliance.gbq.apiTableName");
        this.gbqKey = configuration.getString("compliance.gbq.key");
    }

    /**
     * Get credentials from the key
     *
     * @param gbqKey base64 gbq key
     * @return credentials
     * @throws IOException in case key load fails
     */
    private static Credentials getCredentials(String gbqKey) throws IOException {
        return GoogleCredentialsProvider.getCredentials(gbqKey, SCOPES);
    }

    /**
     * Connect to GBQ service
     *
     * @throws IOException in case connection fails
     */
    protected BigQuery buildBQService() throws IOException {
        return BigQueryOptions.newBuilder().setCredentials(getCredentials(gbqKey)).build().getService();
    }

    /**
     * Get GBQ client to use
     *
     * @return GBQ client
     * @throws IOException in case connection failed
     */
    public BigQuery getClient() throws IOException {
        if (client == null) {
            client = buildBQService();
        }
        return client;
    }

    /**
     * Execute table result.
     *
     * @param job the job
     * @return the table result
     */
    protected TableResult execute(QueryJobConfiguration job) {
        try {
            if (client == null) {
                client = buildBQService();
            }
            return client.query(job);
        } catch (InterruptedException | JobException | IOException dbRequestException) {
            if (dbRequestException instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            log.error("Call DB service to get stats failed with exception: ", dbRequestException);
            throw new PlatformManagementException(PlatformManagementErrorCode.COMPLIANCE_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), dbRequestException, dbRequestException.getMessage());
        }
    }


    /**
     * Gets default params.
     *
     * @param dataset   the dataset
     * @param tableName the table name
     * @param tenantIds the tenant ids
     * @param startDate the start date
     * @param endDate   the end date
     * @return the default params
     */
    public Map<String, Object> getDefaultParams(String dataset, String tableName, List<String> tenantIds, String startDate, String endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("dataset", dataset);
        params.put("tableName", tableName);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("tenantId", tenantIds.stream().map(tenantId -> "'" + tenantId + "'").collect(Collectors.joining(",")));
        return params;
    }

    /* task usage */


    /**
     * Build rest query job query job configuration.
     *
     * @param taskUsageRequest the task usage request
     * @return the query job configuration
     */
    protected QueryJobConfiguration buildTaskUsageQueryJob(@Valid ComplianceRequest taskUsageRequest) {
        Map<String, Object> params = getDefaultParams(taskDataset, taskTableName, taskUsageRequest.getTenantIds(), String.valueOf(taskUsageRequest.getStartDate()), String.valueOf(taskUsageRequest.getEndDate()));
        QueryBuilder queryBuilder = new QueryBuilder(QueryBuilder.TASKS_USAGE_TEMPLATE, params);
        queryBuilder.orderBy(SortField.ReportDate.name(), SortOrder.DESC.name());
        return QueryJobConfiguration.newBuilder(queryBuilder.build()).build();
    }

    /**
     * Build task usage by tenant query job query job configuration.
     *
     * @param billingReportRequest the billing report request
     * @param tenantIdList         the tenant id list
     * @return the query job configuration
     */
    protected QueryJobConfiguration buildBillingReportByCustomerQueryJob( @Valid ComplianceReportRequest billingReportRequest, List<String> tenantIdList) {
        Map<String, Object> params = getDefaultParams(taskDataset, taskTableName, tenantIdList, String.valueOf(billingReportRequest.getStartDate()), String.valueOf(billingReportRequest.getEndDate()));
        QueryBuilder queryBuilder;
        queryBuilder = new QueryBuilder(QueryBuilder.BILLING_BY_CUSTOMER_TEMPLATE, params);
        queryBuilder.orderBy(SortField.BillingStartDate.name(), SortOrder.DESC.name());
        return QueryJobConfiguration.newBuilder(queryBuilder.build()).build();
    }

    /**
     * Build task usage by environment query job query job configuration.
     *
     * @param billingReportRequest the billing usage request
     * @param tenantIdList         the tenant id list
     * @return the query job configuration
     */
    protected QueryJobConfiguration buildBillingReportByEnvironmentQueryJob(@Valid ComplianceReportRequest billingReportRequest, List<String> tenantIdList) {
        Map<String, Object> params = getDefaultParams(taskDataset, taskTableName, tenantIdList, String.valueOf(billingReportRequest.getStartDate()), String.valueOf(billingReportRequest.getEndDate()));
        QueryBuilder queryBuilder;
        queryBuilder = new QueryBuilder(QueryBuilder.BILLING_BY_ENVIRONMENT_TEMPLATE, params);
        queryBuilder.orderBy(SortField.BillingStartDate.name(), SortOrder.DESC.name());
        return QueryJobConfiguration.newBuilder(queryBuilder.build()).build();
    }

    /* API usages */
    /**
     * Build api usage query job query job configuration.
     *
     * @param apiUsageRequest the api usage request
     * @return the query job configuration
     */
    protected QueryJobConfiguration buildApiUsageQueryJob(@Valid ApiUsageRequest apiUsageRequest) {
        Map<String, Object> params = getDefaultParams(apiDataset, apiTableName, apiUsageRequest.getTenantIds(), String.valueOf(apiUsageRequest.getStartDate()), String.valueOf(apiUsageRequest.getEndDate()));
        QueryBuilder queryBuilder = new QueryBuilder(QueryBuilder.API_USAGE_QUERY_TEMPLATE, params);
        if (StringUtils.isNotEmpty(apiUsageRequest.getEndPoint())) {
            queryBuilder.addAdditionalANDParam("HandlerMapping", apiUsageRequest.getEndPoint());
        }
        String builder = queryBuilder.orderBy(SortField.ReportDate.name(), SortOrder.DESC.name()).build();
        return QueryJobConfiguration.newBuilder(builder).build();
    }
    /**
     * Build api usage by date query job query job configuration.
     *
     * @param apiUsageRequest the api usage request
     * @param tenantIdList    the tenant id list
     * @param export          the export
     * @return the query job configuration
     */
    protected QueryJobConfiguration buildApiUsageByDateQueryJob(@Valid ApiUsageReportRequest apiUsageRequest, List<String> tenantIdList, Boolean export) {
        Map<String, Object> params = getDefaultParams(apiDataset, apiTableName, tenantIdList, String.valueOf(apiUsageRequest.getStartDate()), String.valueOf(apiUsageRequest.getEndDate()));
        QueryBuilder childQueryBuilder = new QueryBuilder(QueryBuilder.API_USAGE_ENDPOINT_SUBQUERY_TEMPLATE, params);
        applyApiUsageFilters(childQueryBuilder, apiUsageRequest);
        QueryBuilder queryBuilder;
        queryBuilder = new QueryBuilder(QueryBuilder.getApiUsageByDateTemplate(childQueryBuilder.build()), params);
        if (export) {
            queryBuilder.orderBy(SortField.ReportDate.name(), SortOrder.DESC.name());
        } else {
            makeQueryBuilder(apiUsageRequest, queryBuilder);
        }
        return QueryJobConfiguration.newBuilder(queryBuilder.build()).build();

    }

    protected QueryJobConfiguration buildApiUsageByMonthQueryJob(@Valid ComplianceReportRequest apiUsageRequest, List<String> tenantIdList) {
        Map<String, Object> params = getDefaultParams(apiDataset, apiTableName, tenantIdList, String.valueOf(apiUsageRequest.getStartDate()), String.valueOf(apiUsageRequest.getEndDate()));
        QueryBuilder childQueryBuilder = new QueryBuilder(QueryBuilder.API_USAGE_ENDPOINT_SUBQUERY_TEMPLATE, params);
        QueryBuilder queryBuilder;
        queryBuilder = new QueryBuilder(QueryBuilder.getApiUsageByMonthTemplate(childQueryBuilder.build()), params);
        return QueryJobConfiguration.newBuilder(queryBuilder.build()).build();
    }

    /**
     * Build api usage by tenant query job query job configuration.
     *
     * @param apiUsageRequest the api usage request
     * @param tenantIdList    the tenant id list
     * @param export          the export
     * @return the query job configuration
     */
    protected QueryJobConfiguration buildApiUsageByTenantQueryJob(ApiUsageReportRequest apiUsageRequest, List<String> tenantIdList, Boolean export) {
        Map<String, Object> params = getDefaultParams(apiDataset, apiTableName, tenantIdList, String.valueOf(apiUsageRequest.getStartDate()), String.valueOf(apiUsageRequest.getEndDate()));
        QueryBuilder childQueryBuilder = new QueryBuilder(QueryBuilder.API_USAGE_ENDPOINT_SUBQUERY_TEMPLATE, params);
        applyApiUsageFilters(childQueryBuilder, apiUsageRequest);
        QueryBuilder queryBuilder;
        queryBuilder = new QueryBuilder(QueryBuilder.getApiUsageByTenantTemplate(childQueryBuilder.build()), params);
        if (export) {
            queryBuilder.orderBy(SortField.ReportDate.name(), SortOrder.DESC.name());
        } else {
            makeQueryBuilder(apiUsageRequest, queryBuilder);
        }
        return QueryJobConfiguration.newBuilder(queryBuilder.build()).build();
    }

    /**
     * Build api usage by endpoint query job query job configuration.
     *
     * @param apiUsageRequest the api usage request
     * @param tenantIdList    the tenant id list
     * @param export          the export
     * @return the query job configuration
     */
    protected QueryJobConfiguration buildApiUsageByEndpointQueryJob(ApiUsageReportRequest apiUsageRequest, List<String> tenantIdList, Boolean export) {
        Map<String, Object> params = getDefaultParams(apiDataset, apiTableName, tenantIdList, String.valueOf(apiUsageRequest.getStartDate()), String.valueOf(apiUsageRequest.getEndDate()));
        QueryBuilder childQueryBuilder = new QueryBuilder(QueryBuilder.API_USAGE_ENDPOINT_SUBQUERY_TEMPLATE, params);
        applyApiUsageFilters(childQueryBuilder, apiUsageRequest);
        QueryBuilder queryBuilder;
        queryBuilder = new QueryBuilder(QueryBuilder.getApiUsageEndpointTemplate(childQueryBuilder.build()), params);
        if (export) {
            queryBuilder.orderBy(SortField.ReportDate.name(), SortOrder.DESC.name());
        } else {
            makeQueryBuilder(apiUsageRequest, queryBuilder);
        }
        return QueryJobConfiguration.newBuilder(queryBuilder.build()).build();
    }

    /** CP USAGES */

    protected List<QueryJobConfiguration> buildCpUsageQueryJob(CpUsageRequest cpUsageRequest, Boolean export) {
        Map<String, Object> params = getDefaultParams(rsuDataset, rsuTableName, cpUsageRequest.getTenantIds(), String.valueOf(cpUsageRequest.getStartDate()), String.valueOf(cpUsageRequest.getEndDate()));
        List<QueryJobConfiguration> queryJobConfigurations = new ArrayList<>();

        if (cpUsageRequest.isEntityTypeBreakdown()) {
            List<String> queryTemplates = getChildQueryTemplatesOnEntityRule(cpUsageRequest);
            for (String queryTemplate : queryTemplates) {
                QueryBuilder queryBuilder = new QueryBuilder(queryTemplate, params);
                if (StringUtils.isNotEmpty(cpUsageRequest.getEntityType())) {
                    queryBuilder.addAdditionalANDParam("matchRuleEntityType", cpUsageRequest.getEntityType());
                }

                if (export) {
                    queryBuilder.orderBy(SortField.ReportDate.name(), SortOrder.DESC.name());
                } else {
                    if (Objects.nonNull(cpUsageRequest.getSortField()) && Objects.nonNull(cpUsageRequest.getSortOrder())) {
                        queryBuilder.orderBy(cpUsageRequest.getSortField(), cpUsageRequest.getSortOrder());
                    }

                    if (Objects.nonNull(cpUsageRequest.getSize()) && Objects.nonNull(cpUsageRequest.getOffset())) {
                        queryBuilder.limit(String.valueOf(cpUsageRequest.getSize()));
                        queryBuilder.offset(String.valueOf(cpUsageRequest.getOffset()));
                    }
                }

                queryJobConfigurations.add(QueryJobConfiguration.newBuilder(queryBuilder.build()).build());
            }
        } else {
            QueryBuilder queryBuilder = new QueryBuilder(QueryBuilder.CP_USAGE_TEMPLATE, params);

            if (export) {
                queryBuilder.orderBy(SortField.ReportDate.name(), SortOrder.DESC.name());
            } else {
                if (Objects.nonNull(cpUsageRequest.getSortField()) && Objects.nonNull(cpUsageRequest.getSortOrder())) {
                    queryBuilder.orderBy(cpUsageRequest.getSortField(), cpUsageRequest.getSortOrder());
                }

                if (Objects.nonNull(cpUsageRequest.getSize()) && Objects.nonNull(cpUsageRequest.getOffset())) {
                    queryBuilder.limit(String.valueOf(cpUsageRequest.getSize()));
                    queryBuilder.offset(String.valueOf(cpUsageRequest.getOffset()));
                }
            }

            queryJobConfigurations.add(QueryJobConfiguration.newBuilder(queryBuilder.build()).build());
        }

        return queryJobConfigurations;
    }

    /**
     * Build cp usage by date query job query job configuration.
     *
     * @param cpUsageRequest the cp usage request
     * @param tenantIdList   the tenant id list
     * @param export         the export
     * @return the query job configuration
     */
    protected List<QueryJobConfiguration> buildCpUsageByDateQueryJob(CpUsageReportRequest cpUsageRequest, List<String> tenantIdList, Boolean export) {
        List<QueryJobConfiguration> queryJobs = new ArrayList<>();
        List<String> queryTemplates = getChildQueryTemplatesOnEntityRule(cpUsageRequest);
        Map<String, Object> params = getDefaultParams(rsuDataset, rsuTableName, tenantIdList, String.valueOf(cpUsageRequest.getStartDate()), String.valueOf(cpUsageRequest.getEndDate()));

        for (String queryTemplate : queryTemplates) {
            QueryBuilder childQueryBuilder = new QueryBuilder(queryTemplate, params);
            applyCpUsageFilters(childQueryBuilder, cpUsageRequest);

            QueryBuilder queryBuilder = new QueryBuilder(getCpUsageByDateTemplate(childQueryBuilder.build()), params);
            if (export) {
                queryBuilder.orderBy(SortField.ReportDate.name(), SortOrder.DESC.name());
            } else {
                makeQueryBuilder(cpUsageRequest, queryBuilder);
            }

            queryJobs.add(QueryJobConfiguration.newBuilder(queryBuilder.build()).build());
        }

        return queryJobs;
    }


    /**
     * Build cp usage by tenant query job query job configuration.
     *
     * @param cpUsageRequest the cp usage request
     * @param tenantIdList   the tenant id list
     * @param export         the export
     * @return the query job configuration
     */

    protected QueryJobConfiguration buildCpUsageByTenantQueryJob(CpUsageReportRequest cpUsageRequest, List<String> tenantIdList, Boolean export, String queryTemplate) {
        Map<String, Object> params = getDefaultParams(rsuDataset, rsuTableName, tenantIdList, String.valueOf(cpUsageRequest.getStartDate()), String.valueOf(cpUsageRequest.getEndDate()));
        QueryBuilder childQueryBuilder = new QueryBuilder(queryTemplate, params);
        applyCpUsageFilters(childQueryBuilder, cpUsageRequest);

        QueryBuilder queryBuilder = new QueryBuilder(QueryBuilder.getCpUsageByTenantTemplate(childQueryBuilder.build()), params);
        if (export) {
            queryBuilder.orderBy(SortField.ReportDate.name(), SortOrder.DESC.name());
        } else {
            makeQueryBuilder(cpUsageRequest, queryBuilder);
        }

        return QueryJobConfiguration.newBuilder(queryBuilder.build()).build();
    }

    /* RSU USAGES */

    /**
     * Build rest query job query job configuration.
     *
     * @param complianceRequest the rsu usage request
     * @return the query job configuration
     */
    protected QueryJobConfiguration buildRsuUsageQueryJob(ComplianceRequest complianceRequest) {
        Map<String, Object> params = getDefaultParams(rsuDataset, rsuTableName, complianceRequest.getTenantIds(), String.valueOf(complianceRequest.getStartDate()), String.valueOf(complianceRequest.getEndDate()));
        QueryBuilder queryBuilder = new QueryBuilder(QueryBuilder.RSU_USAGE_QUERY_TEMPLATE, params);
        queryBuilder.orderBy(SortField.ReportDate.name(), SortOrder.DESC.name());
        return QueryJobConfiguration.newBuilder(queryBuilder.build()).build();
    }
    /**
     * Build rsu usage by tenant query job query job configuration.
     *
     * @param rsuUsageRequest the cp usage request
     * @param tenantIdList    the tenant id list
     * @param export          the export
     * @return the query job configuration
     */
    protected QueryJobConfiguration buildRsuUsageByTenantQueryJob(ComplianceReportRequest rsuUsageRequest, List<String> tenantIdList, Boolean export) {
        Map<String, Object> params = getDefaultParams(rsuDataset, rsuTableName, tenantIdList, String.valueOf(rsuUsageRequest.getStartDate()), String.valueOf(rsuUsageRequest.getEndDate()));
        QueryBuilder queryBuilder;
        queryBuilder = new QueryBuilder(QueryBuilder.RSU_USAGE_BY_TENANT_TEMPLATE, params);
        if (export) {
            queryBuilder.orderBy(SortField.ReportDate.name(), SortOrder.DESC.name());
        } else {
            makeQueryBuilder(rsuUsageRequest, queryBuilder);
        }
        return QueryJobConfiguration.newBuilder(queryBuilder.build()).build();
    }

    /**
     * Build rsu usage breakdown query job query job configuration.
     *
     * @param rsuUsageRequest the cp usage request
     * @param tenantIdList    the tenant id list
     * @param export          the export
     * @return the query job configuration
     */
    protected QueryJobConfiguration buildRsuUsageBreakdownQueryJob(ComplianceReportRequest rsuUsageRequest, List<String> tenantIdList, Boolean export) {
        Map<String, Object> params = getDefaultParams(rsuDataset, rsuTableName, tenantIdList, String.valueOf(rsuUsageRequest.getStartDate()), String.valueOf(rsuUsageRequest.getEndDate()));
        QueryBuilder queryBuilder;
        queryBuilder = new QueryBuilder(QueryBuilder.RSU_USAGE_BY_BREAKDOWN_TEMPLATE, params);
        if (export) {
            queryBuilder.orderBy(SortField.ReportDate.name(), SortOrder.DESC.name());
        } else {
            makeQueryBuilder(rsuUsageRequest, queryBuilder);
        }
        return QueryJobConfiguration.newBuilder(queryBuilder.build()).build();
    }


    /* UTILS */
    /**
     * Make query builder.
     *
     * @param cpUsageRequest the cp usage request
     * @param queryBuilder   the query builder
     */
    protected void makeQueryBuilder(ComplianceReportRequest cpUsageRequest, QueryBuilder queryBuilder) {
        if (Objects.nonNull(cpUsageRequest.getSortField()) && Objects.nonNull(cpUsageRequest.getSortOrder())) {
            queryBuilder.orderBy(cpUsageRequest.getSortField(), cpUsageRequest.getSortOrder());
        } else {
            queryBuilder.orderBy(SortField.ReportDate.name(), SortOrder.DESC.name());
        }
        if (Objects.nonNull(cpUsageRequest.getSize()) && Objects.nonNull(cpUsageRequest.getOffset())) {
            queryBuilder.limit(String.valueOf(cpUsageRequest.getSize()));
            queryBuilder.offset(String.valueOf(cpUsageRequest.getOffset()));
        }
    }
    protected void applyApiUsageFilters(QueryBuilder childQueryBuilder, ApiUsageReportRequest apiUsageRequest) {
        if (StringUtils.isNotEmpty(apiUsageRequest.getEndPoint())) {
            childQueryBuilder.addAdditionalANDParam("HandlerMapping", apiUsageRequest.getEndPoint());
        }
        if (StringUtils.isNotEmpty(apiUsageRequest.getHttpMethod())) {
            childQueryBuilder.addAdditionalANDParam("HTTPMethod", apiUsageRequest.getHttpMethod());
        }
        if (apiUsageRequest.getSourceSystem() != null && apiUsageRequest.getSourceSystem().length != 0) {
            childQueryBuilder.addINParam("SourceSystem", apiUsageRequest.getSourceSystem());
        }

    }

    protected void applyCpUsageFilters(QueryBuilder childQueryBuilder, CpUsageReportRequest cpUsageRequest) {
        if (StringUtils.isNotEmpty(cpUsageRequest.getEntityType())) {
            childQueryBuilder.addAdditionalANDParam("matchRuleEntityType", cpUsageRequest.getEntityType());
        }
    }

    protected Double calculateAverageUsage(Long totalUsage, String month, String year) {
        return calculateAverageUsageCommon(totalUsage, month, year);
    }

    protected Double calculateAverageUsage(String maxUsage, String month, String year) {
        double maxUsageValue = Double.parseDouble(maxUsage.isEmpty() ? "0.00" : maxUsage);
        return calculateAverageUsageCommon(maxUsageValue, month, year);
    }

    private double calculateAverageUsageCommon(double totalUsage, String month, String year) {
        int monthNumber = Month.valueOf(month.toUpperCase(Locale.ROOT)).getValue();
        int yearNumber = Integer.parseInt(year);
        YearMonth yearMonth = YearMonth.of(yearNumber, monthNumber);
        int numberOfDaysInMonth = yearMonth.lengthOfMonth();
        LocalDate currentDate = LocalDate.now();
        if (currentDate.getMonthValue() == monthNumber && currentDate.getYear() == yearNumber) {
            long daysTillYesterday = ChronoUnit.DAYS.between(yearMonth.atDay(1), currentDate.minusDays(1));
            numberOfDaysInMonth = (int) daysTillYesterday + 1;
            numberOfDaysInMonth = Math.max(numberOfDaysInMonth, 1);
        }
        double averageUsage = totalUsage / numberOfDaysInMonth;
        DecimalFormat df = new DecimalFormat("#.##");
        df.setRoundingMode(RoundingMode.HALF_UP);
        String roundedAverage = df.format(averageUsage);
        return Double.parseDouble(roundedAverage);
    }

    protected String getChildQueryTemplatesOnCpUsage(CpUsageReportRequest cpUsageRequest) {
        if ("nonMatchRules".equals(cpUsageRequest.getEntityRule())) {
            return QueryBuilder.CP_USAGE_BY_ENTITY_FOR_NON;
        } else if (StringUtils.isEmpty(cpUsageRequest.getEntityRule())) {
            return QueryBuilder.CP_USAGE_ENTITY_TEMPLATE;
        }
        return QueryBuilder.CP_USAGE_ENTITY_TEMPLATE;
    }

    private static List<String> getChildQueryTemplates(String entityRule) {
        List<String> queryTemplates = new ArrayList<>();
        if (entityRule != null && !entityRule.isEmpty()) {
            // If both matchRule and nonMatchRule are present
            if ("bothRules".equals(entityRule)) {
                queryTemplates.add(QueryBuilder.CP_USAGE_ENTITY_TEMPLATE);
                queryTemplates.add(QueryBuilder.CP_USAGE_BY_ENTITY_FOR_NON);
            }
            // If only nonMatchRule is present
            else if ("nonMatchRules".equals(entityRule)) {
                queryTemplates.add(QueryBuilder.CP_USAGE_BY_ENTITY_FOR_NON);
            }
        }
        if (queryTemplates.isEmpty()) {
            queryTemplates.add(QueryBuilder.CP_USAGE_ENTITY_TEMPLATE);
        }

        return queryTemplates;
    }

    protected List<String> getChildQueryTemplatesOnEntityRule(CpUsageReportRequest cpUsageReportRequest) {
        return getChildQueryTemplates(cpUsageReportRequest.getEntityRule());
    }

    protected List<String> getChildQueryTemplatesOnEntityRule(CpUsageRequest cpUsageRequest) {
        return getChildQueryTemplates(cpUsageRequest.getEntityRule());
    }



}
