package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

import java.util.Objects;

@Getter
public class SleepPipelineTaskConfig extends AbstractPipelineTaskConfig {
    final Long sleepTime;

    @JsonCreator
    public SleepPipelineTaskConfig(
            @JsonProperty(value = "name") String name,
            @JsonProperty(value = "sleepTime") Long sleepTime) {
        super(name, TaskType.SLEEP_TASK);
        this.sleepTime = sleepTime;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SleepPipelineTaskConfig that)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        return Objects.equals(getSleepTime(), that.getSleepTime());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getSleepTime());
    }

    @Override
    public String toString() {
        return "SleepPipelineTaskConfig{" +
                "sleepTime=" + sleepTime +
                "} " + super.toString();
    }
}
