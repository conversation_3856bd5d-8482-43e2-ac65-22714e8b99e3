import com.google.api.core.AbstractApiFuture;
import com.google.api.core.ApiFuture;
import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.SetOptions;
import com.google.cloud.functions.Context;
import com.google.common.testing.TestLogHandler;
import com.reltio.pms.trail.TriggerJobsFunction;
import com.reltio.pms.trail.domain.AuthSecrets;
import com.reltio.pms.trail.service.GenericException;
import com.reltio.pms.trail.service.impl.SecretsServiceImpl;
import com.reltio.pms.trail.service.impl.SimpleReltioAPIServiceImpl;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.logging.LogRecord;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.reltio.pms.trail.FunctionConstants.*;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class TriggerJobsTest {

    private static final TestLogHandler LOG_HANDLER = new TestLogHandler();
    private static final Logger logger = Logger.getLogger(TriggerJobsFunction.class.getName());
    private TriggerJobsFunction.FirestoreEvent event;
    private Context context;
    private ApiFuture apiFuture, wrtieApiFuture;
    Set<LogRecord> messages;

    @Mock
    Firestore firestore;

    @Mock
    SecretsServiceImpl secretService;

    @Mock
    SimpleReltioAPIServiceImpl reltioAPIService;

    @Mock
    DocumentReference documentReference;

    @Before
    public void beforeTest() throws Exception {
        logger.addHandler(LOG_HANDLER);
        context = new Context() {
            @Override
            public String eventId() {
                return "001";
            }

            @Override
            public String timestamp() {
                return "1622006650";
            }

            @Override
            public String eventType() {
                return "cloud.firestore.document.create";
            }

            @Override
            public String resource() {
                return "DATABASE/documents/REQUESTERS_tst-01/email@example/OFFER_TYPES/Nonexistent-Free";
            }
        };
        event = new TriggerJobsFunction.FirestoreEvent();
        apiFuture = new AbstractApiFuture() {
            @Override
            public Object get() throws InterruptedException, ExecutionException {
                return true;
            }
        };
        when(secretService.getSecrets()).thenReturn(new AuthSecrets("", ""));
        when(reltioAPIService.post(any(), anyString())).thenReturn("{\"jobId\" : \"job001\"}");
        wrtieApiFuture = new AbstractApiFuture() {
            @Override
            public Object get() throws InterruptedException, ExecutionException {
                return "{\"jobId\" : \"job001\"}";
            }
        };
        when(firestore.document(anyString())).thenReturn(documentReference);
        when(documentReference.set(any(Object.class), any(SetOptions.class))).thenReturn(wrtieApiFuture);
    }

    @After
    public void afterTest() {
        LOG_HANDLER.clear();
    }

    @Test
    public void TriggerJobsTestPositive() throws Exception {
        Map<String, Object> innerFields = new HashMap<>();
        Map<String, Map<String, Object>> fields = new HashMap<>();
        innerFields.put(FS_BOOLEAN_VALUE, true);
        fields.put(REQ_TERMS_ACCEPTED, innerFields);
        TriggerJobsFunction.FirestoreValue value = new TriggerJobsFunction.FirestoreValue();
        value.setFields(fields);
        event.setValue(value);

        when(firestore.runTransaction(any())).thenReturn(apiFuture);
        when(documentReference.update(any())).thenReturn(wrtieApiFuture);

        new TriggerJobsFunction(firestore, secretService, reltioAPIService).accept(event, context);

        verify(firestore, times(1)).runTransaction(any());

        messages = new LinkedHashSet<>(LOG_HANDLER.getStoredLogRecords());
        List<String> msg = messages.stream().map(LogRecord::getMessage).collect(Collectors.toList());
        String message = msg.get(0);
        assertTrue(message.contains("Calling Create Job API"));
        message = msg.get(1);
        assertTrue(message.contains("Create Job API completed:"));
        message = msg.get(2);
        assertTrue(message.contains("Updating the Firestore with Job ID"));
        message = msg.get(3);
        assertTrue(message.contains("Completed updating the Firestore with Job ID"));
        message = msg.get(4);
        assertTrue(message.contains("The Job Creation for Requester Completed Successfully"));

    }

    @Test
    public void TriggerJobsAlreadyExistingJobTest() throws Exception {
        Map<String, Object> innerFields = new HashMap<>();
        Map<String, Map<String, Object>> fields = new HashMap<>();
        innerFields.put(FS_BOOLEAN_VALUE, true);
        fields.put(REQ_TERMS_ACCEPTED, innerFields);
        innerFields.clear();
        innerFields.put(FS_STRING_VALUE, "001");
        fields.put(REQ_JOB_ID, innerFields);
        TriggerJobsFunction.FirestoreValue value = new TriggerJobsFunction.FirestoreValue();
        value.setFields(fields);
        event.setValue(value);

        new TriggerJobsFunction(firestore, secretService, reltioAPIService).accept(event, context);

        String message = LOG_HANDLER.getStoredLogRecords().get(0).getMessage();
        assertTrue(message.contains("Requester already have the Job associated with that. No action needed here"));
        verifyNoInteractions(firestore);
        verifyNoInteractions(documentReference);
        verifyNoInteractions(reltioAPIService);
        verifyNoInteractions(secretService);
    }

    @Test
    public void TriggerJobsTermsNotAcceptedTest() throws Exception {
        Map<String, Object> innerFields = new HashMap<>();
        Map<String, Map<String, Object>> fields = new HashMap<>();
        innerFields.put(FS_BOOLEAN_VALUE, false);
        fields.put(REQ_TERMS_ACCEPTED, innerFields);
        TriggerJobsFunction.FirestoreValue value = new TriggerJobsFunction.FirestoreValue();
        value.setFields(fields);
        event.setValue(value);

        new TriggerJobsFunction(firestore, secretService, reltioAPIService).accept(event, context);

        verifyNoInteractions(firestore);
        verifyNoInteractions(documentReference);
        verifyNoInteractions(reltioAPIService);
        verifyNoInteractions(secretService);
    }

    @Test
    public void TriggerJobsNoLockObtainedTest() throws Exception {
        Map<String, Object> innerFields = new HashMap<>();
        Map<String, Map<String, Object>> fields = new HashMap<>();
        innerFields.put(FS_BOOLEAN_VALUE, true);
        fields.put(REQ_TERMS_ACCEPTED, innerFields);
        TriggerJobsFunction.FirestoreValue value = new TriggerJobsFunction.FirestoreValue();
        value.setFields(fields);
        event.setValue(value);
        ApiFuture apiFutureFalseResponse = new AbstractApiFuture() {
            @Override
            public Object get() throws InterruptedException, ExecutionException {
                return false;
            }
        };

        when(firestore.runTransaction(any())).thenReturn(apiFutureFalseResponse);

        new TriggerJobsFunction(firestore, secretService, reltioAPIService).accept(event, context);

        verify(firestore, times(1)).document(anyString());
        verify(firestore, times(1)).runTransaction(any());
        verifyNoMoreInteractions(firestore);
        verifyNoInteractions(documentReference);
        verifyNoInteractions(reltioAPIService);
        verifyNoInteractions(secretService);
    }

    @Test
    public void TriggerJobsSecretsExceptionTest() throws Exception {
        Map<String, Object> innerFields = new HashMap<>();
        Map<String, Map<String, Object>> fields = new HashMap<>();
        innerFields.put(FS_BOOLEAN_VALUE, true);
        fields.put(REQ_TERMS_ACCEPTED, innerFields);
        TriggerJobsFunction.FirestoreValue value = new TriggerJobsFunction.FirestoreValue();
        value.setFields(fields);
        event.setValue(value);

        when(firestore.runTransaction(any())).thenReturn(apiFuture);
        when(secretService.getSecrets()).thenThrow(new NullPointerException());
        when(documentReference.update(any())).thenReturn(wrtieApiFuture);

        new TriggerJobsFunction(firestore, secretService, reltioAPIService).accept(event, context);

        verify(firestore, times(2)).document(anyString());
        verify(firestore, times(1)).runTransaction(any());
        verify(documentReference, times(1)).update(any());
        verifyNoMoreInteractions(documentReference);
        verifyNoMoreInteractions(firestore);
        verifyNoInteractions(reltioAPIService);

        messages = new LinkedHashSet<>(LOG_HANDLER.getStoredLogRecords());
        List<String> msg = messages.stream().map(LogRecord::getMessage).collect(Collectors.toList());
        String message = msg.get(0);
        assertTrue(message.contains("The Job creation failed for"));
        message = msg.get(1);
        assertTrue(message.contains("Deleting the Firestore with Job ID"));
        message = msg.get(2);
        assertTrue(message.contains("Completed deleting JOB ID in the Firestore"));
    }

    @Test
    public void TriggerJobsReltioApiExceptionTest() throws Exception {
        Map<String, Object> innerFields = new HashMap<>();
        Map<String, Map<String, Object>> fields = new HashMap<>();
        innerFields.put(FS_BOOLEAN_VALUE, true);
        fields.put(REQ_TERMS_ACCEPTED, innerFields);
        TriggerJobsFunction.FirestoreValue value = new TriggerJobsFunction.FirestoreValue();
        value.setFields(fields);
        event.setValue(value);

        when(firestore.runTransaction(any())).thenReturn(apiFuture);
        when(reltioAPIService.post(any(), any())).thenThrow(new GenericException("something happened"));
        when(documentReference.update(any())).thenReturn(wrtieApiFuture);

        new TriggerJobsFunction(firestore, secretService, reltioAPIService).accept(event, context);

        verify(firestore, times(2)).document(anyString());
        verify(firestore, times(1)).runTransaction(any());
        verify(documentReference, times(1)).update(any());
        verify(reltioAPIService, times(1)).post(any(), any());
        verifyNoMoreInteractions(documentReference);
        verifyNoMoreInteractions(firestore);
        verifyNoMoreInteractions(reltioAPIService);

        messages = new LinkedHashSet<>(LOG_HANDLER.getStoredLogRecords());
        List<String> msg = messages.stream().map(LogRecord::getMessage).collect(Collectors.toList());
        String message = msg.get(0);
        assertTrue(message.contains("Calling Create Job API"));
        message = msg.get(1);
        assertTrue(message.contains("The Job creation failed for"));
        message = msg.get(2);
        assertTrue(message.contains("Deleting the Firestore with Job ID"));
        message = msg.get(3);
        assertTrue(message.contains("Completed deleting JOB ID in the Firestore"));
    }

    @Test
    public void TriggerJobsDocRefUpdateExceptionTest() throws Exception {
        Map<String, Object> innerFields = new HashMap<>();
        Map<String, Map<String, Object>> fields = new HashMap<>();
        innerFields.put(FS_BOOLEAN_VALUE, true);
        fields.put(REQ_TERMS_ACCEPTED, innerFields);
        TriggerJobsFunction.FirestoreValue value = new TriggerJobsFunction.FirestoreValue();
        value.setFields(fields);
        event.setValue(value);

        when(firestore.runTransaction(any())).thenReturn(apiFuture);
        when(reltioAPIService.post(any(), any())).thenThrow(new GenericException("something happened"));
        when(documentReference.update(any())).thenThrow(InterruptedException.class);

        new TriggerJobsFunction(firestore, secretService, reltioAPIService).accept(event, context);

        verify(firestore, times(2)).document(anyString());
        verify(firestore, times(1)).runTransaction(any());
        verify(documentReference, times(1)).update(any());
        verify(reltioAPIService, times(1)).post(any(), any());
        verifyNoMoreInteractions(documentReference);
        verifyNoMoreInteractions(firestore);
        verifyNoMoreInteractions(reltioAPIService);

        messages = new LinkedHashSet<>(LOG_HANDLER.getStoredLogRecords());
        List<String> msg = messages.stream().map(LogRecord::getMessage).collect(Collectors.toList());
        String message = msg.get(0);
        assertTrue(message.contains("Calling Create Job API"));
        message = msg.get(1);
        assertTrue(message.contains("The Job creation failed for"));
        message = msg.get(2);
        assertTrue(message.contains("Deleting the Firestore with Job ID"));
        message = msg.get(3);
        assertTrue(message.contains("Error updating Firestore document:"));
    }

    @Test
    public void TriggerJobsDocRefSetExceptionTest() throws Exception {
        Map<String, Object> innerFields = new HashMap<>();
        Map<String, Map<String, Object>> fields = new HashMap<>();
        innerFields.put(FS_BOOLEAN_VALUE, true);
        fields.put(REQ_TERMS_ACCEPTED, innerFields);
        TriggerJobsFunction.FirestoreValue value = new TriggerJobsFunction.FirestoreValue();
        value.setFields(fields);
        event.setValue(value);

        when(firestore.runTransaction(any())).thenReturn(apiFuture);
        when(documentReference.update(any())).thenReturn(wrtieApiFuture);
        when(documentReference.set(any(Object.class), any(SetOptions.class))).thenThrow(InterruptedException.class);

        new TriggerJobsFunction(firestore, secretService, reltioAPIService).accept(event, context);

        verify(firestore, times(2)).document(anyString());
        verify(firestore, times(1)).runTransaction(any());
        verify(documentReference, times(1)).set(any(Object.class), any(SetOptions.class));
        verify(reltioAPIService, times(1)).post(any(), any());
        verifyNoMoreInteractions(documentReference);
        verifyNoMoreInteractions(firestore);
        verifyNoMoreInteractions(reltioAPIService);

        messages = new LinkedHashSet<>(LOG_HANDLER.getStoredLogRecords());
        List<String> msg = messages.stream().map(LogRecord::getMessage).collect(Collectors.toList());
        String message = msg.get(0);
        assertTrue(message.contains("Calling Create Job API"));
        message = msg.get(1);
        assertTrue(message.contains("Create Job API completed:"));
        message = msg.get(2);
        assertTrue(message.contains("Updating the Firestore with Job ID"));
        message = msg.get(3);
        assertTrue(message.contains("Error updating Firestore document:"));
        message = msg.get(4);
        assertTrue(message.contains("The Job Creation for Requester Completed Successfully:"));
    }
}
