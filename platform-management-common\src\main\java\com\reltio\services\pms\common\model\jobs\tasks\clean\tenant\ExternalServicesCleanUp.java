package com.reltio.services.pms.common.model.jobs.tasks.clean.tenant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExternalServicesCleanUp {

    @JsonProperty(value = "workflow")
    private boolean workflow;

    @JsonCreator
    public ExternalServicesCleanUp(@JsonProperty(value = "workflow")  boolean workflow) {
        this.workflow = workflow;
    }

    public boolean isWorkflow() {
        return workflow;
    }

    public void setWorkflow(boolean workflow) {
        this.workflow = workflow;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if(o == null || this.getClass() != o.getClass()) return false;

        ExternalServicesCleanUp that = (ExternalServicesCleanUp) o;
        return isWorkflow() == that.isWorkflow();
    }

    @Override
    public int hashCode() {
        return Objects.hash(isWorkflow());
    }

    @Override
    public String toString() {
        return "ExternalServicesCleanUp{" +
                "workflow=" + workflow +
                '}';
    }

}
