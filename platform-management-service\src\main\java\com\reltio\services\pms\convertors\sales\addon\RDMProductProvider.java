package com.reltio.services.pms.convertors.sales.addon;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.common.sales.model.addon.RdmProductConfig;
import com.reltio.services.pms.convertors.sales.AbstractTenantAddOnProductProvider;
import com.reltio.services.pms.service.SalesPackageService;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

@Service
public class RDMProductProvider extends AbstractTenantAddOnProductProvider<RdmProductConfig> {


    public RDMProductProvider(SalesPackageService salesPackageService) {
        super(salesPackageService);
    }


    @Override
    public PMSProductName getProductName() {
        return PMSProductName.RDM;
    }

    @Override
    public Map<String, Set<String>> getProductCodesByTenant() {
        return salesPackageService.getSalesAddOnsByProductCodes(PMSProductName.RDM);
    }

    @Override
    public BaseProductConfig getProductConfig(Set<SalesConfig> salesConfigs, Set<String> tenantCodes, String currentTenantCode) {
        if (salesConfigs.isEmpty()) {
            return null;
        }

        RdmProductConfig rdmProductConfig = new RdmProductConfig();
        rdmProductConfig.setPmsProductName(getProductName());
        rdmProductConfig.addAllSalesConfigs(salesConfigs);
        rdmProductConfig.setQuantity(getQuantity(rdmProductConfig));
        return rdmProductConfig;
    }

}
