package com.reltio.services.pms.common.model.supportability.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.reltio.services.pms.common.model.supportability.Facet;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class TenantHealthParams extends Params {
    private final String healthCheckId;

    public TenantHealthParams(Long startTime, Long endTime, String tenantId, String healthCheckId, Facet facet) {
        super(startTime, endTime, tenantId, facet);
        this.healthCheckId = healthCheckId;
    }
}
