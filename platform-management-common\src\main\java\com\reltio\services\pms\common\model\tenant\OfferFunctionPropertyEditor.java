package com.reltio.services.pms.common.model.tenant;

import java.beans.PropertyEditorSupport;

public class OfferFunctionPropertyEditor extends PropertyEditorSupport {

    @Override
    public String getAsText() {
        OfferFunction offerFunction = (OfferFunction) getValue();
        return offerFunction == null ? null : offerFunction.getValue();
    }

    @Override
    public void setAsText(String text) {
        setValue(OfferFunction.convertFromString(text));
    }
}
