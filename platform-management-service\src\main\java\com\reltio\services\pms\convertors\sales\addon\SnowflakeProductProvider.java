package com.reltio.services.pms.convertors.sales.addon;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.convertors.sales.AbstractTenantAddOnProductProvider;
import com.reltio.services.pms.service.SalesPackageService;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

@Service
public class SnowflakeProductProvider extends AbstractTenantAddOnProductProvider<BaseProductConfig> {


    public SnowflakeProductProvider(SalesPackageService salesPackageService) {
        super(salesPackageService);
    }


    @Override
    public PMSProductName getProductName() {
        return PMSProductName.SNOWFLAKE_CONNECTOR;
    }

    @Override
    public Map<String, Set<String>> getProductCodesByTenant() {
        return salesPackageService.getSalesAddOnsByProductCodes(PMSProductName.SNOWFLAKE_CONNECTOR);
    }

    @Override
    public BaseProductConfig getProductConfig(Set<SalesConfig> salesConfigs, Set<String> tenantCodes, String currentTenantCode) {
        if (salesConfigs.isEmpty()) {
            return null;
        }

        BaseProductConfig productConfig = new BaseProductConfig();
        productConfig.setPmsProductName(getProductName());
        productConfig.addAllSalesConfigs(salesConfigs);
        productConfig.setQuantity(getQuantity(productConfig));
        return productConfig;
    }

}
