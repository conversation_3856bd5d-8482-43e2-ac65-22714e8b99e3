package com.reltio.services.pms.service.compliance.impl;

import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuBreakdownModel;
import com.reltio.services.pms.common.model.compliance.response.rsuusage.RsuBreakdownResponse;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.SalesAccount;
import com.reltio.services.pms.service.compliance.RsuUsageService;
import com.reltio.services.pms.service.compliance.util.ComplianceUtil;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * The type Rsu usage service.
 */
@Service
public class RsuUsageServiceImpl extends ComplianceUtil implements RsuUsageService {
    @Override
    public RsuBreakdownResponse getRsuBreakdownResponse(Collection<SalesAccount> accountCollection, Collection<ReltioTenant> tenantCollection, RsuBreakdownResponse rsuBreakdownResponse) {
        List<RsuBreakdownModel> breakdownModelList = rsuBreakdownResponse.getRsuBreakdownModelList();
        for (RsuBreakdownModel breakdownModel : breakdownModelList) {
            String tenantId = breakdownModel.getTenantId();
            if (tenantId != null) {
                breakdownModel.setTenantPurpose(getPurpose(tenantId, tenantCollection));
            }
        }

        rsuBreakdownResponse.setRsuBreakdownModelList(breakdownModelList);
        return rsuBreakdownResponse;
    }
}
