package com.reltio.services.pms.common.model.compliance.response.cpusage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The type Cp usage by month model.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class CpUsageByMonthModel {

    /**
     * The Year.
     */
    @JsonProperty("year")
    String year;

    /**
     * The Month.
     */
    @JsonProperty("month")
    String month;

    /**
     * The Cp usage.
     */
    @JsonProperty("maxCpUsage")
    Long maxCpUsage;
}
