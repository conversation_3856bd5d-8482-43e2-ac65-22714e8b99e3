package com.reltio.services.pms.clients.reltio.dnb;

public class DNBConnectorClientHelper {

    public String createUrlForGetCreatedMapping(String profile, String baseUrl) {
        return String.format("%s/b2bIntegration/config/profiles/%s/mappings/config", baseUrl, profile);
    }

    public String createUrlToRegisterProfile(String profile, String baseUrl) {
        return String.format("%s/b2bIntegration/config/profiles/%s/tenants", baseUrl,  profile);
    }

    public String createUrlForGetProfile(String baseUrl) {
        return String.format("%s/b2bIntegration/config/tenants", baseUrl);
    }

    public String createUrlForCreateProfile(String profile, String baseUrl) {
        return String.format("%s/b2bIntegration/config/profiles/%s", baseUrl, profile);
    }

    public String createUrlToAddDnbMappingsToProfile(String profile, String baseUrl, String mapKey) {
        return String.format("%s/b2bIntegration/config/profiles/%s/mappings/%s", baseUrl, profile, mapKey);
    }

}
