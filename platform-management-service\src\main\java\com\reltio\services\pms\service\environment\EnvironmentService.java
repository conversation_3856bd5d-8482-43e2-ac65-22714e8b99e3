package com.reltio.services.pms.service.environment;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableSet;
import com.reltio.collection.CollectionUtils;
import com.reltio.devops.common.environment.CloudProvider;
import com.reltio.devops.common.environment.ServicePurpose;
import com.reltio.services.pms.clients.external.bitbucket.BitBucketClient;
import com.reltio.services.pms.clients.external.bitbucket.BitBucketClientFactory;
import com.reltio.services.pms.clients.external.gcp.pubsub.GBTClient;
import com.reltio.services.pms.common.exception.InvalidDocumentIdException;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.AllowedOptions;
import com.reltio.services.pms.common.model.Cluster;
import com.reltio.services.pms.common.model.DataSetLocation;
import com.reltio.services.pms.common.model.Environment;
import com.reltio.services.pms.common.model.EnvironmentAccessType;
import com.reltio.services.pms.common.model.EnvironmentParam;
import com.reltio.services.pms.common.model.EnvironmentPurpose;
import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.common.model.compliance.response.EnvironmentValidateResponse;
import com.reltio.services.pms.common.model.compliance.response.Error;
import com.reltio.services.pms.common.model.compliance.response.Warning;
import com.reltio.services.pms.dao.EnvironmentDao;
import com.reltio.services.pms.service.StorageTemplateService;
import com.reltio.services.pms.service.Util;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.reltio.devops.common.environment.ServiceType.COSMOSDB;
import static com.reltio.services.pms.clients.external.gcp.pubsub.GBTClient.MAX_TABLE_COUNT;

@Service
public class EnvironmentService {

    public static final String ENVIRONMENT_ACCESS_TYPE = "environmentAccessType";
    private static final Logger LOG = Logger.getLogger(EnvironmentService.class);
    private static final String CASSANDRA_CLUSTER_TYPE = "cassandra";
    private static final String SERVICE_HISTORY_TYPE = "history";
    private static final String ELASTICSEARCH_CLUSTER_TYPE = "elasticsearch";
    private static final String BIT_BUCKET_PATH = "%s/%s.json";
    private static final ImmutableSet<com.reltio.devops.common.environment.ServiceType> VALID_DATA_STORAGE = ImmutableSet.of(com.reltio.devops.common.environment.ServiceType.DYNAMODB, com.reltio.devops.common.environment.ServiceType.CASSANDRA, com.reltio.devops.common.environment.ServiceType.SPANNERDB, COSMOSDB);
    private static final ImmutableSet<com.reltio.devops.common.environment.ServiceType> VALID_MATCH_STORAGE = ImmutableSet.of(com.reltio.devops.common.environment.ServiceType.DYNAMODB, com.reltio.devops.common.environment.ServiceType.CASSANDRA, com.reltio.devops.common.environment.ServiceType.SPANNERDB, COSMOSDB);
    private static final ImmutableSet<com.reltio.devops.common.environment.ServiceType> VALID_HISTORY_STORAGE =
            ImmutableSet.of(com.reltio.devops.common.environment.ServiceType.GBT,
                    com.reltio.devops.common.environment.ServiceType.CASSANDRA, com.reltio.devops.common.environment.ServiceType.DYNAMODB, COSMOSDB);
    private static final Logger LOGGER = Logger.getLogger(EnvironmentService.class);
    private final EnvironmentDao environmentDao;
    private final ClusterService clusterService;
    private final BitBucketClient bitBucketClient;
    private final String repoName;
    private final String branch;
    private final String folder;
    private final ObjectMapper objectMapper;
    private final StorageTemplateService storageTemplateService;
    private final GBTClient gbtClient;
    private final boolean isUnitTest;

    @Autowired
    public EnvironmentService(EnvironmentDao environmentDao,
                              ClusterService clusterService, BitBucketClientFactory bitBucketClientFactory,
                              StorageTemplateService storageTemplateService,
                              @Value("${bitBucket.userName}") String bitBucketUserName,
                              @Value("${bitbucket.pwd}") String bitBucketPassword,
                              @Value("${reltio.environments.bitBucket.repoName}") String repoName,
                              @Value("${reltio.environments.bitBucket.branch}") String branch,
                              @Value("${reltio.environments.bitBucket.folder}") String folder,
                              @Value("${unittest:false}") String isUnitTest,
                              GBTClient gbtClient) {
        this.clusterService = clusterService;
        this.bitBucketClient = bitBucketClientFactory.createClient(bitBucketUserName, bitBucketPassword);
        this.storageTemplateService = storageTemplateService;
        this.repoName = repoName;
        this.branch = branch;
        this.folder = folder;
        this.environmentDao = environmentDao;
        this.objectMapper = new ObjectMapper();
        this.gbtClient = gbtClient;
        this.isUnitTest = Boolean.getBoolean(isUnitTest);
        Set<String> properties = new HashSet<>(Arrays.asList(bitBucketUserName, bitBucketPassword, repoName, branch, folder));
        PropertiesValidator.validateProperties(properties, LOGGER);

    }

    private static void validateAllowedOptions(AllowedOptions allowedOptions, Error error) {
        if (allowedOptions == null) {
            error.getMissingEnvironmentParam().add("environmentParam.allowedOptions");
        } else {
            if (CollectionUtils.isEmpty(allowedOptions.getAllowedCustomerTypes())) {
                error.getMissingEnvironmentParam().add("environmentParam.allowedOptions.allowedCustomerTypes");
            }
            if (CollectionUtils.isEmpty(allowedOptions.getAllowedTenantPurposes())) {
                error.getMissingEnvironmentParam().add("environmentParam.allowedOptions.allowedTenantPurposes");
            }
            if (CollectionUtils.isEmpty(allowedOptions.getAllowedTenantSizes())) {
                error.getMissingEnvironmentParam().add("environmentParam.allowedOptions.allowedTenantSizes");
            }
            if (CollectionUtils.isEmpty(allowedOptions.getAllowedDepartments())) {
                error.getMissingEnvironmentParam().add("environmentParam.allowedOptions.allowedDepartments");
            }
        }
    }

    private static void validateDefaultUrls(Map<ServiceType, String> defaultUrls, Warning warning) {
        if (defaultUrls != null) {
            Set<String> defaultUrlNotPresent = Environment.defaultUrlList.stream().filter(e -> !defaultUrls.containsKey(e))
                    .map(Enum::name).collect(Collectors.toSet());
            defaultUrlNotPresent.addAll(defaultUrls.entrySet().stream().filter(e -> StringUtils.isBlank(e.getValue())).map(m -> m.getKey().name()).collect(Collectors.toSet()));
            warning.setMissingDefaultUrls(defaultUrlNotPresent);
        } else {
            warning.setMissingDefaultUrls(Environment.defaultUrlList.stream().map(Enum::name).collect(Collectors.toSet()));
        }
    }

    private static void validateStoragePriorityMap(Map<ServicePurpose, List<com.reltio.devops.common.environment.ServiceType>> storagePriorityMap, Warning warning) {

        if (storagePriorityMap != null) {
            Set<String> storagePriorityWarning = storagePriorityMap.entrySet().stream().filter(e -> e.getValue() != null).filter(e -> e.getValue().size() > 1)
                    .map(e -> e.getKey() + " -> " + e.getValue()).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(storagePriorityMap.get(ServicePurpose.DATA))) {
                storagePriorityWarning.add(ServicePurpose.DATA.name() + " is missing");
            }
            if (CollectionUtils.isEmpty(storagePriorityMap.get(ServicePurpose.MATCH))) {
                storagePriorityWarning.add(ServicePurpose.MATCH.name() + " is missing");
            }
            if (CollectionUtils.isEmpty(storagePriorityMap.get(ServicePurpose.HISTORY))) {
                storagePriorityWarning.add(ServicePurpose.HISTORY.name() + " is missing");
            }
            warning.setStoragePriorityList(storagePriorityWarning);
        } else {
            warning.setStoragePriorityList(Collections.singleton("noStoragePriorityList"));
        }
    }

    public boolean getUnitTest() {
        return isUnitTest;
    }

    public Environment updateEnvironmentURL(String environmentName, String environmentUrl) {
        Environment existingEnvironment = getEnvironment(environmentName);
        existingEnvironment.setUrl(environmentUrl);
        environmentDao.update(existingEnvironment);
        return existingEnvironment;
    }

    @Cacheable("environments")
    public Environment getEnvironment(String name) {
        try {
            return environmentDao.get(name);
        } catch (InvalidDocumentIdException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.ENVIRONMENT_IS_NOT_REGISTERED, HttpStatus.INTERNAL_SERVER_ERROR.value(), name);
        }
    }

    public Collection<Environment> getAllEnvironments() {
        return environmentDao.getAll();
    }

    public Optional<Environment> getEnvironmentSafe(String name) {
        try {
            return Optional.of(environmentDao.get(name));
        } catch (InvalidDocumentIdException e) {
            return Optional.empty();
        }
    }

    public Optional<Cluster> getCustomerSpecificCluster(String environment, String type, String service, String customerName) {
        Set<String> clusters = getEnvironment(environment).getClusters();
        return clusters.stream()
                .map(clusterService::get)
                .filter(e -> type.equalsIgnoreCase(e.getType()))
                .filter(e -> e.getServices().contains(service))
                .filter(Cluster::getNewAllocationsPossible)
                .filter(e -> e.getCustomers() != null && e.getCustomers().contains(customerName))
                .findFirst();
    }

    public Optional<Cluster> getCustomerSpecificClusterGbt(String environment, String service, String customerName, String historyBigTableProject) {
        Set<String> clusters = getEnvironment(environment).getClusters();
        List<Cluster> s = clusters.stream()
                .map(clusterService::get)
                .filter(e -> "gbt".equalsIgnoreCase(e.getType()))
                .filter(e -> e.getServices().contains(service))
                .filter(Cluster::getNewAllocationsPossible)
                .filter(e -> e.getCustomers() != null && e.getCustomers().contains(customerName)).collect(Collectors.toList());
        return s.stream().collect(Collectors.toMap(Function.identity(),
                        e -> gbtClient.checkTableCount(historyBigTableProject, e.getUrlList().get(0))))
                .entrySet().stream().sorted(Map.Entry.comparingByValue(Comparator.naturalOrder()))
                .filter(v -> v.getValue() < MAX_TABLE_COUNT).map(Map.Entry::getKey)
                .findFirst();
    }

    public boolean isCustomerSpecificClustersExist(String environment, String customerName, String type) {
        Set<String> clusters = getEnvironment(environment).getClusters();
        return clusters.stream()
                .map(clusterService::get)
                .filter(Cluster::getNewAllocationsPossible)
                .filter(e -> type.equalsIgnoreCase(e.getType()))
                .filter(e -> e.getCustomers() != null && e.getCustomers().contains(customerName)).count() > 0;
    }

    public boolean hasCassandraOrESDedicatedClisters(String environment, String customerName) {
        return isCustomerSpecificClustersExist(environment, customerName, CASSANDRA_CLUSTER_TYPE) || isCustomerSpecificClustersExist(environment, customerName, ELASTICSEARCH_CLUSTER_TYPE);
    }

    public Optional<Cluster> getDefaultCluster(String environment, String type, String service) {
        Set<String> clusters = getEnvironment(environment).getClusters();
        return clusters.stream()
                .map(clusterService::get)
                .filter(e -> type.equalsIgnoreCase(e.getType()))
                .filter(e -> e.getServices().contains(service))
                .filter(Cluster::getNewAllocationsPossible)
                .filter(e -> e.getCustomers() == null || e.getCustomers().isEmpty())
                .findFirst();
    }

    public Optional<Cluster> getDefaultClusterGbt(String environment, String service, String historyBigTableProject) {
        Set<String> clusters = getEnvironment(environment).getClusters();
        return clusters.stream()
                .map(clusterService::get)
                .filter(e -> "gbt".equalsIgnoreCase(e.getType()))
                .filter(e -> e.getServices().contains(service))
                .filter(Cluster::getNewAllocationsPossible)
                .filter(e -> e.getCustomers() == null || e.getCustomers().isEmpty())
                .collect(Collectors.toMap(Function.identity(),
                        e -> gbtClient.checkTableCount(historyBigTableProject, e.getUrlList().get(0))))
                .entrySet().stream().sorted(Map.Entry.comparingByValue(Comparator.naturalOrder()))
                .filter(v -> v.getValue() < MAX_TABLE_COUNT).map(Map.Entry::getKey)
                .findFirst();

    }

    public Optional<Cluster> getGeneralService(String environment) {
        Set<String> clusters = getEnvironment(environment).getClusters();
        return clusters.stream()
                .map(clusterService::get)
                .filter(e -> "general".equalsIgnoreCase(e.getType()))
                .filter(Cluster::getNewAllocationsPossible)
                .findFirst();

    }

    public Set<String> getAllEnvironmentNames() {
        return environmentDao.getAll().stream().map(Environment::getName).collect(Collectors.toSet());
    }

    public String getCloudProviderForEnv(String env) {
        CloudProvider cloud = getEnvironment(env).getCloud();
        if (cloud != null) {
            return cloud.toString().toLowerCase(Locale.ROOT);
        } else {
            return clusterService.getCloud(env);
        }
    }

    public Set<String> getAllEnvironmentsForTypeElasticsearch(String env) {
        Collection<Cluster> clusters = clusterService.getClusterWithFilter("type", ELASTICSEARCH_CLUSTER_TYPE);
        Set<String> allEnvironments = new HashSet<>();
        for (Cluster cluster : clusters) {
            List<String> services = cluster.getServices();
            if (services != null && services.contains("data")) {
                List<String> urlList = cluster.getUrlList();
                List<String> filterUrlList = urlList.stream().filter(url -> !(url.toLowerCase(Locale.ROOT).contains("-ssrv-"))).collect(Collectors.toList());
                Set<String> environments = cluster.getEnvironments();
                if (!filterUrlList.isEmpty() && environments != null && environments.size() > 1 && environments.contains(env)) {
                    allEnvironments.addAll(environments);
                    allEnvironments.remove(env);
                }
            }
        }
        return allEnvironments;
    }

    public Environment cloneFromBitbucket(String name, boolean updateClusters, String environmentUrl) {
        String path = String.format(BIT_BUCKET_PATH, folder, name);
        try {
            boolean environmentFromBitbucket = bitBucketClient.isFileExist(repoName, branch, path);
            if (environmentFromBitbucket) {
                String envFileFromBitbucket = bitBucketClient.getFile(repoName, branch, path);
                JsonNode environmentJsonNode = objectMapper.readTree(envFileFromBitbucket);
                if(environmentJsonNode.get("enabled")!=null && ! environmentJsonNode.get("enabled").asBoolean()){
                    return updateSecondaryEnvironment(name,updateClusters,environmentUrl);
                }
                return updateEnvironment(name, updateClusters, environmentUrl, path, null);
            } else {
                return updateSecondaryEnvironment(name, updateClusters, environmentUrl);
            }

        } catch (IOException | RestClientException | InvalidDocumentIdException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.ENVIRONMENT_DOES_NOT_EXIST_IN_REPOSITORY, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }

    }

    private String getSecondryName(String name) throws InvalidDocumentIdException {
        return environmentDao.get(name).getSecondryName();
    }

    private Environment updateEnvironment(String environmentName, boolean updateClusters, String environmentUrl, String path, String secondaryEnvName) throws IOException {
        String environmentFromBitbucket = bitBucketClient.getFile(repoName, branch, path);
        boolean isEnvironmentExist = environmentDao.isExists(environmentName);
        JsonNode environmentJsonNode = objectMapper.readTree(environmentFromBitbucket);
        String securityComplianceValue = environmentJsonNode.get("securityCompliance").asText();
        EnvironmentAccessType type = environmentJsonNode.get(ENVIRONMENT_ACCESS_TYPE) == null ? EnvironmentAccessType.CUSTOMER : EnvironmentAccessType.valueOf(environmentJsonNode.get(ENVIRONMENT_ACCESS_TYPE).asText().toUpperCase(Locale.ROOT));
        Environment environment = isEnvironmentExist ? getEnvironment(environmentName) :
                new Environment(environmentName, environmentUrl, environmentJsonNode.get("region").asText(),
                        null, null, null, null,
                        null, environmentJsonNode.get("template"), null,
                        null, environmentJsonNode.get("securityCompliance").asText(), false, null,
                        type);
        if (environment.getSecurityCompliance() == null) {
            environment.setSecurityCompliance(securityComplianceValue);
        }
        if (environment.getSkip()) {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), "The environment is skipped from updates. Please use resume endpoint to enable updates");
        }
        String currentHash = environment.getMasterHash();
        if (updateClusters) {
            String newHash = bitBucketClient.getBranchHash(repoName, branch);
            if (!Objects.equals(currentHash, newHash)) {
                List<Cluster> clusters;
                if (secondaryEnvName == null) {
                    clusters = updateClusters(environmentName);
                } else {
                    clusters = updateClusters(secondaryEnvName);
                }
                Set<String> allClusterId = clusters.stream().filter(Cluster::getNewAllocationsPossible).map(Cluster::getID).collect(Collectors.toSet());
                environment.setClusters(allClusterId);
                environment.setTemplate(environmentJsonNode.get("template"));
                Map<ServiceType, String> defaultUrls = getDefaultUrlsFromJson(environmentName, clusters, Environment.getSupervisedServices());
                environment.addDefaultUrls(defaultUrls);
                currentHash = newHash;
            }
        }
        environment.setMasterHash(currentHash);
        if (isEnvironmentExist) {
            environmentDao.update(environment);
        } else {
            environmentDao.create(environment);
        }
        return environment;
    }

    private Map<ServiceType, String> getDefaultUrlsFromJson(String envName, List<Cluster> allServices, List<ServiceType> supervisedServices) {
        Map<ServiceType, String> defaultUrlMap = new HashMap<>();
        for (Cluster service : allServices) {
            ServiceType st = getServiceType(service);
            if (supervisedServices.contains(st)) {
                List<String> urls = service.getUrlList();
                if (urls.size() == 1) {
                    defaultUrlMap.put(st, String.format("https://%s", urls.get(0)));
                } else {
                    LOG.warn(String.format("Service type %s in %s environment contains %s urls, it's impossible to define a default one", st, envName, urls.size()));
                }
            }
        }
        return defaultUrlMap;
    }

    private ServiceType getServiceType(Cluster service) {
        try {
            return ServiceType.convertFromString(service.getType());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    public Environment updateDefaultUrlValues(String envName, Map<ServiceType, String> incomingValues) {
        Environment environment = getEnvironment(envName);
        environment.addDefaultUrls(incomingValues);
        environmentDao.update(environment);
        return environment;
    }

    private List<Cluster> updateClusters(String environmentName) {
        List<Cluster> allClusters = new ArrayList<>(clusterService.updateEnvironmentClusters(environmentName));
        Set<String> incomingClusterIds = allClusters.stream().map(Cluster::getID).collect(Collectors.toSet());
        clusterService.deleteOldClustersFromEnv(incomingClusterIds, environmentName);
        storageTemplateService.correctTemplatesForDeletedCluster(environmentName);
        return allClusters;
    }

    public EnvironmentParam updateEnvironmentParam(String environmentName, EnvironmentParam param) {
        Environment environment = getEnvironment(environmentName);
        environment.setEnvironmentParam(param);
        environmentDao.update(environment);
        return param;
    }

    public Environment createOrupdateEnvironment(Environment environment) {
        if (environment.getClusters() != null) {
            for (String cluster : environment.getClusters()) {
                if (!clusterService.clusterExsists(cluster)) {
                    throw new PlatformManagementException(PlatformManagementErrorCode.CLUSTER_NOT_FOUND, HttpStatus.BAD_REQUEST.value(), cluster);
                }
            }
        }
        return environmentDao.createOrUpdate(environment);
    }

    public String getEnvironmentByProperties(String cloud, String region, EnvironmentPurpose purpose, boolean isHipaa) {
        Collection<Environment> applicableEnvs = getAllEnvironments().stream()
                .filter(environment -> environment.getEnvironmentParam() != null && environment.getEnvironmentParam().getGeographicRegion() != null).collect(Collectors.toList());
        for (Environment env : applicableEnvs) {
            boolean sameRegion = Objects.equals(env.getEnvironmentParam().getGeographicRegion(), region);
            boolean sameCloud = Objects.equals(getCloudProviderForEnv(env.getName()), cloud.toLowerCase(Locale.ROOT));
            boolean samePurpose = Objects.equals(env.getEnvironmentParam().getEnvironmentPurpose(), purpose);
            boolean isHippa = Objects.equals(env.isHipaa(), isHipaa);
            if ((sameCloud && sameRegion) && samePurpose && isHippa) {
                return env.getName();
            }
        }
        return null;
    }

    public Environment createStoragePriorityListMapping(Map<ServicePurpose, List<com.reltio.devops.common.environment.ServiceType>> storagePriorityList, String environmentName) {
        Environment environment = getEnvironment(environmentName);
        environment.setStoragePriorityList(storagePriorityList);
        return environmentDao.update(environment);

    }

    public Cluster getMatchStorageService(String environment, String customerName, String matchStorageArn, Map<ServicePurpose, List<com.reltio.devops.common.environment.ServiceType>> storagePriorityList) {

        if (matchStorageArn != null) {
            String region = getEnvironment(environment).getRegion();
            return new Cluster("match.dynamodb", "aws", region, "dynamodb", Collections.emptyList(), Collections.singleton(environment), Collections.singletonList(matchStorageArn), Collections.emptySet(), Collections.emptySet(), true, null);
        }
        Stream<Supplier<Optional<Cluster>>> supplierStream = storagePriorityList.get(ServicePurpose.MATCH).stream().flatMap(e -> getClusterStorageService(environment, e.toString().toLowerCase(Locale.ROOT), "match", customerName));

        return supplierStream
                .map(Supplier::get)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Not able to find match cluster for environment" + " " + environment));

    }

    public Cluster getHistoryStorageService(String environment, String customerName, String historyBigTableProject, Map<ServicePurpose, List<com.reltio.devops.common.environment.ServiceType>> storagePriorityList) {

        //todo: add validation in pipeline validation that this list can have GBT and  CASSANDRA
        Stream<Supplier<Optional<Cluster>>> supplierStream;
        Supplier<Optional<Cluster>> customerSpecificGbtCluster;
        Supplier<Optional<Cluster>> defaultGbtCluster;
        if (!storagePriorityList.get(ServicePurpose.HISTORY).isEmpty() && storagePriorityList.get(ServicePurpose.HISTORY).get(0) == com.reltio.devops.common.environment.ServiceType.GBT) {
            customerSpecificGbtCluster = () -> getCustomerSpecificClusterGbt(environment, SERVICE_HISTORY_TYPE, customerName, historyBigTableProject);
            defaultGbtCluster = () -> getDefaultClusterGbt(environment, SERVICE_HISTORY_TYPE, historyBigTableProject);
        } else if (!storagePriorityList.get(ServicePurpose.HISTORY).isEmpty() && storagePriorityList.get(ServicePurpose.HISTORY).get(0) == com.reltio.devops.common.environment.ServiceType.CASSANDRA) {
            customerSpecificGbtCluster = () -> getCustomerSpecificCluster(environment, CASSANDRA_CLUSTER_TYPE, SERVICE_HISTORY_TYPE, customerName);
            defaultGbtCluster = () -> getDefaultCluster(environment, CASSANDRA_CLUSTER_TYPE, SERVICE_HISTORY_TYPE);
        } else {
            return getDataStorageService(environment, customerName, null, storagePriorityList);
        }
        supplierStream = Stream.of(customerSpecificGbtCluster, defaultGbtCluster);

        return supplierStream
                .map(Supplier::get)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Not able to find history cluster for environment" + " "+ environment));
    }

    public Cluster getDataStorageService(String environment, String customerName, String dataStorageRoleArn, Map<ServicePurpose, List<com.reltio.devops.common.environment.ServiceType>> storagePriorityList) {

        if (dataStorageRoleArn != null) {
            String region = getEnvironment(environment).getRegion();
            return new Cluster("data.dynamodb", "aws", region, "dynamodb", Collections.emptyList(), Collections.singleton(environment), Collections.singletonList(dataStorageRoleArn), Collections.emptySet(), Collections.emptySet(), true, null);
        }
        Stream<Supplier<Optional<Cluster>>> supplierStream = storagePriorityList.get(ServicePurpose.DATA).stream().
                flatMap(e -> getClusterStorageService(environment, e.toString().toLowerCase(Locale.ROOT), "data", customerName));

        return supplierStream
                .map(Supplier::get)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Not able to find data cluster for environment" + " "+ environment));
    }

    private Stream<Supplier<Optional<Cluster>>> getClusterStorageService(String environment, String type, String service, String customerName) {
        Supplier<Optional<Cluster>> customerSpecificDynamoCluster = () -> getCustomerSpecificCluster(environment, type, service, customerName);
        Supplier<Optional<Cluster>> defaultDynamoCluster = () -> getDefaultCluster(environment, type, service);
        return Stream.of(customerSpecificDynamoCluster, defaultDynamoCluster);
    }

    public Cluster getSearchCluster(String environment, String customerName) {
        Supplier<Optional<Cluster>> customerSpecificEsCluster = () -> getCustomerSpecificCluster(environment, ELASTICSEARCH_CLUSTER_TYPE, "data", customerName);
        Supplier<Optional<Cluster>> defaultEsCluster = () -> getDefaultCluster(environment, ELASTICSEARCH_CLUSTER_TYPE, "data");

        return Stream.of(customerSpecificEsCluster, defaultEsCluster)
                .map(Supplier::get)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Not able to find elastic search cluster for search config"));

    }

    public Environment updateEnvironmentStoragePriorityList(Environment environment) {
        if (environment.getClusters() != null) {
            for (String cluster : environment.getClusters()) {
                if (!clusterService.clusterExsists(cluster)) {
                    throw new PlatformManagementException(PlatformManagementErrorCode.CLUSTER_NOT_FOUND, HttpStatus.BAD_REQUEST.value(), cluster);
                }
            }
        }
        return environmentDao.updateStoragePriorityList(environment);
    }

    public void validateStoragePriorityList(String envId ,Map<ServicePurpose, List<com.reltio.devops.common.environment.ServiceType>> storagePriorityList) {
        if (storagePriorityList == null || storagePriorityList.isEmpty()) {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), String.format("Please use Api to configure storagePriorityList for the environment : %s",envId));
        }
        if (CollectionUtils.isNotEmpty(storagePriorityList.get(ServicePurpose.DATA)) && CollectionUtils.isNotEmpty(storagePriorityList.get(ServicePurpose.MATCH)) &&
                CollectionUtils.isNotEmpty(storagePriorityList.get(ServicePurpose.HISTORY))) {
            if (!VALID_DATA_STORAGE.containsAll(storagePriorityList.get(ServicePurpose.DATA))) {
                throw new PlatformManagementException(PlatformManagementErrorCode.NOT_A_VALID_STORAGE, HttpStatus.BAD_REQUEST.value(), String.format("DATA: %s for environment %s", storagePriorityList.get(ServicePurpose.DATA) , envId));
            }

            if (!VALID_MATCH_STORAGE.containsAll(storagePriorityList.get(ServicePurpose.MATCH))) {
                throw new PlatformManagementException(PlatformManagementErrorCode.NOT_A_VALID_STORAGE, HttpStatus.BAD_REQUEST.value(), String.format("MATCH: %s for environment %s", storagePriorityList.get(ServicePurpose.MATCH), envId));
            }

            if (!VALID_HISTORY_STORAGE.containsAll(storagePriorityList.get(ServicePurpose.HISTORY))) {
                throw new PlatformManagementException(PlatformManagementErrorCode.NOT_A_VALID_STORAGE, HttpStatus.BAD_REQUEST.value(), String.format("HISTORY: %s for environment %s", storagePriorityList.get(ServicePurpose.HISTORY), envId));
            }
        } else {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), String.format("Please use Api to configure storagePriorityList for the environment : %s",envId));
        }
    }

    /**
     * @param serviceTypes    list of serviceTypes coming from task.
     * @param environmentName Name of the environment
     */

    public void validateServiceUrl(List<ServiceType> serviceTypes, String environmentName) {
        Environment environment = getEnvironment(environmentName);
        Map<ServiceType, String> defaultUrls = environment.getDefaultUrls();

        if (defaultUrls == null || defaultUrls.isEmpty()) {
            throw new PlatformManagementException(
                    PlatformManagementErrorCode.BAD_REQUEST,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(),
                    String.format("DefaultUrl list does not exist in the environment %s or its empty, please add the same.",environmentName)
            );
        }

        List<ServiceType> missingUrls = serviceTypes.stream()
                .filter(serviceType -> !defaultUrls.containsKey(serviceType))
                .collect(Collectors.toList());

        if (!missingUrls.isEmpty()) {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(),
                    String.format("Please add the missing urls to the env %s from the list ",environmentName) +" "+ new ArrayList<>(missingUrls));
        }

    }

    public Environment updateDataSetAndCloud(String envName, String dataSet, String deploymentCloud) {
        Environment environment = getEnvironment(envName);
        try {
            environment.setDataSetLocation(DataSetLocation.valueOf(dataSet));
            environment.setCloud(CloudProvider.valueOf(deploymentCloud));
        } catch (IllegalArgumentException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(),
                    String.format(
                            "Please pass dataSet %s and deploymentCloud %s for environment: %s",
                            Arrays.stream(DataSetLocation.values()).collect(Collectors.toList()),
                            Arrays.stream(CloudProvider.values()).collect(Collectors.toList()),
                            envName
                    ));
        }
        environmentDao.update(environment);
        return environment;
    }

    public Environment deleteEnv(String envName) {
        try {
            Environment environment = environmentDao.get(envName);
            if (Util.isNotProd(folder)) {
                environmentDao.delete(envName);
            }
            return environment;
        } catch (InvalidDocumentIdException e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.ENVIRONMENT_IS_NOT_REGISTERED, HttpStatus.NOT_FOUND.value(), envName, e.getMessage());
        }
    }

    private Environment updateSecondaryEnvironment(String name, boolean updateClusters, String environmentUrl) throws InvalidDocumentIdException, IOException {
        if (getSecondryName(name) == null) {
            throw new PlatformManagementException(PlatformManagementErrorCode.SECONDARY_NAME_DOES_NOT_EXIST, HttpStatus.NOT_FOUND.value(), name);
        } else {
            String secondaryPath = String.format(BIT_BUCKET_PATH, folder, getSecondryName(name));
            return updateEnvironment(name, updateClusters, environmentUrl, secondaryPath, getSecondryName(name));
        }
    }

    public EnvironmentValidateResponse validateEnvironment(String envName) {
        EnvironmentValidateResponse environmentValidateResponse = new EnvironmentValidateResponse();
        Environment environment = getEnvironment(envName);

        Warning warning = new Warning();
        validateStoragePriorityMap(environment.getStoragePriorityList(), warning);
        validateDefaultUrls(environment.getDefaultUrls(), warning);
        environmentValidateResponse.setWarning(warning);

        Error error = new Error();
        if (environment.getCloud() == null) {
            error.getMissingPropAtEnv().add("Cloud");
        }
        if (environment.getDataSetLocation() == null) {
            error.getMissingPropAtEnv().add("dataSetLocation");
        }
        if (StringUtils.isEmpty(environment.getRegion())) {
            error.getMissingPropAtEnv().add("region");
        }
        EnvironmentParam environmentParam = environment.getEnvironmentParam();
        validateEnvironmentParam(environmentParam, error);

        environmentValidateResponse.setError(error);
        return environmentValidateResponse;
    }

    private void validateEnvironmentParam(EnvironmentParam environmentParam, Error error) {
        if (environmentParam == null) {
            error.getMissingPropAtEnv().add("environmentParam");
            error.getMissingEnvironmentParam().add("All environmentParam are missing");
        } else {
            if (environmentParam.getEnvironmentPurpose() == null) {
                error.getMissingEnvironmentParam().add("environmentPurpose");
            }
            if (environmentParam.getDefaultTtl() == null) {
                error.getMissingEnvironmentParam().add("defaultTtl");
            }
            if (environmentParam.getTenantSize() == null) {
                error.getMissingEnvironmentParam().add("maxTenantSizeSupported");
            }
            if (StringUtils.isEmpty(environmentParam.getReltioRegion())) {
                error.getMissingEnvironmentParam().add("reltioRegion");
            }
            if (StringUtils.isEmpty(environmentParam.getGeographicRegion())) {
                error.getMissingEnvironmentParam().add("geographicRegion");
            }
            if (StringUtils.isEmpty(environmentParam.getRihEnvironment())) {
                error.getMissingEnvironmentParam().add("rihEnvironment");
            }
            if (StringUtils.isEmpty(environmentParam.getDefaultPipelineId())) {
                error.getMissingEnvironmentParam().add("defaultPipelineId");
            }
            if (environmentParam.getDefaultPackageType() == null) {
                error.getMissingEnvironmentParam().add("defaultPackageType");
            }
            if (StringUtils.isEmpty(environmentParam.getDefaultProductEdition())) {
                error.getMissingEnvironmentParam().add("defaultProductEdition");
            }
            AllowedOptions allowedOptions = environmentParam.getAllowedOptions();
            validateAllowedOptions(allowedOptions, error);
        }
    }

}