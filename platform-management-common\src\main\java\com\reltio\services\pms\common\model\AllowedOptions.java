package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.TenantPurpose;
import lombok.Getter;

import java.util.Set;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
public class AllowedOptions {
    @JsonProperty("allowedCustomerTypes")
    private Set<CustomerType> allowedCustomerTypes;

    @JsonProperty("allowedTenantPurposes")
    private Set<TenantPurpose> allowedTenantPurposes;

    @JsonProperty("allowedTenantSizes")
    private Set<TenantSize> allowedTenantSizes;

    @JsonProperty("allowedDepartments")
    private Set<DepartmentDetails> allowedDepartments;

    // Default constructor for Jackson deserialization
    public AllowedOptions() {
    }

    // Parameterized constructor
    public AllowedOptions(Set<CustomerType> allowedCustomerTypes, Set<TenantPurpose> allowedTenantPurposes, Set<TenantSize> allowedTenantSizes, Set<DepartmentDetails> allowedDepartments) {
        this.allowedCustomerTypes = allowedCustomerTypes;
        this.allowedTenantPurposes = allowedTenantPurposes;
        this.allowedTenantSizes = allowedTenantSizes;
        this.allowedDepartments = allowedDepartments;
    }
}
