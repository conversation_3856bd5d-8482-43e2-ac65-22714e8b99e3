package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.google.cloud.firestore.CollectionReference;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.config.PMSConfig;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Map;
import java.util.Set;

@Service
public class PmsConfigsDao extends AbstractRootCollectionDao<PMSConfig> {

    private static final String CONFIG_COLLECTION_NAME = "PMS_CONFIG";
    private static final Logger LOGGER = Logger.getLogger(PmsConfigsDao.class);

    @Autowired
    public PmsConfigsDao(CredentialsProvider provider,
                         @Value("${firestore.env.name}") String deployedEnv,
                         ReltioUserHolder reltioUserHolder) {
        super(provider, CONFIG_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    protected TypeReference<PMSConfig> getTypeReference() {
        return new TypeReference<PMSConfig>() {
        };
    }

    @Override
    protected PMSConfig update(CollectionReference collectionReference, PMSConfig entity) {
        String username = getUsername();
        Long time = System.currentTimeMillis();
        entity.setUpdatedBy(username);
        entity.setUpdatedTime(time);
        return createOrUpdate(collectionReference, entity);

    }

    private PMSConfig createOrUpdate(CollectionReference collectionReference, PMSConfig entity) {
        try {
            String id = entity.getID();
            Map<String, Object> objectMap = convertToMap(entity);
            collectionReference.document(id).set(objectMap);
            return entity;
        } catch (Exception e) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_DB_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), e);
        }
    }


}
