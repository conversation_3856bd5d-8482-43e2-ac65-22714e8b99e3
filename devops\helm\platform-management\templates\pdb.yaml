{{- if .Values.pdbEnabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{.Values.serviceName}}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{.Values.serviceName}}
    environment: {{ .Release.Namespace }}
    service: {{ .Release.Namespace }}-{{.Values.serviceName}}
spec:
  minAvailable: {{ .Values.pdbMinAvailable | default (include "reltio.pdb.defaultMinAvailable" .Values.minReplicas ) }}
  selector:
    matchLabels:
      app: {{.Values.serviceName}}
{{- end }}