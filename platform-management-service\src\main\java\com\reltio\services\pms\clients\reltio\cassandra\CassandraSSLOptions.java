package com.reltio.services.pms.clients.reltio.cassandra;

import com.datastax.driver.core.exceptions.DriverException;
import com.reltio.storage.cassandra.security.CassandraTrustManagerFactory;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.ssl.SslHandler;
import org.apache.log4j.Logger;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.TrustManagerFactory;
import java.io.ByteArrayInputStream;
import java.security.KeyStore;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

public class CassandraSSLOptions extends SslNettyOptions {

    private static final Logger logger = Logger.getLogger(CassandraSSLOptions.class);
    char[] pwd;
    private final SSLContext sslContext = createContext();

    public CassandraSSLOptions() throws NoSuchAlgorithmException {
    }

    protected SslHandler createSslHandler(SocketChannel channel) {
        return new SslHandler(createEngine(sslContext));
    }

    private static SSLContext createContext() throws NoSuchAlgorithmException {
        SSLContext sslContext = SSLContext.getInstance("TLS");
        char[] pwd = sslContext.getProtocol().replace("TLS", "").toCharArray();
        TrustManagerFactory tmf = TrustManagerFactory.getInstance(CassandraTrustManagerFactory.getFactoryName());
        try {
            KeyStore ks = KeyStore.getInstance(CassandraTrustManagerFactory.getKeyStoreType());
            ks.load(new ByteArrayInputStream(new byte[]{}), pwd);
            tmf.init(ks);

            sslContext.init(null, tmf.getTrustManagers(), null);

            sslContext.init(null, tmf.getTrustManagers(), new SecureRandom());
            return sslContext;
        } catch (Exception e) {
            logger.error("Error occurs during creating Cassandra SSLContext", e);
            throw new DriverException(e);
        }
    }

    private static SSLEngine createEngine(SSLContext sslContext) {
        SSLEngine engine = sslContext.createSSLEngine();
        engine.setUseClientMode(true);
        return engine;
    }
}
