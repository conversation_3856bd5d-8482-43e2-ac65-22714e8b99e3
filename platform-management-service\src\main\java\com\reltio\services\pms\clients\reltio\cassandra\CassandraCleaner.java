package com.reltio.services.pms.clients.reltio.cassandra;

import com.datastax.driver.core.exceptions.InvalidQueryException;
import com.reltio.cache.InMemoryCachingMapService;
import com.reltio.common.config.CassandraConfigMapping;
import com.reltio.common.config.CassandraConfigWithDSMapping;
import com.reltio.common.config.TenantConfiguration;
import com.reltio.common.config.db.ActivityLogColumnFamily;
import com.reltio.common.config.db.ActivityLogV2ColumnFamily;
import com.reltio.common.config.db.DataColumnFamily;
import com.reltio.common.config.db.HistoryColumnFamily;
import com.reltio.common.config.db.InteractionsColumnFamily;
import com.reltio.common.config.db.MatchColumnFamily;
import com.reltio.db.layer.base.objects.ITenantID;
import com.reltio.db.layer.base.objects.impl.object.TenantID;
import com.reltio.locks.ILockerAccess;
import com.reltio.locks.impl.InMemoryLockerAccess;
import com.reltio.metrics.service.MetricsWriterServiceStub;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.validator.model.OperationStatus;
import com.reltio.services.pms.validator.model.OperationStatusHelper;
import com.reltio.services.pms.validator.model.Resource;
import com.reltio.services.pms.validator.model.ResourceType;
import com.reltio.storage.cassandra.system.ISystemDAOsFactory;
import com.reltio.storage.cassandra.system.SystemDAOExternalProviderImpl;
import com.reltio.storage.cassandra.system.SystemDAOsFactory;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.configuration.Configuration;
import org.apache.commons.configuration.MapConfiguration;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class CassandraCleaner {
    private static final Logger log = Logger.getLogger(CassandraCleaner.class);
    private static final String CASSANDRA_RESOURCE_COLLISION = "Keyspace %s@%s can not be delete as it used by another tenant";
    private final CassandraService cassandraService;
    private final NodeToolService nodeToolService;

    @Autowired
    public CassandraCleaner(CassandraService cassandraService, NodeToolService nodeToolService) {
        this.cassandraService = cassandraService;
        this.nodeToolService = nodeToolService;
    }

    private void prepareForCleanup(String env, TenantConfiguration tenantConfiguration) {
        nodeToolService.flushKeyspaces(tenantConfiguration, env);
        nodeToolService.disableAutocompaction(tenantConfiguration);
        nodeToolService.invalidateKeyCache(PhysicalConfigurationHelper.getAllHosts(tenantConfiguration));
    }

    private void finalSteps(TenantConfiguration tenantConfiguration) {
        nodeToolService.invalidateKeyCache(PhysicalConfigurationHelper.getAllHosts(tenantConfiguration));
        nodeToolService.enableAutocompaction(tenantConfiguration);
    }

    public void cleanup(String env, TenantConfiguration configuration, boolean force) {
        prepareForCleanup(env, configuration);
        cleanKeySpaces(env, configuration, force);
        finalSteps(configuration);
    }

    public void cleanupActivityLog(String env, TenantConfiguration configuration, boolean force) {
        prepareForCleanup(env, configuration);
        cleanALKeySpaces(env, configuration, force);
        finalSteps(configuration);
    }

    /**
     * Clean all C* keyspaces in tenant
     *
     * @param env                 environment to use
     * @param tenantConfiguration tenant to clean C* keyspaces
     * @param force               if true drop, if false truncate
     */
    private void cleanKeySpaces(String env, TenantConfiguration tenantConfiguration, boolean force) {
        Map<String, String> privatePublicIP = nodeToolService.flushKeyspaces(tenantConfiguration, env);
        List<String> activitiesCFs = Stream.of(ActivityLogColumnFamily.values())
                .map(ActivityLogColumnFamily::name).collect(Collectors.toList());
        cleanKeyspace(Collections.emptyList(), tenantConfiguration, tenantConfiguration.getDataStorageConfig().getActivityLogKeyspaceConfig(), activitiesCFs, privatePublicIP, force, false);
        List<String> activitiesCFs2 = Stream.of(ActivityLogV2ColumnFamily.values())
                .map(ActivityLogV2ColumnFamily::name).collect(Collectors.toList());
        CassandraConfigWithDSMapping keyspace = tenantConfiguration.getDataStorageConfig().getActivityLogV2KeyspaceConfig();
        cleanKeyspace(Collections.emptyList(), tenantConfiguration, keyspace, activitiesCFs2, privatePublicIP, force, false);
        List<String> dataCFs = Stream.of(DataColumnFamily.values())
                .map(DataColumnFamily::name).collect(Collectors.toList());
        cleanKeyspace(Collections.emptyList(), tenantConfiguration, tenantConfiguration.getDataStorageConfig().getDataKeyspaceConfig(), dataCFs, privatePublicIP, force, false);
        List<String> interactionsCFs = Stream.of(InteractionsColumnFamily.values())
                .map(InteractionsColumnFamily::name).collect(Collectors.toList());
        cleanKeyspace(Collections.emptyList(), tenantConfiguration, tenantConfiguration.getDataStorageConfig().getInteractionKeyspaceConfig(), interactionsCFs, privatePublicIP, force, false);
        List<String> matchCFs = Stream.of(MatchColumnFamily.values(tenantConfiguration)).map(MatchColumnFamily::name).collect(Collectors.toList());
        cleanKeyspace(Collections.emptyList(), tenantConfiguration, tenantConfiguration.getDataStorageConfig().getMatchKeyspaceConfig(), matchCFs, privatePublicIP, force, false);
    }

    private void cleanALKeySpaces(String env, TenantConfiguration tenantConfiguration, boolean force) {
        Map<String, String> privatePublicIP = nodeToolService.flushKeyspaces(tenantConfiguration, env);
        List<String> activitiesCFs2 = Stream.of(ActivityLogV2ColumnFamily.values())
                .map(ActivityLogV2ColumnFamily::name).collect(Collectors.toList());
        CassandraConfigWithDSMapping keyspace = tenantConfiguration.getDataStorageConfig().getActivityLogV2KeyspaceConfig();
        cleanKeyspace(Collections.emptyList(), tenantConfiguration, keyspace, activitiesCFs2, privatePublicIP, force, false);
    }

    /**
     * Clean specified keyspace
     *
     * @param resources           resources to check collisions
     * @param keyspaceConfig      keyspace config to use
     * @param tenantConfiguration tenant configuration
     * @param dataCFs             Column Families to process
     * @param privatePublicIps    map of C* ips
     * @param force               flag to truncate or drop CFs
     * @param ignoreErrors        flag to ignore errors
     * @return list of operation statuses
     */
    private List<OperationStatus> cleanKeyspace(List<Resource> resources, TenantConfiguration tenantConfiguration, CassandraConfigWithDSMapping keyspaceConfig, Collection<String> dataCFs, Map<String, String> privatePublicIps, boolean force, boolean ignoreErrors) {
        List<OperationStatus> statuses = new ArrayList<>();
        CassandraConfigMapping cassandraConfigMapping = PhysicalConfigurationHelper.getCassandraConfigMapping(tenantConfiguration, keyspaceConfig);
        String keyspaceName = cassandraConfigMapping.getKeyspaceName();
        String keyspaceHost = cassandraConfigMapping.getHost();
        if (StringUtils.isEmpty(keyspaceHost) || StringUtils.isEmpty(keyspaceName)) {
            return statuses;
        }
        if (force) {
            deleteOrSkipKeyspace(resources, cassandraConfigMapping, privatePublicIps, ignoreErrors).ifPresent(statuses::add);
        } else {
            statuses.addAll(truncateOrDropCfs(keyspaceHost, keyspaceName, privatePublicIps, dataCFs));
        }

        return statuses;
    }

    /**
     * Truncate or drop CFs
     *
     * @param hosts            C* hosts
     * @param keyspace         keyspace
     * @param privatePublicIps C* ips
     * @param cfs              CFs to operate on
     * @return collection of operation status
     */
    private Collection<OperationStatus> truncateOrDropCfs(String hosts, String keyspace, Map<String, String> privatePublicIps, Collection<String> cfs) {
        Collection<OperationStatus> statusList = new ArrayList<>();
        try {
            cassandraService.truncateCFsUsingJavaDriver(hosts, privatePublicIps, keyspace, cfs);
            statusList.add(OperationStatusHelper.success("Truncate Keyspace " + keyspace));
        } catch (CassandraException e) {
            statusList.add(OperationStatusHelper.error(e.getMessage()));
            log.error(String.format("Cassandra exception occurred while TRUNCATE CFs hosts: %s, keyspace: %s, tables: %s", hosts, keyspace, cfs), e);
        }
        return statusList;
    }

    private boolean canDeleteResource(List<Resource> resources, String resourceString, ResourceType type, boolean ignoreErrors) {
        if (!ignoreErrors) {
            for (Resource resource : resources) {
                if (resource.getType() == type && (resource.getCluster() + resource.getName()).equals(resourceString)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Delete keyspace. Skip in case keyspace == null or keyspace could not be deleted because of resource collisions.
     *
     * @param resources           resources to check collisions with
     * @param keyspaceConfig      keyspace config
     * @param privatePublicIpsSys ips for C* cluster
     * @param ignoreErrors        flag to ignore errors
     * @return optional of operation status
     */
    private Optional<OperationStatus> deleteOrSkipKeyspace(List<Resource> resources, CassandraConfigMapping keyspaceConfig, Map<String, String> privatePublicIpsSys, boolean ignoreErrors) {
        if (keyspaceConfig == null) {
            return Optional.empty();
        }
        String hash = keyspaceConfig.getClusterName() + keyspaceConfig.getKeyspaceName();
        if (canDeleteResource(resources, hash, ResourceType.KEYSPACE, ignoreErrors)) {
            return Optional.of(deleteKeyspace(keyspaceConfig, privatePublicIpsSys));
        } else {
            return Optional.of(OperationStatusHelper.warning(String.format(CASSANDRA_RESOURCE_COLLISION, keyspaceConfig.getKeyspaceName(), keyspaceConfig.getClusterName())));
        }
    }

    private OperationStatus deleteKeyspace(CassandraConfigMapping mapping, Map<String, String> privatePublicIpsSys) {
        try {
            cassandraService.deleteKeyspaceUsingJavaDriver(mapping.getHost(), mapping.getKeyspaceName(), privatePublicIpsSys);
            return OperationStatusHelper.success(String.format("Keyspace %s@%s is deleted", mapping.getKeyspaceName(), mapping.getClusterName()));
        } catch (Exception e) {
            String delMsg = String.format("Deleting keyspace %s on hosts: %s throws an error %s", mapping.getKeyspaceName(), mapping.getHost(), e.getMessage());
            log.error(delMsg, e);
            return OperationStatusHelper.warning(delMsg);
        }

    }

    public List<OperationStatus> cleanCassandraTenantHistory(String env, TenantConfiguration tenantConfiguration, boolean force, String tenantId) {
        List<OperationStatus> statusList = new ArrayList<>();
        try {
            // Make FLUSH and Invalidate KS
            log.info(String.format("Starting to FLUSH Cassandra Keyspaces for %s@%s", tenantConfiguration.getTenantId(), env));
            Map<String, String> privatePublicIps = nodeToolService.flushKeyspaces(tenantConfiguration, env);
            log.info(String.format("Invalidating Cassandra Keyspaces for %s@%s", tenantConfiguration.getTenantId(), env));
            nodeToolService.invalidateKeyCache(PhysicalConfigurationHelper.getAllHosts(tenantConfiguration));


            if (PhysicalConfigurationHelper.isCassandraHistory(tenantConfiguration)) {


                // History CFs
                String historyKeyspaceName = tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig().getKeyspaceName();
                String historyKeyspaceHost = tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig().getHost();
                String historyKeyspaceClusterName = tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig().getClusterName();

                boolean isSeparateHistory = true;
                if (historyKeyspaceClusterName.equals(tenantConfiguration.getDataStorageConfig().getDataKeyspaceConfig().getClusterName()) || historyKeyspaceHost.equals(tenantConfiguration.getDataStorageConfig().getMatchKeyspaceConfig().getClusterName())) {
                    if (historyKeyspaceName.equals(tenantConfiguration.getDataStorageConfig().getDataKeyspaceConfig().getKeyspaceName()) || historyKeyspaceHost.equals(tenantConfiguration.getDataStorageConfig().getMatchKeyspaceConfig().getKeyspaceName())) {
                        isSeparateHistory = false;
                    }
                }

                List<String> historyCFs = new ArrayList<>(Arrays.stream(HistoryColumnFamily.values()).map(HistoryColumnFamily::name).toList());

                log.info(String.format("Starting to Clean History Keyspace %s", historyKeyspaceName));
                if (force) {
                    if (isSeparateHistory) {
                        cassandraService.deleteKeyspaceUsingJavaDriver(historyKeyspaceHost, historyKeyspaceName, privatePublicIps);
                        statusList.add(OperationStatusHelper.success("Drop History Keyspace " + historyKeyspaceName));
                    } else {
                        try {
                            cassandraService.dropCFsUsingJavaDriver(historyKeyspaceHost, privatePublicIps, historyKeyspaceName, historyCFs);
                            statusList.add(OperationStatusHelper.success("Drop History CFs " + historyCFs + " from Keyspace " + historyKeyspaceName));
                        } catch (CassandraException e) {
                            statusList.add(OperationStatusHelper.warning(String.format("Drop History CFs %s from Keyspace %s with message: %s", historyCFs, historyKeyspaceName, e.getMessage())));
                        }

                    }
                } else {
                    cassandraService.truncateCFsUsingJavaDriver(historyKeyspaceHost, privatePublicIps, historyKeyspaceName, historyCFs);
                    statusList.add(OperationStatusHelper.success("Truncate History Keyspace " + historyKeyspaceName));
                }
                nodeToolService.invalidateKeyCache(Arrays.asList(historyKeyspaceHost.split(",")));
            }
        } catch (InvalidQueryException invalidQuery) {
            statusList.add(OperationStatusHelper.warning(String.format("Can not clean history because of: %s", invalidQuery.getMessage())));
            log.warn(String.format("Can not clean history %s@%s because of %s)", tenantId, env, invalidQuery.getMessage()));
        } catch (Exception e) {
            statusList.add(OperationStatusHelper.warning(String.format("Clean History Keyspace ends with: %s", e.getMessage())));
            log.error(String.format("Can't clean tenant %s@%s. Error: %s", tenantId, env, e.getMessage()));
        }

        return statusList;
    }

    public void deleteTenantFromSysKeyspace(String environment ,String tenantId, Map<String, Object> properties) {
        try {
            ITenantID iTenantID = new TenantID(tenantId, "fakeId");
            ISystemDAOsFactory factory = systemDAOsFactory(properties);
            factory.tenants().remove(tenantId);
            factory.metadataConfigDAO().remove(tenantId);
            factory.localizationDAO().deleteI18N(iTenantID);
            factory.permissions().removeConfiguration(tenantId);
            log.info(String.format("Delete tenant %s from system keyspace was successful", tenantId));
        } catch (Exception e) {
            String message = String.format("Delete tenant %s@%s from system keyspace failed.", tenantId, environment);
            log.error(message, e);
            throw new PlatformManagementException(PlatformManagementErrorCode.DELETE_TENANT_FROM_SYSTEM_KEYSPACE_FAILED , HttpStatus.INTERNAL_SERVER_ERROR.value(), tenantId);
        }
    }

    private ISystemDAOsFactory systemDAOsFactory(Map<String, Object> properties) {
        Configuration envConfig = new MapConfiguration(properties);
        ILockerAccess lockers = new InMemoryLockerAccess();
        return new SystemDAOsFactory(envConfig, null, lockers, new MetricsWriterServiceStub(), new InMemoryCachingMapService(), new SystemDAOExternalProviderImpl(), false);
    }

}
