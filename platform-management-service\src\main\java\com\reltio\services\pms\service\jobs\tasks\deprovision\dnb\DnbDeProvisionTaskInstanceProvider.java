package com.reltio.services.pms.service.jobs.tasks.deprovision.dnb;

import com.reltio.services.pms.common.model.jobs.tasks.dnbDeprovision.DnbDeProvisionTaskInstance;
import com.reltio.services.pms.common.model.jobs.tasks.dnbDeprovision.DnbDeProvisionTaskInstanceParams;
import com.reltio.services.pms.common.model.pipeline.tasks.DnbDeProvisionPipelineTaskConfig;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.jobs.tasks.TaskInstanceProvider;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.UUID;

@Service
public class DnbDeProvisionTaskInstanceProvider implements TaskInstanceProvider<DnbDeProvisionTaskInstance, DnbDeProvisionPipelineTaskConfig, DnbDeProvisionTaskInstanceParams> {

    @Override
    public TaskType getTaskType() {
        return TaskType.DNB_DE_PROVISION_TASK;
    }

    @Override
    public DnbDeProvisionTaskInstance getTaskDetail(String envId,String jobId,DnbDeProvisionPipelineTaskConfig pipelineTaskConfig,
                                                     DnbDeProvisionTaskInstanceParams taskInstanceParams){
        return new DnbDeProvisionTaskInstance(
                UUID.randomUUID().toString(),
                TaskType.DNB_DE_PROVISION_TASK_NAME,
                jobId,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                envId,
                taskInstanceParams.getTenantId(),
                Collections.emptyList(),
                taskInstanceParams.isFailOnError()
        );
    }
}
