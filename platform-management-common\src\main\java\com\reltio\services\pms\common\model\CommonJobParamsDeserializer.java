package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.reltio.services.pms.common.sales.PMSProductName;

import java.io.IOException;
import java.util.EnumSet;
import java.util.Map;

public class CommonJobParamsDeserializer extends StdDeserializer<CommonJobParams> {
    private final ObjectMapper mapper;

    private static final String PIPELINE_ID = "pipelineId";

    private static final String SKIP_TASKS = "skipTasks";

    private static final String END_DATE = "endDate";

    private static final String CHECK_DUPLICATES = "checkDuplicates";

    private static final String RIH = "rih";

    private static final String OWNED_BY_RELTIO_DEPT = "ownedByReltioDept";

    private static final String SHORT_DESCRIPTION = "shortDescription";

    private static final String CUSTOMER_CONFIG_PARAMS = "customerConfigParams";

    private static final String TENANT_CONFIG_PARAMS = "tenantConfigParams";

    private static final String CLEANSE_CONFIG_PARAMS = "cleanseConfigParams";

    private static final String RDM_CONFIG_PARAMS = "rdmConfigParams";

    private static final String STREAMING_CONFIG_PARAMS = "streamingConfigParams";

    private static final String DATABASE_OPTIONS = "databaseOptions";


    public CommonJobParamsDeserializer() {
        this(null);
    }

    public CommonJobParamsDeserializer(Class<?> vc) {
        super(vc);
        mapper = new ObjectMapper();
    }

    @Override
    public CommonJobParams deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        JsonNode node = jp.getCodec().readTree(jp);
        String pipelineId = null;
        if (node.hasNonNull(PIPELINE_ID)) {
            pipelineId = node.get(PIPELINE_ID).asText();
        }

        EnumSet<PMSProductName> skipTasks = null;
        if (node.hasNonNull(SKIP_TASKS) && !node.get(SKIP_TASKS).isEmpty()) {
            skipTasks = mapper.readValue(node.get(SKIP_TASKS).traverse(), new TypeReference<EnumSet<PMSProductName>>() {
            });
        }

        String endDate = null;
        if (node.hasNonNull(END_DATE)) {
            endDate = node.get(END_DATE).asText();
        }

        Boolean checkDuplicates = null;
        if (node.hasNonNull(CHECK_DUPLICATES)) {
            checkDuplicates = node.get(CHECK_DUPLICATES).asBoolean();
        }

        Map<String, String> rihKey = null;
        if (node.hasNonNull(RIH)) {
            rihKey = mapper.convertValue(node.get(RIH), Map.class);
        }
        String ownedByReltioDept = null;
        if (node.hasNonNull(OWNED_BY_RELTIO_DEPT)) {
            ownedByReltioDept = node.get(OWNED_BY_RELTIO_DEPT).asText();
        }

        String shortDescription = null;
        if (node.hasNonNull(SHORT_DESCRIPTION)) {
            shortDescription = node.get(SHORT_DESCRIPTION).asText();
        }
        CommonJobParams.CustomerConfigParams customerConfigParams = mapper.convertValue(node.get(CUSTOMER_CONFIG_PARAMS), CommonJobParams.CustomerConfigParams.class);

        CommonJobParams.TenantConfigParams tenantConfigParams = mapper.convertValue(node.get(TENANT_CONFIG_PARAMS), CommonJobParams.TenantConfigParams.class);

        CommonJobParams.CleanseConfigParams cleanseConfigParams = new CommonJobParams.CleanseConfigParams();
        if (node.hasNonNull(CLEANSE_CONFIG_PARAMS)) {
            cleanseConfigParams = mapper.convertValue(node.get(CLEANSE_CONFIG_PARAMS), CommonJobParams.CleanseConfigParams.class);
        }

        CommonJobParams.DatabaseOptions databaseOptions = new CommonJobParams.DatabaseOptions();
        if (node.hasNonNull(DATABASE_OPTIONS)) {
            databaseOptions = mapper.convertValue(node.get(DATABASE_OPTIONS), CommonJobParams.DatabaseOptions.class);
        }

        CommonJobParams.RdmConfigParams rdmConfigParams = new CommonJobParams.RdmConfigParams();
        if (node.hasNonNull(RDM_CONFIG_PARAMS)) {
            rdmConfigParams = mapper.convertValue(node.get(RDM_CONFIG_PARAMS), CommonJobParams.RdmConfigParams.class);
        }

        CommonJobParams.StreamingConfigParams streamingConfigParams = new CommonJobParams.StreamingConfigParams();
        if (node.hasNonNull(STREAMING_CONFIG_PARAMS)) {
            streamingConfigParams = mapper.convertValue(node.get(STREAMING_CONFIG_PARAMS), CommonJobParams.StreamingConfigParams.class);
        }

        return new CommonJobParams(pipelineId, skipTasks, endDate, checkDuplicates, rihKey, ownedByReltioDept, shortDescription,
                customerConfigParams, tenantConfigParams, databaseOptions, rdmConfigParams, streamingConfigParams, cleanseConfigParams);
    }

}
