package com.reltio.services.pms.clients.external.aws.s3;

import com.reltio.services.pms.clients.external.aws.AwsCredentialsFactory;
import com.reltio.services.pms.clients.external.aws.credentials.AccessSecretKeyCredentialsProviderImageHosting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

@Slf4j
@Service
public class S3ClientConfig {
    private final AccessSecretKeyCredentialsProviderImageHosting s3AccountCredentials;

    @Autowired
    public S3ClientConfig(AccessSecretKeyCredentialsProviderImageHosting s3AccountCredentials) {
        this.s3AccountCredentials = s3AccountCredentials;
    }

    public S3Client createClient(String regionStr) {

        AwsCredentialsProvider provider = AwsCredentialsFactory.createAwsCredentialsProvider(s3AccountCredentials);
        return S3Client.builder()
                .credentialsProvider(provider)
                .region(Region.of(regionStr))
                .build();
    }

}
