package com.reltio.services.pms.common.model.config;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * The type Sales force add ons.
 */
@Getter
@Setter
public class ReltioPackageTypeProductCodes extends PMSConfig {

    /**
     * The Add ons.
     */
    @JsonProperty("productCodes")
    private Map<ReltioPackageType, List<Map<String, PMSProductName>>> productCodes;


    /**
     * Instantiates a new Sales force add ons.
     *
     * @param configName the config name
     */
    @JsonCreator
    public ReltioPackageTypeProductCodes(@JsonProperty("configName") String configName,
                                         @JsonProperty("productCodes") Map<ReltioPackageType, List<Map<String, PMSProductName>>> productCodes) {
        super(configName);
        this.productCodes = productCodes;
    }


}
