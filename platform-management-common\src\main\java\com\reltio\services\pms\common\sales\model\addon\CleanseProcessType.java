package com.reltio.services.pms.common.sales.model.addon;

import com.fasterxml.jackson.databind.util.StdConverter;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;


@Getter
@AllArgsConstructor
public enum CleanseProcessType {


    CASS("cass"),
    SERP("serp"),
    CASS_SERP("c+serp"),
    DEFAULT("e+v+g"),
    RGEO("rgeo");


    private final String value;


    public static CleanseProcessType convertFromString(String value) {
        return Arrays.stream(CleanseProcessType.values())
                .filter(e -> e.value.equals(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid value '" + value + "'"));
    }

    public static Set<CleanseProcessType> convertMultiFromString(String value) {
        return Arrays.stream(CleanseProcessType.values()).filter(e -> Arrays.stream(value.split("\\+")) // Escape '+' since it's a regex metacharacter
                .anyMatch(v -> v.equals(e.value)))
                .collect(Collectors.toSet());
    }

    public static final class CleanseProcessTypeConverter extends StdConverter<String, CleanseProcessType> {

        @Override
        public CleanseProcessType convert(String value) {
            return CleanseProcessType.convertFromString(value);
        }
    }
}
