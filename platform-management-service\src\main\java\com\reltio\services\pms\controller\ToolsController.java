package com.reltio.services.pms.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/tools")
public class ToolsController {

    @GetMapping("/externalId")
    public ResponseEntity<Map<String, String>> getRandomExternalId() {
        return ResponseEntity.ok(Map.of("externalId", UUID.randomUUID().toString()));
    }
}
