package com.reltio.services.pms.common.model.compliance.request;

import com.reltio.services.pms.common.annotation.ValidEnum;
import com.reltio.services.pms.common.model.compliance.SortField;
import com.reltio.services.pms.common.model.compliance.SortOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import java.util.List;

/**
 * The type Compliance report request.
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class ComplianceReportRequest {

    /**
     * The Start date.
     */
    @Pattern(regexp = "\\d{8}", message = "startDate should be in yyyyMMdd format")
    @NotEmpty(message = "startDate is required")
    String startDate;
    /**
     * The End date.
     */
    @Pattern(regexp = "\\d{8}", message = "endDate should be in yyyyMMdd format")
    @NotEmpty(message = "endDate is required")
    String endDate;

    /**
     * The page size.
     */
    Integer size;
    /**
     * The page offset number.
     */

    Integer offset;
    /**
     * The order for sort.
     */
    @ValidEnum(enumClass = SortOrder.class,message = "Provide valid sortOrder")
    String sortOrder;
    /**
     * The cloumn name for sorting.
     */
    @ValidEnum(enumClass = SortField.class,message = "Provide valid sortField")
    String sortField;

    /**
     * The account ids.
     */
    @NotEmpty(message = "accountIds are required")
    List<String> accountIds;

    /**
     * The purpose filter.
     */
    String tenantPurpose;
    /**
     * The tenantId filter.
     */
    String tenantId;

    /**
     * The includeAllTenants flag.
     */
    Boolean includeAllTenants;

}
