package com.reltio.services.pms.common.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.annotation.Nullable;

@AllArgsConstructor
@Builder
@Getter
@NoArgsConstructor
@Setter
public class MatchDataArn {
    String tenantId;
    @Nullable String  matchStorageArn;
    @Nullable String dataStorageArn;
}
