package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.tenant.Subscription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OfferTypesDao extends AbstractLevel1CollectionDao<Subscription> {

    private static final String OFFERTYPES_COLLECTION_NAME = "OFFER_TYPES";

    @Autowired
    public OfferTypesDao(CredentialsProvider provider, RequesterDao requesterDao,
                   ReltioUserHolder reltioUserHolder) {
        super(provider, requesterDao, OFFERTYPES_COLLECTION_NAME, reltioUserHolder);
    }

    @Override
    protected TypeReference<Subscription> getTypeReference() {
        return new TypeReference<Subscription>() {
        };
    }

}
