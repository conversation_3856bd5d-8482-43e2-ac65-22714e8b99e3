package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;

public class FernPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonCreator
    public FernPipelineTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.FERN_TASK);
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.FERN;
    }

    @Override
    public String toString() {
        return "FERN_TASK{}";
    }
}
