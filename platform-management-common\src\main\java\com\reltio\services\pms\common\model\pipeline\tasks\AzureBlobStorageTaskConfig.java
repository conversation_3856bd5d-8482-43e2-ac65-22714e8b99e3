package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;

public class AzureBlobStorageTaskConfig extends AbstractPipelineTaskConfig{

    @JsonCreator
    public AzureBlobStorageTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.AZURE_BLOB_STORAGE_PROVISION_TASK);
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.AZURE_BLOB_STORAGE;
    }
}
