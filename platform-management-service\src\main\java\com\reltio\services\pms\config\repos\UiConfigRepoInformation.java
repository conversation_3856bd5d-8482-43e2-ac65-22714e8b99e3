package com.reltio.services.pms.config.repos;

import com.reltio.services.pms.validator.PropertiesValidator;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.apache.log4j.Logger;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Setter
@Getter
@Configuration
public class UiConfigRepoInformation {
    private String userName;
    private String password;
    private String repoName;
    private String branch;
    private static final Logger LOGGER = Logger.getLogger(UiConfigRepoInformation.class);

    public UiConfigRepoInformation(@Value("${bitBucket.userName}")String userName,
                                   @Value("${bitbucket.pwd}") String password,
                                   @Value("${reltio.uiConfig.bitbucket.repoName}") String repoName,
                                   @Value("${reltio.uiConfig.bitbucket.branch}") String branch) {
        this.userName = userName;
        this.password = password;
        this.repoName = repoName;
        this.branch = branch;
        Set<String> properties=new HashSet<>(Arrays.asList(userName,password,repoName,branch));
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

}

