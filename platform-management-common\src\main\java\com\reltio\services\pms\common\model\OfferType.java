package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.util.StdConverter;

import java.util.Arrays;

@JsonDeserialize(converter = OfferType.TypeConverter.class)
public enum OfferType {
    ACCOUNT360("Account360"),
    CUSTOMER360("Customer360"),
    IDENTITY360("Identity360"),
    CONSUMER360("Consumer360"),
    LSCONSUMER360("LSConsumer360"),
    PRODUCT360("Product360"),
    LSPRODUCT360("LSProduct360"),
    SITEINTELLIGENCE("SiteIntelligence");

    private final String value;

    OfferType(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return value;
    }

    public static OfferType convertFromString(String value) {
        return Arrays.stream(OfferType.values())
                .filter(e -> e.value.equals(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid value '" + value + "'"));

    }

    public static final class TypeConverter extends StdConverter<String, OfferType> {
        @Override
        public OfferType convert(String value) {
            return OfferType.convertFromString(value);

        }
    }
}
