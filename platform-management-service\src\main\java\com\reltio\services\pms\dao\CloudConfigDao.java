package com.reltio.services.pms.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.gax.core.CredentialsProvider;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.config.CloudConfig;
import com.reltio.services.pms.validator.PropertiesValidator;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Set;

@Service
public class CloudConfigDao extends AbstractRootCollectionDao<CloudConfig>{
    private static final String CONFIG_COLLECTION_NAME = "PMS_CONFIG";
    private static final Logger LOGGER = Logger.getLogger(CloudConfigDao.class);

    @Autowired
    public CloudConfigDao(CredentialsProvider provider,
                          @Value("${firestore.env.name}") String deployedEnv,
                          ReltioUserHolder reltioUserHolder) {
        super(provider, CONFIG_COLLECTION_NAME, deployedEnv, reltioUserHolder);
        Set<String> properties= Collections.singleton(deployedEnv);
        PropertiesValidator.validateProperties(properties,LOGGER);
    }

    @Override
    protected TypeReference<CloudConfig> getTypeReference() {
        return new TypeReference<CloudConfig>() {
        };
    }
}
