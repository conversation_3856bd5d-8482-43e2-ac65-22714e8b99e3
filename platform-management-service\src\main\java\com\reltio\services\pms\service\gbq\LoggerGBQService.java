package com.reltio.services.pms.service.gbq;

import com.google.cloud.bigquery.BigQuery;
import com.google.cloud.bigquery.BigQueryError;
import com.google.cloud.bigquery.BigQueryOptions;
import com.google.cloud.bigquery.InsertAllRequest;
import com.google.cloud.bigquery.InsertAllResponse;
import com.google.cloud.bigquery.Schema;
import com.google.cloud.bigquery.StandardTableDefinition;
import com.google.cloud.bigquery.Table;
import com.google.cloud.bigquery.TableId;
import com.google.cloud.bigquery.TableInfo;
import com.reltio.services.pms.common.model.gbq.LoggerGBQModel;
import org.apache.commons.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

import static com.reltio.services.pms.clients.external.gcp.GoogleCredentialsProvider.getCredentials;

@Service
public class LoggerGBQService {
    private final Logger LOG = LoggerFactory.getLogger(LoggerGBQService.class);
    private static final DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy_MM_dd");
    public static final List<String> SCOPES = Collections.singletonList("https://www.googleapis.com/auth/bigquery");
    public static final String GBQ_CREDENTIALS = "gbq.logger.credentials";
    private static final int QUEUE_SIZE = 10;
    private BigQuery bigQuery;
    private TableId tableId;
    private final Queue<LoggerGBQModel> gbqRecordQueue = new ConcurrentLinkedQueue<>();

    private final Configuration configuration;

    public LoggerGBQService(Configuration configuration) {
        this.configuration = configuration;
    }

    private void init() throws IOException {
        String projectId = configuration.getString("gbq.logger.project.id");
        String datasetId = configuration.getString("gbq.logger.dataset");
        String tableName = configuration.getString("gbq.logger.table");
        String gbqCredentials = configuration.getString(GBQ_CREDENTIALS);
        synchronized (this) {
            this.bigQuery = BigQueryOptions.newBuilder().setProjectId(projectId)
                    .setCredentials(getCredentials(gbqCredentials, SCOPES)).build().getService();
            this.tableId = TableId.of(projectId, datasetId, tableName);
        }

        Table table = bigQuery.getTable(tableId);
        if (Objects.isNull(table)) {
            Schema schema = Schema.of(LoggerGBQModel.getSchema());
            bigQuery.create(TableInfo.of(tableId, StandardTableDefinition.of(schema)));
        }

    }

    public void addRecord(LoggerGBQModel gbqRecord) {
        gbqRecordQueue.add(gbqRecord);
    }

    public boolean gbqLoggingEnabled() {
        return configuration.containsKey(GBQ_CREDENTIALS);
    }

    public InsertAllRequest insertToTableGBQ(TableId tableId) {
        InsertAllRequest.Builder builder = InsertAllRequest.newBuilder(tableId);
        int i = 0;
        boolean cycle = true;
        LoggerGBQModel gbqRecord;
        while (!gbqRecordQueue.isEmpty() && cycle) {
            gbqRecord = gbqRecordQueue.poll();
            if (Objects.nonNull(gbqRecord)) {
                builder.addRow(gbqRecord.getRecordMap());
                i++;
                if (i >= QUEUE_SIZE) {
                    cycle = false;
                }
            } else {
                cycle = false;
            }
        }
        try {
            return builder.setTemplateSuffix("_" + dtf.format(LocalDateTime.now())).build();
        } catch (Exception e) {
            LOG.warn("Exception raised while setting template suffix for creating table {}", e.getMessage());
            return null;
        }
    }

    public void postRecordsToGBQ() throws IOException {
        if (configuration.containsKey(GBQ_CREDENTIALS)) {
            if (Objects.isNull(tableId)) {
                init();
            }

            InsertAllRequest insertAllRequest = insertToTableGBQ(tableId);
            if (Objects.nonNull(insertAllRequest)) {
                InsertAllResponse response = bigQuery.insertAll(insertAllRequest);
                if (response.hasErrors()) {
                    for (Map.Entry<Long, List<BigQueryError>> entry : response.getInsertErrors().entrySet()) {
                        LOG.warn("Cant update gbq. Error: {} - {}", entry.getKey(), entry.getValue());
                    }
                }
            }
        }
    }
}
