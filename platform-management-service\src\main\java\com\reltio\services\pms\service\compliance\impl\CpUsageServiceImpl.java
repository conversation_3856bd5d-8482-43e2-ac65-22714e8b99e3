package com.reltio.services.pms.service.compliance.impl;

import com.reltio.collection.CollectionUtils;
import com.reltio.services.pms.common.model.compliance.response.CpUsageEntityResponse;
import com.reltio.services.pms.common.model.compliance.response.CpUsageResponse;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByEntityModel;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByEntityResponse;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByTenantModel;
import com.reltio.services.pms.common.model.compliance.response.cpusage.CpUsageByTenantResponse;
import com.reltio.services.pms.common.sales.model.ReltioTenant;
import com.reltio.services.pms.common.sales.model.SalesAccount;
import com.reltio.services.pms.service.compliance.CpUsageService;
import com.reltio.services.pms.service.compliance.util.ComplianceUtil;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

/**
 * The type Cp usage service.
 */
@Service
public class CpUsageServiceImpl extends ComplianceUtil implements CpUsageService {
    /**
     * Gets cp usage by tenant.
     *
     * @param accountCollection the account collection
     * @param tenantCollection  the tenant collection
     * @param cpUsageByTenant   the cp usage by tenant
     * @return the cp usage by tenant
     */
    @Override
    public CpUsageByTenantResponse getCpUsageByTenant(Collection<SalesAccount> accountCollection, Collection<ReltioTenant> tenantCollection, CpUsageByTenantResponse cpUsageByTenant) {
        List<CpUsageByTenantModel> byTenantList = cpUsageByTenant.getCpUsageByTenantModel();
        for (CpUsageByTenantModel tenantModel : byTenantList) {
            tenantModel.setCustomer(getCustomerName(tenantModel.getTenantId(), tenantCollection, accountCollection));
            tenantModel.setPurpose(getPurpose(tenantModel.getTenantId(), tenantCollection));
        }
        cpUsageByTenant.setCpUsageByTenantModel(byTenantList);
        return cpUsageByTenant;
    }


    /**
     * Gets cp usage by entity.
     *
     * @param accountCollection the account collection
     * @param tenantCollection  the tenant collection
     * @param cpUsageByEntity   the cp usage by entity
     * @return the cp usage by entity
     */
    @Override
    public CpUsageByEntityResponse getCpUsageByEntity(Collection<SalesAccount> accountCollection, Collection<ReltioTenant> tenantCollection, List<CpUsageResponse> cpUsageByEntity) {
        List<CpUsageByEntityModel> byEntityModelList = new LinkedList<>();
        for (CpUsageResponse response : cpUsageByEntity) {
            CpUsageEntityResponse tenantModel = (CpUsageEntityResponse) response;
            CpUsageByEntityModel model = new CpUsageByEntityModel();
            model.setDate(tenantModel.getReportDate());
            model.setTenantId(tenantModel.getTenantId());
            model.setEntityType(tenantModel.getMatchRuleEntityType());
            model.setEntityCount(tenantModel.getMatchRuleEntityCount());
            model.setCustomer(getCustomerName(tenantModel.getTenantId(), tenantCollection, accountCollection));
            model.setPurpose(getPurpose(tenantModel.getTenantId(), tenantCollection));
            byEntityModelList.add(model);
        }
        return new CpUsageByEntityResponse(byEntityModelList, getTotalCount(cpUsageByEntity));
    }

    /**
     * Gets total count.
     *
     * @param cpUsageByEntity the cp usage by entity
     * @return the total count
     */
    private Long getTotalCount(List<CpUsageResponse> cpUsageByEntity) {
        if (CollectionUtils.isNotEmpty(cpUsageByEntity)) {
            CpUsageEntityResponse response = (CpUsageEntityResponse) cpUsageByEntity.get(NumberUtils.INTEGER_ZERO);
            return Long.valueOf(response.getTotalCount());
        }
        return NumberUtils.LONG_ZERO;
    }

}
