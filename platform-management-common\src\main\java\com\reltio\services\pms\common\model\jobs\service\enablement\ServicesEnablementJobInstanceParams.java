package com.reltio.services.pms.common.model.jobs.service.enablement;

import com.reltio.services.pms.common.model.jobs.tasks.auth.AuthTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.azureBlobStorage.AzureBlobStorageTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.dnb.connector.DNBConnectorTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.dtss.DTSSTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.fern.FernTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.imagehosting.ImageHostingTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.email.notification.EmailNotificationTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.rdm.RDMServiceDeploymentTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.rdm.RdmTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.rih.RihGenericTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServicesEnablementBaseTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.sfdcconnector.SFDCConnectorEnablementTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.streaming.provision.StreamingTaskInstanceParams;
import com.reltio.services.pms.common.model.jobs.tasks.update.tenant.UpdateTenantTaskInstanceParms;
import com.reltio.services.pms.common.model.jobs.tasks.workato.WorkatoTaskInstanceParms;
import com.reltio.services.pms.common.model.jobs.tasks.workflow.WorkflowTaskInstanceParams;
import com.reltio.services.pms.common.model.tenant.TenantSize;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.HashSet;

public class ServicesEnablementJobInstanceParams implements WorkflowTaskInstanceParams, AuthTaskInstanceParams, RdmTaskInstanceParams,
        StreamingTaskInstanceParams, SFDCConnectorEnablementTaskInstanceParams, DNBConnectorTaskInstanceParams, DTSSTaskInstanceParams,
        UpdateTenantTaskInstanceParms, WorkatoTaskInstanceParms, ImageHostingTaskInstanceParams,
        EmailNotificationTaskInstanceParams, FernTaskInstanceParams, ServicesEnablementBaseTaskInstanceParams,
        AzureBlobStorageTaskInstanceParams, RihGenericTaskInstanceParams, RDMServiceDeploymentTaskInstanceParams {

    private final Set<String> tenants;
    private final String rdmTenantId;
    private final String streamingCloud;
    private final String offerType;
    private final List<String> cleanseRegions;
    private final TenantSize tenantSize;
    private final String dnbApiSecret;
    private final String dnbApiKey;
    private final String rdmHostNameUrl;
    private final boolean configureDefaultQueue;
    private final String loqateProcesses;
    private final Set<String> queueNames;
    private final ReltioPackageType reltioPackageType;
    private final String awsAccountId;
    private final String gitBranchName;

    public ServicesEnablementJobInstanceParams(Set<String> tenants, String rdmTenantId, boolean configureDefaultQueue, String streamingCloud,
                                               String offerType, String loqateProcesses, List<String> cleanseRegions, TenantSize tenantSize,
                                               String dnbApiSecret, String dnbApiKey, String rdmHostNameUrl, Set<String> queueNames,ReltioPackageType reltioPackageType,
                                                String awsAccountId, String gitBranchName) {
        this.tenants = tenants;
        this.rdmTenantId = rdmTenantId;
        this.configureDefaultQueue = configureDefaultQueue;
        this.streamingCloud = streamingCloud;
        this.offerType = offerType;
        this.loqateProcesses = loqateProcesses;
        this.cleanseRegions = cleanseRegions;
        this.tenantSize = tenantSize;
        this.dnbApiSecret = dnbApiSecret;
        this.dnbApiKey = dnbApiKey;
        this.rdmHostNameUrl = rdmHostNameUrl;
        this.queueNames = queueNames;
        this.reltioPackageType=reltioPackageType;
        this.awsAccountId = awsAccountId;
        this.gitBranchName = gitBranchName;
    }

    @Override
    public Set<String> getTenantsForServiceEnablement() {
        return tenants;
    }

    @Override
    public Set<String> getEmailsToAuthorizeAccess() {
        return Collections.emptySet();
    }

    @Override
    public String getTenantId() {
        return null;
    }

    @Override
    public TenantPurpose getTenantPurpose() {
        return null;
    }


    @Override
    public String getLoqateProcesses() {
        return loqateProcesses;
    }

    @Override
    public List<String> getCleanseRegions() {
        return cleanseRegions;
    }

    @Override
    public TenantSize getTenantSize() {
        return tenantSize;
    }

    @Override
    public String getRdmTenantId() {
        return rdmTenantId;
    }

    public Set<String> getTenants() {
        return tenants;
    }

    @Override
    public String getCustomerId() {
        return null;
    }

    @Override
    public String getCustomerName() {
        return null;
    }

    @Override
    public Set<String> getClients() {
        return Collections.emptySet();
    }

    @Override
    public List<String> getMdmTenantId() {
        return tenants == null ? Collections.emptyList() : new ArrayList<>(tenants);
    }

    @Override
    public Boolean reuseRdmTenant() {
        return true;
    }

    @Override
    public String getIndustry() {
        return null;
    }

    @Override
    public String getProductEdition() {
        return offerType;
    }

    @Override
    public String getDnbApiSecret() {
        return dnbApiSecret;
    }

    @Override
    public String getDnbApiKey() {
        return dnbApiKey;
    }

    @Override
    public boolean isConfigureDefaultQueue() {
        return configureDefaultQueue;
    }

    @Override
    public Boolean validateSameCustomerId() {
        return Boolean.FALSE;
    }

    @Override
    public String getRdmHostNameUrl() {
        return rdmHostNameUrl;
    }

    @Override
    public String streamingCloud() {
        return streamingCloud;
    }

    @Override
    public ReltioPackageType getReltioPackageType(){
        return reltioPackageType;
    }

    @Override
    public Set<String> getQueueNames() {
        if(queueNames==null) {
            return Collections.emptySet();
        }
        return new HashSet<>(queueNames);
    }

    @Override
    public String getOfferType() {
        return offerType;
    }

    @Override
    public String getAwsAccountId() {
        return this.awsAccountId;
    }

    @Override
    public String getGitBranchName() {
        return this.gitBranchName;
    }

}
