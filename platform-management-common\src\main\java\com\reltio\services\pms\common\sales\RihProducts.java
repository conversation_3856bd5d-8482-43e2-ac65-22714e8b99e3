package com.reltio.services.pms.common.sales;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum RihProducts {
    ZOOM_INFO,
    PURVIEW,
    COLLIBRA,
    DNB_DATABLOCKS,
    DATABRICKS,
    SFDC_RIH,
    MEDPRO,
    BVD,
    ALATION;

    public static RihProducts convertFromString(String value) {

        return Arrays.stream(RihProducts.values())
                .filter(e ->  e.name().equals(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid value '" + value + "'"));
    }
}
