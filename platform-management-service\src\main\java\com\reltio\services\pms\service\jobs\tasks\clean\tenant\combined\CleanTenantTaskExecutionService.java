package com.reltio.services.pms.service.jobs.tasks.clean.tenant.combined;

import com.reltio.common.config.CassandraConfigWithDSMapping;
import com.reltio.common.config.DataStorageType;
import com.reltio.common.config.TenantConfiguration;
import com.reltio.common.config.TenantDatabaseConfiguration;
import com.reltio.services.pms.clients.external.gcp.pubsub.GBTClient;
import com.reltio.services.pms.clients.reltio.cassandra.CassandraCleaner;
import com.reltio.services.pms.clients.reltio.irs.IRSClient;
import com.reltio.services.pms.clients.reltio.mdm.MDMCleanTenantClient;
import com.reltio.services.pms.clients.reltio.mdm.MDMClient;
import com.reltio.services.pms.clients.reltio.workflow.WorkflowServiceClient;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.common.model.gcp.GcpBigTable;
import com.reltio.services.pms.common.model.jobs.tasks.TaskStatus;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.combined.CleanTenantTaskInstance;
import com.reltio.services.pms.service.environment.EnvironmentService;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.AbstractTaskExecutionService;
import com.reltio.services.pms.service.jobs.tasks.provisioning.matchiq.MatchIQClient;
import com.reltio.services.pms.service.jobs.tasks.provisioning.matchiq.RIQClient;
import com.reltio.services.pms.validator.ResourcesValidator;
import org.apache.log4j.Logger;
import org.springframework.http.HttpStatus;

import java.util.EnumSet;
import java.util.Objects;
import java.util.Set;

import static com.reltio.services.pms.clients.reltio.cassandra.PhysicalConfigurationHelper.isDataStorageCassandra;
import static com.reltio.services.pms.clients.reltio.cassandra.PhysicalConfigurationHelper.isDataStorageDynamoDB;
import static com.reltio.services.pms.clients.reltio.cassandra.PhysicalConfigurationHelper.isDataStorageSpannerDB;

@Deprecated
public class CleanTenantTaskExecutionService extends AbstractTaskExecutionService<CleanTenantTaskInstance> {
    private static final Logger LOG = Logger.getLogger(CleanTenantTaskExecutionService.class);
    private static final String CRUD_QUEUE_CLEAN_UP_COMPLETED = "CRUD queue cleanup completed.";
    private static final String MATCH_QUEUE_CLEAN_UP_COMPLETED = "Match queue cleanup completed.";
    private static final String DYNAMO_DB_CLEAN_UP_COMPLETED = "DynamoDB cleanup completed.";
    private static final String SPANNER_DB_CLEAN_UP_COMPLETED = "SpannerDB cleanup completed.";
    private static final String GRAPH_CLEAN_UP_COMPLETED = "Graph cleanup completed.";
    private static final String STREAMING_DESTINATION_CLEAN_UP_COMPLETED = "Streaming destination cleanup completed.";
    private static final String ACTIVITY_LOG_CLEAN_UP_COMPLETED = "Activity log cleanup completed.";
    private static final String MAINTANANCE_MODE_ENABLED = "Maintanance mode enabled.";
    private static final String MAINTANANCE_MODE_DISABLED = "Maintanance mode disabled.";
    private static final String WORKFLOW_CLEAN_UP_COMPLETED = "Workflow cleanup completed.";
    private static final String ELASTIC_SEARCH_CLEAN_UP_COMPLETED = "Elastic search cleanup completed.";
    private static final String CASSANDRA_CLEAN_UP_COMPLETED = "Cassandra cleanup completed.";
    private static final String GBT_CLEAN_UP_COMPLETED = "GBT cleanup completed.";
    private static final String RESOURCE_VALIDATION_COMPLETED = "Resource validation completed.";
    private static final String MATCH_IQ_CLEAN_UP_COMPLETED = "Match IQ cleanup completed.";
    private static final String TENANT_PHYSICAL_CONFIG_UPDATED = "Physical config updated.";
    private static final String RETRIEVED_TENANT_PHYSICAL_CONFIG = "Retrieving tenant physical config completed.";
    private static final String CHECKING_TENANT_ISOLATION_COMPLETED = "Checking tenant isolation completed.";
    private static final String IRSQUEUE_CLEAN_UP_COMPLETED = "IRS Queue cleanup completed.";
    private static final String LOOKUP_CLEAN_UP_COMPLETED = "Lookup cleanup completed.";
    private static final String RIQ_CLEAN_UP_COMPLETED = "RIQ cleanup completed.";
    private static final long SLEEP_DURATION = 300000;
    private final MDMClient mdmClient;
    private final MDMCleanTenantClient mdmCleanTenantClient;
    private final WorkflowServiceClient workflowServiceClient;
    private final GBTClient gbtClient;
    private final CassandraCleaner cassandraCleaner;
    private final ResourcesValidator resourcesValidator;
    private final MatchIQClient matchIQClient;
    private final EnvironmentService environmentService;
    private final RIQClient riqClient;
    private final IRSClient irsClient;

    protected CleanTenantTaskExecutionService(String envId,
                                              CleanTenantTaskInstance taskDetail,
                                              GrafanaDashboardGBQService grafanaDashboardGBQService,
                                              MDMClient mdmClient,
                                              WorkflowServiceClient workflowServiceClient,
                                              GBTClient gbtClient,
                                              CassandraCleaner cassandraCleaner,
                                              ResourcesValidator resourcesValidator,
                                              MatchIQClient matchIQClient,
                                              EnvironmentService environmentService,
                                              RIQClient riqClient,
                                              IRSClient irsClient,
                                              MDMCleanTenantClient mdmCleanTenantClient) {
        super(envId, taskDetail, grafanaDashboardGBQService);
        this.mdmClient = mdmClient;
        this.mdmCleanTenantClient = mdmCleanTenantClient;
        this.workflowServiceClient = workflowServiceClient;
        this.gbtClient = gbtClient;
        this.cassandraCleaner = cassandraCleaner;
        this.resourcesValidator = resourcesValidator;
        this.matchIQClient = matchIQClient;
        this.environmentService = environmentService;
        this.riqClient = riqClient;
        this.irsClient = irsClient;
    }

    @Override
    public void executeTask() {
        LOG.info(String.format("Delete tenant data jobId: %s", taskDetail.getJobId()));

        TenantConfiguration tenantConfiguration = mdmClient.getTenantConfiguration(taskDetail.getEnvId(), taskDetail.getTenantName());
        Set<DataStorageType> storageTypes = getAllStorages(tenantConfiguration);
        addEventToTask(RETRIEVED_TENANT_PHYSICAL_CONFIG);
        validateTenant(tenantConfiguration);
        addEventToTask(CHECKING_TENANT_ISOLATION_COMPLETED);
        resourcesValidator.validateResources(taskDetail.getEnvId(), taskDetail.getTenantName());
        addEventToTask(RESOURCE_VALIDATION_COMPLETED);
        if (Objects.nonNull(tenantConfiguration.getMaintenance()) && tenantConfiguration.getMaintenance().equals(Boolean.TRUE)) {
            throw new PlatformManagementException(PlatformManagementErrorCode.INCORRECT_TENANT_STATE,
                    HttpStatus.BAD_REQUEST.value(), taskDetail.getTenantName(), Boolean.TRUE.toString());
        }
        mdmCleanTenantClient.enableMaintenanceModeForTenant(taskDetail.getEnvId(), taskDetail.getTenantName());
        addEventToTask(MAINTANANCE_MODE_ENABLED);
        waitAfterMaintenanceModeChange();
        try {
            cleanQueues();
            cleanAnalyticsCleanup();
            cleanActivitiesAndHistory(tenantConfiguration);
            cleanPrimary(tenantConfiguration, storageTypes);
            cleanExternalService();
            cleanElasticSearchIndices();
            if (taskDetail.getMetaDataCleanUp().isLookUpsClean()) {
                mdmCleanTenantClient.cleanLookup(envId, taskDetail.getTenantName());
                addEventToTask(LOOKUP_CLEAN_UP_COMPLETED);
            }
            mdmClient.updateTenantPhysicalConfigOnSpecificDataLoad(envId, tenantConfiguration, taskDetail.getTenantName());
            addEventToTask(TENANT_PHYSICAL_CONFIG_UPDATED);
        } catch (Exception ex) {
            throw new PlatformManagementException(PlatformManagementErrorCode.CLEAN_UP_TASK_FAILED, HttpStatus.EXPECTATION_FAILED.value(), ex, taskDetail.getTaskId());
        } finally {
            mdmCleanTenantClient.disableMaintenanceModeForTenant(envId, taskDetail.getTenantName());
            addEventToTask(MAINTANANCE_MODE_DISABLED);
        }
        waitAfterMaintenanceModeChange();
        taskDetail.setStatus(TaskStatus.COMPLETED);
    }

    private void waitAfterMaintenanceModeChange() {
        if (!environmentService.getUnitTest()) {
            try {
                Thread.sleep(SLEEP_DURATION);
            } catch (InterruptedException ex) {
                Thread.currentThread().interrupt();
            }
        }
    }

    private void cleanElasticSearchIndices() {
        if (taskDetail.getDataCleanUpOptions().isSecondary()) {
            mdmCleanTenantClient.cleanTenantIndex(taskDetail.getEnvId(), taskDetail.getTenantName());
            addEventToTask(ELASTIC_SEARCH_CLEAN_UP_COMPLETED);
        }
    }

    private void cleanGraf() {
        if (taskDetail.getDataCleanUpOptions().isGraphClean()) {
            mdmCleanTenantClient.cleanGraphSafe(taskDetail.getEnvId(), taskDetail.getTenantName());
            addEventToTask(GRAPH_CLEAN_UP_COMPLETED);
        }
    }

    private void cleanExternalQueue() {
        if (taskDetail.isExternalQueue()) {
            mdmCleanTenantClient.clearStreamingDestinations(taskDetail.getEnvId(), taskDetail.getTenantName(), Boolean.FALSE);
            addEventToTask(STREAMING_DESTINATION_CLEAN_UP_COMPLETED);
        }
    }

    private void cleanExternalService() {
        if (taskDetail.getExternalServicesCleanUp().isWorkflow()) {
            workflowServiceClient.terminateProcessInstances(taskDetail.getTenantName(), taskDetail.getEnvId());
            addEventToTask(WORKFLOW_CLEAN_UP_COMPLETED);
        }
    }

    private void cleanAnalyticsCleanup() {
        if (taskDetail.getAnalyticsCleanUp().isRiq()) {
            doCleanupRiq();
        }
        if (taskDetail.getAnalyticsCleanUp().isMatchIQ()) {
            doCleanMatchIq();
        }
    }

    private void doCleanupRiq() {
        if (Objects.isNull(environmentService.getEnvironment(taskDetail.getEnvId()).getDefaultUrls().get(ServiceType.RI_API))) {
            LOG.warn(String.format("RIQ service is not supported in %s", taskDetail.getEnvId()));
            return;
        }
        riqClient.cleanUpRIQ(environmentService.getEnvironment(taskDetail.getEnvId()).getDefaultUrls().get(ServiceType.RI_API), taskDetail.getTenantName());
        addEventToTask(RIQ_CLEAN_UP_COMPLETED);
    }

    private void doCleanMatchIq() {
        if (Objects.isNull(environmentService.getEnvironment(taskDetail.getEnvId()).getDefaultUrls().get(ServiceType.ML))) {
            LOG.warn(String.format("MatchIQ service is not supported in %s", taskDetail.getEnvId()));
            return;
        }
        matchIQClient.cleanMatchIq(environmentService.getEnvironment(taskDetail.getEnvId()).getDefaultUrls().get(ServiceType.ML), taskDetail.getTenantName());
        addEventToTask(MATCH_IQ_CLEAN_UP_COMPLETED);
    }

    private void cleanActivitiesAndHistory(TenantConfiguration tenantConfiguration) {
        if (taskDetail.getDataCleanUpOptions().isActivities()) {
            mdmCleanTenantClient.cleanActivityLog(taskDetail.getTenantName(), environmentService.getEnvironment(taskDetail.getEnvId()).getUrl());
            addEventToTask(ACTIVITY_LOG_CLEAN_UP_COMPLETED);
        }
        if (taskDetail.getDataCleanUpOptions().isHistory()) {
            if (tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig() != null &&
                    tenantConfiguration.getDataStorageConfig().getHistoryKeyspaceConfig().getCassandraVersion() != null) {
                cassandraCleaner.cleanCassandraTenantHistory(taskDetail.getEnvId(), tenantConfiguration, true, taskDetail.getTenantName());
                addEventToTask(CASSANDRA_CLEAN_UP_COMPLETED);
            } else if (tenantConfiguration.getDataStorageConfig().getHistoryBigTableConfig() != null) {

                String bigTableProjectId = tenantConfiguration.getDataStorageConfig().getHistoryBigTableConfig().getProject();
                String bigTableInstanceId = tenantConfiguration.getDataStorageConfig().getHistoryBigTableConfig().getInstanceId();
                String bigTableZoneName = tenantConfiguration.getDataStorageConfig().getHistoryBigTableConfig().getTableName();

                GcpBigTable gcpBigTable = new GcpBigTable(bigTableProjectId, bigTableInstanceId, bigTableZoneName);
                gbtClient.deleteGBT(gcpBigTable);
                addEventToTask(GBT_CLEAN_UP_COMPLETED);
            }
        }
    }

    private void cleanQueues() {
        if (taskDetail.getDataCleanUpOptions().isInternalQueue()) {
            mdmCleanTenantClient.clearCrudQueue(taskDetail.getEnvId(), taskDetail.getTenantName(), Boolean.FALSE);
            addEventToTask(CRUD_QUEUE_CLEAN_UP_COMPLETED);

            mdmCleanTenantClient.clearMatchQueue(taskDetail.getEnvId(), taskDetail.getTenantName(), Boolean.FALSE);
            addEventToTask(MATCH_QUEUE_CLEAN_UP_COMPLETED);

            irsClient.resetIRSTenant(taskDetail.getEnvId(), taskDetail.getTenantName());
            addEventToTask(IRSQUEUE_CLEAN_UP_COMPLETED);
        }
        cleanExternalQueue();
    }

    private void cleanPrimary(TenantConfiguration tenantConfiguration, Set<DataStorageType> tenantDataStorages) {
        if (taskDetail.getDataCleanUpOptions().isPrimary()) {
            if (tenantDataStorages.contains(DataStorageType.DYNAMO_DB)) {
                mdmCleanTenantClient.cleanTenantDynamoDBStorage(taskDetail.getEnvId(), taskDetail.getTenantName(), false,
                        false);
                addEventToTask(DYNAMO_DB_CLEAN_UP_COMPLETED);
            }
            if (tenantDataStorages.contains(DataStorageType.CASSANDRA_EXECUTOR)) {
                cassandraCleaner.cleanup(taskDetail.getEnvId(), tenantConfiguration, false);
                addEventToTask(CASSANDRA_CLEAN_UP_COMPLETED);
            }
            if (tenantDataStorages.contains(DataStorageType.SPANNER_DB)) {
                mdmCleanTenantClient.cleanTenantSpannerDBStorage(taskDetail.getEnvId(), taskDetail.getTenantName(),
                        false,
                        false);
                addEventToTask(SPANNER_DB_CLEAN_UP_COMPLETED);
            }
        }
        cleanGraf();
    }

    private Set<DataStorageType> getAllStorages(TenantConfiguration tenantConfiguration) {
        EnumSet<DataStorageType> storageTypes = EnumSet.noneOf(DataStorageType.class);
        TenantDatabaseConfiguration tenantDatabaseConfiguration = tenantConfiguration.getDataStorageConfig();
        CassandraConfigWithDSMapping dataConfig = tenantDatabaseConfiguration.getDataKeyspaceConfig();
        CassandraConfigWithDSMapping matchConfig = tenantDatabaseConfiguration.getMatchKeyspaceConfig();
        if (isDataStorageDynamoDB(tenantDatabaseConfiguration, dataConfig) || isDataStorageDynamoDB(tenantDatabaseConfiguration, matchConfig)) {
            storageTypes.add(DataStorageType.DYNAMO_DB);
        }
        if (isDataStorageSpannerDB(tenantDatabaseConfiguration, dataConfig) || isDataStorageSpannerDB(tenantDatabaseConfiguration,
                matchConfig)) {
            storageTypes.add(DataStorageType.SPANNER_DB);
        }
        if (isDataStorageCassandra(tenantDatabaseConfiguration, dataConfig) || isDataStorageCassandra(tenantDatabaseConfiguration, matchConfig)) {
            storageTypes.add(DataStorageType.CASSANDRA_EXECUTOR);
        }
        return storageTypes;
    }

    private void validateTenant(TenantConfiguration tenantConfiguration) {
        if (!tenantConfiguration.getDataStorageConfig().getIsolated()) {
            throw new PlatformManagementException(PlatformManagementErrorCode.TENANT_NOT_ISOLATED, HttpStatus.BAD_REQUEST.value(), taskDetail.getTenantName());
        }
    }

    protected void addEventToTask(String event) {
        taskDetail.addEvent(event);
    }

}
