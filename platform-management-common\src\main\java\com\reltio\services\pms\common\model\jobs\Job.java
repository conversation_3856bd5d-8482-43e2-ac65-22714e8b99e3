package com.reltio.services.pms.common.model.jobs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import com.reltio.services.pms.common.model.pipeline.SkipReason;
import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.Getter;

import java.util.ArrayList;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.List;
import java.util.Objects;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
public class Job extends BaseFirestoreEntity {
    @JsonProperty(value = "parentJobId")
    private String parentJobId;
    @JsonProperty(value = "jobId")
    private String jobId;
    @JsonProperty(value = "pipelineId")
    private String pipelineId;
    @JsonProperty(value = "startTime")
    private Long startTime;
    @JsonProperty(value = "finishTime")
    private Long finishTime;
    @JsonProperty(value = "status")
    private JobStatus status;
    @JsonProperty(value = "tasks")
    private List<String> tasks;
    @JsonProperty(value = "envId")
    private String envId;
    @JsonProperty(value = "skippedTasks")
    private EnumMap<SkipReason, EnumSet<PMSProductName>> skippedTasks;
    @JsonProperty(value = "owners")
    private List<String> owners;
    @JsonProperty(value = "deProvisioning")
    private Boolean deProvisioning;

    public Job(@JsonProperty(value = "parentJobId") String parentJobId,
               @JsonProperty(value = "jobId") String jobId,
               @JsonProperty(value = "pipelineId") String pipelineId,
               @JsonProperty(value = "startTime") Long startTime,
               @JsonProperty(value = "finishTime") Long finishTime,
               @JsonProperty(value = "status") JobStatus status,
               @JsonProperty(value = "tasks") List<String> tasks,
               @JsonProperty(value = "envId") String envId,
               @JsonProperty(value = "skippedTasks") EnumMap<SkipReason, EnumSet<PMSProductName>> skippedTasks,
               @JsonProperty(value = "owners") List<String> owners,
               @JsonProperty(value = "deProvisioning")Boolean deProvisioning) {
        this.parentJobId=parentJobId;
        this.jobId = jobId;
        this.pipelineId = pipelineId;
        this.startTime = startTime;
        this.finishTime = finishTime;
        this.status = status;
        this.tasks = tasks == null ? new ArrayList<>() : tasks;
        this.envId = envId;
        this.skippedTasks = skippedTasks;
        this.owners = owners;
        this.deProvisioning = deProvisioning == null ? Boolean.FALSE : deProvisioning;
    }

    protected Job() {
    }

    public Job(Job job) {
        this.parentJobId=job.parentJobId;
        this.jobId = job.jobId;
        this.pipelineId = job.pipelineId;
        this.createdBy = job.createdBy;
        this.createdTime = job.createdTime;
        this.updatedBy = job.updatedBy;
        this.updatedTime = job.updatedTime;
        this.startTime = job.startTime;
        this.finishTime = job.finishTime;
        this.status = job.status;
        this.tasks = new ArrayList<>(job.tasks);
        this.skippedTasks = job.skippedTasks;
        this.owners = job.owners;
        this.deProvisioning = job.deProvisioning;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public void setFinishTime(Long finishTime) {
        this.finishTime = finishTime;
    }

    public void setStatus(JobStatus status) {
        this.status = status;
    }

    public void setTasks(List<String> tasks) {
        this.tasks = tasks;
    }

    public void setEnvId(String envId) {
        this.envId = envId;
    }

    public Long jobDuration() {
        if (startTime != null && finishTime != null) {
            return finishTime - startTime;
        } else {
            return 0L;
        }
    }

    @Override
    public String getID() {
        return getJobId();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Job)) {
            return false;
        }
        Job job = (Job) o;
        return Objects.equals(getJobId(), job.getJobId()) &&
                Objects.equals(getPipelineId(), job.getPipelineId()) &&
                Objects.equals(getCreatedBy(), job.getCreatedBy()) &&
                Objects.equals(getCreatedTime(), job.getCreatedTime()) &&
                Objects.equals(getUpdatedBy(), job.getUpdatedBy()) &&
                Objects.equals(getUpdatedTime(), job.getUpdatedTime()) &&
                Objects.equals(getStartTime(), job.getStartTime()) &&
                Objects.equals(getFinishTime(), job.getFinishTime()) &&
                getStatus() == job.getStatus() &&
                Objects.equals(getTasks(), job.getTasks()) &&
                Objects.equals(getSkippedTasks(), job.getSkippedTasks());
    }

    @Override
    public int hashCode() {
        return Objects.hash(jobId, pipelineId, startTime, finishTime, status, tasks, envId, skippedTasks);
    }
}
