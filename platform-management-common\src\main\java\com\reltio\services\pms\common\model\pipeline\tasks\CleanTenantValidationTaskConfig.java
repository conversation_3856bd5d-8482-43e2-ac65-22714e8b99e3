package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class CleanTenantValidationTaskConfig extends AbstractPipelineTaskConfig {

    @JsonCreator
    public CleanTenantValidationTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.DELETE_TENANT_DATA_VALIDATION_TASK);
    }

}
