package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;

import java.util.Objects;

public class RdmTaskConfig extends AbstractPipelineTaskConfig {

    @JsonProperty(value = "productEdition")
    private String productEdition;

    @JsonProperty(value = "cache")
    private RdmCache rdmCache;

    @JsonProperty(value = "publishRDMErrors")
    private Boolean publishRDMErrors ;

    @JsonProperty(value = "trackTranscodeErrors")
    private Boolean trackTranscodeErrors;

    @JsonCreator
    public RdmTaskConfig(
            @JsonProperty(value = "name") String name,
            @JsonProperty(value = "productEdition") String productEdition,
            @JsonProperty(value = "cache") RdmCache rdmCache ,
            @JsonProperty(value = "publishRDMErrors" , defaultValue = "false") Boolean publishRDMErrors,
            @JsonProperty(value = "trackTranscodeErrors" , defaultValue = "true") Boolean trackTranscodeErrors) {
        super(name, TaskType.RDM_TASK);
        this.productEdition = productEdition;
        this.publishRDMErrors = publishRDMErrors != null ? publishRDMErrors : Boolean.FALSE;
        this.rdmCache = rdmCache != null ? rdmCache : new RdmCache(true , "TranscodeInMemory");
        this.trackTranscodeErrors = Objects.nonNull(trackTranscodeErrors) ? trackTranscodeErrors : Boolean.TRUE;
    }


    public String getProductEdition() {
        return productEdition;
    }

    public RdmCache getRdmCache() {
        return rdmCache;
    }

    public Boolean getPublishRDMErrors() {
        return publishRDMErrors;
    }

    public Boolean getTrackTranscodeErrors() {
        return trackTranscodeErrors;
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.RDM;
    }

    @Override
    public boolean visibleInContract() {
        return true;
    }

}
