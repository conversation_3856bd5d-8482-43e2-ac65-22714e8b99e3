package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;

public class SFDCConnectorEnablementPipelineTaskConfig extends AbstractPipelineTaskConfig {

    @JsonCreator
    public SFDCConnectorEnablementPipelineTaskConfig(@JsonProperty(value = "name") String name) {
        super(name, TaskType.SFDC_CONNECTOR_ENABLEMENT_TASK);
    }

    @Override
    public PMSProductName getProductName() {
        return PMSProductName.SALESFORCE_CONNECTOR;
    }

    @Override
    public boolean visibleInContract() {
        return true;
    }
}
