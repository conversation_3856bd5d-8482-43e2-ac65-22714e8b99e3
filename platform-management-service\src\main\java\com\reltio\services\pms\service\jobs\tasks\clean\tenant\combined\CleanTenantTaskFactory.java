package com.reltio.services.pms.service.jobs.tasks.clean.tenant.combined;

import com.reltio.services.pms.clients.external.gcp.pubsub.GBTClient;
import com.reltio.services.pms.clients.reltio.cassandra.CassandraCleaner;
import com.reltio.services.pms.clients.reltio.irs.IRSClient;
import com.reltio.services.pms.clients.reltio.mdm.MDMCleanTenantClient;
import com.reltio.services.pms.clients.reltio.mdm.MDMClient;
import com.reltio.services.pms.clients.reltio.workflow.WorkflowServiceClient;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.combined.CleanTenantTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.environment.EnvironmentService;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.TaskFactory;
import com.reltio.services.pms.service.jobs.tasks.provisioning.matchiq.MatchIQClient;
import com.reltio.services.pms.service.jobs.tasks.provisioning.matchiq.RIQClient;
import com.reltio.services.pms.validator.ResourcesValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Deprecated
public class CleanTenantTaskFactory implements TaskFactory<CleanTenantTaskInstance, CleanTenantTaskExecutionService> {
    private final MDMClient mdmClient;
    private final MDMCleanTenantClient mdmCleanTenantClient;
    private final WorkflowServiceClient workflowServiceClient;
    private final GBTClient gbtClient;
    private final CassandraCleaner cassandraCleaner;
    private final ResourcesValidator resourcesValidator;
    private final MatchIQClient matchIQClient;
    private final EnvironmentService environmentService;
    private final RIQClient riqClient;
    private final IRSClient irsClient;

    @Override
    public TaskType getTaskType() {
        return TaskType.DELETE_TENANT_DATA_TASK;
    }

    @Autowired
    public CleanTenantTaskFactory(MDMClient mdmClient,
                                  WorkflowServiceClient workflowServiceClient,
                                  GBTClient gbtClient,
                                  CassandraCleaner cassandraCleaner,
                                  ResourcesValidator resourcesValidator,
                                  MatchIQClient matchIQClient,
                                  EnvironmentService environmentService,
                                  RIQClient riqClient,
                                  IRSClient irsClient,
                                  MDMCleanTenantClient mdmCleanTenantClient) {
        this.mdmClient = mdmClient;
        this.workflowServiceClient = workflowServiceClient;
        this.gbtClient = gbtClient;
        this.cassandraCleaner = cassandraCleaner;
        this.resourcesValidator = resourcesValidator;
        this.matchIQClient = matchIQClient;
        this.environmentService = environmentService;
        this.riqClient = riqClient;
        this.irsClient = irsClient;
        this.mdmCleanTenantClient = mdmCleanTenantClient;
    }

    @Override
    public CleanTenantTaskExecutionService createTask(String envId, CleanTenantTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService) {
        return new CleanTenantTaskExecutionService(envId, taskDetail, grafanaDashboardGBQService, mdmClient, workflowServiceClient, gbtClient, cassandraCleaner, resourcesValidator, matchIQClient, environmentService, riqClient, irsClient, mdmCleanTenantClient);
    }
}
