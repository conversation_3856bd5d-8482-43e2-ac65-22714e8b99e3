package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.Getter;

import java.util.EnumSet;
import java.util.List;
import java.util.Optional;

@Getter
public class DeProvisionTenantJobRequest extends CreateJobRequest {

    @JsonProperty(value = "tenantId")
    private final String tenantId;
    @JsonProperty(value = "failOnError")
    private final Boolean failOnError;
    @JsonProperty(value = "deleteIhBucket")
    private final Boolean deleteIhBucket;
    @JsonProperty(value = "deleteGcpServiceAccount")
    private final Boolean deleteGcpServiceAccount;
    @JsonProperty(value = "streamingCloud")
    private final String streamingCloud;
    @JsonProperty(value = "rdmTenantId")
    private final String rdmTenantId;
    @JsonProperty(value = "detachAllAssociatedRdmTenants")
    private final Boolean detachAllAssociatedRdmTenants;
    @JsonProperty(value = "deleteRdmIfNoAssociatedMdm")
    private final Boolean deleteRdmIfNoAssociatedMdm;
    @JsonProperty("deleteAssociatedUsers")
    private boolean deleteAssociatedUsers;
    @JsonProperty("removeAssociatedRdms")
    private boolean removeAssociatedRdms;
    @JsonProperty("removeGroupAssignment")
    private boolean removeGroupAssignment;
    @JsonProperty("authCustomerId")
    private String authCustomerId;
    @JsonProperty("fullCustomerDeprovision")
    private boolean fullCustomerDeprovision;
    @JsonProperty("usersToDelete")
    private List<String> usersToDelete;

    public DeProvisionTenantJobRequest(@JsonProperty(value = "pipelineId") String pipelineId,
                                       @JsonProperty(value = "tenantId") String tenantId,
                                       @JsonProperty(value = "failOnError") Boolean failOnError,
                                       @JsonProperty(value = "skipTasks") EnumSet<PMSProductName> skipTasks,
                                       @JsonProperty(value = "deleteIhBucket")Boolean deleteIhBucket,
                                       @JsonProperty(value = "deleteGcpServiceAccount")Boolean deleteGcpServiceAccount,
                                       @JsonProperty(value = "streamingCloud")String streamingCloud,
                                       @JsonProperty(value = "rdmTenantId")String rdmTenantId,
                                       @JsonProperty(value = "detachAllAssociatedRdmTenants") Boolean detachAllAssociatedRdmTenants,
                                       @JsonProperty(value = "deleteRdmIfNoAssociatedMdm")Boolean deleteRdmIfNoAssociatedMdm,
                                       @JsonProperty("deleteAssociatedUsers")boolean deleteAssociatedUsers,
                                       @JsonProperty("removeAssociatedRdms")boolean removeAssociatedRdms,
                                       @JsonProperty("removeGroupAssignment")boolean removeGroupAssignment,
                                       @JsonProperty("authCustomerId")String authCustomerId,
                                       @JsonProperty("fullCustomerDeprovision")boolean fullCustomerDeprovision,
                                       @JsonProperty("usersToDelete")List<String> usersToDelete) {

        super(pipelineId, skipTasks);
        this.tenantId = tenantId;
        this.failOnError = Optional.ofNullable(failOnError).orElse(Boolean.TRUE);
        this.deleteIhBucket = Optional.ofNullable(deleteIhBucket).orElse(Boolean.FALSE);
        this.deleteGcpServiceAccount = Optional.ofNullable(deleteGcpServiceAccount).orElse(Boolean.FALSE);
        this.streamingCloud = streamingCloud;
        this.rdmTenantId = rdmTenantId;
        this.detachAllAssociatedRdmTenants = Optional.ofNullable(detachAllAssociatedRdmTenants).orElse(Boolean.FALSE);
        this.deleteRdmIfNoAssociatedMdm = Optional.ofNullable(deleteRdmIfNoAssociatedMdm).orElse(Boolean.FALSE);
        this.deleteAssociatedUsers = deleteAssociatedUsers;
        this.removeAssociatedRdms = removeAssociatedRdms;
        this.removeGroupAssignment = removeGroupAssignment;
        this.authCustomerId = authCustomerId;
        this.fullCustomerDeprovision = fullCustomerDeprovision;
        this.usersToDelete = usersToDelete;
    }

}
