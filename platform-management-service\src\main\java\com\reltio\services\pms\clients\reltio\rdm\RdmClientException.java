package com.reltio.services.pms.clients.reltio.rdm;

public class RdmClientException extends RuntimeException {

    public RdmClientException() {
    }

    public RdmClientException(String message) {
        super(message);
    }

    public RdmClientException(String message, Throwable cause) {
        super(message, cause);
    }

    public RdmClientException(Throwable cause) {
        super(cause);
    }

    public RdmClientException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
