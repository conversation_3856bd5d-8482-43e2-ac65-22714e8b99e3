package com.reltio.services.pms.clients.external.captcha;

import com.reltio.services.pms.common.IPMSRestTemplate;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.regex.Pattern;

@Component("captchaService")
public class CaptchaService {

    private static final String GOOGLE_RECAPTCHA_API_VERIFY_URL = "https://www.google.com/recaptcha/api/siteverify";
    private static final Pattern RESPONSE_PATTERN = Pattern.compile("[A-Za-z0-9_-]+");
    private final IPMSRestTemplate restTemplate;
    private final HttpServletRequest request;
    private final String recaptchaSecretKey;


    @Autowired
    public CaptchaService(IPMSRestTemplate restTemplate,
                          HttpServletRequest request,
                          @Value("${google.recaptcha.secret.key:secretKey}") String recaptchaSecretKey) {
        this.restTemplate = restTemplate;
        this.request = request;
        this.recaptchaSecretKey = recaptchaSecretKey;
    }

    public void validateRequest() {
        final String response = request.getParameter("g-recaptcha-response");
        if (!responseSanityCheck(response)) {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), "No Recaptcha Response");
        }

        MultiValueMap<String, Object> requestMap = new LinkedMultiValueMap<>();
        requestMap.put("secret", Collections.singletonList(getReCaptchaSecret()));
        requestMap.put("response", Collections.singletonList(response));

        GoogleResponse googleResponse = restTemplate.postForObject(GOOGLE_RECAPTCHA_API_VERIFY_URL, requestMap, GoogleResponse.class);

        if (googleResponse != null && !googleResponse.isSuccess()) {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value(), "Invalid Recaptcha Response");
        }
    }

    private String getReCaptchaSecret() {
        return recaptchaSecretKey;
    }

    private boolean responseSanityCheck(String response) {
        return StringUtils.hasLength(response) && RESPONSE_PATTERN.matcher(response).matches();
    }
}
