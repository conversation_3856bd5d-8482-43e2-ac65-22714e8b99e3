package com.reltio.services.pms.common.model.gbq;

import com.google.cloud.bigquery.Field;
import com.google.cloud.bigquery.LegacySQLTypeName;
import lombok.Getter;


import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class LoggerGBQModel {
    @Getter
    private static final Set<Field> schema = new HashSet<>();

    static {
        schema.add(Field.of("method", LegacySQLTypeName.STRING));
        schema.add(Field.of("endpoint", LegacySQLTypeName.STRING));
        schema.add(Field.of("user", LegacySQLTypeName.STRING));
        schema.add(Field.of("time", LegacySQLTypeName.INTEGER));
        schema.add(Field.of("httpCode", LegacySQLTypeName.INTEGER));
        schema.add(Field.of("errorBody", LegacySQLTypeName.STRING));
        schema.add(Field.of("httpParameters", LegacySQLTypeName.STRING));
        schema.add(Field.of("requestBody", LegacySQLTypeName.STRING));
    }
    @Getter
    private final Map<String, Object> recordMap = new HashMap<>();

    public LoggerGBQModel(String method, String endpoint, String user, long time, int code, String body, String httpParameters, String requestBody) {
        recordMap.put("method", method);
        recordMap.put("endpoint", endpoint);
        recordMap.put("user", user);
        recordMap.put("time", time);
        recordMap.put("httpCode", code);
        recordMap.put("errorBody", body);
        recordMap.put("httpParameters", httpParameters);
        recordMap.put("requestBody", requestBody);
    }
}
