package com.reltio.services.pms.common.sales.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SalesOpportunity {

    @JsonProperty("id")
    private String id;
    @JsonProperty("name")
    private String name;
    @JsonProperty("description")
    private String description;
    @JsonProperty("type")
    private String type;

    @JsonProperty("businessNeed")
    private String businessNeed;
    @JsonProperty("marketSegment")
    private String marketSegment;
    @JsonProperty("dataDomain")
    private String dataDomain;
    @JsonProperty("deploymentRegion")
    private String deploymentRegion;


    public SalesOpportunity() {
    }

    public SalesOpportunity(String id, String name, String description, String type, String businessNeed, String marketSegment, String dataDomain, String deploymentRegion) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.type = type;
        this.businessNeed = businessNeed;
        this.marketSegment = marketSegment;
        this.dataDomain = dataDomain;
        this.deploymentRegion = deploymentRegion;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBusinessNeed() {
        return businessNeed;
    }

    public void setBusinessNeed(String businessNeed) {
        this.businessNeed = businessNeed;
    }

    public String getMarketSegment() {
        return marketSegment;
    }

    public void setMarketSegment(String marketSegment) {
        this.marketSegment = marketSegment;
    }

    public String getDataDomain() {
        return dataDomain;
    }

    public void setDataDomain(String dataDomain) {
        this.dataDomain = dataDomain;
    }

    public String getDeploymentRegion() {
        return deploymentRegion;
    }

    public void setDeploymentRegion(String deploymentRegion) {
        this.deploymentRegion = deploymentRegion;
    }
}

