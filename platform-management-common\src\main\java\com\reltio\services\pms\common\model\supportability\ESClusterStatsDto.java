package com.reltio.services.pms.common.model.supportability;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * ESClusterDto
 * Created by apylkov
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ESClusterStatsDto implements StatsDto {
    @JsonProperty("startTime")
    Long startTime;
    @JsonProperty("endTime")
    Long endTime;
    @JsonProperty("cpu")
    CpuUsage cpu;
    @JsonProperty("memory")
    MemoryUsage memoryUsage;
    @JsonProperty("indexation")
    Indexation indexation;
}
