package com.reltio.services.pms.common.sales.model.addon;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.model.BaseProductConfig;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class SFDCConnectorConfig extends BaseProductConfig {

    @JsonProperty("sfdcConnectorUsername")
    private String sfdcConnectorUsername;

    public SFDCConnectorConfig() {
        pmsProductName = PMSProductName.SALESFORCE_CONNECTOR;
    }

    public SFDCConnectorConfig(String sfdcConnectorUsername) {
        this.sfdcConnectorUsername = sfdcConnectorUsername;
    }
}