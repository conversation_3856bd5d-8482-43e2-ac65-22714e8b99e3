package com.reltio.services.pms.common.model.pipeline.tasks;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class ConfigureStreamingAndAnalyticsTaskConfig extends AbstractPipelineTaskConfig {

    @JsonProperty(value = "enable")
    private final boolean enable;

    @JsonCreator
    public ConfigureStreamingAndAnalyticsTaskConfig(@JsonProperty(value = "name") String name,
                                                    @JsonProperty(value = "enable")boolean enable) {
        super(name,TaskType.CONFIGURE_STREAMING_AND_ANALYTICS_TASK);
        this.enable = enable;
    }
}
