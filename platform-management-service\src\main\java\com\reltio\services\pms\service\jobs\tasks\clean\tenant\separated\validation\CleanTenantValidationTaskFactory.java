package com.reltio.services.pms.service.jobs.tasks.clean.tenant.separated.validation;

import com.reltio.services.pms.clients.reltio.mdm.MDMClient;
import com.reltio.services.pms.common.model.jobs.tasks.clean.tenant.separated.validation.CleanTenantValidationTaskInstance;
import com.reltio.services.pms.common.model.pipeline.tasks.TaskType;
import com.reltio.services.pms.service.gbq.GrafanaDashboardGBQService;
import com.reltio.services.pms.service.jobs.tasks.TaskFactory;
import com.reltio.services.pms.validator.ResourcesValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CleanTenantValidationTaskFactory implements TaskFactory<CleanTenantValidationTaskInstance,
        CleanTenantValidationTaskService> {
    private final MDMClient mdmClient;
    private final ResourcesValidator resourcesValidator;

    @Override
    public TaskType getTaskType() {
        return TaskType.DELETE_TENANT_DATA_VALIDATION_TASK;
    }

    @Autowired
    public CleanTenantValidationTaskFactory(MDMClient mdmClient,
                                            ResourcesValidator resourcesValidator) {
        this.mdmClient = mdmClient;
        this.resourcesValidator = resourcesValidator;
    }

    @Override
    public CleanTenantValidationTaskService createTask(String envId, CleanTenantValidationTaskInstance taskDetail, GrafanaDashboardGBQService grafanaDashboardGBQService) {
        return new CleanTenantValidationTaskService(envId, taskDetail, grafanaDashboardGBQService, mdmClient, resourcesValidator);
    }
}