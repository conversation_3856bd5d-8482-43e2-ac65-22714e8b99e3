package com.reltio.services.pms.service.jobs.tasks.deprovision.auth;

import com.reltio.services.pms.clients.reltio.auth.ReltioAuthClient;
import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.common.model.User;
import com.reltio.services.pms.service.environment.EnvironmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AuthDeProvisionHelper {
    private final ReltioAuthClient reltioAuthClient;

    private final EnvironmentService environmentService;

    @Autowired
    public AuthDeProvisionHelper(ReltioAuthClient reltioAuthClient, EnvironmentService environmentService) {
        this.reltioAuthClient = reltioAuthClient;
        this.environmentService = environmentService;
    }

    public void removeTenantFromAuthResources(String tenantId, String envId) {
        removeTenantFromAuthResources(tenantId, envId, Collections.emptyList(), Collections.emptyList());
    }

    public void removeTenantFromAuthResources(String tenantId, String envId, List<String> skipUsers, List<String> skipCustomers) {
        List<User> authUsers = reltioAuthClient.getAuthUsersListFromTenant(tenantId);
        List<User> toProcessUsers = authUsers.stream()
                .filter(u -> !skipUsers.contains(u.getEmail()))
                .toList();

        Set<String> authCustomersId = authUsers.stream()
                .map(User::getCustomer)
                .collect(Collectors.toSet());
        Set<String> toProcessCustomers = authCustomersId.stream()
                .filter(cust -> !skipCustomers.contains(cust))
                .collect(Collectors.toSet());
        Map<String, Set<String>> groupsWithCustomer = reltioAuthClient.getGroupsForTheTenant(tenantId);
        String samlConfigUrl = environmentService.getEnvironment(envId).getDefaultUrls().get(ServiceType.SAML_CONFIG);
        removeTenantFromAuthCustomers(toProcessCustomers,tenantId,samlConfigUrl);
        removeTenantFromUsers(toProcessUsers, tenantId);
        removeTenantFromGroups(groupsWithCustomer,tenantId);
    }

    private void removeTenantFromUsers(List<User> authUsers, String tenantId) {
        authUsers.forEach(user -> {
            user.getUserPermissions().getRoles()
                    .values()
                    .forEach(tenantIds -> tenantIds.remove(tenantId));
            reltioAuthClient.updateUserRoles(user, true);
        });
    }


    private void removeTenantFromAuthCustomers(Set<String> authCustomersId, String tenantId, String samlConfigUrl) {
        authCustomersId.stream()
                .map(reltioAuthClient::getCustomer)
                .filter(customer -> customer.getTenants().contains(tenantId))
                .forEach(customer -> reltioAuthClient.removeTenantFromCustomer(
                        customer.getID(), tenantId, samlConfigUrl
                ));
    }


    private void removeTenantFromGroups(Map<String, Set<String>> groups, String tenantId) {
        groups.forEach((customerId, groupsAssociated) ->
                groupsAssociated.forEach(groupName -> reltioAuthClient.removeTenantFromGroups(customerId, groupName, tenantId)));
    }

}
