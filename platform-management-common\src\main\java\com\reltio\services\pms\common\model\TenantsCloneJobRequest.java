package com.reltio.services.pms.common.model;

import com.reltio.services.pms.common.sales.PMSProductName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.EnumSet;
import java.util.List;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TenantsCloneJobRequest {

    private String pipelineId;

    private  String sourceTenantId;

    private String targetTenantId;

    private String sourceEnvId;

    private String targetEnvId;

    private Integer backupRetentionPeriod;

    private String ticketId;

    private String sourceAccountBackupVaultName;

    private String targetAccountBackupVaultName;

    private List<String> skipTables;

    private boolean forceOverridePhyConfig;

    private boolean tenantData;

    private boolean historyData;

    private EnumSet<PMSProductName> skipTasks;

}
