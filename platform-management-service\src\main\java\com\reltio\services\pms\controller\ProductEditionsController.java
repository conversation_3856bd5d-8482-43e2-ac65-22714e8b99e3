package com.reltio.services.pms.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.ProductEdition;
import com.reltio.services.pms.common.model.SampleData;
import com.reltio.services.pms.common.sales.model.ReltioPackageType;
import com.reltio.services.pms.service.ProductEditionsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.reltio.services.pms.common.exception.PlatformManagementErrorCode.NO_LOOKUP_FOR_CONFIGURED_FOR_PRODUCT_EDITION;
import static com.reltio.services.pms.common.exception.PlatformManagementErrorCode.RDM_LOOKUP_PATH_NOT_CONFIGURED_IN_PRODUCT_EDITION;

@RestController
@RequestMapping(value = "/api/v1/productEditions", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name= "Product Edition")
@ReltioSecured(resourceClass = Pms.Environment.Config.Product.class)
public class ProductEditionsController {
    private static final Logger LOG = Logger.getLogger(ProductEditionsController.class);

    private final ProductEditionsService service;

    @Autowired
    public ProductEditionsController(ProductEditionsService service) {
        this.service = service;
    }

    @PostMapping(value = "", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ProductEdition createProductEditions(@RequestBody ProductEdition productEdition) {
        ProductEdition productEditionResponse = service.createProductEditions(productEdition);
        setDefaultPackageType(productEditionResponse);
        return productEditionResponse;
    }

    @PutMapping(value = "/{name}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ProductEdition updatProductEditions(@PathVariable String name,
                                               @RequestBody ProductEdition productEdition){
        if (!name.equals(productEdition.getName())) {
            throw new PlatformManagementException(PlatformManagementErrorCode.BAD_REQUEST, HttpStatus.BAD_REQUEST.value());
        }
        ProductEdition productEditionResponse = service.updateProductEditions(productEdition);
        setDefaultPackageType(productEditionResponse);
        return productEditionResponse;
    }

    @GetMapping(value = "/{name}")
    public ProductEdition getProductEditions(@PathVariable String name, @RequestParam(name = "reltioPackageType", required = false, defaultValue = "MDM") ReltioPackageType reltioPackageType) {
        String productEditionNameWithReltioPackageType = service.getProductEditionNameWithReltioPackageType(name, reltioPackageType);
        return service.getProductEdition(productEditionNameWithReltioPackageType);
    }

    @GetMapping(value = "/{name}/businessConfig")
    public JsonNode getBusinessConfig(@PathVariable String name, @RequestParam(name = "reltioPackageType", required = false, defaultValue = "MDM") ReltioPackageType reltioPackageType) {
        String productEditionNameWithReltioPackageType = service.getProductEditionNameWithReltioPackageType(name, reltioPackageType);
        return service.getBusinessConfig(productEditionNameWithReltioPackageType);
    }

    @GetMapping(value = "/dynamicBusinessConfig/**")
    public JsonNode getDynamicBusinessConfig(HttpServletRequest request) {
        String fullFolderPath = request.getRequestURI().replace("/api/v1/productEditions/dynamicBusinessConfig/", "");
        return service.getDynamicBusinessConfig(fullFolderPath);
    }

    @GetMapping(value = "/{name}/lookUps")
    public JsonNode getLookUps(@PathVariable String name, @RequestParam(name = "reltioPackageType", required = false, defaultValue = "MDM") ReltioPackageType reltioPackageType) {
        String productEditionNameWithReltioPackageType = service.getProductEditionNameWithReltioPackageType(name, reltioPackageType);
        Optional<JsonNode> lookups = service.getLookUps(productEditionNameWithReltioPackageType);
        if (!lookups.isPresent())
            throw new PlatformManagementException(NO_LOOKUP_FOR_CONFIGURED_FOR_PRODUCT_EDITION, HttpStatus.NOT_FOUND.value(), productEditionNameWithReltioPackageType);
        else
            return lookups.get();
    }

    @GetMapping(value = "/{name}/rdmLookUps")
    public List<JsonNode> getRdmLookUps(@PathVariable String name, @RequestParam(name = "reltioPackageType", required = false, defaultValue = "MDM") ReltioPackageType reltioPackageType) throws JsonProcessingException {
        String productEditionNameWithReltioPackageType = service.getProductEditionNameWithReltioPackageType(name, reltioPackageType);
        List<JsonNode> rdmLookups = service.getRdmLookUpsAndSourceDetailsFromDirectory(productEditionNameWithReltioPackageType);
        if (rdmLookups == null || rdmLookups.size() == 0) {
            throw new PlatformManagementException(RDM_LOOKUP_PATH_NOT_CONFIGURED_IN_PRODUCT_EDITION, HttpStatus.INTERNAL_SERVER_ERROR.value(), productEditionNameWithReltioPackageType);
        }
        return rdmLookups;
    }

    @GetMapping(value = "/{name}/uiConfig")
    public JsonNode getUiConfig(@PathVariable String name, @RequestParam(name = "reltioPackageType", required = false, defaultValue = "MDM") ReltioPackageType reltioPackageType) throws JsonProcessingException {
        String productEditionNameWithReltioPackageType = service.getProductEditionNameWithReltioPackageType(name, reltioPackageType);
        return service.getUiConfig(productEditionNameWithReltioPackageType);
    }

    @GetMapping(value = "/{name}/sampleData")
    public SampleData getSampleData(@PathVariable String name, @RequestParam(name = "reltioPackageType", required = false, defaultValue = "MDM") ReltioPackageType reltioPackageType) {
        String productEditionNameWithReltioPackageType = service.getProductEditionNameWithReltioPackageType(name, reltioPackageType);
        return service.getSampleData(productEditionNameWithReltioPackageType);
    }

    @DeleteMapping(value = "/{name}")
    public ProductEdition deleteProductEdition(@PathVariable String name, @RequestParam(value = "reltioPackageType") ReltioPackageType reltioPackageType) {
        String productEditionNameWithReltioPackageType = service.getProductEditionNameWithReltioPackageType(name, reltioPackageType);
        return service.deleteProductEdition(productEditionNameWithReltioPackageType);
    }

    @GetMapping(value = "")
    public Collection<ProductEdition> getAllProductEditions() {
        return service.getAllProductEditions();
    }

    @GetMapping(value = "/getProductEditionWithReltioPackageType")
    public Collection<ProductEdition> getProductEditionsWithReltioPackageType(@RequestParam(value= "name" , required = false)String name ,
                                                                              @RequestParam(value="reltioPackageType" , required = false)ReltioPackageType reltioPackageType) {
        return service.getProductEditionsWithReltioPackageType(name,reltioPackageType);
    }

    private void setDefaultPackageType(ProductEdition productEdition) {
        if (productEdition.getReltioPackageType() == null) {
            productEdition.setReltioPackageType(ReltioPackageType.MDM);
        }
    }

}
