package com.reltio.services.pms.common.model.jobs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.reltio.services.pms.common.model.BaseFirestoreEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class JobIndexItem extends BaseFirestoreEntity {

    @JsonProperty(value = "jobId")
    private String jobId;

    @JsonProperty(value = "envId")
    private String envId;

    @JsonProperty(value = "contractId")
    private String contractId;

    @JsonProperty(value = "status")
    private JobStatus status;

    @JsonProperty(value = "affectedTenants")
    private Set<String> affectedTenants = new HashSet<>();

    @Override
    public String getID() {
        return jobId;
    }

    public void addAffectedTenant(String tenantId) {
        affectedTenants.add(tenantId);
    }

    public void addAffectedTenants(Set<String> tenantId) {
        if (affectedTenants == null) {
            affectedTenants = new HashSet<>();
        }
        affectedTenants.addAll(tenantId);
    }
}
