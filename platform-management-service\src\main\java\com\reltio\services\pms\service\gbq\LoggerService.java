package com.reltio.services.pms.service.gbq;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Joiner;
import com.reltio.services.commons.v6.infra.security.ReltioUserHolder;
import com.reltio.services.pms.common.model.gbq.LoggerGBQModel;
import com.reltio.services.pms.interceptor.CachedBodyHttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Scanner;
import java.util.Set;

@Service
public class LoggerService {
    private static final Set<String> endpointsToIgnore = new HashSet<>();
    private static final String logRegex = "(\r\n|\r|\n)";

    static {
        endpointsToIgnore.add("/swagger-resources/configuration/ui");
        endpointsToIgnore.add("/swagger-resources/configuration/security");
        endpointsToIgnore.add("/swagger-resources");
        endpointsToIgnore.add("/v2/api-docs");
    }

    private final Logger LOG = LoggerFactory.getLogger(LoggerService.class);
    private final LoggerGBQService gbqService;
    private final ReltioUserHolder reltioUserHolder;
    private final StringBuilder requestBody = new StringBuilder();
    private final ObjectMapper mapper = new ObjectMapper();

    public LoggerService(LoggerGBQService gbqService,
                         ReltioUserHolder reltioUserHolder) {
        this.reltioUserHolder = reltioUserHolder;
        this.gbqService = gbqService;
    }

    public void logRequest(HttpServletRequest httpServletRequest, Object body) throws IOException {

        StringBuilder stringBuilder = new StringBuilder(60);
        Map<String, String> parameters = buildParametersMap(httpServletRequest);
        String userName = setUserName();
        stringBuilder.append("REQUEST ")
                .append("userName=[").append(userName).append("] ")
                .append("method=[").append(httpServletRequest.getMethod()).append("] ")
                .append("path=[").append(httpServletRequest.getRequestURI()).append("] ")
                .append("headers=[").append(buildHeadersMap(httpServletRequest)).append("] ");

        if (!parameters.isEmpty()) {
            stringBuilder.append("parameters=[").append(parameters).append("] ");
        }

        if (Objects.nonNull(body)) {
            stringBuilder.append("body=[").append(processSecrets(body)).append(']');
        }

        String logData = stringBuilder.toString().replaceAll(logRegex, "_");
        LOG.info("{}", logData);
    }

    public void logResponse(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object body) throws IOException {
        if (gbqService.gbqLoggingEnabled()) {
            if (!httpServletRequest.getMethod().equalsIgnoreCase("GET")) {
                CachedBodyHttpServletRequest requestWrapper = new CachedBodyHttpServletRequest(httpServletRequest);
                Scanner s = new Scanner(requestWrapper.getInputStream(), StandardCharsets.UTF_8.toString()).useDelimiter("\\A");
                while (s.hasNext()) {
                    requestBody.append(s.next());
                }
            }
            //StringBuilder stringBuilder = new StringBuilder(80);
            String userName = setUserName();
            String method = httpServletRequest.getMethod();
            String path = httpServletRequest.getRequestURI();
            int code = httpServletResponse.getStatus();
            String bodyString;
            if (code != 200) {
                bodyString = processSecrets(body);
            } else {
                bodyString = "";
            }
            long time = System.currentTimeMillis();
            Map<String, String> parametersMap = buildParametersMap(httpServletRequest);
            String parameters = Joiner.on(",").withKeyValueSeparator("=").join(Optional.ofNullable(parametersMap).orElseGet(Collections::emptyMap));
            if (!endpointsToIgnore.contains(path)) {
                gbqService.addRecord(new LoggerGBQModel(method, path, userName, time, code, bodyString,
                        parameters,
                        processSecrets(requestBody.toString())));
            }
            requestBody.setLength(0);
            gbqService.postRecordsToGBQ();
        }
    }

    String processSecrets(Object body) {
        Map<String, Object> map;
        try {
            if (body instanceof String) {
                map = mapper.readValue((String) body, Map.class);
            } else {
                map = mapper.convertValue(body, Map.class);
            }
            return mapper.writeValueAsString(processSecretsInMap(map));
        } catch (Exception e) {
            return "Cant deserialize body";
        }

    }

    Map<String, Object> processSecretsInMap(Map<String, Object> input) {
        for (String key : input.keySet()) {
            Object value = input.get(key);
            if (value instanceof String) {
                if (isSecret(key)) {
                    input.put(key, "********");
                }
            } else if (value instanceof Map) {
                input.put(key, processSecretsInMap((Map<String, Object>) value));
            } else if (value instanceof Collection) {
                Collection<Object> collection = (Collection<Object>) value;
                for (Object object : collection) {
                    if (object instanceof Map) {
                        processSecretsInMap((Map<String, Object>) object);
                    }
                }
            }
        }
        return input;
    }

    private static boolean isSecret(String property) {
        String lowerCase = property.toLowerCase();
        return lowerCase.contains("pass")
                || lowerCase.contains("secret")
                || lowerCase.contains("key");
    }

    private String setUserName() {
        try {
            return reltioUserHolder.getUserDetails().getUsername();
        } catch (NullPointerException e) {
            return "anonymous";
        }
    }

    protected Map<String, String> buildParametersMap(HttpServletRequest httpServletRequest) {
        Map<String, String> resultMap = new HashMap<>();
        Enumeration<String> parameterNames = httpServletRequest.getParameterNames();

        while (parameterNames.hasMoreElements()) {
            String key = parameterNames.nextElement();
            String value = String.valueOf(httpServletRequest.getParameter(key));
            resultMap.put(key, value);
        }

        return resultMap;
    }

    protected Map<String, String> buildHeadersMap(HttpServletRequest request) {
        Map<String, String> map = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String key = headerNames.nextElement();
            //Lets skip logging token
            if (key.equalsIgnoreCase("authorization")) {
                continue;
            }
            String value = request.getHeader(key);
            map.put(key, value);
        }

        return map;
    }

}
