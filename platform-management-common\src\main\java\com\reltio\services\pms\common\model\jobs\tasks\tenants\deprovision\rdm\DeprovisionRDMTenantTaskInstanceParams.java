package com.reltio.services.pms.common.model.jobs.tasks.tenants.deprovision.rdm;

import com.reltio.services.pms.common.model.jobs.tasks.TaskInstanceParams;

/**
 * DeprovisionRDMTenantTaskInstanceParams
 * Created by apylkov
 */
public interface DeprovisionRDMTenantTaskInstanceParams extends TaskInstanceParams {
    String getRdmTenant();

    String getTenantId();

    boolean detachAllAssociatedRdmTenants();

    boolean deleteRdmIfNoAssociatedMdm();

    boolean isFailOnError();
}
