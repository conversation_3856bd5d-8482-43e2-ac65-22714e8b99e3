package com.reltio.services.pms.convertors.sales.addon;

import com.reltio.services.pms.common.sales.PMSProductName;
import com.reltio.services.pms.common.sales.TenantPurpose;
import com.reltio.services.pms.common.sales.model.SalesConfig;
import com.reltio.services.pms.common.sales.model.addon.QueueProductConfig;
import com.reltio.services.pms.convertors.sales.AbstractTenantAddOnProductProvider;
import com.reltio.services.pms.service.SalesPackageService;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

@Service
public class QueuesProductProvider extends AbstractTenantAddOnProductProvider<QueueProductConfig> {


    public QueuesProductProvider(SalesPackageService salesPackageService) {
        super(salesPackageService);
    }


    @Override
    public PMSProductName getProductName() {
        return PMSProductName.QUEUES;
    }

    @Override
    public Map<String, Set<String>> getProductCodesByTenant() {
        return salesPackageService.getSalesAddOnsByProductCodes(PMSProductName.QUEUES);
    }

    @Override
    public QueueProductConfig getProductConfig(Set<SalesConfig> salesConfigs, Set<String> tenantCodes, String currentTenantCode) {
        if (salesConfigs.isEmpty()) {
            return null;
        }

        QueueProductConfig queuesConfig = new QueueProductConfig();
        queuesConfig.setPmsProductName(getProductName());
        queuesConfig.addAllSalesConfigs(salesConfigs);

        Integer totalTenants = tenantCodes.size();
        Integer totalQueues = getQuantity(queuesConfig);
        queuesConfig.setQuantity(1);
        if (totalTenants == 1) {
            queuesConfig.setQuantity(totalQueues);
        } else if (totalTenants < totalQueues) {
            Integer extraQueues = totalQueues - totalTenants;
            // get sales product code for PROD tenant purpose
            String productTenantCode = salesPackageService.getProductCodeByTenantPurpose(TenantPurpose.PROD);
            if (productTenantCode.equals(currentTenantCode)) {
                queuesConfig.setQuantity(extraQueues + 1);
            }
        }

        return queuesConfig;
    }

}
