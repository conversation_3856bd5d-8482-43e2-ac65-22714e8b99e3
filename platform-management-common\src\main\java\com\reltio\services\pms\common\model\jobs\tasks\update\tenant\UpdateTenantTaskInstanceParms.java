package com.reltio.services.pms.common.model.jobs.tasks.update.tenant;

import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServicesEnablementBaseTaskInstanceParams;
import com.reltio.services.pms.common.model.tenant.TenantSize;

import java.util.List;

public interface UpdateTenantTaskInstanceParms extends ServicesEnablementBaseTaskInstanceParams {

    String getLoqateProcesses();

    List<String> getCleanseRegions();

    TenantSize getTenantSize();
}
