package com.reltio.services.pms.controller;

import com.reltio.auth.annotation.ReltioSecured;
import com.reltio.security.domain.Pms;
import com.reltio.services.pms.common.model.StorageTemplate;
import com.reltio.services.pms.service.StorageTemplateService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;

@RestController
@RequestMapping(value = "/api/v1/environments/{environmentName}/templates", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name= "Templates")
@ReltioSecured(resourceClass = Pms.Environment.Config.Templates.class)
public class StorageTemplateController {

    private final StorageTemplateService storageTemplateService;

    @Autowired
    public StorageTemplateController(StorageTemplateService storageTemplateService) {
        this.storageTemplateService = storageTemplateService;
    }

    @GetMapping("/{templateName}")
    public StorageTemplate getStorageTemplate(@PathVariable(value = "environmentName") String environment,
                                              @PathVariable(value = "templateName") String name) {
        StorageTemplate template = storageTemplateService.getStorageTemplate(name, environment);
        return template;
    }

    @GetMapping("/all")
    public Collection<StorageTemplate> getAllTemplates(@PathVariable(value = "environmentName") String environment) {
        Collection<StorageTemplate> templates = storageTemplateService.getAllTemplates(environment);
        return templates;
    }

    @PutMapping(value = "/{templateName}", consumes = MediaType.APPLICATION_JSON_VALUE)
    public StorageTemplate updateStorageTemplate(@PathVariable(value = "environmentName") String environment,
                                                 @PathVariable(value = "templateName") String name,
                                                 @RequestBody StorageTemplate template) {
        return storageTemplateService.updateStorageTemplate(environment, name, template);
    }

    @PostMapping(value = "", consumes = MediaType.APPLICATION_JSON_VALUE)
    public StorageTemplate updateStorageTemplate(@PathVariable(value = "environmentName") String environment,
                                                 @RequestBody StorageTemplate template) {
        return storageTemplateService.createStorageTemplate(environment, template);
    }

}
