package com.reltio.services.pms.common.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.stream.Collectors;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TenantStorageConfig extends BaseFirestoreEntity {

    private final String default_skeleton_filename = "storageModelWithCassandra.json";
    private final String configSkeleton;
    private StorageTemplate storageTemplate;

    @JsonProperty("offering")
    private String offering; //life sciences, finance, etc

    @JsonProperty("environment")
    private String environment;

    @JsonProperty("tenantName")
    private String tenantName;

    @JsonProperty("customerName")
    private String customerName;

    @JsonProperty("tenantId")
    private String tenantId;

    @JsonProperty("matchHost")
    private String matchHost;

    @JsonProperty("matchCluster")
    private String matchCluster;

    @JsonProperty("dataHost")
    private String dataHost;

    @JsonProperty("dataCluster")
    private String dataCluster;

    @JsonProperty("historyProject")
    private String historyProject;

    @JsonProperty("historyInstance")
    private String historyInstance;

    @JsonProperty("historyTableName")
    private String historyTableName;

    @JsonProperty("esHost")
    private String esHost;

    @JsonProperty("esCluster")
    private String esCluster;

    @JsonProperty("indexOvStrategy")
    private String indexOvStrategy;

    @JsonProperty("matchingStrategy")
    private String matchingStrategy;

    @JsonProperty("dataloadUrl")
    private String dataloadUrl;

    @JsonCreator
    public TenantStorageConfig(@JsonProperty("environment") String environment,
                               @JsonProperty("offering") String offering,
                               @JsonProperty("tenantName") String tenantName,
                               @JsonProperty("customerName") String customerName,
                               @JsonProperty("tenantId") String tenantId,
                               @JsonProperty("matchHost") String matchHost,
                               @JsonProperty("matchCluster") String matchCluster,
                               @JsonProperty("dataHost") String dataHost,
                               @JsonProperty("dataCluster") String dataCluster,
                               @JsonProperty("historyProject") String historyProject,
                               @JsonProperty("historyInstance") String historyInstance,
                               @JsonProperty("historyTableName") String historyTableName,
                               @JsonProperty("esHost") String esHost,
                               @JsonProperty("esCluster") String esCluster,
                               @JsonProperty("indexOvStrategy") String indexOvStrategy,
                               @JsonProperty("matchingStrategy") String matchingStrategy,
                               @JsonProperty("dataloadUrl") String dataloadUrl) {
        this.environment = environment;
        this.offering = offering;
        this.tenantName = tenantName;
        this.customerName = customerName;
        this.tenantId = tenantId;
        this.matchHost = matchHost;
        this.matchCluster = matchCluster;
        this.dataHost = dataHost;
        this.dataCluster = dataCluster;
        this.historyProject = historyProject;
        this.historyInstance = historyInstance;
        this.historyTableName = historyTableName;
        this.esHost = esHost;
        this.esCluster = esCluster;
        this.indexOvStrategy = indexOvStrategy;
        this.matchingStrategy = matchingStrategy;
        this.dataloadUrl = dataloadUrl;
        this.configSkeleton = new BufferedReader(new InputStreamReader(TenantStorageConfig.class.getClassLoader().getResourceAsStream(default_skeleton_filename))).lines()
                .collect(Collectors.joining("\n"));
    }

    public TenantStorageConfig(String environment, String offering, String tenantName, String customerName, String tenantId, StorageTemplate storageTemplate) {
        this.environment = environment;
        this.offering = offering;
        this.tenantName = tenantName;
        this.customerName = customerName;
        this.tenantId = tenantId;
        this.storageTemplate = storageTemplate;
        this.matchCluster = storageTemplate.getMatchCluster();
        this.matchHost = storageTemplate.getMatchHost();
        this.dataHost = storageTemplate.getDataHost();
        this.dataCluster = storageTemplate.getDataCluster();
        this.historyProject = storageTemplate.getHistoryProject();
        this.historyInstance = storageTemplate.getHistoryInstance();
        this.historyTableName = String.format("%s-%s", storageTemplate.getEnvironment(), tenantId);
        this.esHost = storageTemplate.getEsHost();
        this.esCluster = storageTemplate.getEsCluster();
        this.indexOvStrategy = storageTemplate.getIndexOvStrategy();
        this.matchingStrategy = storageTemplate.getMatchingStrategy();
        this.dataloadUrl = storageTemplate.getDataLoadUrl();
        this.configSkeleton = new BufferedReader(new InputStreamReader(TenantStorageConfig.class.getClassLoader().getResourceAsStream(default_skeleton_filename))).lines()
                .collect(Collectors.joining("\n"));
    }

    @Override
    public String toString() {
        return configSkeleton.replace("#tenantName#", tenantName)
                .replace("#customerName#", customerName)
                .replace("#tenantId#", tenantId)
                .replace("#matchCluster#", matchCluster)
                .replace("#matchHosts#", matchHost)
                .replace("#dataHost#", dataHost)
                .replace("#dataCluster#", dataCluster)
                .replace("#tenantKeySpace#", tenantId.toLowerCase())
                .replace("#historyProject#", historyProject)
                .replace("#historyInstance#", historyInstance)
                .replace("#historyTableName#", historyTableName)
                .replace("#esHost#", esHost)
                .replace("#esCluster#", esCluster)
                .replace("#indexOvStrategy#", indexOvStrategy)
                .replace("#matchingStrategy#", matchingStrategy);
    }

    @Override
    public String getID() {
        return tenantId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TenantStorageConfig that = (TenantStorageConfig) o;

        if (environment != null ? !environment.equals(that.environment) : that.environment != null) return false;
        return tenantId != null ? tenantId.equals(that.tenantId) : that.tenantId == null;
    }

    @Override
    public int hashCode() {
        int result = environment != null ? environment.hashCode() : 0;
        result = 31 * result + (tenantId != null ? tenantId.hashCode() : 0);
        return result;
    }

    public String getDataloadUrl() {
        return dataloadUrl;
    }

    public String getEnvironment() {
        return environment;
    }
}
