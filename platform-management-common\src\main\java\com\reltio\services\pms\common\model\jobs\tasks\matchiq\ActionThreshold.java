package com.reltio.services.pms.common.model.jobs.tasks.matchiq;


import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

public class ActionThreshold {
    @JsonProperty(value = "type")
    private String type;

    @JsonProperty(value = "threshold")
    private String threshold;

    @JsonProperty(value = "label")
    private String label;

    @JsonCreator
    public ActionThreshold(@JsonProperty(value = "type") String type,
                           @JsonProperty(value = "threshold")String threshold,
                           @JsonProperty(value = "label") String label) {
        this.type = type;
        this.threshold = threshold;
        this.label = label;
    }

    public String getType() {
        return type;
    }

    public ActionThreshold setType(String type) {
        this.type = type;
        return this;
    }

    public String getThreshold() {
        return threshold;
    }

    public ActionThreshold setThreshold(String threshold) {
        this.threshold = threshold;
        return this;
    }

    public String getLabel() {
        return label;
    }

    public ActionThreshold setLabel(String label) {
        this.label = label;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ActionThreshold)) {
            return false;
        }

        ActionThreshold that = (ActionThreshold) o;
        return Objects.equals(getType(), that.getType()) &&
                Objects.equals(getThreshold(), that.getThreshold()) &&
                Objects.equals(getLabel(), that.getLabel());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getType(), getThreshold(), getLabel());
    }

    @Override
    public String toString() {
        return "ActionThreshold{" +
                "type='" + type + '\'' +
                ", threshold='" + threshold + '\'' +
                ", label='" + label + '\'' +
                '}';
    }
}
