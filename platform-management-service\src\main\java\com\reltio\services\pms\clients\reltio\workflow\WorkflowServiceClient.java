package com.reltio.services.pms.clients.reltio.workflow;

import com.fasterxml.jackson.databind.JsonNode;
import com.reltio.services.pms.clients.reltio.IReltioClient;
import com.reltio.services.pms.common.PMSRestTemplate;
import com.reltio.services.pms.common.exception.PlatformManagementErrorCode;
import com.reltio.services.pms.common.exception.PlatformManagementException;
import com.reltio.services.pms.common.model.Environment;
import com.reltio.services.pms.common.model.ServiceType;
import com.reltio.services.pms.service.environment.EnvironmentService;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Service
public class WorkflowServiceClient implements IReltioClient {
    private static final Logger LOG = Logger.getLogger(WorkflowServiceClient.class);
    private static final String WORKFLOW_REGISTRATION_URL = "%s/workflow-adapter/workflow/registrations";
    private static final String WORKFLOW_CLEAN_TENANT_URL = "%s/workflow-adapter/workflow/%s/jobs/cleanTenant";
    private static final String WORKFLOW_TASK_STATUS_URL = "%s/workflow-adapter/workflow/jobs/%s";
    private static final String WORKFLOW_TERMINATE_PROCESS_URL = "%s/workflow-adapter/workflow/%s/jobs/cleanProcessInstances";
    private static final String WORKFLOW_PROCESS_INSTANCE_DETAILS_URL = "%s/workflow-adapter/workflow/jobs/%s";
    private static final String COMPLETED_STATUS = "COMPLETED";
    private static final String FAILED_STATUS = "FAILED";
    private static final String ENVIRONMENT_URL = "EnvironmentURL";
    private static final String TASK_STATUS = "state";
    private static final String TASK_ID = "backgroundTaskId";
    private static final int MAX_RETRIES = 20;
    private static final long RETRY_DELAY = 60_000;
    private final PMSRestTemplate rest;
    private final EnvironmentService environmentService;
    private final String STATUS_ENDPOINT = "%s/workflow-adapter/workflow/status";

    @Autowired
    public WorkflowServiceClient(PMSRestTemplate rest,
                                 EnvironmentService environmentService) {
        this.rest = rest;
        this.environmentService = environmentService;
    }

    public boolean isEnabledForTenant(String tenantId, String env) {
        String response = fetchWorkflowRegistration(env);
        return response.contains(tenantId);
    }

    @Cacheable(value = "rihEnvironments", cacheManager = "rihCache", key = "'workflow-' + #env")
    public String fetchWorkflowRegistration(String env) {
        Environment environment = environmentService.getEnvironment(env);
        if (environment.getDefaultUrls().containsKey(ServiceType.WORKFLOW)) {
            String workflowUrl = environment.getDefaultUrls().get(ServiceType.WORKFLOW);
            String registrationUrl = String.format(WORKFLOW_REGISTRATION_URL, workflowUrl);

            HttpHeaders headers = new HttpHeaders();
            headers.put(ENVIRONMENT_URL, Collections.singletonList(environment.getUrl()));
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
            return rest.exchange(registrationUrl, HttpMethod.GET, requestEntity, String.class);
        }
        return "";
    }

    public boolean checkWorkflowStatus(String env) {
        Environment environment = environmentService.getEnvironment(env);
        String workflowUrl = environment.getDefaultUrls().get(ServiceType.WORKFLOW);
        String workFlowStatusURL = String.format(STATUS_ENDPOINT, workflowUrl);
        try {
            ResponseEntity<JsonNode> response = rest.getForEntity(workFlowStatusURL, JsonNode.class);
            Objects.requireNonNull(response.getBody(), "Response body is null");
            JsonNode statusResponse = response.getBody();
            return statusResponse != null && statusResponse.has("status") && statusResponse.get("status").asText().equals("OK");
        } catch (Exception e) {
            return false;
        }
    }

    public void enableWorkflow(String env,
                               String tenantId) {
        switchWorkflow(env, tenantId, true);
    }

    private HttpHeaders getHeaders(Environment environment) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.put(ENVIRONMENT_URL, Collections.singletonList(environment.getUrl()));
        return headers;
    }

    public void terminateProcessInstances(String tenantId, String env) {
        Environment environment = environmentService.getEnvironment(env);
        String workflowUrl = environment.getDefaultUrls().get(ServiceType.WORKFLOW);
        if (Objects.isNull(workflowUrl)) {
            LOG.warn(String.format("Workflow Service is not supported in env: %s", env));
            return;
        }
        if (isEnabledForTenant(tenantId, env)) {
            String workflowTerminationUrl = String.format(WORKFLOW_TERMINATE_PROCESS_URL, workflowUrl, tenantId);
            Map<String, Set<String>> body = new HashMap<>();
            Set<String> tenantSet = new HashSet<>();
            tenantSet.add(tenantId);
            body.put("tenantIds", tenantSet);
            try {
                HttpEntity<Map<String, Set<String>>> requestEntity = new HttpEntity<>(body, getHeaders(environment));
                rest.exchange(
                        workflowTerminationUrl, HttpMethod.POST, requestEntity, JsonNode.class);
            } catch (Exception ex) {
                if (ex instanceof HttpClientErrorException.BadRequest && ex.getMessage().contains("com.reltio.workflow.core.KeyedException")) {
                    LOG.warn(String.format("Exception raised while cleaning workflow with message %s -->", ex.getMessage()));
                } else {
                    LOG.error(String.format("Error while terminating process instance for %s@%s, Error: %s", tenantId, env, ex.getMessage()), ex);
                    throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), ex);
                }
            }
        }
    }

    public JsonNode getProcessInstancesDetails(String processInstanceId, String env) {
        Environment environment = environmentService.getEnvironment(env);
        String workflowUrl = environment.getDefaultUrls().get(ServiceType.WORKFLOW);
        String workflowProcessInstanceDetailsURL = String.format(WORKFLOW_PROCESS_INSTANCE_DETAILS_URL,
                workflowUrl, processInstanceId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.put(ENVIRONMENT_URL, Collections.singletonList(environment.getUrl()));

        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
        JsonNode response = rest.exchange(
                workflowProcessInstanceDetailsURL, HttpMethod.GET, requestEntity, JsonNode.class);

        return response;
    }

    public JsonNode runUntilStatus(String processInstanceId, String env) {
        JsonNode response = getProcessInstancesDetails(processInstanceId, env);

        if (!COMPLETED_STATUS.contains(StringUtils.strip(response.get("state").toString(), "\""))) {
            String message = String.format("Task is not complete. Task status is %s",
                    StringUtils.strip(response.get("state").toString(), "\""));
            throw new PlatformManagementException(PlatformManagementErrorCode.RETRY_EXCEPTION, HttpStatus.NOT_FOUND.value(), message);
        }

        return response;
    }

    /**
     * Deregister tenantid from workflow service
     *
     * @param env      env to use
     * @param tenantId tenant id to deregister
     */
    public void disableWorkflow(String tenantId,
                                String env) {
        switchWorkflow(env, tenantId, false);
    }

    /**
     * Enable or Disable workflow
     *
     * @param env      env to use to get workflow url
     * @param tenantId tenant id to use for request
     * @param enable   register or deregister tenant
     */
    private void switchWorkflow(String env,
                                String tenantId, boolean enable) {
        Environment environment = environmentService.getEnvironment(env);
        String workflowUrl = environment.getDefaultUrls().get(ServiceType.WORKFLOW);
        String workFlowRegistrationURL = String.format(WORKFLOW_REGISTRATION_URL, workflowUrl);

        Map<String, Set<String>> tenantToRegister = Collections.singletonMap("tenantIds", Collections.singleton(tenantId));
        HttpHeaders headers = new HttpHeaders();
        headers.put(ENVIRONMENT_URL, Collections.singletonList(environment.getUrl()));
        HttpEntity<Map<String, Set<String>>> request = new HttpEntity<>(tenantToRegister, headers);
        if (enable) {
            rest.postForEntity(workFlowRegistrationURL, request, JsonNode.class);
        } else {
            if (isEnabledForTenant(tenantId, env)) {
                    rest.exchange(workFlowRegistrationURL, HttpMethod.DELETE, request, String.class);
            }
        }
    }

    public String cleanupWorkflowService(String tenantId, String env) {
        Environment environment = environmentService.getEnvironment(env);
        String workflowUrl = environment.getDefaultUrls().get(ServiceType.WORKFLOW);
        String cleanTenantJobUrl = String.format(WORKFLOW_CLEAN_TENANT_URL, workflowUrl, tenantId);

        HttpHeaders headers = getHeaders(environment);
        HttpEntity<Void> request = new HttpEntity<>(headers);

        JsonNode responseBody = rest.exchange(cleanTenantJobUrl, HttpMethod.POST, request, JsonNode.class);

        if (responseBody != null && responseBody.has(TASK_ID)) {
            return responseBody.get(TASK_ID).asText();
        } else {
            throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "Failed to retrieve background task ID for cleanup.");
        }
    }

    public boolean checkTaskStatus(String taskId, String env) {
        Environment environment = environmentService.getEnvironment(env);
        String workflowUrl = environment.getDefaultUrls().get(ServiceType.WORKFLOW);
        String taskStatusUrl = String.format(WORKFLOW_TASK_STATUS_URL, workflowUrl, taskId);

        HttpHeaders headers = getHeaders(environment);
        HttpEntity<Void> request = new HttpEntity<>(headers);

        for (int attempt = 0; attempt < MAX_RETRIES; attempt++) {
            try {
                JsonNode responseBody = rest.exchange(taskStatusUrl, HttpMethod.GET, request, JsonNode.class);

                if (responseBody != null && responseBody.has(TASK_STATUS)) {
                    String status = responseBody.get(TASK_STATUS).asText();
                    if (COMPLETED_STATUS.equalsIgnoreCase(status)) {
                        return true;
                    } else if (FAILED_STATUS.equalsIgnoreCase(status)) {
                        return false;
                    }
                } else {
                    throw new PlatformManagementException(PlatformManagementErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "Failed to retrieve task status.");
                }
            } catch (Exception ex) {
                LOG.error(String.format("Error during task status check, Error: %s", ex.getMessage()), ex);
            }
            try {
                Thread.sleep(RETRY_DELAY);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        return false;
    }
}
