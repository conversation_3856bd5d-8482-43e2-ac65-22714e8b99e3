package com.reltio.services.pms.common.model.jobs.tasks.auth;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.reltio.services.pms.common.model.AuthTaskUtil;
import com.reltio.services.pms.common.model.config.CustomSettings;
import com.reltio.services.pms.common.model.jobs.tasks.services.enablement.base.ServiceEnablementBaseTaskInstance;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

public class AuthTaskInstanceDeserializer extends StdDeserializer<AuthTaskInstance> {
    private final ObjectMapper mapper;

    public AuthTaskInstanceDeserializer() {
        this(null);
    }

    public AuthTaskInstanceDeserializer(Class<?> vc) {
        super(vc);
        mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Override
    public AuthTaskInstance deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        JsonNode node = jp.getCodec().readTree(jp);
        ServiceEnablementBaseTaskInstance taskInstance = getGeneralTaskInstance(node);
        AuthTaskInstance.CustomerConfig customerConfig = getCustomerConfig(node);
        AuthTaskInstance.ClientConfig clientConfig = getClientConfig(node);
        AuthTaskInstance.UserConfig userConfig = getUserConfig(node);
        List<AuthTaskInstance.GroupConfig> groupConfig=getGroupConfig(node);
        String rdmTenant = node.hasNonNull(AuthTaskConstants.RDM_TENANT_ID) ? node.get(AuthTaskConstants.RDM_TENANT_ID).asText() : null;
        Boolean reuseRdmTenant = node.hasNonNull(AuthTaskConstants.REUSE_RDM_TENANT) ? node.get(AuthTaskConstants.REUSE_RDM_TENANT).asBoolean() : null;
        return new AuthTaskInstance(taskInstance.getTaskId(), taskInstance.getName(), taskInstance.getJobId(), taskInstance.getStartTime(), taskInstance.getFinishTime(), taskInstance.getStatus(),
                taskInstance.getLastUpdatedTime(), taskInstance.getTaskFailureContext(), taskInstance.getExecutingNodeName(), taskInstance.getServiceNodeStatus(), taskInstance.getFailedTenants(),
                taskInstance.getTotalProcessedTenants(), taskInstance.getTenantsForServiceEnablement(), taskInstance.getEvents(),
                taskInstance.getEnvId(), customerConfig, clientConfig, userConfig, groupConfig, rdmTenant,
                reuseRdmTenant);
    }

    private ServiceEnablementBaseTaskInstance getGeneralTaskInstance(JsonNode node) {
        if (node.hasNonNull("type")) {
            ((ObjectNode) node).remove("type");
        }

        return mapper.convertValue(node, ServiceEnablementBaseTaskInstance.class);
    }

    private AuthTaskInstance.UserConfig getUserConfig(JsonNode node) {
        AuthTaskInstance.UserConfig userConfig = new AuthTaskInstance.UserConfig();
        if (node.hasNonNull(AuthTaskConstants.USER_CONFIG)) {
            userConfig = mapper.convertValue(node.get(AuthTaskConstants.USER_CONFIG), AuthTaskInstance.UserConfig.class);
        } else {
            if (node.hasNonNull(AuthTaskConstants.PREASSIGNED_ROLE_NAME)) {
                userConfig.setRequesterSpecificRoleName(node.get(AuthTaskConstants.PREASSIGNED_ROLE_NAME).textValue());
            }
            if (node.hasNonNull(AuthTaskConstants.ACCESS_LEVEL) && node.get(AuthTaskConstants.ACCESS_LEVEL).hasNonNull(AuthTaskConstants.MAPPING_NAME)
                    && node.get(AuthTaskConstants.ACCESS_LEVEL).hasNonNull(AuthTaskConstants.ACCESS_LEVEL)) {
                userConfig.setAccessLevel(node.get(AuthTaskConstants.ACCESS_LEVEL).get(AuthTaskConstants.ACCESS_LEVEL).textValue());
                userConfig.setRoleMappingName(node.get(AuthTaskConstants.ACCESS_LEVEL).get(AuthTaskConstants.MAPPING_NAME).textValue());
            }
            if (node.hasNonNull(AuthTaskConstants.REQUESTER_EMAIL)) {
                userConfig.setOwners(Collections.singleton(node.get(AuthTaskConstants.REQUESTER_EMAIL).asText()));
            }
            if(node.hasNonNull(AuthTaskConstants.USER_NAME_PREFIX)){
                userConfig.setUserNamePrefix(node.get(AuthTaskConstants.USER_NAME_PREFIX).asText());
            }
            if(node.hasNonNull(AuthTaskConstants.DEFAULT_PASSWORD)){
                userConfig.setDefaultPassword(node.get(AuthTaskConstants.DEFAULT_PASSWORD).asBoolean());
            }
        }
        return userConfig;
    }

    public  List<AuthTaskInstance.GroupConfig> getGroupConfig(JsonNode node) {
        List<AuthTaskInstance.GroupConfig> groupConfig =new ArrayList<>();
        JsonNode groupConfigListJsonNode=node.get("groupConfig");
        if(Objects.nonNull(groupConfigListJsonNode)) {
            Integer groupConfigListSize = groupConfigListJsonNode.size();

            for (int elements = 0; elements < groupConfigListSize; elements++) {
                JsonNode groupConfigJsonNode = groupConfigListJsonNode.get(elements);
                AuthTaskInstance.GroupConfig groupConfigInstance = new AuthTaskInstance.GroupConfig();
                if (groupConfigJsonNode.hasNonNull(AuthTaskConstants.GROUP_ID)) {
                    groupConfigInstance.setGroupId((groupConfigJsonNode.get(AuthTaskConstants.GROUP_ID).asText()));
                }
                if (groupConfigJsonNode.hasNonNull(AuthTaskConstants.Customer)) {
                    groupConfigInstance.setCustomer((groupConfigJsonNode.get(AuthTaskConstants.Customer).asText()));
                }
                if (groupConfigJsonNode.hasNonNull(AuthTaskConstants.ROLES)) {
                    ArrayNode rolesArrayNode = (ArrayNode) groupConfigJsonNode.get(AuthTaskConstants.ROLES);
                    Set<String> rolesSet = new HashSet<>();
                    if (rolesArrayNode != null) {
                        for (JsonNode roleNode : rolesArrayNode) {
                            rolesSet.add(roleNode.asText());
                        }
                    }
                    groupConfigInstance.setRoles(rolesSet);
                }
                groupConfig.add(groupConfigInstance);
            }
        }
        return groupConfig;

    }

    private CustomSettings getCustomerSettings(JsonNode node) {
        CustomSettings customerSettings = new CustomSettings();
        if (node.get(AuthTaskConstants.CUSTOMER_CONFIG).hasNonNull(AuthTaskConstants.CUSTOMER_SETTINGS)) {
            customerSettings = mapper.convertValue(node.get(AuthTaskConstants.CUSTOMER_CONFIG).get(AuthTaskConstants.CUSTOMER_SETTINGS), CustomSettings.class);
        }
        return customerSettings;
    }

    private AuthTaskInstance.CustomerConfig getCustomerConfig(JsonNode node) {
        AuthTaskInstance.CustomerConfig customerConfig = new AuthTaskInstance.CustomerConfig();
        if (node.hasNonNull(AuthTaskConstants.CUSTOMER_CONFIG)) {
            customerConfig = mapper.convertValue(node.get(AuthTaskConstants.CUSTOMER_CONFIG), AuthTaskInstance.CustomerConfig.class);
            customerConfig = AuthTaskUtil.setCaseSensitiveLoginEnabled(node,customerConfig);
            if (node.get(AuthTaskConstants.CUSTOMER_CONFIG).hasNonNull(AuthTaskConstants.CUSTOMER_SETTINGS))
            {
                customerConfig.setCustomSettings(getCustomerSettings(node));
            }
        } else {
            if (node.hasNonNull(AuthTaskConstants.CUSTOMER_SPECIFIC_ROLE_BODY)) {
                customerConfig.setRoleBody(node.get(AuthTaskConstants.CUSTOMER_SPECIFIC_ROLE_BODY));
            }
            if (node.hasNonNull(AuthTaskConstants.CUSTOMER_ID)) {
                customerConfig.setCustomerId(node.get(AuthTaskConstants.CUSTOMER_ID).textValue());
            }
            if(node.hasNonNull(AuthTaskConstants.CUSTOMER_ID_PREFIX)){
                customerConfig.setCustomerIdPrefix(node.get(AuthTaskConstants.CUSTOMER_ID_PREFIX).asText());
            }
        }
        return customerConfig;
    }

    private AuthTaskInstance.ClientConfig getClientConfig(JsonNode node) {
        AuthTaskInstance.ClientConfig clientConfig = new AuthTaskInstance.ClientConfig();
        if (node.hasNonNull(AuthTaskConstants.CLIENT_CONFIG)) {
            clientConfig = mapper.convertValue(node.get(AuthTaskConstants.CLIENT_CONFIG), AuthTaskInstance.ClientConfig.class);
        } else {
            if (node.hasNonNull(AuthTaskConstants.CLIENTS_COUNT)) {
                clientConfig.setClientsCount(node.get(AuthTaskConstants.CLIENTS_COUNT).asLong());
            }
            Set<String> clients = new HashSet<>();
            if (node.hasNonNull(AuthTaskConstants.CUSTOMER_ID)) {
                clients.add(node.get(AuthTaskConstants.CUSTOMER_ID).textValue());
                clientConfig.setApplicationClients(clients);
            }
        }
        return clientConfig;
    }

}